using Application.IntegrationApis.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.IntegrationApis.Commands;

/// <summary>
/// Handler for CreateIntegrationApiWithConfigurationCommand
/// </summary>
public class CreateIntegrationApiWithConfigurationCommandHandler : IRequestHandler<CreateIntegrationApiWithConfigurationCommand, Result<CreateIntegrationApiWithConfigurationResponseDto>>
{
    private readonly IRepository<IntegrationApi> _integrationApiRepository;
    private readonly IRepository<IntegrationConfiguration> _configurationRepository;
    private readonly IReadRepository<Integration> _integrationRepository;
    private readonly IReadRepository<Product> _productRepository;
    private readonly IReadRepository<Domain.Entities.Object> _objectRepository;
    private readonly ILogger<CreateIntegrationApiWithConfigurationCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateIntegrationApiWithConfigurationCommandHandler(
        IRepository<IntegrationApi> integrationApiRepository,
        IRepository<IntegrationConfiguration> configurationRepository,
        IReadRepository<Integration> integrationRepository,
        IReadRepository<Product> productRepository,
        IReadRepository<Domain.Entities.Object> objectRepository,
        ILogger<CreateIntegrationApiWithConfigurationCommandHandler> logger)
    {
        _integrationApiRepository = integrationApiRepository;
        _configurationRepository = configurationRepository;
        _integrationRepository = integrationRepository;
        _productRepository = productRepository;
        _objectRepository = objectRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<CreateIntegrationApiWithConfigurationResponseDto>> Handle(
        CreateIntegrationApiWithConfigurationCommand request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting CreateIntegrationApiWithConfiguration operation for Integration {IntegrationId}, Object {ObjectId}", 
            request.IntegrationId, request.ObjectId);

        try
        {
            // Step 1: Validate dependencies
            var validationResult = await ValidateRequestAsync(request, cancellationToken);
            if (!validationResult.Succeeded)
            {
                return Result<CreateIntegrationApiWithConfigurationResponseDto>.Failure(validationResult.Message ?? "Validation failed");
            }

            var (integration, product, targetObject) = validationResult.Data!;

            // Step 2: Create IntegrationApi
            var integrationApi = await CreateIntegrationApiAsync(request, cancellationToken);
            _logger.LogInformation("Created IntegrationApi with ID: {IntegrationApiId}", integrationApi.Id);

            // Step 3: Create IntegrationConfiguration (linking Integration + IntegrationApi + Object)
            var configuration = await CreateIntegrationConfigurationAsync(
                request.IntegrationId, 
                integrationApi.Id, 
                request, 
                cancellationToken);
            _logger.LogInformation("Created IntegrationConfiguration with ID: {ConfigurationId}", configuration.Id);

            // Step 4: Build response
            var response = BuildResponse(integrationApi, configuration, integration, product, targetObject);

            _logger.LogInformation("Successfully completed CreateIntegrationApiWithConfiguration operation. Created {RecordCount} records", 
                response.Summary.TotalRecordsCreated);

            return Result<CreateIntegrationApiWithConfigurationResponseDto>.Success(response, 
                "IntegrationApi and configuration created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during CreateIntegrationApiWithConfiguration operation for Integration {IntegrationId}", 
                request.IntegrationId);
            return Result<CreateIntegrationApiWithConfigurationResponseDto>.Failure(
                $"Failed to create integration API with configuration: {ex.Message}");
        }
    }

    /// <summary>
    /// Validate the request and dependencies
    /// </summary>
    private async Task<Result<(Integration Integration, Product Product, Domain.Entities.Object Object)>> ValidateRequestAsync(
        CreateIntegrationApiWithConfigurationCommand request, 
        CancellationToken cancellationToken)
    {
        // Validate required fields
        if (string.IsNullOrWhiteSpace(request.Name))
        {
            return Result<(Integration, Product, Domain.Entities.Object)>.Failure("API name is required");
        }

        if (string.IsNullOrWhiteSpace(request.EndpointUrl))
        {
            return Result<(Integration, Product, Domain.Entities.Object)>.Failure("Endpoint URL is required");
        }

        // Validate Integration exists
        var integration = await _integrationRepository.GetByIdAsync(request.IntegrationId, cancellationToken);
        if (integration == null)
        {
            return Result<(Integration, Product, Domain.Entities.Object)>.Failure($"Integration with ID {request.IntegrationId} not found");
        }

        // Validate Product exists
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null)
        {
            return Result<(Integration, Product, Domain.Entities.Object)>.Failure($"Product with ID {request.ProductId} not found");
        }

        // Validate Object exists
        var targetObject = await _objectRepository.GetByIdAsync(request.ObjectId, cancellationToken);
        if (targetObject == null)
        {
            return Result<(Integration, Product, Domain.Entities.Object)>.Failure($"Object with ID {request.ObjectId} not found");
        }

        return Result<(Integration, Product, Domain.Entities.Object)>.Success((integration, product, targetObject));
    }

    /// <summary>
    /// Create IntegrationApi entity
    /// </summary>
    private async Task<IntegrationApi> CreateIntegrationApiAsync(
        CreateIntegrationApiWithConfigurationCommand request, 
        CancellationToken cancellationToken)
    {
        var integrationApi = new IntegrationApi
        {
            ProductId = request.ProductId,
            Name = request.Name.Trim(),
            EndpointUrl = request.EndpointUrl.Trim(),
            Schema = request.Schema,
            IsActive = request.IsActive
        };

        return await _integrationApiRepository.AddAsync(integrationApi, cancellationToken);
    }

    /// <summary>
    /// Create IntegrationConfiguration entity
    /// </summary>
    private async Task<IntegrationConfiguration> CreateIntegrationConfigurationAsync(
        Guid integrationId,
        Guid integrationApiId,
        CreateIntegrationApiWithConfigurationCommand request, 
        CancellationToken cancellationToken)
    {
        var configuration = new IntegrationConfiguration
        {
            IntegrationId = integrationId,
            IntegrationApiId = integrationApiId,
            ObjectId = request.ObjectId,
            Direction = request.Direction,
            IsActive = request.IsConfigActive
        };

        return await _configurationRepository.AddAsync(configuration, cancellationToken);
    }

    /// <summary>
    /// Build the response DTO
    /// </summary>
    private static CreateIntegrationApiWithConfigurationResponseDto BuildResponse(
        IntegrationApi integrationApi,
        IntegrationConfiguration configuration,
        Integration integration,
        Product product,
        Domain.Entities.Object targetObject)
    {
        return new CreateIntegrationApiWithConfigurationResponseDto
        {
            IntegrationApi = new IntegrationApiDetailsDto
            {
                Id = integrationApi.Id,
                ProductId = integrationApi.ProductId,
                ProductName = product.Name,
                Name = integrationApi.Name,
                EndpointUrl = integrationApi.EndpointUrl,
                Schema = integrationApi.Schema,
                IsActive = integrationApi.IsActive,
                CreatedAt = integrationApi.CreatedAt,
                CreatedBy = integrationApi.CreatedBy
            },
            Configuration = new IntegrationConfigurationDetailsDto
            {
                Id = configuration.Id,
                IntegrationId = configuration.IntegrationId,
                IntegrationName = integration.Name,
                IntegrationApiId = configuration.IntegrationApiId,
                ObjectId = configuration.ObjectId,
                ObjectName = targetObject.Name,
                Direction = configuration.Direction,
                IsActive = configuration.IsActive,
                CreatedAt = configuration.CreatedAt,
                CreatedBy = configuration.CreatedBy
            },
            Summary = new OperationSummaryDto
            {
                TotalRecordsCreated = 2,
                CreatedEntityIds = new List<Guid> { integrationApi.Id, configuration.Id },
                OperationTimestamp = DateTime.UtcNow,
                Message = "IntegrationApi and IntegrationConfiguration created successfully"
            }
        };
    }
}
