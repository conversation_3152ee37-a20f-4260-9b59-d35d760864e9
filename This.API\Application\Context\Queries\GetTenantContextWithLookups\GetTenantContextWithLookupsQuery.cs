using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetTenantContextWithLookups;

/// <summary>
/// Query to get tenant context with its associated tenant lookups by tenant context ID
/// </summary>
public class GetTenantContextWithLookupsQuery : IRequest<Result<TenantContextWithLookupsDto>>
{
    /// <summary>
    /// TenantContext ID
    /// </summary>
    public Guid TenantContextId { get; set; }

    /// <summary>
    /// Whether to include inactive tenant lookups
    /// </summary>
    public bool IncludeInactiveLookups { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetTenantContextWithLookupsQuery(Guid tenantContextId, bool includeInactiveLookups = false)
    {
        TenantContextId = tenantContextId;
        IncludeInactiveLookups = includeInactiveLookups;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetTenantContextWithLookupsQuery()
    {
    }
}
