using Application.ComprehensiveEntityData.DTOs;
using Abstraction.Database.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.ComprehensiveEntityData.Queries;

/// <summary>
/// Handler for getting comprehensive subscription data across all tenants
/// </summary>
public class GetComprehensiveSubscriptionsQueryHandler : IRequestHandler<GetComprehensiveSubscriptionsQuery, Result<ComprehensiveSubscriptionResponseDto>>
{
    private readonly IComprehensiveSubscriptionRepository _repository;
    private readonly ILogger<GetComprehensiveSubscriptionsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetComprehensiveSubscriptionsQueryHandler(
        IComprehensiveSubscriptionRepository repository,
        ILogger<GetComprehensiveSubscriptionsQueryHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the comprehensive subscription data query
    /// </summary>
    public async Task<Result<ComprehensiveSubscriptionResponseDto>> Handle(
        GetComprehensiveSubscriptionsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing comprehensive subscription data query - TenantId: {TenantId}, ProductId: {ProductId}",
                request.TenantId, request.ProductId);

            var startTime = DateTime.UtcNow;

            var responseObj = await _repository.GetComprehensiveSubscriptionsAsync(
                tenantId: request.TenantId,
                productId: request.ProductId,
                status: request.Status,
                subscriptionType: request.SubscriptionType,
                isActive: request.IsActive,
                isExpired: request.IsExpired,
                pricingTier: request.PricingTier,
                searchTerm: request.SearchTerm,
                startDateFrom: request.StartDateFrom,
                startDateTo: request.StartDateTo,
                endDateFrom: request.EndDateFrom,
                endDateTo: request.EndDateTo,
                expiringWithinDays: request.ExpiringWithinDays,
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                orderBy: request.OrderBy,
                orderDirection: request.OrderDirection,
                includeSummary: request.IncludeSummary,
                cancellationToken: cancellationToken);

            var response = (ComprehensiveSubscriptionResponseDto)responseObj;
            var processingTime = DateTime.UtcNow - startTime;

            _logger.LogInformation("Successfully processed comprehensive subscription data in {ProcessingTime}ms",
                processingTime.TotalMilliseconds);

            return Result<ComprehensiveSubscriptionResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing comprehensive subscription data query");
            return Result<ComprehensiveSubscriptionResponseDto>.Failure("Error processing comprehensive subscription data");
        }
    }
}
