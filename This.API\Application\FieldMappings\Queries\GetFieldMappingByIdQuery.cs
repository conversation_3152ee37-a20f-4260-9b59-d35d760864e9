using Application.FieldMappings.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Queries;

/// <summary>
/// Get field mapping by ID query
/// </summary>
public class GetFieldMappingByIdQuery : IRequest<Result<ViewFieldMappingDto>>
{
    /// <summary>
    /// Field Mapping ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetFieldMappingByIdQuery(Guid id)
    {
        Id = id;
    }
}
