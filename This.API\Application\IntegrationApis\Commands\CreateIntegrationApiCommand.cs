using Application.IntegrationApis.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationApis.Commands;

/// <summary>
/// Create integration API command
/// </summary>
public class CreateIntegrationApiCommand : CreateIntegrationApiDto, IRequest<Result<ViewIntegrationApiDto>>
{
}

/// <summary>
/// Update integration API command
/// </summary>
public class UpdateIntegrationApiCommand : UpdateIntegrationApiDto, IRequest<Result<ViewIntegrationApiDto>>
{
}

/// <summary>
/// Delete integration API command
/// </summary>
public class DeleteIntegrationApiCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Integration API ID to delete
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteIntegrationApiCommand(Guid id)
    {
        Id = id;
    }
}

/// <summary>
/// Create multiple integration APIs command
/// </summary>
public class CreateIntegrationApisCommand : IRequest<Result<List<ViewIntegrationApiDto>>>
{
    /// <summary>
    /// List of integration APIs to create
    /// </summary>
    public List<CreateIntegrationApiDto> IntegrationApis { get; set; } = new();
}
