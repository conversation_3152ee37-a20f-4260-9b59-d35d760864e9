/**
 * Tenant API Test Page
 * A dedicated page for testing tenant-aware API functionality
 */

import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Form, Alert, Badge, ListGroup } from 'react-bootstrap';
import { Database, Play, Copy, RefreshCw, CheckCircle, XCircle, Info } from 'lucide-react';
import { useTenantContext } from '../hooks/useTenantContext';
import { withTenantHeader, debugTenantApi, copyApiCall } from '../utils/tenantApiUtils';

export const TenantApiTest: React.FC = () => {
  const { selectedTenantId, selectedTenant, availableTenants } = useTenantContext();
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTest, setSelectedTest] = useState('getSubscriptions');

  // Test scenarios
  const apiTests = [
    {
      id: 'getSubscriptions',
      name: 'Get Subscriptions',
      description: 'Test GET /api/subscriptions with tenant header',
      endpoint: '/subscriptions',
      method: 'GET',
      requiresTenant: true,
      testFunction: withTenantHeader.getSubscriptions
    },
    {
      id: 'getSubscriptionStats',
      name: 'Get Subscription Stats',
      description: 'Test GET /api/subscriptions/stats with tenant header',
      endpoint: '/subscriptions/stats',
      method: 'GET',
      requiresTenant: true,
      testFunction: withTenantHeader.getSubscriptionStats
    },
    {
      id: 'createProductStructure',
      name: 'Create Product Structure',
      description: 'Test POST /api/comprehensive-entity/create-product-structure with tenant header',
      endpoint: '/comprehensive-entity/create-product-structure',
      method: 'POST',
      requiresTenant: true,
      testFunction: withTenantHeader.createProductStructure
    }
  ];

  const sampleProductData = {
    products: [
      {
        name: "Test Product via Tenant API",
        type: "product",
        metadata: [
          {
            name: "Name",
            type: "Text",
            description: "Product name",
            required: true,
            isActive: true
          },
          {
            name: "Version",
            type: "Text",
            description: "Product version",
            required: true,
            isActive: true,
            defaultValue: "1.0.0"
          }
        ]
      }
    ]
  };

  // Run API test
  const runTest = async (testId: string) => {
    setIsLoading(true);
    const test = apiTests.find(t => t.id === testId);
    if (!test) return;

    const startTime = Date.now();
    const testResult: any = {
      id: testId,
      name: test.name,
      endpoint: test.endpoint,
      method: test.method,
      tenant: selectedTenantId,
      startTime: new Date().toISOString(),
      success: false,
      duration: 0,
      response: null,
      error: null
    };

    try {
      // Check if tenant is required
      if (test.requiresTenant && !selectedTenantId) {
        throw new Error('This test requires a tenant to be selected');
      }

      // Run the test
      let response;
      if (testId === 'createProductStructure') {
        response = await test.testFunction(sampleProductData);
      } else {
        response = await test.testFunction();
      }

      testResult.success = true;
      testResult.response = response;
      testResult.duration = Date.now() - startTime;
    } catch (error: any) {
      testResult.success = false;
      testResult.error = error.message || 'Unknown error';
      testResult.duration = Date.now() - startTime;
    }

    setTestResults(prev => [testResult, ...prev.slice(0, 9)]); // Keep last 10 results
    setIsLoading(false);
  };

  // Copy curl command to clipboard
  const copyCurlCommand = async (testId: string) => {
    const test = apiTests.find(t => t.id === testId);
    if (!test) return;

    try {
      if (testId === 'createProductStructure') {
        await copyApiCall.toCurl(test.endpoint, test.method, sampleProductData);
      } else {
        await copyApiCall.toCurl(test.endpoint, test.method);
      }
      alert('Curl command copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy curl command:', error);
    }
  };

  // Log current configuration
  useEffect(() => {
    debugTenantApi.logApiConfig();
  }, [selectedTenantId]);

  return (
    <div className="d-flex flex-column min-vh-100">
      <main className="flex-grow-1 py-4">
        <Container>
          <Row className="mb-4">
            <Col>
              <div className="d-flex justify-content-between align-items-center">
                <h1 className="h3 mb-0">
                  <Database className="me-2" />
                  Tenant API Test Console
                </h1>
                <Badge bg={selectedTenantId ? 'success' : 'warning'}>
                  {selectedTenantId ? `Tenant: ${selectedTenant?.name || selectedTenantId}` : 'No Tenant Selected'}
                </Badge>
              </div>
              <p className="text-muted mt-2">
                Test API endpoints with automatic tenant header injection
              </p>
            </Col>
          </Row>

          {/* Current Configuration */}
          <Row className="mb-4">
            <Col md={6}>
              <Card>
                <Card.Header>
                  <h5 className="mb-0">Current Configuration</h5>
                </Card.Header>
                <Card.Body>
                  <ListGroup variant="flush">
                    <ListGroup.Item className="d-flex justify-content-between">
                      <span>Base URL:</span>
                      <code>https://localhost:7222/api</code>
                    </ListGroup.Item>
                    <ListGroup.Item className="d-flex justify-content-between">
                      <span>Selected Tenant:</span>
                      <span>
                        {selectedTenantId ? (
                          <Badge bg="success">{selectedTenant?.name || selectedTenantId}</Badge>
                        ) : (
                          <Badge bg="secondary">None</Badge>
                        )}
                      </span>
                    </ListGroup.Item>
                    <ListGroup.Item className="d-flex justify-content-between">
                      <span>Available Tenants:</span>
                      <span>{availableTenants.length}</span>
                    </ListGroup.Item>
                    <ListGroup.Item className="d-flex justify-content-between">
                      <span>Tenant Header:</span>
                      <code>{selectedTenantId ? `tenant: ${selectedTenantId}` : 'Not set'}</code>
                    </ListGroup.Item>
                  </ListGroup>
                </Card.Body>
              </Card>
            </Col>

            <Col md={6}>
              <Card>
                <Card.Header>
                  <h5 className="mb-0">Test Controls</h5>
                </Card.Header>
                <Card.Body>
                  <Form.Group className="mb-3">
                    <Form.Label>Select Test</Form.Label>
                    <Form.Select 
                      value={selectedTest} 
                      onChange={(e) => setSelectedTest(e.target.value)}
                      disabled={isLoading}
                    >
                      {apiTests.map(test => (
                        <option key={test.id} value={test.id}>
                          {test.method} - {test.name}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>

                  <div className="d-flex gap-2">
                    <Button 
                      variant="primary" 
                      onClick={() => runTest(selectedTest)}
                      disabled={isLoading}
                      className="d-flex align-items-center gap-2"
                    >
                      {isLoading ? <RefreshCw size={16} className="spinning" /> : <Play size={16} />}
                      {isLoading ? 'Running...' : 'Run Test'}
                    </Button>
                    
                    <Button 
                      variant="outline-secondary" 
                      onClick={() => copyCurlCommand(selectedTest)}
                      className="d-flex align-items-center gap-2"
                    >
                      <Copy size={16} />
                      Copy cURL
                    </Button>
                  </div>

                  {!selectedTenantId && (
                    <Alert variant="warning" className="mt-3 mb-0">
                      <Info size={16} className="me-2" />
                      <strong>Note:</strong> Please select a tenant from the sidebar to test tenant-specific APIs.
                    </Alert>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </Row>

          {/* Test Results */}
          <Row>
            <Col>
              <Card>
                <Card.Header>
                  <h5 className="mb-0">Test Results</h5>
                </Card.Header>
                <Card.Body>
                  {testResults.length === 0 ? (
                    <div className="text-center py-5">
                      <Database size={48} className="mb-3 text-muted" />
                      <p className="text-muted">No test results yet. Run a test to see results.</p>
                    </div>
                  ) : (
                    <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
                      {testResults.map((result, index) => (
                        <Card key={index} className="mb-3">
                          <Card.Header className="d-flex justify-content-between align-items-center">
                            <div className="d-flex align-items-center gap-2">
                              {result.success ? (
                                <CheckCircle size={16} className="text-success" />
                              ) : (
                                <XCircle size={16} className="text-danger" />
                              )}
                              <span className="fw-bold">{result.name}</span>
                              <Badge bg={result.success ? 'success' : 'danger'}>
                                {result.success ? 'Success' : 'Failed'}
                              </Badge>
                            </div>
                            <small className="text-muted">
                              {result.duration}ms
                            </small>
                          </Card.Header>
                          <Card.Body>
                            <Row>
                              <Col md={6} className="mb-3">
                                <strong>Request Details:</strong>
                                <div className="mt-2">
                                  <div><strong>Method:</strong> {result.method}</div>
                                  <div><strong>Endpoint:</strong> {result.endpoint}</div>
                                  <div><strong>Tenant:</strong> {result.tenant || 'None'}</div>
                                  <div><strong>Time:</strong> {new Date(result.startTime).toLocaleTimeString()}</div>
                                </div>
                              </Col>
                              <Col md={6}>
                                <strong>Response:</strong>
                                <div className="mt-2">
                                  {result.success ? (
                                    <pre className="bg-light p-2 rounded small" style={{ maxHeight: '200px', overflow: 'auto' }}>
                                      {JSON.stringify(result.response, null, 2)}
                                    </pre>
                                  ) : (
                                    <Alert variant="danger" className="mb-0">
                                      <strong>Error:</strong> {result.error}
                                    </Alert>
                                  )}
                                </div>
                              </Col>
                            </Row>
                          </Card.Body>
                        </Card>
                      ))}
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </main>

      <style jsx>{`
        .spinning {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};