using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetPagedContexts;

/// <summary>
/// Handler for GetPagedContextsQuery
/// </summary>
public class GetPagedContextsQueryHandler : IRequestHandler<GetPagedContextsQuery, PagedResponse<ContextDto, object>>
{
    private readonly IRepository<Domain.Entities.Context> _contextRepository;
    private readonly ILogger<GetPagedContextsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetPagedContextsQueryHandler(
        IRepository<Domain.Entities.Context> contextRepository,
        ILogger<GetPagedContextsQueryHandler> logger)
    {
        _contextRepository = contextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PagedResponse<ContextDto, object>> Handle(GetPagedContextsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting paged contexts: Page={PageNumber}, Size={PageSize}, IncludeInactive={IncludeInactive}, Category={Category}, SearchTerm={SearchTerm}",
                request.PageNumber, request.PageSize, request.IncludeInactive, request.Category, request.SearchTerm);

            // Calculate pagination
            var skip = (request.PageNumber - 1) * request.PageSize;

            // Create specifications for data and count
            var dataSpec = new ContextsWithFiltersSpec(
                searchTerm: request.SearchTerm,
                category: request.Category,
                includeInactive: request.IncludeInactive,
                skip: skip,
                take: request.PageSize);

            var countSpec = new ContextsCountSpec(
                searchTerm: request.SearchTerm,
                category: request.Category,
                includeInactive: request.IncludeInactive);

            // Execute queries using specifications
            var contexts = await _contextRepository.ListAsync(dataSpec, cancellationToken);
            var totalCount = await _contextRepository.CountAsync(countSpec, cancellationToken);

            // Map to DTOs
            var contextDtos = contexts.Adapt<List<ContextDto>>();

            _logger.LogInformation("Successfully retrieved {Count} contexts out of {TotalCount} total", contextDtos.Count, totalCount);

            return PagedResponse<ContextDto, object>.Success(contextDtos, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting paged contexts");
            return PagedResponse<ContextDto, object>.Failure($"Error retrieving paged contexts: {ex.Message}");
        }
    }
}
