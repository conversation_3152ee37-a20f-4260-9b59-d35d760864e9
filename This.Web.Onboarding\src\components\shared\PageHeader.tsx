import React from 'react';
import { PlusCircle } from 'lucide-react';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  onAdd?: () => void;
  addLabel?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  onAdd,
  addLabel = 'Add New',
}) => {
  return (
    <div className="flex justify-between items-center mb-6">
      <div>
        <h1 className="text-lg font-bold text-gray-900 font-lexend">{title}</h1>
        {subtitle && <p className="mt-1 text-sm text-gray-500">{subtitle}</p>}
      </div>
      {onAdd && (
        <button
          type="button"
          onClick={onAdd}
          className="btn btn-primary"
        >
          <PlusCircle size={18} />
          <span>{addLabel}</span>
        </button>
      )}
    </div>
  );
};

export default PageHeader;