using MediatR;
using Shared.Common.Response;
using Application.ObjectValues.Queries.GetMetadataKeyValues;

namespace Application.ObjectValues.Queries.GetObjectLookupData;

/// <summary>
/// Query to get ObjectLookup data and corresponding metadata key values
/// </summary>
public class GetObjectLookupDataQuery : IRequest<Result<GetObjectLookupDataResponse>>
{
    /// <summary>
    /// ObjectLookup ID to fetch data for
    /// </summary>
    public Guid ObjectLookupId { get; set; }

    /// <summary>
    /// Tenant ID for the query
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Whether to create/recreate the view before querying
    /// </summary>
    public bool CreateView { get; set; } = true;

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectLookupDataQuery(Guid objectLookupId, string tenantId, bool createView = true, int pageNumber = 1, int pageSize = 10)
    {
        ObjectLookupId = objectLookupId;
        TenantId = tenantId;
        CreateView = createView;
        PageNumber = pageNumber;
        PageSize = pageSize;
    }
}

/// <summary>
/// Response for GetObjectLookupDataQuery
/// </summary>
public class GetObjectLookupDataResponse
{
    /// <summary>
    /// ObjectLookup configuration data
    /// </summary>
    public ObjectLookupDto ObjectLookup { get; set; } = new();

    /// <summary>
    /// Metadata key values from the corresponding object
    /// </summary>
    public ObjectLookUpValuesResponse ObjectLookUpValues { get; set; } = new();

    /// <summary>
    /// Response message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// DTO for ObjectLookup data
/// </summary>
public class ObjectLookupDto
{
    /// <summary>
    /// ObjectLookup ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Name of the lookup configuration
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Source type for the lookup (object name)
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// Field to use for display text
    /// </summary>
    public string DisplayField { get; set; } = string.Empty;

    /// <summary>
    /// Field to use for the value
    /// </summary>
    public string ValueField { get; set; } = string.Empty;

    /// <summary>
    /// Optional Object ID if source type is Object-based
    /// </summary>
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Metadata field to use for display (for metadata-based lookups)
    /// </summary>
    public string? MetadataFieldForDisplay { get; set; }

    /// <summary>
    /// Metadata field to use for value (for metadata-based lookups)
    /// </summary>
    public string? MetadataFieldForValue { get; set; }

    /// <summary>
    /// Whether this lookup supports tenant filtering
    /// </summary>
    public bool SupportsTenantFiltering { get; set; }

    /// <summary>
    /// Field to sort by
    /// </summary>
    public string SortBy { get; set; } = string.Empty;

    /// <summary>
    /// Sort order (ASC/DESC)
    /// </summary>
    public string SortOrder { get; set; } = string.Empty;

    /// <summary>
    /// Whether the lookup is active
    /// </summary>
    public bool IsActive { get; set; }
}
