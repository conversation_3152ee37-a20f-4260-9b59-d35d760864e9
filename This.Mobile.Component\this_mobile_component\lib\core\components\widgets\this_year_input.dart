import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// A customizable year input widget following the 'this_componentName_relatedTo' naming convention
/// This widget handles year input with validation, range constraints, and various input methods
class ThisYearInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final int? value;
  final ValueChanged<int?> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final int? minYear;
  final int? maxYear;
  final bool showDropdown;
  final bool allowFutureYears;
  final bool allowPastYears;
  final String? Function(int?)? customValidation;

  const ThisYearInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.minYear,
    this.maxYear,
    this.showDropdown = false,
    this.allowFutureYears = true,
    this.allowPastYears = true,
    this.customValidation,
  });

  @override
  State<ThisYearInput> createState() => _ThisYearInputState();
}

class _ThisYearInputState extends State<ThisYearInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];

  int get _currentYear => DateTime.now().year;
  int get _effectiveMinYear => widget.minYear ?? (_allowPastYears ? 1900 : _currentYear);
  int get _effectiveMaxYear => widget.maxYear ?? (_allowFutureYears ? _currentYear + 100 : _currentYear);

  bool get _allowPastYears => widget.allowPastYears;
  bool get _allowFutureYears => widget.allowFutureYears;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value?.toString() ?? '');
    _focusNode = FocusNode();
    _validateValue(widget.value);
  }

  @override
  void didUpdateWidget(ThisYearInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value?.toString() ?? '';
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  List<String> _validateValue(int? value) {
    final errors = <String>[];

    // Required validation
    if (widget.required && value == null) {
      errors.add('${widget.label} is required');
      return errors;
    }

    if (value != null) {
      // Year range validation
      if (value < _effectiveMinYear) {
        errors.add('Year must be ${_effectiveMinYear} or later');
      } else if (value > _effectiveMaxYear) {
        errors.add('Year must be ${_effectiveMaxYear} or earlier');
      }

      // Past/Future year validation
      if (!_allowPastYears && value < _currentYear) {
        errors.add('Past years are not allowed');
      } else if (!_allowFutureYears && value > _currentYear) {
        errors.add('Future years are not allowed');
      }

      // Custom validation
      if (widget.customValidation != null) {
        final customError = widget.customValidation!(value);
        if (customError != null) {
          errors.add(customError);
        }
      }
    }

    return errors;
  }

  void _handleChange(String text) {
    int? newValue;
    if (text.isNotEmpty) {
      newValue = int.tryParse(text);
    }

    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  void _handleDropdownChange(int? newValue) {
    _controller.text = newValue?.toString() ?? '';
    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  List<int> _getYearOptions() {
    final years = <int>[];
    for (int year = _effectiveMinYear; year <= _effectiveMaxYear; year++) {
      years.add(year);
    }
    return years.reversed.toList(); // Most recent years first
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Input Field or Dropdown
        if (widget.showDropdown)
          DropdownButtonFormField<int>(
            value: widget.value,
            onChanged: widget.disabled || widget.readOnly ? null : _handleDropdownChange,
            decoration: InputDecoration(
              hintText: widget.placeholder ?? 'Select year',
              hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
              errorText: hasErrors ? _errors.first : null,
              errorStyle: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
              suffixIcon: const Icon(Icons.calendar_today, size: 20),
            ),
            style: LexendTextStyles.lexend14Regular.copyWith(
              color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
            ),
            dropdownColor: ColorPalette.darkToneInk,
            items: _getYearOptions().map((year) {
              return DropdownMenuItem<int>(
                value: year,
                child: Text(
                  year.toString(),
                  style: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.black,
                  ),
                ),
              );
            }).toList(),
          )
        else
          TextFormField(
            controller: _controller,
            focusNode: _focusNode,
            enabled: !widget.disabled,
            readOnly: widget.readOnly,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(4),
            ],
            onChanged: _handleChange,
            decoration: InputDecoration(
              hintText: widget.placeholder ?? 'YYYY',
              hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
              errorText: hasErrors ? _errors.first : null,
              errorStyle: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
              suffixIcon: const Icon(Icons.calendar_today, size: 20),
            ),
            style: LexendTextStyles.lexend14Regular.copyWith(
              color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
            ),
          ),

        // Helper text for year range
        if (!hasErrors && (_effectiveMinYear != 1900 || _effectiveMaxYear != _currentYear + 100))
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Valid range: $_effectiveMinYear - $_effectiveMaxYear',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          ),
      ],
    );
  }
}
