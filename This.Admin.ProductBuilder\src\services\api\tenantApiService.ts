/**
 * Tenant API Service
 * Handles all tenant-related API operations using the Secondary API (Port 7243)
 *
 * API Routing:
 * - GET /tenants - Load tenant dropdown options (Secondary API)
 * - POST /tenants/upsert - Create/update tenants (Secondary API)
 * - GET /tenants/{id} - Get specific tenant (Secondary API)
 * - POST /tenants/{id}/validate - Validate tenant connection (Secondary API)
 */

import { BaseApiService } from '../baseApiService';
import type { PaginationParams, PaginatedResponse, ApiResult } from '../types';
import { HttpClientFactory } from '../httpClient';
import { TENANT_ENDPOINTS, EndpointBuilder } from '../../config/endpoints';
import { dualApiService, apiOperations } from './dualApiService';

// Tenant interfaces
export interface TenantDto {
  id: string;
  name: string;
  connectionString: string;
  readReplicaConnectionString: string | null;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string | null;
}

export interface GetTenantsParams extends PaginationParams {
  isActive?: boolean;
  searchTerm?: string;
  adminEmail?: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface TenantsResponse extends PaginatedResponse<TenantDto> {}

export interface CreateTenantRequest {
  id: string;
  name: string;
  connectionString: string;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string;
}

export interface UpdateTenantRequest extends Partial<CreateTenantRequest> {
  id: string;
}

export interface TenantApiResponse {
  succeeded: boolean;
  message: string | null;
  errors: string[] | null;
  data: TenantDto[];
}

/**
 * Tenant API Service Class
 */
export class TenantApiService extends BaseApiService {
  private dataClient: any;
  private primaryClient: any;

  constructor() {
    // Use tenant data client for read operations
    super(HttpClientFactory.tenantDataClient, 'TenantAPI');
    this.dataClient = HttpClientFactory.tenantDataClient;
    this.primaryClient = HttpClientFactory.primaryClient; // Use primary API (port 7222) for upsert operations
  }

  /**
   * Get tenants with filtering and pagination
   */
  async getTenants(params: GetTenantsParams = {}): Promise<TenantApiResponse> {
    try {
      this.log('getTenants', { params });

      // Build query parameters
      const queryParams = {
        ...this.buildPaginationParams(params),
        ...this.buildSearchParams(params),
        ...this.buildSortParams(params),
      };

      // Add tenant-specific parameters
      if (params.adminEmail) queryParams.adminEmail = params.adminEmail;

      const endpoint = EndpointBuilder.buildTenantEndpoint(queryParams);
      const response = await this.get<TenantApiResponse>(endpoint);

      return response;
    } catch (error) {
      this.handleError('getTenants', error);
    }
  }

  /**
   * Get tenant by ID
   */
  async getTenantById(id: string): Promise<TenantDto> {
    try {
      this.log('getTenantById', { id });

      const endpoint = TENANT_ENDPOINTS.TENANT_BY_ID(id);
      const response = await this.get<TenantDto>(endpoint);

      return response;
    } catch (error) {
      this.handleError('getTenantById', error);
    }
  }

  /**
   * Create new tenant (using creation API)
   */
  async createTenant(data: CreateTenantRequest): Promise<string> {
    try {
      this.log('createTenant', { data });

      // Use primary client for tenant creation (port 7222)
      const response = await this.primaryClient.post<ApiResult<string>>(
        TENANT_ENDPOINTS.TENANT_UPSERT,
        data
      );

      if (!response.data.succeeded) {
        throw new Error(response.data.message || 'Failed to create tenant');
      }

      return response.data.data;
    } catch (error) {
      this.handleError('createTenant', error);
    }
  }

  /**
   * Update existing tenant (upsert operation)
   */
  async updateTenant(data: UpdateTenantRequest): Promise<string> {
    try {
      this.log('updateTenant', { data });

      // Use primary client for tenant updates (port 7222)
      const response = await this.primaryClient.post<ApiResult<string>>(
        TENANT_ENDPOINTS.TENANT_UPSERT,
        data
      );

      if (!response.data.succeeded) {
        throw new Error(response.data.message || 'Failed to update tenant');
      }

      return response.data.data;
    } catch (error) {
      this.handleError('updateTenant', error);
    }
  }

  /**
   * Upsert tenant (create or update)
   */
  async upsertTenant(data: CreateTenantRequest): Promise<string> {
    try {
      this.log('upsertTenant', { data });

      return this.createTenant(data);
    } catch (error) {
      this.handleError('upsertTenant', error);
    }
  }

  /**
   * Get all active tenants
   */
  async getActiveTenants(): Promise<TenantDto[]> {
    try {
      this.log('getActiveTenants', {});

      const response = await this.getTenants({
        isActive: true,
        pageSize: 1000,
        pageNumber: 1
      });

      if (response.succeeded && response.data) {
        return response.data.filter(tenant => tenant.isActive);
      }

      return [];
    } catch (error) {
      this.log('getActiveTenants_fallback', { error: error.message });
      // Return empty array on error instead of throwing
      return [];
    }
  }

  /**
   * Search tenants
   */
  async searchTenants(searchTerm: string, params: GetTenantsParams = {}): Promise<TenantApiResponse> {
    try {
      this.log('searchTenants', { searchTerm, params });

      return this.getTenants({
        ...params,
        searchTerm,
      });
    } catch (error) {
      this.handleError('searchTenants', error);
    }
  }

  /**
   * Get tenants by admin email
   */
  async getTenantsByAdminEmail(adminEmail: string, params: GetTenantsParams = {}): Promise<TenantApiResponse> {
    try {
      this.log('getTenantsByAdminEmail', { adminEmail, params });

      return this.getTenants({
        ...params,
        adminEmail,
      });
    } catch (error) {
      this.handleError('getTenantsByAdminEmail', error);
    }
  }

  /**
   * Validate tenant connection
   */
  async validateTenantConnection(tenantId: string): Promise<boolean> {
    try {
      this.log('validateTenantConnection', { tenantId });

      // Try to get tenant by ID to validate connection
      await this.getTenantById(tenantId);
      return true;
    } catch (error) {
      this.log('validateTenantConnection_failed', { tenantId, error: error.message });
      return false;
    }
  }

  /**
   * Check if tenant exists
   */
  async tenantExists(tenantId: string): Promise<boolean> {
    try {
      this.log('tenantExists', { tenantId });

      await this.getTenantById(tenantId);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get tenant statistics
   */
  async getTenantStats(): Promise<{
    totalTenants: number;
    activeTenants: number;
    inactiveTenants: number;
  }> {
    try {
      this.log('getTenantStats', {});

      const response = await this.getTenants({
        pageSize: 1000,
        pageNumber: 1
      });

      if (!response.succeeded || !response.data) {
        return {
          totalTenants: 0,
          activeTenants: 0,
          inactiveTenants: 0,
        };
      }

      const totalTenants = response.data.length;
      const activeTenants = response.data.filter(t => t.isActive).length;
      const inactiveTenants = totalTenants - activeTenants;

      return {
        totalTenants,
        activeTenants,
        inactiveTenants,
      };
    } catch (error) {
      this.handleError('getTenantStats', error);
    }
  }

  /**
   * Format tenant for dropdown display
   */
  formatTenantForDropdown(tenant: TenantDto): string {
    return `${tenant.name} (${tenant.id})`;
  }

  /**
   * Sort tenants for dropdown
   */
  sortTenantsForDropdown(tenants: TenantDto[]): TenantDto[] {
    return tenants.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Filter tenants by status
   */
  filterTenantsByStatus(tenants: TenantDto[], isActive: boolean): TenantDto[] {
    return tenants.filter(tenant => tenant.isActive === isActive);
  }

  /**
   * Get tenant dropdown options (uses Secondary API - Port 7243)
   * Specifically for Add Subscription modal and other tenant selection dropdowns
   */
  async getTenantDropdownOptions(): Promise<Array<{ value: string; label: string; tenant: TenantDto }>> {
    try {
      this.log('getTenantDropdownOptions', { api: 'Secondary (Port 7243)' });

      // Use the dual API service to ensure we're hitting the Secondary API
      const response = await apiOperations.tenants.getAll({
        isActive: true,
        pageSize: 1000,
        pageNumber: 1
      });

      let tenants: TenantDto[] = [];

      if (response.succeeded && response.data) {
        tenants = response.data.filter(tenant => tenant.isActive);
      } else {
        // Fallback to existing method
        tenants = await this.getActiveTenants();
      }

      const sortedTenants = this.sortTenantsForDropdown(tenants);

      return sortedTenants.map(tenant => ({
        value: tenant.id,
        label: this.formatTenantForDropdown(tenant),
        tenant,
      }));
    } catch (error) {
      this.log('getTenantDropdownOptions_error', { error: error.message });
      return [];
    }
  }

  /**
   * Get tenant dropdown options for Add Subscription modal
   * Explicitly uses Secondary API (Port 7243) as per requirements
   */
  async getTenantDropdownOptionsForSubscription(): Promise<Array<{ value: string; label: string; tenant: TenantDto }>> {
    try {
      this.log('getTenantDropdownOptionsForSubscription', {
        api: 'Secondary (Port 7243)',
        purpose: 'Add Subscription Modal'
      });

      // Direct call to Secondary API using dual API service
      const tenants = await dualApiService.secondary<TenantDto[]>('/tenants', 'GET', {
        isActive: true,
        pageSize: 1000,
        pageNumber: 1
      });

      if (!tenants || !Array.isArray(tenants)) {
        this.log('getTenantDropdownOptionsForSubscription_fallback', { reason: 'Invalid response format' });
        return this.getTenantDropdownOptions();
      }

      const activeTenants = tenants.filter(tenant => tenant.isActive);
      const sortedTenants = this.sortTenantsForDropdown(activeTenants);

      return sortedTenants.map(tenant => ({
        value: tenant.id,
        label: this.formatTenantForDropdown(tenant),
        tenant,
      }));
    } catch (error) {
      this.log('getTenantDropdownOptionsForSubscription_error', {
        error: error.message,
        fallback: 'Using primary method'
      });

      // Fallback to the regular method
      return this.getTenantDropdownOptions();
    }
  }
}

// Export singleton instance
export const tenantApiService = new TenantApiService();
