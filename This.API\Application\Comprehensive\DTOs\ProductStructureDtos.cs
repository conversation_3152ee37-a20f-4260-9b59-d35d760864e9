using System.ComponentModel.DataAnnotations;

namespace Application.Comprehensive.DTOs;

/// <summary>
/// Root structure for creating comprehensive product structure
/// </summary>
public class ProductStructureRequest
{
    /// <summary>
    /// List of products to create
    /// </summary>
    [Required]
    public List<ProductStructureDto> Products { get; set; } = new();
}

/// <summary>
/// Product structure with metadata and objects
/// </summary>
public class ProductStructureDto
{
    /// <summary>
    /// Product ID - if provided, use as database ID; if not provided, generate new GUID
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Product description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Product version
    /// </summary>
    [MaxLength(50)]
    public string? Version { get; set; } = "1.0.0";

    /// <summary>
    /// Whether the product is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Product metadata definitions
    /// </summary>
    public List<MetadataStructureDto>? Metadata { get; set; }

    /// <summary>
    /// Product objects with hierarchical structure
    /// </summary>
    public List<ObjectStructureDto>? Objects { get; set; }
}

/// <summary>
/// Object structure with metadata and child objects
/// </summary>
public class ObjectStructureDto
{
    /// <summary>
    /// Object ID - if provided, use as database ID; if not provided, generate new GUID
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Object description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Object metadata definitions
    /// </summary>
    public List<MetadataStructureDto>? Metadata { get; set; }

    /// <summary>
    /// Child objects (for hierarchical structure)
    /// </summary>
    public List<ObjectStructureDto>? Objects { get; set; }

    /// <summary>
    /// Display definitions for this object
    /// </summary>
    public List<DisplayStructureDto>? Displays { get; set; }
}

/// <summary>
/// Metadata structure definition
/// </summary>
public class MetadataStructureDto
{
    /// <summary>
    /// Metadata ID - if provided, use as database ID; if not provided, generate new GUID
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Metadata name
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Data type name
    /// </summary>
    [Required]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Metadata description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether the field is required
    /// </summary>
    public bool Required { get; set; } = false;

    /// <summary>
    /// Whether the metadata is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Default value for the metadata
    /// </summary>
    public string? DefaultValue { get; set; }

    /// <summary>
    /// Whether the field is visible
    /// </summary>
    public bool IsVisible { get; set; } = true;

    /// <summary>
    /// Whether the field is readonly
    /// </summary>
    public bool IsReadonly { get; set; } = false;
}

/// <summary>
/// Display structure definition for objects
/// </summary>
public class DisplayStructureDto
{
    /// <summary>
    /// Display ID - if provided, use as database ID; if not provided, generate new GUID
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Display name - 'List', 'View', 'Update', 'Create', 'Card', or custom names
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the display
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Display name for UI
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default display for this type
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// URL route template for navigation
    /// </summary>
    [MaxLength(500)]
    public string? RouteTemplate { get; set; }

    /// <summary>
    /// Icon for the display
    /// </summary>
    [MaxLength(100)]
    public string? Icon { get; set; }

    /// <summary>
    /// Sort order for display ordering
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// Whether the display is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Actions associated with this display
    /// </summary>
    public List<ActionStructureDto>? Actions { get; set; }
}

/// <summary>
/// Action structure definition for displays
/// </summary>
public class ActionStructureDto
{
    /// <summary>
    /// Action ID - if provided, use as database ID; if not provided, generate new GUID
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Action name
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the action
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// API endpoint template for API actions
    /// </summary>
    [MaxLength(500)]
    public string? EndpointTemplate { get; set; }

    /// <summary>
    /// Navigation target for Navigation actions
    /// </summary>
    [MaxLength(500)]
    public string? NavigationTarget { get; set; }

    /// <summary>
    /// Icon for the action
    /// </summary>
    [MaxLength(100)]
    public string? Icon { get; set; }

    /// <summary>
    /// Button style - 'Primary', 'Secondary', 'Danger', etc.
    /// </summary>
    [MaxLength(50)]
    public string? ButtonStyle { get; set; }

    /// <summary>
    /// Confirmation dialog message
    /// </summary>
    [MaxLength(1000)]
    public string? ConfirmationMessage { get; set; }

    /// <summary>
    /// Success message to display after action completion
    /// </summary>
    [MaxLength(1000)]
    public string? SuccessMessage { get; set; }

    /// <summary>
    /// Error message to display on action failure
    /// </summary>
    [MaxLength(1000)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether the action is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Access level for DisplayAction relationship - 'Public', 'Protected', 'Private'
    /// </summary>
    [MaxLength(50)]
    public string AccessLevel { get; set; } = "Public";

    /// <summary>
    /// Whether this is a default action for the display
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Sort order for action ordering within the display
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// Whether the action is visible in toolbar
    /// </summary>
    public bool IsVisibleInToolbar { get; set; } = true;

    /// <summary>
    /// Whether the action is visible in context menu
    /// </summary>
    public bool IsVisibleInContextMenu { get; set; } = true;
}

/// <summary>
/// Response for comprehensive product structure creation
/// </summary>
public class ProductStructureCreationResult
{
    /// <summary>
    /// Whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Summary message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Total number of products created
    /// </summary>
    public int TotalProductsCreated { get; set; }

    /// <summary>
    /// Total number of products that already existed
    /// </summary>
    public int TotalProductsExisting { get; set; }

    /// <summary>
    /// Total number of objects created
    /// </summary>
    public int TotalObjectsCreated { get; set; }

    /// <summary>
    /// Total number of objects that already existed
    /// </summary>
    public int TotalObjectsExisting { get; set; }

    /// <summary>
    /// Total number of metadata entries created
    /// </summary>
    public int TotalMetadataCreated { get; set; }

    /// <summary>
    /// Total number of metadata entries that already existed
    /// </summary>
    public int TotalMetadataExisting { get; set; }

    /// <summary>
    /// Total number of object-metadata links created
    /// </summary>
    public int TotalObjectMetadataCreated { get; set; }

    /// <summary>
    /// Total number of object-metadata links that already existed
    /// </summary>
    public int TotalObjectMetadataExisting { get; set; }

    /// <summary>
    /// Total number of displays created
    /// </summary>
    public int TotalDisplaysCreated { get; set; }

    /// <summary>
    /// Total number of displays that already existed
    /// </summary>
    public int TotalDisplaysExisting { get; set; }

    /// <summary>
    /// Total number of actions created
    /// </summary>
    public int TotalActionsCreated { get; set; }

    /// <summary>
    /// Total number of actions that already existed
    /// </summary>
    public int TotalActionsExisting { get; set; }

    /// <summary>
    /// Total number of display-action relationships created
    /// </summary>
    public int TotalDisplayActionsCreated { get; set; }

    /// <summary>
    /// Total number of display-action relationships that already existed
    /// </summary>
    public int TotalDisplayActionsExisting { get; set; }

    /// <summary>
    /// Total number of roles created
    /// </summary>
    public int TotalRolesCreated { get; set; }

    /// <summary>
    /// List of created products with their details
    /// </summary>
    public List<CreatedProductInfo> CreatedProducts { get; set; } = new();

    /// <summary>
    /// Any errors that occurred during processing
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Processing warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// Processing metrics
    /// </summary>
    public ProcessingMetrics Metrics { get; set; } = new();
}

/// <summary>
/// Information about a processed product (created or existing)
/// </summary>
public class CreatedProductInfo
{
    /// <summary>
    /// Product ID (created or existing)
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Whether this product was created or already existed
    /// </summary>
    public bool WasCreated { get; set; }

    /// <summary>
    /// Number of metadata fields created for this product
    /// </summary>
    public int MetadataFieldsCreated { get; set; }

    /// <summary>
    /// Number of metadata fields that already existed for this product
    /// </summary>
    public int MetadataFieldsExisting { get; set; }

    /// <summary>
    /// Number of objects created for this product
    /// </summary>
    public int ObjectsCreated { get; set; }

    /// <summary>
    /// Number of objects that already existed for this product
    /// </summary>
    public int ObjectsExisting { get; set; }

    /// <summary>
    /// Root objects created under this product
    /// </summary>
    public List<ProductStructureCreatedObjectInfo> RootObjects { get; set; } = new();
}

/// <summary>
/// Information about a processed object in product structure (created or existing)
/// </summary>
public class ProductStructureCreatedObjectInfo
{
    /// <summary>
    /// Object ID (created or existing)
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Whether this object was created or already existed
    /// </summary>
    public bool WasCreated { get; set; }

    /// <summary>
    /// Parent object ID (if any)
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Hierarchy level (0 = root)
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// Number of metadata fields created for this object
    /// </summary>
    public int MetadataFieldsCreated { get; set; }

    /// <summary>
    /// Number of metadata fields that already existed for this object
    /// </summary>
    public int MetadataFieldsExisting { get; set; }

    /// <summary>
    /// Number of displays created for this object
    /// </summary>
    public int DisplaysCreated { get; set; }

    /// <summary>
    /// Number of displays that already existed for this object
    /// </summary>
    public int DisplaysExisting { get; set; }

    /// <summary>
    /// Number of actions created for this object
    /// </summary>
    public int ActionsCreated { get; set; }

    /// <summary>
    /// Number of actions that already existed for this object
    /// </summary>
    public int ActionsExisting { get; set; }

    /// <summary>
    /// Number of display-action relationships created for this object
    /// </summary>
    public int DisplayActionsCreated { get; set; }

    /// <summary>
    /// Number of display-action relationships that already existed for this object
    /// </summary>
    public int DisplayActionsExisting { get; set; }

    /// <summary>
    /// Child objects created under this object
    /// </summary>
    public List<ProductStructureCreatedObjectInfo> Children { get; set; } = new();
}

/// <summary>
/// Processing metrics
/// </summary>
public class ProcessingMetrics
{
    /// <summary>
    /// Total processing time in milliseconds
    /// </summary>
    public long TotalProcessingTimeMs { get; set; }

    /// <summary>
    /// Validation time in milliseconds
    /// </summary>
    public long ValidationTimeMs { get; set; }

    /// <summary>
    /// Database operations time in milliseconds
    /// </summary>
    public long DatabaseTimeMs { get; set; }

    /// <summary>
    /// Number of database queries executed
    /// </summary>
    public int DatabaseQueriesCount { get; set; }

    /// <summary>
    /// Maximum hierarchy depth processed
    /// </summary>
    public int MaxHierarchyDepth { get; set; }
}
