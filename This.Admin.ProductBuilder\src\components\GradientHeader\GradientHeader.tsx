import React from 'react';
import { Container } from 'react-bootstrap';

interface GradientHeaderProps {
  title?: string;
  subtitle?: string;
  showDate?: boolean;
  children?: React.ReactNode;
  className?: string;
}

export const GradientHeader: React.FC<GradientHeaderProps> = ({
  title,
  subtitle,
  showDate = true,
  children,
  className = ''
}) => {
  const formatDate = () => {
    return new Date().toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  return (
    <header className={`gradient-header text-white py-4 shadow-lg ${className}`}>
      <Container>
        <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex flex-column">
            {title && <h1 className="h3 mb-1">{title}</h1>}
            {subtitle && <p className="mb-1 text-white-50 small">{subtitle}</p>}
            {showDate && (
              <div className="text-white small opacity-75">
                {formatDate()}
              </div>
            )}
          </div>
          {children && (
            <div className="d-flex gap-2">
              {children}
            </div>
          )}
        </div>
      </Container>
    </header>
  );
};
