import 'package:json_annotation/json_annotation.dart';

part 'navigation_response_model.g.dart';

/// Navigation API response model
@JsonSerializable()
class NavigationResponse {
  @JsonKey(name: 'navigationItems')
  final List<NavigationItemDto>? navigationItems;
  
  @Json<PERSON><PERSON>(name: 'totalCount')
  final int? totalCount;
  
  @JsonKey(name: 'maxDepth')
  final int? maxDepth;

  const NavigationResponse({
    this.navigationItems,
    this.totalCount,
    this.maxDepth,
  });

  factory NavigationResponse.fromJson(Map<String, dynamic> json) =>
      _$NavigationResponseFromJson(json);

  Map<String, dynamic> toJson() => _$NavigationResponseToJson(this);
}

/// Navigation item DTO from API
@JsonSerializable()
class NavigationItemDto {
  @JsonKey(name: 'id')
  final String id;
  
  @JsonKey(name: 'name')
  final String name;
  
  @<PERSON>son<PERSON><PERSON>(name: 'displayName')
  final String? displayName;
  
  @Json<PERSON><PERSON>(name: 'description')
  final String? description;
  
  @JsonKey(name: 'objectType')
  final String? objectType;
  
  @JsonKey(name: 'parentId')
  final String? parentId;
  
  @JsonKey(name: 'hierarchyLevel')
  final int? hierarchyLevel;
  
  @JsonKey(name: 'hierarchyPath')
  final String? hierarchyPath;
  
  @JsonKey(name: 'iconName')
  final String? iconName;
  
  @JsonKey(name: 'iconType')
  final String? iconType; // 'lucide', 'material', 'custom'
  
  @JsonKey(name: 'isActive')
  final bool? isActive;
  
  @JsonKey(name: 'isVisible')
  final bool? isVisible;
  
  @JsonKey(name: 'sortOrder')
  final int? sortOrder;
  
  @JsonKey(name: 'permissions')
  final List<String>? permissions;
  
  @JsonKey(name: 'metadata')
  final Map<String, dynamic>? metadata;
  
  @JsonKey(name: 'children')
  final List<NavigationItemDto>? children;

  const NavigationItemDto({
    required this.id,
    required this.name,
    this.displayName,
    this.description,
    this.objectType,
    this.parentId,
    this.hierarchyLevel,
    this.hierarchyPath,
    this.iconName,
    this.iconType,
    this.isActive,
    this.isVisible,
    this.sortOrder,
    this.permissions,
    this.metadata,
    this.children,
  });

  factory NavigationItemDto.fromJson(Map<String, dynamic> json) =>
      _$NavigationItemDtoFromJson(json);

  Map<String, dynamic> toJson() => _$NavigationItemDtoToJson(this);
}

/// Object metadata response for dynamic object types
@JsonSerializable()
class ObjectMetadataResponse {
  @JsonKey(name: 'objectTypes')
  final List<ObjectTypeDto>? objectTypes;
  
  @JsonKey(name: 'categories')
  final List<CategoryDto>? categories;

  const ObjectMetadataResponse({
    this.objectTypes,
    this.categories,
  });

  factory ObjectMetadataResponse.fromJson(Map<String, dynamic> json) =>
      _$ObjectMetadataResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ObjectMetadataResponseToJson(this);
}

/// Object type DTO
@JsonSerializable()
class ObjectTypeDto {
  @JsonKey(name: 'id')
  final String id;
  
  @JsonKey(name: 'name')
  final String name;
  
  @JsonKey(name: 'displayName')
  final String? displayName;
  
  @JsonKey(name: 'description')
  final String? description;
  
  @JsonKey(name: 'category')
  final String? category;
  
  @JsonKey(name: 'iconName')
  final String? iconName;
  
  @JsonKey(name: 'isActive')
  final bool? isActive;
  
  @JsonKey(name: 'permissions')
  final List<String>? permissions;

  const ObjectTypeDto({
    required this.id,
    required this.name,
    this.displayName,
    this.description,
    this.category,
    this.iconName,
    this.isActive,
    this.permissions,
  });

  factory ObjectTypeDto.fromJson(Map<String, dynamic> json) =>
      _$ObjectTypeDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ObjectTypeDtoToJson(this);
}

/// Category DTO
@JsonSerializable()
class CategoryDto {
  @JsonKey(name: 'id')
  final String id;
  
  @JsonKey(name: 'name')
  final String name;
  
  @JsonKey(name: 'displayName')
  final String? displayName;
  
  @JsonKey(name: 'iconName')
  final String? iconName;
  
  @JsonKey(name: 'sortOrder')
  final int? sortOrder;

  const CategoryDto({
    required this.id,
    required this.name,
    this.displayName,
    this.iconName,
    this.sortOrder,
  });

  factory CategoryDto.fromJson(Map<String, dynamic> json) =>
      _$CategoryDtoFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryDtoToJson(this);
}
