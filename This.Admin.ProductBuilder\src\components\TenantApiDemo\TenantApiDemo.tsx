/**
 * Tenant API Demo Component
 * Demonstrates how to use the tenant-aware API service
 */

import React, { useState } from 'react';
import { Card, Button, Form, Alert, Row, Col, Badge } from 'react-bootstrap';
import { Play, Copy, RefreshCw, Database, Code } from 'lucide-react';
import { useTenantContext } from '../../hooks/useTenantContext';
import { comprehensiveEntityService, generateCurlCommand } from '../../services/tenantAwareApiService';

export const TenantApiDemo: React.FC = () => {
  const { selectedTenantId, selectedTenant } = useTenantContext();
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedApi, setSelectedApi] = useState('getSubscriptions');
  const [showCurl, setShowCurl] = useState(false);

  const apiOptions = [
    { value: 'getSubscriptions', label: 'Get Subscriptions', method: 'GET', endpoint: '/api/comprehensive-entity/subscriptions' },
    { value: 'getProducts', label: 'Get Products', method: 'GET', endpoint: '/api/comprehensive-entity/products' },
    { value: 'createProductStructure', label: 'Create Product Structure', method: 'POST', endpoint: '/api/comprehensive-entity/create-product-structure' },
  ];

  const sampleProductData = {
    products: [
      {
        name: "Test Product",
        type: "product",
        metadata: [
          {
            name: "Name",
            type: "Text",
            description: "Product name",
            required: true,
            isActive: true
          },
          {
            name: "Description",
            type: "Textarea",
            description: "Product description",
            required: false,
            isActive: true
          },
          {
            name: "Version",
            type: "Text",
            description: "Product version",
            required: true,
            isActive: true,
            defaultValue: "1.0.0"
          },
          {
            name: "IsActive",
            type: "Boolean",
            description: "Whether the product is active",
            required: true,
            isActive: true,
            defaultValue: "true"
          }
        ]
      }
    ]
  };

  const handleApiCall = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      let response;
      
      switch (selectedApi) {
        case 'getSubscriptions':
          response = await comprehensiveEntityService.getSubscriptions({
            pageNumber: 1,
            pageSize: 10,
            includeSummary: true
          });
          break;
        case 'getProducts':
          response = await comprehensiveEntityService.getProducts({
            pageNumber: 1,
            pageSize: 10
          });
          break;
        case 'createProductStructure':
          response = await comprehensiveEntityService.createProductStructure(sampleProductData);
          break;
        default:
          throw new Error('Unknown API method');
      }

      setResult(response);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getCurrentApiConfig = () => {
    return apiOptions.find(opt => opt.value === selectedApi);
  };

  const getCurlCommand = () => {
    const apiConfig = getCurrentApiConfig();
    if (!apiConfig) return '';
    
    const data = selectedApi === 'createProductStructure' ? sampleProductData : undefined;
    return generateCurlCommand(apiConfig.method, apiConfig.endpoint, data, selectedTenantId || undefined);
  };

  return (
    <div className="tenant-api-demo">
      <Row>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">
                <Database className="me-2" size={20} />
                Tenant-Aware API Demo
              </h5>
              <Badge bg={selectedTenantId ? 'success' : 'secondary'}>
                {selectedTenantId ? `Tenant: ${selectedTenant?.name || selectedTenantId}` : 'All Tenants'}
              </Badge>
            </Card.Header>
            <Card.Body>
              <Form.Group className="mb-3">
                <Form.Label>Select API Endpoint</Form.Label>
                <Form.Select 
                  value={selectedApi} 
                  onChange={(e) => setSelectedApi(e.target.value)}
                >
                  {apiOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.method} - {option.label}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              <div className="d-flex gap-2">
                <Button 
                  variant="primary" 
                  onClick={handleApiCall}
                  disabled={isLoading}
                  className="d-flex align-items-center gap-2"
                >
                  {isLoading ? <RefreshCw size={16} className="spinning" /> : <Play size={16} />}
                  {isLoading ? 'Calling...' : 'Call API'}
                </Button>
                
                <Button 
                  variant="outline-secondary" 
                  onClick={() => setShowCurl(!showCurl)}
                  className="d-flex align-items-center gap-2"
                >
                  <Code size={16} />
                  {showCurl ? 'Hide' : 'Show'} cURL
                </Button>
              </div>

              {showCurl && (
                <div className="mt-3">
                  <Form.Label>Equivalent cURL Command:</Form.Label>
                  <div className="curl-command">
                    <pre className="bg-dark text-white p-3 rounded small">
                      {getCurlCommand()}
                    </pre>
                    <Button 
                      size="sm" 
                      variant="outline-primary"
                      onClick={() => copyToClipboard(getCurlCommand())}
                      className="mt-2"
                    >
                      <Copy size={14} className="me-1" />
                      Copy
                    </Button>
                  </div>
                </div>
              )}

              {error && (
                <Alert variant="danger" className="mt-3">
                  <strong>Error:</strong> {error}
                </Alert>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col md={6}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">API Response</h5>
            </Card.Header>
            <Card.Body>
              {result ? (
                <div className="api-result">
                  <pre className="bg-light p-3 rounded small overflow-auto" style={{ maxHeight: '500px' }}>
                    {JSON.stringify(result, null, 2)}
                  </pre>
                  <Button 
                    size="sm" 
                    variant="outline-primary"
                    onClick={() => copyToClipboard(JSON.stringify(result, null, 2))}
                    className="mt-2"
                  >
                    <Copy size={14} className="me-1" />
                    Copy Response
                  </Button>
                </div>
              ) : (
                <div className="text-muted text-center py-5">
                  <Database size={40} className="mb-3 opacity-50" />
                  <p>No API response yet. Click "Call API" to see results.</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <style jsx>{`
        .spinning {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        .curl-command {
          position: relative;
        }

        .curl-command pre {
          max-height: 200px;
          overflow-y: auto;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.4;
          white-space: pre-wrap;
          word-break: break-all;
        }

        .api-result pre {
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.4;
        }
      `}</style>
    </div>
  );
};