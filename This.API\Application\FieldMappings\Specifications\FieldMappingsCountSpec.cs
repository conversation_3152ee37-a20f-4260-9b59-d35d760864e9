using Ardalis.Specification;
using Domain.Entities;

namespace Application.FieldMappings.Specifications;

/// <summary>
/// Specification to count field mappings with filters
/// </summary>
public class FieldMappingsCountSpec : Specification<FieldMapping>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public FieldMappingsCountSpec(
        string? searchTerm,
        string? apiName,
        string? sourceType,
        Guid? objectMetadataId,
        Guid? userId,
        Guid? roleId,
        string? targetObjectName)
    {
        Query.Where(fm => true); // Base query

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(fm => fm.SourceField.Contains(searchTerm) ||
                             (fm.ApiName != null && fm.ApiName.Contains(searchTerm)) ||
                             (fm.TargetObjectName != null && fm.TargetObjectName.Contains(searchTerm)));
        }

        if (!string.IsNullOrEmpty(apiName))
        {
            Query.Where(fm => fm.ApiName == apiName);
        }

        if (!string.IsNullOrEmpty(sourceType))
        {
            Query.Where(fm => fm.SourceType == sourceType);
        }

        if (objectMetadataId.HasValue)
        {
            Query.Where(fm => fm.ObjectMetadataId == objectMetadataId.Value);
        }

        if (userId.HasValue)
        {
            Query.Where(fm => fm.UserId == userId.Value);
        }

        if (roleId.HasValue)
        {
            Query.Where(fm => fm.RoleId == roleId.Value);
        }

        if (!string.IsNullOrEmpty(targetObjectName))
        {
            Query.Where(fm => fm.TargetObjectName == targetObjectName);
        }
    }
}
