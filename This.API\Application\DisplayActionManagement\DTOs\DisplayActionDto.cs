using Application.ActionManagement.DTOs;
using Application.DisplayManagement.DTOs;

namespace Application.DisplayActionManagement.DTOs;

/// <summary>
/// DisplayAction response DTO
/// </summary>
public class DisplayActionDto
{
    /// <summary>
    /// DisplayAction ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Object ID
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Display ID
    /// </summary>
    public Guid DisplayId { get; set; }

    /// <summary>
    /// Action ID
    /// </summary>
    public Guid ActionId { get; set; }

    /// <summary>
    /// Access level - 'Public', 'Protected', 'Private'
    /// </summary>
    public string AccessLevel { get; set; } = "Public";

    /// <summary>
    /// Whether this is a default action
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Sort order for action ordering
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// Whether the action is visible in toolbar
    /// </summary>
    public bool IsVisibleInToolbar { get; set; }

    /// <summary>
    /// Whether the action is visible in context menu
    /// </summary>
    public bool IsVisibleInContextMenu { get; set; }

    /// <summary>
    /// Whether the action is visible in row actions
    /// </summary>
    public bool IsVisibleInRowActions { get; set; }

    /// <summary>
    /// Whether the display action is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the display action is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last modification timestamp
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified by user ID
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Display information
    /// </summary>
    public DisplayDto? Display { get; set; }

    /// <summary>
    /// Action information
    /// </summary>
    public ActionDto? Action { get; set; }
}
