import { useEffect, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Onboarding from './Onboarding';
import { TEST_DATA, shouldUseTestData, setCurrentApplicationDetails } from '../../shared/testData';
import { OnboardingProvider } from '../../contexts/OnboardingContext';
import type { ApplicationDetails } from '../../types/onboarding';

// State interface for OnboardingWrapper
interface OnboardingWrapperState {
  loading: boolean;
  applicationDetails: ApplicationDetails | null;
  waitingForPostMessage: boolean;
}

const OnboardingWrapper = () => {
  const useTestData = shouldUseTestData();

  const [state, setState] = useState<OnboardingWrapperState>({
    loading: true,
    applicationDetails: useTestData ? TEST_DATA.applicationDetails : null,
    waitingForPostMessage: !useTestData,
  });

  // Listen for postMessage data when in iframe
  useEffect(() => {
    if (useTestData) {
      // Using test data - set loading to false immediately
      setState(prev => ({ ...prev, loading: false }));
      return;
    }

    const handleMessage = (event: MessageEvent) => {
      const { applicationDetails: messageApplicationDetails } = event.data;

      if (messageApplicationDetails && messageApplicationDetails.tenantId) {
        // Set the current application details for services to use
        setCurrentApplicationDetails(messageApplicationDetails);

        setState(prev => ({
          ...prev,
          applicationDetails: messageApplicationDetails,
          waitingForPostMessage: false,
          loading: false,
        }));
      }
    };

    // Set up timeout for postMessage (only in iframe mode)
    // Note: Removed error handling - iframe mode will wait indefinitely for valid data
    const timeoutId = setTimeout(() => {
      console.warn('PostMessage timeout - no data received from parent window after 10 seconds');
    }, 10000); // 10 second timeout for logging only

    // Request data from parent window
    try {
      window.parent.postMessage({ type: 'REQUEST_TENANT_DATA' }, '*');
    } catch (error) {
      console.warn('Failed to request data from parent window:', error);
    }

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
      clearTimeout(timeoutId);
    };
  }, [useTestData]);

  // Show loading screen while waiting for data
  if (state.loading || state.waitingForPostMessage) {
    const title = state.waitingForPostMessage ? "Connecting to Parent Application" : "Loading Application";
    const message = state.waitingForPostMessage
      ? "Waiting for tenant data from the main application..."
      : "Preparing onboarding flow...";

    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">{title}</h2>
          <p className="text-sm text-gray-600 mb-4">{message}</p>
        </div>
      </div>
    );
  }

  const finalApplicationDetails = state.applicationDetails || TEST_DATA.applicationDetails;

  return (
    <OnboardingProvider>
      <ToastContainer position="top-right" autoClose={5000} hideProgressBar={false} />
      <Onboarding
        applicationDetails={finalApplicationDetails}
      />
    </OnboardingProvider>
  );
};

export default OnboardingWrapper;
