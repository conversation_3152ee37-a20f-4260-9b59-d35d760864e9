using MediatR;
using Shared.Common.Response;
using Abstraction.Database.Repositories;

namespace Application.ObjectValues.Queries.GetMetadataKeyValues;

/// <summary>
/// Query to get all values for a specific metadata key from object instance view
/// </summary>
public class GetMetadataKeyValuesQuery : IRequest<Result<ObjectLookUpValuesResponse>>
{
    /// <summary>
    /// Object name to get metadata key values for
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;

    /// <summary>
    /// Metadata key to get values for
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// Tenant ID for the view
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Whether to create/recreate the view before querying
    /// </summary>
    public bool CreateView { get; set; } = true;

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetMetadataKeyValuesQuery(string objectName, string metadataKey, string tenantId, bool createView = true, int pageNumber = 1, int pageSize = 10)
    {
        ObjectName = objectName;
        MetadataKey = metadataKey;
        TenantId = tenantId;
        CreateView = createView;
        PageNumber = pageNumber;
        PageSize = pageSize;
    }
}

/// <summary>
/// Response for GetMetadataKeyValuesQuery
/// </summary>
public class ObjectLookUpValuesResponse
{
    /// <summary>
    /// Object name
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;

    /// <summary>
    /// Metadata key that was queried
    /// </summary>
    public string MetadataKey { get; set; } = string.Empty;

    /// <summary>
    /// Tenant ID
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// View name that was queried
    /// </summary>
    public string ViewName { get; set; } = string.Empty;

    /// <summary>
    /// Result of view creation (if CreateView was true)
    /// </summary>
    public string ViewCreationResult { get; set; } = string.Empty;

    /// <summary>
    /// Values for the metadata key with RefId
    /// </summary>
    public List<MetadataKeyValueResult> Values { get; set; } = new();

    /// <summary>
    /// Total number of values (for pagination)
    /// </summary>
    public int TotalValues { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Whether there is a previous page
    /// </summary>
    public bool HasPreviousPage { get; set; }

    /// <summary>
    /// Whether there is a next page
    /// </summary>
    public bool HasNextPage { get; set; }

    /// <summary>
    /// Response message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}
