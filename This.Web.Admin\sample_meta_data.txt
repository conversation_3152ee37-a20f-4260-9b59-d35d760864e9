{"succeeded": true, "data": {"products": [{"id": "8c38b02f-4c1e-4416-a1bf-c705d1ac8761", "name": "KitchenSync", "description": "KitchenSync", "version": "1.0.0", "isActive": true, "isUserImported": true, "isRoleAssigned": true, "apiKey": null, "isOnboardCompleted": true, "applicationUrl": "http://localhost:5174/", "icon": "ic-cube", "createdAt": "2025-06-20T18:53:36.079154Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedAt": "2025-06-23T13:12:53.893037Z", "modifiedBy": "00000000-0000-0000-0000-000000000000", "isDeleted": false, "metadata": [], "rootObjects": [{"id": "50f97590-6969-400c-9523-9e698f982b98", "name": "MealKit", "description": null, "parentObjectId": null, "isActive": true, "icon": null, "createdAt": "2025-06-20T18:53:36.084735Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedAt": "2025-06-20T18:53:35.981864Z", "modifiedBy": null, "isDeleted": false, "hierarchyLevel": 0, "hierarchyPath": "MealKit", "metadata": [{"metadata": {"id": "0ea45756-2369-4a50-b162-500dde07f354", "name": "preparationTime", "displayLabel": "preparationTime", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Select date and time...", "inputType": "datetime-local", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid datetime format", "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "datetime", "category": "Input", "uiComponent": "ThisDateTime", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "c1cf12ef-95c2-4653-8af9-8c2c1e74c143", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "b079677c-4775-4827-beff-511314fa8fc1", "name": "mealType", "displayLabel": "mealType", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "4fb9bfa0-7f3e-4592-a212-5084ca8bbab5", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "bfe5a592-71f2-4014-be70-3936a0eb25bf", "name": "ServingSize", "displayLabel": "ServingSize", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "c3b9ca3f-570b-41f0-beec-6f9827b12c5a", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "f5309219-0179-4df4-b4a3-7e051af1d274", "name": "Description", "displayLabel": "Description", "helpText": "Object description", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 10, "maxLength": 5000, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "textarea", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": null, "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "textarea", "category": "Input", "uiComponent": "ThisTextarea", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "e09c70a9-5aa0-498d-8312-e483a0e4ef1a", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "de338ff7-d2be-4b99-a2f6-7086d0090397", "name": "servingSize", "displayLabel": "servingSize", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "e77973ee-3290-40ce-9ad1-a7dd34ce12bc", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "c364887f-9857-4ac5-80ac-e9feaa5cdf69", "name": "isFrozen", "displayLabel": "isFrozen", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "10ff87dc-c7c0-4d4d-96a0-823ee4a9b8bf", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "ef637a8a-3726-4997-8b79-005a5f54dd7a", "name": "Name", "displayLabel": "Name", "helpText": "Product name", "fieldOrder": 1, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "47433398-b3b9-46bc-a10a-1096dac89514", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "29454f92-d4d6-4a6d-8ae9-9983a2b90585", "name": "IsActive", "displayLabel": "IsActive", "helpText": "Whether the object is active", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": null, "inputType": "checkbox", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "boolean", "category": "Input", "uiComponent": "ThisCheckbox", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "e10ade1d-dcd6-46b9-8468-bc88c3866d86", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "51c9e47f-3ea4-4a41-abcc-65e50ab6b25d", "name": "ParentObjectId", "displayLabel": "ParentObjectId", "helpText": "Parent object identifier", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "e862a8b4-e781-40f4-954f-1d0035630122", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}], "childObjects": [{"id": "08facc7e-e96c-40a9-959c-1a58da721e3e", "name": "SpicePacket", "description": null, "parentObjectId": "50f97590-6969-400c-9523-9e698f982b98", "isActive": true, "icon": null, "createdAt": "2025-06-20T18:53:36.084735Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedAt": "2025-06-20T18:53:36.016143Z", "modifiedBy": null, "isDeleted": false, "hierarchyLevel": 1, "hierarchyPath": "MealKit > SpicePacket", "metadata": [{"metadata": {"id": "00836c24-79a5-451a-9adb-115cb9b2e458", "name": "packetWeightGrams", "displayLabel": "packetWeightGrams", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": "^[0-9]+(\\.[0-9]+)?$", "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter number...", "inputType": "number", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Please enter a valid number", "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": "Value is too small", "maxValueErrorMessage": "Value is too large", "fileTypeErrorMessage": null, "dataTypeName": "number", "category": "Input", "uiComponent": "ThisNumber", "decimalPlaces": 2, "stepValue": 1, "metadataLinkId": "ebca72ce-419e-4a5c-a1cf-e5fb152dd5d0", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "ed3b7804-ded1-472a-b2f5-76140b2430cc", "name": "spiceOrigin", "displayLabel": "spiceOrigin", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "8163b821-b43c-49e4-8ce9-0b6bd264605d", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "f5309219-0179-4df4-b4a3-7e051af1d274", "name": "Description", "displayLabel": "Description", "helpText": "Object description", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 10, "maxLength": 5000, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "textarea", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": null, "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "textarea", "category": "Input", "uiComponent": "ThisTextarea", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "f987a004-2b65-4c09-b547-47c92af04776", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "f713f8de-1635-44e9-8c6b-154ce1864e75", "name": "spiceType", "displayLabel": "spiceType", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "777afcbe-bd3c-4f60-b450-eae7d89839f9", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "1627e6a1-0d97-42ab-8374-3ab7824e3d74", "name": "isAllergenFree", "displayLabel": "isAllergenFree", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "be5e861a-03cb-40a0-9130-b5488a1c6a60", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "29454f92-d4d6-4a6d-8ae9-9983a2b90585", "name": "IsActive", "displayLabel": "IsActive", "helpText": "Whether the object is active", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": null, "inputType": "checkbox", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "boolean", "category": "Input", "uiComponent": "ThisCheckbox", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "70a0e74c-8b52-43af-8fd6-e3218091352c", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "000a6101-3b92-4d15-82d6-dfc5057b1f62", "name": "isNonGMO", "displayLabel": "isNonGMO", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": null, "inputType": "checkbox", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "boolean", "category": "Input", "uiComponent": "ThisCheckbox", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "c6db82bf-f9cd-44bb-9d08-01ebe9c893ec", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "0298fdf8-e22a-445b-9f13-97e3d69d17ad", "name": "isOrganic", "displayLabel": "isOrganic", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": null, "inputType": "checkbox", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": null, "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "boolean", "category": "Input", "uiComponent": "ThisCheckbox", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "72d53cb4-018f-423d-bb5a-247bcf6d4ce7", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "1a4b91ef-a28c-4a90-8022-f871af1e5a0d", "name": "harvestSeason", "displayLabel": "harvestSeason", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "14119d72-c73e-4abc-815c-5da411d1adfd", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "574d5ae8-003e-4e13-9df3-d0585ad8387e", "name": "currency", "displayLabel": "currency", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "d550ceb9-9a50-44d1-bd6f-54080563c804", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "6066bbf3-878b-419a-bace-0d0c8dd857dc", "name": "availableStock", "displayLabel": "availableStock", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "dfd3291c-8737-42b1-9e74-d274d9226006", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "9e4a4df9-af22-4c69-92bc-216aaf827e10", "name": "grade", "displayLabel": "grade", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "849d85d2-06e2-46b6-a661-14d52532b61a", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "a77f2bfe-a561-4fed-bf2a-128f5be3f768", "name": "manufacturingDate", "displayLabel": "manufacturingDate", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Select date and time...", "inputType": "datetime-local", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid datetime format", "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "datetime", "category": "Input", "uiComponent": "ThisDateTime", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "be89855b-3a06-4f84-882d-946be5669d0b", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "b287c308-ff98-4cb7-9a16-90a99cb971e2", "name": "expirationDate", "displayLabel": "expirationDate", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": null, "maxLength": null, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Select date and time...", "inputType": "datetime-local", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid datetime format", "minLengthErrorMessage": null, "maxLengthErrorMessage": null, "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "datetime", "category": "Input", "uiComponent": "ThisDateTime", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "ff7022ff-aead-4eba-b709-890d3b885dd1", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "b3525513-e6d4-4512-bf5d-beedaab9e12f", "name": "ingredients", "displayLabel": "ingredients", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "cab6be58-a89f-4ab3-8f9e-8169b6511026", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "b3c112b8-2995-4516-b1f4-9163c5f7dbb0", "name": "barcode", "displayLabel": "barcode", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "60546562-4ddc-4678-93c9-b373dfc5d605", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "b726da15-87ba-48ad-86d9-05b1a347eda1", "name": "packetDimensions", "displayLabel": "packetDimensions", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "7cfa8aef-51da-40ce-835f-86d558fc4253", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "bfa651b7-598e-4697-9825-65f7abb6ebfb", "name": "price", "displayLabel": "price", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "be20273d-ac10-44f0-a694-3ef85932073e", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}, {"metadata": {"id": "c46cd4cd-10b6-4d56-bb33-51a1bb74958d", "name": "packageMaterial", "displayLabel": "packageMaterial", "helpText": "Custom field", "fieldOrder": null, "isVisible": true, "isReadonly": false, "validationPattern": null, "minLength": 1, "maxLength": 255, "minValue": null, "maxValue": null, "isRequired": false, "placeholder": "Enter text...", "inputType": "text", "inputMask": null, "defaultOptions": null, "allowsMultiple": false, "allowsCustomOptions": false, "maxSelections": null, "allowedFileTypes": null, "maxFileSizeBytes": null, "errorMessage": null, "requiredErrorMessage": "This field is required", "patternErrorMessage": "Invalid format", "minLengthErrorMessage": "Text is too short", "maxLengthErrorMessage": "Text is too long", "minValueErrorMessage": null, "maxValueErrorMessage": null, "fileTypeErrorMessage": null, "dataTypeName": "text", "category": "Input", "uiComponent": "ThisText", "decimalPlaces": null, "stepValue": null, "metadataLinkId": "96e4f212-670e-40ca-8b52-7126865b56a6", "isUnique": false, "isVisibleInList": true, "isVisibleInEdit": true, "isVisibleInCreate": true, "isVisibleInView": true, "isCalculated": true, "contextId": null, "tenantContextId": null, "objectLookupId": null, "isEditable": false, "isSearchable": false, "isSortable": false, "sortOrder": 0, "displayFormat": null}, "values": []}], "childObjects": [], "displays": [{"id": "422bee85-a1c8-4e77-9fed-a833b8b2042d", "name": "List", "description": "List Items", "displayName": "List", "isDefault": true, "routeTemplate": "", "icon": "", "sortOrder": 0, "isActive": true, "createdAt": "2025-06-23T14:52:53.561997Z", "modifiedAt": "2025-06-23T14:52:53.381102Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedBy": null, "isDeleted": false, "actions": [{"actionId": "0df8ebca-c8ce-4954-8add-a74028d28bfc", "name": "Create", "description": "Create", "endpointTemplate": "", "navigationTarget": "", "icon": "", "buttonStyle": "", "confirmationMessage": "Create", "successMessage": "Created Sucessfully", "errorMessage": "Faild", "actionIsActive": true, "actionCreatedAt": "2025-06-23T14:52:53.961072Z", "actionModifiedAt": "2025-06-23T14:52:53.890991Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "5c5ea348-2e97-45a7-90cc-31486c74f13f", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002857Z", "displayActionModifiedAt": "2025-06-24T15:17:39.437115Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}, {"actionId": "94b0e7fd-ccac-458b-b960-af0feaadbe78", "name": "View", "description": "View object details", "endpointTemplate": null, "navigationTarget": "/objects/view/{id}", "icon": "eye", "buttonStyle": "Secondary", "confirmationMessage": null, "successMessage": null, "errorMessage": null, "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864093Z", "actionModifiedAt": "2025-06-23T16:26:30.758207Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "5df12663-937c-44b6-9553-d3c2313f2484", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002856Z", "displayActionModifiedAt": "2025-06-24T15:17:39.444967Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}]}, {"id": "4f6beb3d-d576-418c-ac14-fea705c3fa27", "name": "Create", "description": null, "displayName": "Create Child", "isDefault": false, "routeTemplate": "/childobjects/create", "icon": "plus", "sortOrder": 2, "isActive": true, "createdAt": "2025-06-23T16:26:32.766094Z", "modifiedAt": "2025-06-23T16:26:31.777754Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedBy": null, "isDeleted": false, "actions": [{"actionId": "1abb95a0-b265-4e76-8c37-53835c4b80a1", "name": "Save", "description": "Save child", "endpointTemplate": "/api/childobjects", "navigationTarget": null, "icon": "save", "buttonStyle": "Primary", "confirmationMessage": null, "successMessage": "Child object created.", "errorMessage": "Creation failed.", "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864078Z", "actionModifiedAt": "2025-06-23T16:26:31.810052Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "5b2fb49d-6f44-4ede-914a-82f75e560253", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002856Z", "displayActionModifiedAt": "2025-06-24T15:17:39.45688Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}, {"actionId": "8ac76194-a99a-4c8f-a5a1-605e042e50f5", "name": "Cancel", "description": "Cancel update", "endpointTemplate": null, "navigationTarget": "/objects/list", "icon": "x", "buttonStyle": "Secondary", "confirmationMessage": null, "successMessage": null, "errorMessage": null, "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864092Z", "actionModifiedAt": "2025-06-23T16:26:31.165254Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "d87465cb-5890-4e8a-99da-196c40e62a5f", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002856Z", "displayActionModifiedAt": "2025-06-24T15:17:39.464986Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}]}, {"id": "5ded7922-a278-4ee3-81a4-b8b29966ce31", "name": "Update", "description": null, "displayName": "Update Form", "isDefault": false, "routeTemplate": "/objects/update/{id}", "icon": "edit", "sortOrder": 3, "isActive": true, "createdAt": "2025-06-23T16:26:32.766115Z", "modifiedAt": "2025-06-23T16:26:31.066307Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedBy": null, "isDeleted": false, "actions": [{"actionId": "1abb95a0-b265-4e76-8c37-53835c4b80a1", "name": "Save", "description": "Save child", "endpointTemplate": "/api/childobjects", "navigationTarget": null, "icon": "save", "buttonStyle": "Primary", "confirmationMessage": null, "successMessage": "Child object created.", "errorMessage": "Creation failed.", "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864078Z", "actionModifiedAt": "2025-06-23T16:26:31.810052Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "48c73127-32e0-44da-b685-944430143dfc", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002856Z", "displayActionModifiedAt": "2025-06-24T15:17:39.477077Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}, {"actionId": "8ac76194-a99a-4c8f-a5a1-605e042e50f5", "name": "Cancel", "description": "Cancel update", "endpointTemplate": null, "navigationTarget": "/objects/list", "icon": "x", "buttonStyle": "Secondary", "confirmationMessage": null, "successMessage": null, "errorMessage": null, "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864092Z", "actionModifiedAt": "2025-06-23T16:26:31.165254Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "e98c3fe1-6981-4328-8b1d-ea81add8a85d", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002856Z", "displayActionModifiedAt": "2025-06-24T15:17:39.487201Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}]}, {"id": "f72d90b6-3cdf-4a8b-95eb-82e81a2bd608", "name": "View", "description": null, "displayName": "Detail View", "isDefault": false, "routeTemplate": "/objects/view/{id}", "icon": "eye", "sortOrder": 4, "isActive": true, "createdAt": "2025-06-23T16:26:32.766115Z", "modifiedAt": "2025-06-23T16:26:31.23307Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedBy": null, "isDeleted": false, "actions": [{"actionId": "7021bd1d-8bbe-463c-923e-098e54ec5653", "name": "Edit", "description": "Edit object", "endpointTemplate": null, "navigationTarget": "/objects/update/{id}", "icon": "edit", "buttonStyle": "Primary", "confirmationMessage": null, "successMessage": null, "errorMessage": null, "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864092Z", "actionModifiedAt": "2025-06-23T16:26:31.266109Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "42b01422-1e3e-49cd-8981-160a23d84abf", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002856Z", "displayActionModifiedAt": "2025-06-24T15:17:39.500263Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}]}]}], "displays": [{"id": "422bee85-a1c8-4e77-9fed-a833b8b2042d", "name": "List", "description": "List Items", "displayName": "List", "isDefault": true, "routeTemplate": "", "icon": "", "sortOrder": 0, "isActive": true, "createdAt": "2025-06-23T14:52:53.561997Z", "modifiedAt": "2025-06-23T14:52:53.381102Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedBy": null, "isDeleted": false, "actions": [{"actionId": "0df8ebca-c8ce-4954-8add-a74028d28bfc", "name": "Create", "description": "Create", "endpointTemplate": "", "navigationTarget": "", "icon": "", "buttonStyle": "", "confirmationMessage": "Create", "successMessage": "Created Sucessfully", "errorMessage": "Faild", "actionIsActive": true, "actionCreatedAt": "2025-06-23T14:52:53.961072Z", "actionModifiedAt": "2025-06-23T14:52:53.890991Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "552eb32d-daf2-44c1-9c4c-b419474e4db0", "accessLevel": "string", "isDefault": true, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": true, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-23T14:52:54.325731Z", "displayActionModifiedAt": "2025-06-23T14:52:54.227921Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}, {"actionId": "94b0e7fd-ccac-458b-b960-af0feaadbe78", "name": "View", "description": "View object details", "endpointTemplate": null, "navigationTarget": "/objects/view/{id}", "icon": "eye", "buttonStyle": "Secondary", "confirmationMessage": null, "successMessage": null, "errorMessage": null, "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864093Z", "actionModifiedAt": "2025-06-23T16:26:30.758207Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "32037b3a-79a3-4837-a5fe-c79e76bf1865", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002857Z", "displayActionModifiedAt": "2025-06-24T15:17:39.187128Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}]}, {"id": "4f6beb3d-d576-418c-ac14-fea705c3fa27", "name": "Create", "description": null, "displayName": "Create Child", "isDefault": false, "routeTemplate": "/childobjects/create", "icon": "plus", "sortOrder": 2, "isActive": true, "createdAt": "2025-06-23T16:26:32.766094Z", "modifiedAt": "2025-06-23T16:26:31.777754Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedBy": null, "isDeleted": false, "actions": [{"actionId": "8ac76194-a99a-4c8f-a5a1-605e042e50f5", "name": "Cancel", "description": "Cancel update", "endpointTemplate": null, "navigationTarget": "/objects/list", "icon": "x", "buttonStyle": "Secondary", "confirmationMessage": null, "successMessage": null, "errorMessage": null, "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864092Z", "actionModifiedAt": "2025-06-23T16:26:31.165254Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "3a42176a-1cb0-415a-a6c2-fa579b37d098", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002857Z", "displayActionModifiedAt": "2025-06-24T15:17:39.231383Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}, {"actionId": "1abb95a0-b265-4e76-8c37-53835c4b80a1", "name": "Save", "description": "Save child", "endpointTemplate": "/api/childobjects", "navigationTarget": null, "icon": "save", "buttonStyle": "Primary", "confirmationMessage": null, "successMessage": "Child object created.", "errorMessage": "Creation failed.", "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864078Z", "actionModifiedAt": "2025-06-23T16:26:31.810052Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "54e0605b-3421-4d11-af35-1c162ba434fc", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002857Z", "displayActionModifiedAt": "2025-06-24T15:17:39.221448Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}]}, {"id": "5ded7922-a278-4ee3-81a4-b8b29966ce31", "name": "Update", "description": null, "displayName": "Update Form", "isDefault": false, "routeTemplate": "/objects/update/{id}", "icon": "edit", "sortOrder": 3, "isActive": true, "createdAt": "2025-06-23T16:26:32.766115Z", "modifiedAt": "2025-06-23T16:26:31.066307Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedBy": null, "isDeleted": false, "actions": [{"actionId": "1abb95a0-b265-4e76-8c37-53835c4b80a1", "name": "Save", "description": "Save child", "endpointTemplate": "/api/childobjects", "navigationTarget": null, "icon": "save", "buttonStyle": "Primary", "confirmationMessage": null, "successMessage": "Child object created.", "errorMessage": "Creation failed.", "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864078Z", "actionModifiedAt": "2025-06-23T16:26:31.810052Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "f3229183-85c0-42da-8514-f9df5128eafd", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002857Z", "displayActionModifiedAt": "2025-06-24T15:17:39.244903Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}, {"actionId": "8ac76194-a99a-4c8f-a5a1-605e042e50f5", "name": "Cancel", "description": "Cancel update", "endpointTemplate": null, "navigationTarget": "/objects/list", "icon": "x", "buttonStyle": "Secondary", "confirmationMessage": null, "successMessage": null, "errorMessage": null, "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864092Z", "actionModifiedAt": "2025-06-23T16:26:31.165254Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "dae7678e-ec24-483b-a06f-e2e364533e45", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002857Z", "displayActionModifiedAt": "2025-06-24T15:17:39.251998Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}]}, {"id": "f72d90b6-3cdf-4a8b-95eb-82e81a2bd608", "name": "View", "description": null, "displayName": "Detail View", "isDefault": false, "routeTemplate": "/objects/view/{id}", "icon": "eye", "sortOrder": 4, "isActive": true, "createdAt": "2025-06-23T16:26:32.766115Z", "modifiedAt": "2025-06-23T16:26:31.23307Z", "createdBy": "00000000-0000-0000-0000-000000000000", "modifiedBy": null, "isDeleted": false, "actions": [{"actionId": "7021bd1d-8bbe-463c-923e-098e54ec5653", "name": "Edit", "description": "Edit object", "endpointTemplate": null, "navigationTarget": "/objects/update/{id}", "icon": "edit", "buttonStyle": "Primary", "confirmationMessage": null, "successMessage": null, "errorMessage": null, "actionIsActive": true, "actionCreatedAt": "2025-06-23T16:26:32.864092Z", "actionModifiedAt": "2025-06-23T16:26:31.266109Z", "actionCreatedBy": "00000000-0000-0000-0000-000000000000", "actionModifiedBy": null, "actionIsDeleted": false, "displayActionId": "52502d8b-14cb-47dd-9260-b4644067c5a8", "accessLevel": "Public", "isDefault": false, "sortOrder": 0, "isVisibleInToolbar": true, "isVisibleInContextMenu": true, "isVisibleInRowActions": false, "displayActionIsActive": true, "displayActionCreatedAt": "2025-06-24T15:17:40.002857Z", "displayActionModifiedAt": "2025-06-24T15:17:39.26206Z", "displayActionCreatedBy": "00000000-0000-0000-0000-000000000000", "displayActionModifiedBy": null, "displayActionIsDeleted": false}]}]}]}], "totalObjectsCount": 2, "totalMetadataCount": 28, "maxHierarchyDepth": 1}, "message": null, "statusCode": 200}