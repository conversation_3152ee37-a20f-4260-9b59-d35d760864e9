using Abstraction.Database.Repositories;
using Application.RoleActions.DTOs;
using Application.RoleActions.Specifications;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Common.Response;

namespace Application.RoleActions.Commands;

/// <summary>
/// Update role actions command handler
/// </summary>
public class UpdateRoleActionsCommandHandler : IRequestHandler<UpdateRoleActionsCommand, Result<RoleActionsResponse>>
{
    private readonly IRepository<Domain.Entities.RoleActions> _roleActionsRepository;
    private readonly IRepository<Role> _roleRepository;
    private readonly IRepository<Domain.Entities.Action> _actionRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateRoleActionsCommandHandler(
        IRepository<Domain.Entities.RoleActions> roleActionsRepository,
        IRepository<Role> roleRepository,
        IRepository<Domain.Entities.Action> actionRepository)
    {
        _roleActionsRepository = roleActionsRepository;
        _roleRepository = roleRepository;
        _actionRepository = actionRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<RoleActionsResponse>> Handle(UpdateRoleActionsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate role exists
            var role = await _roleRepository.GetByIdAsync(request.RoleId, cancellationToken);
            if (role == null)
            {
                return Result<RoleActionsResponse>.Failure($"Role with ID {request.RoleId} not found.");
            }

            // Validate all actions exist
            var actionsSpec = new ActionsByIdsSpec(request.ActionIds);
            var actions = await _actionRepository.ListAsync(actionsSpec, cancellationToken);
            var foundActionIds = actions.Select(a => a.Id).ToList();
            var missingActionIds = request.ActionIds.Except(foundActionIds).ToList();

            if (missingActionIds.Any())
            {
                return Result<RoleActionsResponse>.Failure($"Actions with IDs {string.Join(", ", missingActionIds)} not found.");
            }

            // Get existing role actions
            var existingRoleActionsSpec = new RoleActionsByRoleIdSpec(request.RoleId, includeInactive: true);
            var existingRoleActions = await _roleActionsRepository.ListAsync(existingRoleActionsSpec, cancellationToken);

            // Remove existing role actions that are not in the new list
            var actionsToRemove = existingRoleActions
                .Where(ra => !request.ActionIds.Contains(ra.ActionId))
                .ToList();

            foreach (var actionToRemove in actionsToRemove)
            {
                await _roleActionsRepository.DeleteAsync(actionToRemove, cancellationToken);
            }

            // Add new role actions
            var existingActionIds = existingRoleActions.Select(ra => ra.ActionId).ToList();
            var newActionIds = request.ActionIds.Except(existingActionIds).ToList();

            foreach (var actionId in newActionIds)
            {
                var roleAction = new Domain.Entities.RoleActions
                {
                    RoleId = request.RoleId,
                    ActionId = actionId,
                    IsActive = true
                };

                await _roleActionsRepository.AddAsync(roleAction, cancellationToken);
            }

            await _roleActionsRepository.SaveChangesAsync(cancellationToken);

            // Get updated role actions
            var updatedRoleActionsSpec = new RoleActionsByRoleIdSpec(request.RoleId);
            var updatedRoleActions = await _roleActionsRepository.ListAsync(updatedRoleActionsSpec, cancellationToken);

            var response = new RoleActionsResponse
            {
                RoleId = request.RoleId,
                Actions = updatedRoleActions.Adapt<List<RoleActionsDto>>()
            };

            return Result<RoleActionsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<RoleActionsResponse>.Failure($"Error updating role actions: {ex.Message}");
        }
    }
}
