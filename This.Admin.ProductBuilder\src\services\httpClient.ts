/**
 * Centralized HTTP Client Service
 * Handles all API requests with consistent configuration, error handling, and interceptors
 */

import { environment } from '../config/environment';
import type { ApiResponse, ApiError, LoadingState, RequestConfig } from './types';

// Re-export types for convenience
export type { ApiResponse, ApiError, LoadingState, RequestConfig };

// HTTP Client Class
export class HttpClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private loadingStates: Map<string, LoadingState> = new Map();

  constructor(baseUrl: string, defaultHeaders: Record<string, string> = {}) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.defaultHeaders = {
      'Content-Type': environment.apiContentType,
      'Accept': environment.apiAccept,
      ...defaultHeaders,
    };
  }

  /**
   * Set loading state for a request
   */
  private setLoadingState(key: string, state: LoadingState): void {
    this.loadingStates.set(key, state);
  }

  /**
   * Get loading state for a request
   */
  public getLoadingState(key: string): LoadingState {
    return this.loadingStates.get(key) || { isLoading: false, error: null };
  }

  /**
   * Clear loading state
   */
  public clearLoadingState(key: string): void {
    this.loadingStates.delete(key);
  }

  /**
   * Log API request/response if logging is enabled
   */
  private log(type: 'request' | 'response' | 'error', data: any): void {
    if (!environment.enableApiLogging) return;

    const timestamp = new Date().toISOString();
    const prefix = `[HTTP Client ${type.toUpperCase()}] ${timestamp}`;

    switch (type) {
      case 'request':
        console.log(`${prefix} →`, data);
        break;
      case 'response':
        console.log(`${prefix} ←`, data);
        break;
      case 'error':
        console.error(`${prefix} ❌`, data);
        break;
    }
  }

  /**
   * Create request headers
   */
  private createHeaders(config: RequestConfig): Record<string, string> {
    const headers: Record<string, string> = {
      ...this.defaultHeaders,
      ...config.headers as Record<string, string>,
    };

    // Add tenant header if provided
    if (config.tenant) {
      headers['tenant'] = config.tenant;
    }

    return headers;
  }

  /**
   * Create full URL
   */
  private createUrl(endpoint: string): string {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${this.baseUrl}${cleanEndpoint}`;
  }

  /**
   * Handle API errors
   */
  private async handleError(response: Response, requestUrl: string): Promise<ApiError> {
    let errorData: any;
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

    try {
      const text = await response.text();
      if (text) {
        try {
          errorData = JSON.parse(text);
          errorMessage = errorData.message || errorData.error || errorMessage;
        } catch {
          errorMessage = text;
        }
      }
    } catch {
      // Ignore error reading response body
    }

    const apiError: ApiError = {
      message: errorMessage,
      status: response.status,
      statusText: response.statusText,
      data: errorData,
    };

    this.log('error', {
      url: requestUrl,
      status: response.status,
      statusText: response.statusText,
      error: apiError,
    });

    return apiError;
  }

  /**
   * Retry logic with exponential backoff
   */
  private async withRetry<T>(
    operation: () => Promise<T>,
    attempts: number = environment.apiRetryAttempts,
    delay: number = environment.apiRetryDelay
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (attempts <= 1) {
        throw error;
      }

      await new Promise(resolve => setTimeout(resolve, delay));
      return this.withRetry(operation, attempts - 1, delay * 2);
    }
  }

  /**
   * Make HTTP request with timeout
   */
  private async makeRequestWithTimeout(
    url: string,
    config: RequestConfig
  ): Promise<Response> {
    const timeout = config.timeout || environment.apiTimeout;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...config,
        signal: controller.signal,
        headers: this.createHeaders(config),
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Core request method
   */
  public async request<T = any>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const url = this.createUrl(endpoint);
    const requestKey = `${config.method || 'GET'}_${endpoint}`;

    // Set loading state
    this.setLoadingState(requestKey, { isLoading: true, error: null });

    try {
      this.log('request', {
        method: config.method || 'GET',
        url,
        headers: this.createHeaders(config),
        body: config.body,
      });

      const response = await this.withRetry(
        () => this.makeRequestWithTimeout(url, config),
        config.retryAttempts,
        config.retryDelay
      );

      if (!response.ok) {
        const error = await this.handleError(response, url);
        this.setLoadingState(requestKey, { isLoading: false, error });
        throw error;
      }

      // Handle response
      const text = await response.text();
      let data: T;

      if (!text) {
        data = {} as T;
      } else {
        try {
          data = JSON.parse(text) as T;
        } catch {
          data = text as unknown as T;
        }
      }

      const apiResponse: ApiResponse<T> = {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      };

      this.log('response', {
        status: response.status,
        statusText: response.statusText,
        data,
      });

      // Clear loading state
      this.setLoadingState(requestKey, { isLoading: false, error: null });

      return apiResponse;
    } catch (error) {
      const apiError: ApiError = error instanceof Error
        ? { message: error.message, originalError: error }
        : { message: String(error) };

      this.setLoadingState(requestKey, { isLoading: false, error: apiError });
      throw apiError;
    }
  }

  // Convenience methods
  public async get<T = any>(endpoint: string, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  public async post<T = any>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  public async put<T = any>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  public async delete<T = any>(endpoint: string, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  public async patch<T = any>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
}

/**
 * HTTP Client Factory
 * Creates pre-configured HTTP clients for different API services
 */
export class HttpClientFactory {
  private static clients: Map<string, HttpClient> = new Map();

  /**
   * Get or create HTTP client for a service
   */
  public static getClient(serviceType: 'primary' | 'secondary' | 'subscription-data' | 'tenant-data' | 'subscription-creation'): HttpClient {
    if (this.clients.has(serviceType)) {
      return this.clients.get(serviceType)!;
    }

    let baseUrl: string;
    const defaultHeaders: Record<string, string> = {};

    switch (serviceType) {
      case 'primary':
      case 'subscription-data':
        baseUrl = environment.apiBaseUrl;
        break;
      case 'secondary':
      case 'tenant-data':
      case 'subscription-creation':
        baseUrl = environment.apiLeadratBaseUrl;
        break;
      default:
        throw new Error(`Unknown service type: ${serviceType}`);
    }

    const client = new HttpClient(baseUrl, defaultHeaders);
    this.clients.set(serviceType, client);
    return client;
  }

  /**
   * Clear all cached clients (useful for testing or configuration changes)
   */
  public static clearClients(): void {
    this.clients.clear();
  }

  /**
   * Get primary API client (subscription data, templates, etc.)
   */
  public static get primaryClient(): HttpClient {
    return this.getClient('primary');
  }

  /**
   * Get secondary API client (tenant management, subscription creation)
   */
  public static get secondaryClient(): HttpClient {
    return this.getClient('secondary');
  }

  /**
   * Get subscription data API client (alias for primary)
   */
  public static get subscriptionDataClient(): HttpClient {
    return this.getClient('primary');
  }

  /**
   * Get tenant data API client (alias for secondary)
   */
  public static get tenantDataClient(): HttpClient {
    return this.getClient('secondary');
  }

  /**
   * Get subscription creation API client (alias for secondary)
   */
  public static get subscriptionCreationClient(): HttpClient {
    return this.getClient('secondary');
  }
}

// Export default clients for convenience
export const primaryClient = HttpClientFactory.primaryClient;
export const secondaryClient = HttpClientFactory.secondaryClient;
export const subscriptionDataClient = HttpClientFactory.subscriptionDataClient;
export const tenantDataClient = HttpClientFactory.tenantDataClient;
export const subscriptionCreationClient = HttpClientFactory.subscriptionCreationClient;
