import axios from 'axios';
import { env, isDevelopment } from '../config/environment';

// Base URLs from environment configuration
const BASE_URL = env.API_BASE_URL;
const LRB_BASE_URL = env.LRB_BASE_URL;

// Configure axios defaults based on environment
axios.defaults.timeout = env.API_TIMEOUT;

// Add request interceptor for debugging in development
if (isDevelopment() && env.ENABLE_DEBUG) {
  axios.interceptors.request.use(
    (config) => {
      return config;
    },
    (error) => {
      console.error('❌ API Request Error:', error);
      return Promise.reject(error);
    }
  );

  axios.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      console.error('❌ API Response Error:', error.response?.status, error.config?.url);
      return Promise.reject(error);
    }
  );
}

// Types
export interface TenantData {
  id: string;
  name: string;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string | null;
  product?: {
    id: string;
    name: string;
    isUserImported?: boolean;
    isRoleAssigned?: boolean;
    isOnboardCompleted?: boolean;
  };
}

export interface User {
  id: string;
  userName: string;
  firstName: string;
  lastName: string;
  email: string;
  isActive: boolean;
  emailConfirmed: boolean;
  phoneNumber: string;
  phoneNumberConfirmed: boolean;
  imageUrl: string | null;
  isDeleted: boolean;
  lastModifiedOn: string;
  createdOn: string;
  createdBy: string;
  lastModifiedBy: string;
  isMFAEnabled: boolean;
  otp: string | null;
  otpUpdatedOn: string | null;
  timeZoneInfo: string | null;
  licenseNo: string | null;
  roles: string[];
}

export interface Role {
  id: string;
  name: string;
  description: string;
}

export interface ImportedUser {
  id: string;
  userName: string;
  firstName: string;
  lastName: string;
  userId: string;
  email: string;
  isActive: boolean;
  emailConfirmed: boolean;
  phoneNumber: string;
  imageUrl: string | null;
  isDeleted: boolean;
  lastModifiedOn: string;
  createdOn: string;
  createdBy: string;
  lastModifiedBy: string;
  isMFAEnabled: boolean;
  otp: string | null;
  otpUpdatedOn: string | null;
  timeZoneInfo: string | null;
  licenseNo: string | null;
}

export interface IntegrationApi {
  id: string;
  name: string;
  schema: string;
}

export interface ObjectItem {
  id: string;
  featureId: string;
  featureName: string;
  parentObjectId: string | null;
  parentObjectName: string | null;
  name: string;
  description: string;
  isActive: boolean;
  childObjectsCount: number;
  metadataCount: number;
  createdAt: string;
  createdBy: string;
  modifiedAt: string | null;
  modifiedBy: string | null;
}

export interface ObjectMetadata {
  id: string;
  objectId: string;
  name: string;
  description: string;
  dataTypeId: string;
  dataTypeName: string;
  isRequired: boolean;
  isActive: boolean;
  createdAt: string;
  createdBy: string;
  modifiedAt: string | null;
  modifiedBy: string | null;
}

// Cache for API responses with request deduplication
class ApiCache {
  private cache = new Map<string, { data: unknown; timestamp: number; ttl: number }>();
  private pendingRequests = new Map<string, Promise<any>>();
  private submissionGuards = new Map<string, boolean>(); // Prevent duplicate POST submissions

  set(key: string, data: unknown, ttl: number = 5 * 60 * 1000) { // Default 5 minutes
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): unknown | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  // Get or set a pending request to prevent duplicate API calls
  getPendingRequest(key: string): Promise<any> | null {
    return this.pendingRequests.get(key) || null;
  }

  setPendingRequest(key: string, promise: Promise<unknown>): void {
    this.pendingRequests.set(key, promise);

    // Clean up when promise resolves/rejects
    promise.finally(() => {
      this.pendingRequests.delete(key);
    });
  }

  // Submission guards for POST operations
  isSubmissionInProgress(key: string): boolean {
    return this.submissionGuards.get(key) || false;
  }

  setSubmissionGuard(key: string): void {
    this.submissionGuards.set(key, true);
  }

  clearSubmissionGuard(key: string): void {
    this.submissionGuards.delete(key);
  }

  clear(pattern?: string) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
      // Also clear pending requests and submission guards
      for (const key of this.pendingRequests.keys()) {
        if (key.includes(pattern)) {
          this.pendingRequests.delete(key);
        }
      }
      for (const key of this.submissionGuards.keys()) {
        if (key.includes(pattern)) {
          this.submissionGuards.delete(key);
        }
      }
    } else {
      this.cache.clear();
      this.pendingRequests.clear();
      this.submissionGuards.clear();
    }
  }

  invalidate(key: string) {
    this.cache.delete(key);
    this.pendingRequests.delete(key);
    this.submissionGuards.delete(key);
  }
}

const apiCache = new ApiCache();

// API Service Class
export class ApiService {
  private static instance: ApiService;

  static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  // User APIs
  async getUsers(tenantId: string, useCache: boolean = true): Promise<User[]> {
    const cacheKey = `users_${tenantId}`;

    if (useCache) {
      const cached = apiCache.get(cacheKey);
      if (cached) return cached as User[];

      // Check if there's already a pending request for users
      const pendingRequest = apiCache.getPendingRequest(cacheKey);
      if (pendingRequest) return pendingRequest;
    }

    const requestPromise = (async () => {
      const response = await axios.get(`${BASE_URL}api/users`, {
        headers: {
          accept: "application/json",
          tenant: tenantId,
        },
      });

      if (!response.data.succeeded) {
        throw new Error(response.data.message || 'Failed to fetch users');
      }

      const users = (response.data.data || []).filter((user: User) => user.isActive !== false);

      if (useCache) {
        apiCache.set(cacheKey, users, 5 * 60 * 1000); // Cache for 5 minutes
      }

      return users;
    })();

    // Store the pending request to prevent duplicates
    if (useCache) {
      apiCache.setPendingRequest(cacheKey, requestPromise);
    }

    return requestPromise;
  }

  async importUsersFromLeadrat(tenantId: string): Promise<ImportedUser[]> {
    try {
      const response = await axios.get(`${LRB_BASE_URL}api/v1/this/users`, {
        headers: {
          accept: "application/json",
          tenant: tenantId,
        },
      });

      return response.data?.items || [];
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw new Error('No users found for this tenant in the external system');
        }
        if (error.response?.status === 403) {
          throw new Error('Access denied to external user system');
        }
        if (error.response?.status && error.response.status >= 500) {
          throw new Error('External user system is temporarily unavailable');
        }
        if (error.code === 'NETWORK_ERROR') {
          throw new Error('Cannot connect to external user system');
        }
      }
      throw new Error('Failed to fetch users from external system');
    }
  }

  async bulkCreateUsers(tenantId: string, users: Record<string, unknown>[], productId?: string): Promise<void> {
    const submissionKey = `bulk_create_users_${tenantId}_${productId || 'default'}`;

    // Check if submission is already in progress - silently return pending request
    if (apiCache.isSubmissionInProgress(submissionKey)) {
      const pendingRequest = apiCache.getPendingRequest(submissionKey);
      if (pendingRequest) {
        return pendingRequest;
      }
    }

    // Check if there's already a pending request
    const pendingRequest = apiCache.getPendingRequest(submissionKey);
    if (pendingRequest) {
      return pendingRequest;
    }

    const requestPromise = (async () => {
      try {
        apiCache.setSubmissionGuard(submissionKey);

        const response = await axios.post(
          `${BASE_URL}api/users/bulk-create`,
          {
            users,
            continueOnError: true,
            validateBeforeCreate: true,
            productId,
          },
          {
            headers: {
              "Content-Type": "application/json",
              tenant: tenantId,
            },
          }
        );

        if (!response.data.succeeded) {
          const serverMessage = response.data.messages?.[0] || response.data.message;
          if (serverMessage) {
            throw new Error(`User creation failed: ${serverMessage}`);
          }
          throw new Error("Unable to create users in the system at this time");
        }

        // Invalidate users cache since new users were imported
        apiCache.invalidate(`users_${tenantId}`);
        apiCache.invalidate(`users_roles_${tenantId}`);
      } catch (error) {
        // Better error handling for user import
        if (axios.isAxiosError(error)) {
          if (error.response?.data?.messages?.[0]) {
            throw new Error(`API Error: ${error.response.data.messages[0]}`);
          }
          if (error.response?.data?.message) {
            throw new Error(`API Error: ${error.response.data.message}`);
          }
          if (error.response?.status === 400) {
            throw new Error('Invalid user data provided for bulk creation');
          }
          if (error.response?.status === 409) {
            throw new Error('Some users already exist in the system');
          }
          if (error.response?.status && error.response.status >= 500) {
            throw new Error('Server is temporarily unavailable. Please try again later.');
          }
          if (error.code === 'NETWORK_ERROR') {
            throw new Error('Network connection failed. Please check your internet connection.');
          }
        }
        throw error;
      } finally {
        apiCache.clearSubmissionGuard(submissionKey);
      }
    })();

    // Store the pending request
    apiCache.setPendingRequest(submissionKey, requestPromise);

    return requestPromise;
  }

  // Role APIs
  async getRoles(tenantId: string, useCache: boolean = true): Promise<Role[]> {
    const cacheKey = `roles_${tenantId}`;

    if (useCache) {
      const cached = apiCache.get(cacheKey);
      if (cached) return cached as Role[];

      // Check if there's already a pending request for roles
      const pendingRequest = apiCache.getPendingRequest(cacheKey);
      if (pendingRequest) return pendingRequest;
    }

    const requestPromise = (async () => {
      const response = await axios.get(`${BASE_URL}api/roles`, {
        headers: {
          accept: "application/json",
          tenant: tenantId,
        },
      });

      if (!response.data.succeeded) {
        throw new Error(response.data.message || 'Failed to fetch roles');
      }

      const roles = response.data.data || [];

      if (useCache) {
        apiCache.set(cacheKey, roles, 15 * 60 * 1000); // Cache for 15 minutes (roles change less frequently)
      }

      return roles;
    })();

    // Store the pending request to prevent duplicates
    if (useCache) {
      apiCache.setPendingRequest(cacheKey, requestPromise);
    }

    return requestPromise;
  }

  async bulkUpdateRoles(tenantId: string, roleAssignments: any[], productId?: string): Promise<void> {
    const submissionKey = `bulk_update_roles_${tenantId}_${productId || 'default'}`;

    // Check if submission is already in progress - silently return pending request
    if (apiCache.isSubmissionInProgress(submissionKey)) {
      const pendingRequest = apiCache.getPendingRequest(submissionKey);
      if (pendingRequest) {
        return pendingRequest;
      }
    }

    // Check if there's already a pending request
    const pendingRequest = apiCache.getPendingRequest(submissionKey);
    if (pendingRequest) {
      return pendingRequest;
    }

    const requestPromise = (async () => {
      try {
        apiCache.setSubmissionGuard(submissionKey);

        // Transform roleAssignments to the expected userRoleUpdates format
        const userRoleUpdatesMap = new Map<string, string[]>();

        // Group role assignments by userId
        roleAssignments.forEach((assignment: any) => {
          const { userId, roleId } = assignment;
          if (!userRoleUpdatesMap.has(userId)) {
            userRoleUpdatesMap.set(userId, []);
          }
          userRoleUpdatesMap.get(userId)!.push(roleId);
        });

        // Convert to the expected format
        const userRoleUpdates = Array.from(userRoleUpdatesMap.entries()).map(([userId, roleIds]) => ({
          userId,
          roleIds
        }));

        const payload = {
          userRoleUpdates,
          continueOnError: true,
          validateBeforeUpdate: true,
          productId
        };

        const response = await axios.post(
          `${BASE_URL}api/users/bulk-update-roles`,
          payload,
          {
            headers: {
              'Content-Type': 'application/json',
              'tenant': tenantId,
            }
          }
        );

        // Check for API response errors
        if (response.data && !response.data.succeeded) {
          const serverMessage = response.data.messages?.[0] || response.data.message;
          if (serverMessage) {
            throw new Error(serverMessage);
          }
          throw new Error("Unable to assign roles at this time");
        }

        // No need to invalidate users cache or fetch users after role assignment
      } catch (error) {
        // Better error handling for role assignment
        if (axios.isAxiosError(error)) {
          if (error.response?.data?.messages?.[0]) {
            throw new Error(error.response.data.messages[0]);
          }
          if (error.response?.data?.message) {
            throw new Error(error.response.data.message);
          }
          if (error.response?.status === 400) {
            throw new Error('Invalid role assignment data');
          }
          if (error.response?.status && error.response.status >= 500) {
            throw new Error('Server is temporarily unavailable');
          }
        }
        throw error;
      } finally {
        apiCache.clearSubmissionGuard(submissionKey);
      }
    })();

    // Store the pending request
    apiCache.setPendingRequest(submissionKey, requestPromise);

    return requestPromise;
  }

  // Batch API calls for efficiency
  async getUsersAndRoles(tenantId: string, useCache: boolean = true): Promise<{ users: User[]; roles: Role[] }> {
    const batchCacheKey = `users_roles_${tenantId}`;

    if (useCache) {
      const cached = apiCache.get(batchCacheKey);
      if (cached) return cached as { users: User[]; roles: Role[] };

      // Check if there's already a pending batch request
      const pendingRequest = apiCache.getPendingRequest(batchCacheKey);
      if (pendingRequest) return pendingRequest;
    }

    const requestPromise = (async () => {
      const [users, roles] = await Promise.all([
        this.getUsers(tenantId, useCache),
        this.getRoles(tenantId, useCache)
      ]);

      const result = { users, roles };

      if (useCache) {
        apiCache.set(batchCacheKey, result, 5 * 60 * 1000); // Cache for 5 minutes
      }

      return result;
    })();

    // Store the pending request to prevent duplicates
    if (useCache) {
      apiCache.setPendingRequest(batchCacheKey, requestPromise);
    }

    return requestPromise;
  }

  // Cache management
  clearCache(pattern?: string) {
    apiCache.clear(pattern);
  }

  // Submission guard methods
  isSubmissionInProgress(key: string): boolean {
    return apiCache.isSubmissionInProgress(key);
  }

  setSubmissionGuard(key: string): void {
    apiCache.setSubmissionGuard(key);
  }

  clearSubmissionGuard(key: string): void {
    apiCache.clearSubmissionGuard(key);
  }

  invalidateCache(key: string) {
    apiCache.invalidate(key);
  }
}

// Export singleton instance
export const apiService = ApiService.getInstance();
