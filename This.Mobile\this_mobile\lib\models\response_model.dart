import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'response_model.g.dart';

/// Main response model for API calls
@JsonSerializable()
class ResponseModel {
  @Json<PERSON><PERSON>(name: 'objectName')
  final String? objectName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'tenantId')
  final String? tenantId;

  @J<PERSON><PERSON><PERSON>(name: 'viewName')
  final String? viewName;

  @Json<PERSON>ey(name: 'viewCreationResult')
  final String? viewCreationResult;

  @Json<PERSON>ey(name: 'columnNames')
  final List<String>? columnNames;

  @Json<PERSON>ey(name: 'viewData')
  final List<Map<String, dynamic>>? viewData;

  @J<PERSON><PERSON><PERSON>(name: 'totalRows')
  final int? totalRows;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'pageNumber')
  final int? pageNumber;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'pageSize')
  final int? pageSize;

  @<PERSON>son<PERSON>ey(name: 'totalPages')
  final int? totalPages;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'hasPreviousPage')
  final bool? hasPreviousPage;

  @J<PERSON><PERSON><PERSON>(name: 'hasNextPage')
  final bool? hasNextPage;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'message')
  final String? message;

  const ResponseModel({
    this.objectName,
    this.tenantId,
    this.viewName,
    this.viewCreationResult,
    this.columnNames,
    this.viewData,
    this.totalRows,
    this.pageNumber,
    this.pageSize,
    this.totalPages,
    this.hasPreviousPage,
    this.hasNextPage,
    this.message,
  });

  factory ResponseModel.fromJson(Map<String, dynamic> json) =>
      _$ResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseModelToJson(this);

  @override
  String toString() {
    return 'ResponseModel{objectName: $objectName, tenantId: $tenantId, viewName: $viewName, viewCreationResult: $viewCreationResult, columnNames: $columnNames, viewData: $viewData, totalRows: $totalRows, pageNumber: $pageNumber, pageSize: $pageSize, totalPages: $totalPages, hasPreviousPage: $hasPreviousPage, hasNextPage: $hasNextPage, message: $message}';
  }
}

/// Product model
@JsonSerializable()
class Product {
  @JsonKey(name: 'id')
  final String? id;
  
  @JsonKey(name: 'name')
  final String? name;
  
  @JsonKey(name: 'description')
  final String? description;
  
  @JsonKey(name: 'version')
  final String? version;
  
  @JsonKey(name: 'isActive')
  final bool? isActive;
  
  @JsonKey(name: 'metadata')
  final List<MetadataWrapper>? metadata;
  
  @JsonKey(name: 'rootObjects')
  final List<RootObject>? rootObjects;

  const Product({
    this.id,
    this.name,
    this.description,
    this.version,
    this.isActive,
    this.metadata,
    this.rootObjects,
  });

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);

  Map<String, dynamic> toJson() => _$ProductToJson(this);
}

/// Metadata wrapper model
@JsonSerializable()
class MetadataWrapper {
  @JsonKey(name: 'metadata')
  final Metadata? metadata;
  
  @JsonKey(name: 'values')
  final List<Value>? values;

  const MetadataWrapper({
    this.metadata,
    this.values,
  });

  factory MetadataWrapper.fromJson(Map<String, dynamic> json) =>
      _$MetadataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$MetadataWrapperToJson(this);
}

/// Metadata model
@JsonSerializable()
class Metadata {
  @JsonKey(name: 'id')
  final String? id;
  
  @JsonKey(name: 'name')
  final String? name;
  
  @JsonKey(name: 'displayLabel')
  final String? displayLabel;
  
  @JsonKey(name: 'helpText')
  final String? helpText;
  
  @JsonKey(name: 'fieldOrder')
  final int? fieldOrder;
  
  @JsonKey(name: 'isVisible')
  final bool? isVisible;
  
  @JsonKey(name: 'isReadonly')
  final bool? isReadonly;
  
  @JsonKey(name: 'validationPattern')
  final String? validationPattern;
  
  @JsonKey(name: 'minLength')
  final int? minLength;
  
  @JsonKey(name: 'maxLength')
  final int? maxLength;
  
  @JsonKey(name: 'minValue')
  final int? minValue;
  
  @JsonKey(name: 'maxValue')
  final int? maxValue;
  
  @JsonKey(name: 'isRequired')
  final bool? isRequired;
  
  @JsonKey(name: 'placeholder')
  final String? placeholder;
  
  @JsonKey(name: 'defaultOptions')
  final String? defaultOptions;
  
  @JsonKey(name: 'maxSelections')
  final int? maxSelections;
  
  @JsonKey(name: 'allowedFileTypes')
  final String? allowedFileTypes;
  
  @JsonKey(name: 'maxFileSize')
  final int? maxFileSize;
  
  @JsonKey(name: 'errorMessage')
  final String? errorMessage;
  
  @JsonKey(name: 'dataType')
  final DataType? dataType;
  
  @JsonKey(name: 'metadataLink')
  final MetadataLink? metadataLink;

  const Metadata({
    this.id,
    this.name,
    this.displayLabel,
    this.helpText,
    this.fieldOrder,
    this.isVisible,
    this.isReadonly,
    this.validationPattern,
    this.minLength,
    this.maxLength,
    this.minValue,
    this.maxValue,
    this.isRequired,
    this.placeholder,
    this.defaultOptions,
    this.maxSelections,
    this.allowedFileTypes,
    this.maxFileSize,
    this.errorMessage,
    this.dataType,
    this.metadataLink,
  });

  factory Metadata.fromJson(Map<String, dynamic> json) =>
      _$MetadataFromJson(json);

  Map<String, dynamic> toJson() => _$MetadataToJson(this);
}

/// Data type model
@JsonSerializable()
class DataType {
  @JsonKey(name: 'id')
  final String? id;
  
  @JsonKey(name: 'name')
  final String? name;
  
  @JsonKey(name: 'displayName')
  final String? displayName;
  
  @JsonKey(name: 'category')
  final String? category;
  
  @JsonKey(name: 'uiComponent')
  final String? uiComponent;
  
  @JsonKey(name: 'validationPattern')
  final String? validationPattern;
  
  @JsonKey(name: 'minLength')
  final int? minLength;
  
  @JsonKey(name: 'maxLength')
  final int? maxLength;
  
  @JsonKey(name: 'minValue')
  final int? minValue;
  
  @JsonKey(name: 'maxValue')
  final int? maxValue;
  
  @JsonKey(name: 'decimalPlaces')
  final int? decimalPlaces;
  
  @JsonKey(name: 'stepValue')
  final int? stepValue;
  
  @JsonKey(name: 'isRequired')
  final bool? isRequired;
  
  @JsonKey(name: 'inputType')
  final String? inputType;
  
  @JsonKey(name: 'inputMask')
  final String? inputMask;
  
  @JsonKey(name: 'placeholder')
  final String? placeholder;
  
  @JsonKey(name: 'htmlAttributes')
  final String? htmlAttributes;
  
  @JsonKey(name: 'defaultOptions')
  final String? defaultOptions;
  
  @JsonKey(name: 'allowsMultiple')
  final bool? allowsMultiple;
  
  @JsonKey(name: 'allowsCustomOptions')
  final bool? allowsCustomOptions;
  
  @JsonKey(name: 'maxSelections')
  final int? maxSelections;
  
  @JsonKey(name: 'allowedFileTypes')
  final String? allowedFileTypes;
  
  @JsonKey(name: 'maxFileSizeBytes')
  final int? maxFileSizeBytes;
  
  @JsonKey(name: 'requiredErrorMessage')
  final String? requiredErrorMessage;
  
  @JsonKey(name: 'patternErrorMessage')
  final String? patternErrorMessage;
  
  @JsonKey(name: 'minLengthErrorMessage')
  final String? minLengthErrorMessage;
  
  @JsonKey(name: 'maxLengthErrorMessage')
  final String? maxLengthErrorMessage;
  
  @JsonKey(name: 'minValueErrorMessage')
  final String? minValueErrorMessage;
  
  @JsonKey(name: 'maxValueErrorMessage')
  final String? maxValueErrorMessage;
  
  @JsonKey(name: 'fileTypeErrorMessage')
  final String? fileTypeErrorMessage;
  
  @JsonKey(name: 'fileSizeErrorMessage')
  final String? fileSizeErrorMessage;
  
  @JsonKey(name: 'isActive')
  final bool? isActive;

  const DataType({
    this.id,
    this.name,
    this.displayName,
    this.category,
    this.uiComponent,
    this.validationPattern,
    this.minLength,
    this.maxLength,
    this.minValue,
    this.maxValue,
    this.decimalPlaces,
    this.stepValue,
    this.isRequired,
    this.inputType,
    this.inputMask,
    this.placeholder,
    this.htmlAttributes,
    this.defaultOptions,
    this.allowsMultiple,
    this.allowsCustomOptions,
    this.maxSelections,
    this.allowedFileTypes,
    this.maxFileSizeBytes,
    this.requiredErrorMessage,
    this.patternErrorMessage,
    this.minLengthErrorMessage,
    this.maxLengthErrorMessage,
    this.minValueErrorMessage,
    this.maxValueErrorMessage,
    this.fileTypeErrorMessage,
    this.fileSizeErrorMessage,
    this.isActive,
  });

  factory DataType.fromJson(Map<String, dynamic> json) =>
      _$DataTypeFromJson(json);

  Map<String, dynamic> toJson() => _$DataTypeToJson(this);
}

/// Metadata link model
@JsonSerializable()
class MetadataLink {
  @JsonKey(name: 'objectMetaDataId')
  final String? objectMetaDataId;
  
  @JsonKey(name: 'isUnique')
  final bool? isUnique;
  
  @JsonKey(name: 'isActive')
  final bool? isActive;
  
  @JsonKey(name: 'shouldVisibleInList')
  final bool? shouldVisibleInList;
  
  @JsonKey(name: 'shouldVisibleInEdit')
  final bool? shouldVisibleInEdit;
  
  @JsonKey(name: 'shouldVisibleInCreate')
  final bool? shouldVisibleInCreate;
  
  @JsonKey(name: 'shouldVisibleInView')
  final bool? shouldVisibleInView;
  
  @JsonKey(name: 'isCalculate')
  final bool? isCalculate;

  const MetadataLink({
    this.objectMetaDataId,
    this.isUnique,
    this.isActive,
    this.shouldVisibleInList,
    this.shouldVisibleInEdit,
    this.shouldVisibleInCreate,
    this.shouldVisibleInView,
    this.isCalculate,
  });

  factory MetadataLink.fromJson(Map<String, dynamic> json) =>
      _$MetadataLinkFromJson(json);

  Map<String, dynamic> toJson() => _$MetadataLinkToJson(this);
}

/// Value model
@JsonSerializable()
class Value {
  @JsonKey(name: 'id')
  final String? id;
  
  @JsonKey(name: 'refId')
  final String? refId;
  
  @JsonKey(name: 'value')
  final String? value;
  
  @JsonKey(name: 'parentObjectValueId')
  final String? parentObjectValueId;

  const Value({
    this.id,
    this.refId,
    this.value,
    this.parentObjectValueId,
  });

  factory Value.fromJson(Map<String, dynamic> json) =>
      _$ValueFromJson(json);

  Map<String, dynamic> toJson() => _$ValueToJson(this);
}

/// Root object model
@JsonSerializable()
class RootObject {
  @JsonKey(name: 'id')
  final String? id;
  
  @JsonKey(name: 'name')
  final String? name;
  
  @JsonKey(name: 'description')
  final String? description;
  
  @JsonKey(name: 'parentObjectId')
  final String? parentObjectId;
  
  @JsonKey(name: 'isActive')
  final bool? isActive;
  
  @JsonKey(name: 'hierarchyLevel')
  final int? hierarchyLevel;
  
  @JsonKey(name: 'hierarchyPath')
  final String? hierarchyPath;
  
  @JsonKey(name: 'metadata')
  final List<MetadataWrapper>? metadata;
  
  @JsonKey(name: 'childObjects')
  final List<String>? childObjects;

  const RootObject({
    this.id,
    this.name,
    this.description,
    this.parentObjectId,
    this.isActive,
    this.hierarchyLevel,
    this.hierarchyPath,
    this.metadata,
    this.childObjects,
  });

  factory RootObject.fromJson(Map<String, dynamic> json) =>
      _$RootObjectFromJson(json);

  Map<String, dynamic> toJson() => _$RootObjectToJson(this);
}


