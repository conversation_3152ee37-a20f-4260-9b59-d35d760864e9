import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>ton, Table, Badge, Form, InputGroup, <PERSON>ner, <PERSON><PERSON>, <PERSON><PERSON>, Pagin<PERSON> } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { templateService, TemplateGrouper } from '../services/templateService';
import type {PaginatedTemplateResponse, GetTemplatesParams, GroupedTemplate } from '../services/templateService';

// Template interface - updated to match new API response
interface Template {
  id: string;
  name: string; // Template name for grouping
  version: string;
  stage: string; // API returns string, not restricted enum
  templateJson: any; // Now parsed object, not string
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  isActive: boolean;
  isDeleted: boolean;
}

// Mock templates data - updated to match new API structure
const mockTemplates: Template[] = [
  {
    id: "template-1",
    name: "KitchenSync",
    version: "1.0.0",
    stage: "draft",
    templateJson: {
      products: [
        {
          name: "KithenSync",
          type: "product",
          metadata: [],
          objects: [
            {
              name: "Chiken",
              type: "object",
              metadata: [
                {
                  name: "ParentObjectId",
                  type: "Text",
                  description: "Parent object identifier",
                  required: false,
                  isActive: true
                },
                {
                  name: "Name",
                  type: "Text",
                  description: "Object name",
                  required: true,
                  isActive: true
                },
                {
                  name: "CutType",
                  type: "Select",
                  description: "Cut Type",
                  required: true,
                  isActive: true
                },
                {
                  name: "WeightInKg",
                  type: "Number",
                  description: "Weight Kg",
                  required: false,
                  isActive: true
                },
                {
                  name: "PricePerKg",
                  type: "Number",
                  description: "Price Per Kg",
                  required: false,
                  isActive: true
                }
              ]
            }
          ]
        }
      ]
    },
    createdAt: "2025-06-15T10:00:00Z",
    createdBy: "00000000-0000-0000-0000-000000000000",
    publishedAt: "2025-06-15T12:00:00Z",
    isActive: true,
    isDeleted: false
  },
  {
    id: "template-2",
    name: "RestaurantPOS",
    version: "2.1.0",
    stage: "live",
    templateJson: {
      products: [
        {
          name: "RestaurantPOS",
          type: "product",
          metadata: [],
          objects: [
            {
              name: "MenuItem",
              type: "object",
              metadata: [
                {
                  name: "Name",
                  type: "Text",
                  description: "Menu item name",
                  required: true,
                  isActive: true
                },
                {
                  name: "Price",
                  type: "Currency",
                  description: "Item price",
                  required: true,
                  isActive: true
                }
              ]
            }
          ]
        }
      ]
    },
    createdAt: "2025-06-10T08:00:00Z",
    createdBy: "00000000-0000-0000-0000-000000000000",
    publishedAt: "2025-06-12T14:00:00Z",
    isActive: true,
    isDeleted: false
  },
  {
    id: "template-3",
    name: "InventoryManager",
    version: "1.5.0",
    stage: "live",
    templateJson: {
      products: [
        {
          name: "InventoryManager",
          type: "product",
          metadata: [],
          objects: [
            {
              name: "Product",
              type: "object",
              metadata: [
                {
                  name: "SKU",
                  type: "Text",
                  description: "Product SKU",
                  required: true,
                  isActive: true
                },
                {
                  name: "Quantity",
                  type: "Number",
                  description: "Stock quantity",
                  required: true,
                  isActive: true
                }
              ]
            }
          ]
        }
      ]
    },
    createdAt: "2025-06-12T09:00:00Z",
    createdBy: "00000000-0000-0000-0000-000000000000",
    publishedAt: "2025-06-13T11:00:00Z",
    isActive: true,
    isDeleted: false
  },
  {
    id: "template-4",
    name: "CustomerCRM",
    version: "3.0.0",
    stage: "beta",
    templateJson: {
      products: [
        {
          name: "CustomerCRM",
          type: "product",
          metadata: [],
          objects: [
            {
              name: "Customer",
              type: "object",
              metadata: [
                {
                  name: "Name",
                  type: "Text",
                  description: "Customer name",
                  required: true,
                  isActive: true
                },
                {
                  name: "Email",
                  type: "Email",
                  description: "Customer email",
                  required: true,
                  isActive: true
                }
              ]
            }
          ]
        }
      ]
    },
    createdAt: "2025-06-08T14:00:00Z",
    createdBy: "00000000-0000-0000-0000-000000000000",
    isActive: true,
    isDeleted: false
  },
  {
    id: "template-5",
    name: "ProjectTracker",
    version: "2.3.0",
    stage: "live",
    templateJson: {
      products: [
        {
          name: "ProjectTracker",
          type: "product",
          metadata: [],
          objects: [
            {
              name: "Task",
              type: "object",
              metadata: [
                {
                  name: "Title",
                  type: "Text",
                  description: "Task title",
                  required: true,
                  isActive: true
                },
                {
                  name: "Status",
                  type: "Select",
                  description: "Task status",
                  required: true,
                  isActive: true
                }
              ]
            }
          ]
        }
      ]
    },
    createdAt: "2025-06-05T16:00:00Z",
    createdBy: "00000000-0000-0000-0000-000000000000",
    publishedAt: "2025-06-07T10:00:00Z",
    isActive: true,
    isDeleted: false
  },
  {
    id: "template-6",
    name: "EventPlanner",
    version: "1.2.0",
    stage: "draft",
    templateJson: {
      products: [
        {
          name: "EventPlanner",
          type: "product",
          metadata: [],
          objects: [
            {
              name: "Event",
              type: "object",
              metadata: [
                {
                  name: "Name",
                  type: "Text",
                  description: "Event name",
                  required: true,
                  isActive: true
                },
                {
                  name: "Date",
                  type: "Date",
                  description: "Event date",
                  required: true,
                  isActive: true
                }
              ]
            }
          ]
        }
      ]
    },
    createdAt: "2025-06-14T12:00:00Z",
    createdBy: "00000000-0000-0000-0000-000000000000",
    isActive: true,
    isDeleted: false
  },
  {
    id: "template-7",
    name: "BookingSystem",
    version: "4.1.0",
    stage: "live",
    templateJson: {
      products: [
        {
          name: "BookingSystem",
          type: "product",
          metadata: [],
          objects: [
            {
              name: "Booking",
              type: "object",
              metadata: [
                {
                  name: "CustomerName",
                  type: "Text",
                  description: "Customer name",
                  required: true,
                  isActive: true
                },
                {
                  name: "BookingDate",
                  type: "DateTime",
                  description: "Booking date and time",
                  required: true,
                  isActive: true
                }
              ]
            }
          ]
        }
      ]
    },
    createdAt: "2025-06-01T08:00:00Z",
    createdBy: "00000000-0000-0000-0000-000000000000",
    publishedAt: "2025-06-02T15:00:00Z",
    isActive: true,
    isDeleted: false
  },
  {
    id: "template-8",
    name: "FinanceTracker",
    version: "2.0.0",
    stage: "archived",
    templateJson: {
      products: [
        {
          name: "FinanceTracker",
          type: "product",
          metadata: [],
          objects: [
            {
              name: "Transaction",
              type: "object",
              metadata: [
                {
                  name: "Amount",
                  type: "Currency",
                  description: "Transaction amount",
                  required: true,
                  isActive: true
                },
                {
                  name: "Category",
                  type: "Select",
                  description: "Transaction category",
                  required: true,
                  isActive: true
                }
              ]
            }
          ]
        }
      ]
    },
    createdAt: "2025-05-28T10:00:00Z",
    createdBy: "00000000-0000-0000-0000-000000000000",
    publishedAt: "2025-05-30T14:00:00Z",
    isActive: false,
    isDeleted: false
  }
];

export const Templates: React.FC = () => {
  const navigate = useNavigate();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [groupedTemplates, setGroupedTemplates] = useState<GroupedTemplate[]>([]);
  const [filteredGroupedTemplates, setFilteredGroupedTemplates] = useState<GroupedTemplate[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [stageFilter, setStageFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    pageNumber: 1,
    pageSize: 50,
    totalCount: 0,
    totalPages: 0
  });
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedProductVersions, setSelectedProductVersions] = useState<Template[]>([]);

  // Client-side pagination for filtered results
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch templates from API
  const fetchTemplates = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params: GetTemplatesParams = {
        pageNumber: pagination.pageNumber,
        pageSize: pagination.pageSize,
        isActive: true,
        includeDeleted: false
      };

      console.log('Fetching templates with params:', params);
      const response: PaginatedTemplateResponse = await templateService.getTemplates(params);
      console.log('API Response:', response);

      // Store individual templates for version selection
      setTemplates(response.data);

      // Group templates by productId for display
      const grouped = TemplateGrouper.groupTemplatesByProductId(response.data);
      console.log('Grouped templates:', grouped);
      setGroupedTemplates(grouped);

      // Update pagination info
      setPagination(prev => ({
        ...prev,
        totalCount: response.totalCount,
        totalPages: response.totalPages
      }));

    } catch (err) {
      console.error('Error fetching templates:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch templates');

      // Fallback to mock data on error
      console.log('Using mock data as fallback');
      setTemplates(mockTemplates);
      const grouped = TemplateGrouper.groupTemplatesByProductId(mockTemplates);
      setGroupedTemplates(grouped);
    } finally {
      setIsLoading(false);
    }
  };

  // Load templates on component mount
  useEffect(() => {
    fetchTemplates();
  }, [pagination.pageNumber, pagination.pageSize]);

  // Filter grouped templates based on search and filters
  useEffect(() => {
    const filtered = TemplateGrouper.filterGroupedTemplates(
      groupedTemplates,
      searchTerm,
      stageFilter,
      statusFilter
    );
    setFilteredGroupedTemplates(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [groupedTemplates, searchTerm, stageFilter, statusFilter]);

  // Pagination calculations for filtered results
  const totalPages = Math.ceil(filteredGroupedTemplates.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedTemplates = filteredGroupedTemplates.slice(startIndex, endIndex);

  // Get stage badge variant
  const getStageBadgeVariant = (stage: string) => {
    switch (stage) {
      case 'live': return 'success';
      case 'beta': return 'warning';
      case 'archived': return 'secondary';
      default: return 'primary';
    }
  };

  // Handle create template - directly navigate to Product Builder
  const handleCreateTemplate = () => {
    navigate('/product-builder');
  };

  // Handle edit template - show version selection if multiple versions exist
  const handleEditTemplate = (groupedTemplate: GroupedTemplate) => {
    // Find all templates with the same name
    const allVersions = templates.filter(t => t.name === groupedTemplate.name);

    if (allVersions.length === 1) {
      // Only one version, navigate directly
      navigateToEdit(allVersions[0]);
    } else {
      // Multiple versions, show selection modal
      const sortedVersions = allVersions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      setSelectedProductVersions(sortedVersions);
      setShowEditModal(true);
    }
  };

  // Navigate to edit mode with template
  const navigateToEdit = (template: Template) => {
    try {
      let parsedTemplateJson;

      // Check if templateJson is a string and needs parsing
      if (typeof template.templateJson === 'string') {
        try {
          parsedTemplateJson = JSON.parse(template.templateJson);
          console.log('Parsed templateJson from string:', parsedTemplateJson);
        } catch (parseError) {
          console.error('Error parsing templateJson string:', parseError);
          alert('Error: Template JSON is not valid. Please check the template format.');
          return;
        }
      } else if (typeof template.templateJson === 'object' && template.templateJson !== null) {
        // Already an object
        parsedTemplateJson = template.templateJson;
        console.log('Using templateJson as object:', parsedTemplateJson);
      } else {
        console.error('Invalid templateJson format:', template.templateJson);
        alert('Error: Template JSON is missing or invalid.');
        return;
      }

      // Validate that the parsed JSON has the expected structure
      if (!parsedTemplateJson.products || !Array.isArray(parsedTemplateJson.products)) {
        console.error('Invalid template structure - missing products array:', parsedTemplateJson);
        alert('Error: Template does not contain valid products data.');
        return;
      }

      // Create template data with parsed JSON
      const templateData = {
        ...template,
        templateJson: parsedTemplateJson
      };

      console.log('Navigating to product builder with template data:', templateData);

      navigate('/product-builder', {
        state: {
          templateData: templateData,
          mode: 'edit'
        }
      });
    } catch (error) {
      console.error('Error loading template data for edit:', error);
      alert('Error loading template data. Please check the template format.');
    }
  };

  // Get product count from grouped template
  const getProductCount = (groupedTemplate: GroupedTemplate) => {
    try {
      let templateJson = groupedTemplate.displayTemplate.templateJson;

      // Parse templateJson if it's a string
      if (typeof templateJson === 'string') {
        templateJson = JSON.parse(templateJson);
      }

      return templateJson.products?.length || 0;
    } catch {
      return 0;
    }
  };

  // Get object count from grouped template
  const getObjectCount = (groupedTemplate: GroupedTemplate) => {
    try {
      let templateJson = groupedTemplate.displayTemplate.templateJson;

      // Parse templateJson if it's a string
      if (typeof templateJson === 'string') {
        templateJson = JSON.parse(templateJson);
      }

      let count = 0;
      templateJson.products?.forEach((product: any) => {
        count += product.objects?.length || 0;
      });
      return count;
    } catch {
      return 0;
    }
  };

  // Get product name from grouped template
  const getProductName = (groupedTemplate: GroupedTemplate) => {
    // Use the template name directly from the API
    return groupedTemplate.templateName || groupedTemplate.name || 'Unnamed Template';
  };

  return (
    <div className="d-flex flex-column min-vh-100">
      {/* Main Content */}
      <main className="flex-grow-1" style={{ padding: '0.25rem 1rem 0.25rem 1rem' }}>
        <div style={{ maxWidth: 'none', width: '100%' }}>
          {/* Search and Filters with Action Buttons */}
          <div className="modern-card mb-1">
            <div className="modern-card-body" style={{ padding: '1rem' }}>
              <Row className="g-3 align-items-center">
                <Col md={4}>
                  <InputGroup className="shadow-sm">
                    <InputGroup.Text className="bg-light border-end-0" style={{ borderColor: 'var(--card-border)' }}>
                      🔍
                    </InputGroup.Text>
                    <Form.Control
                      type="text"
                      placeholder="Search templates by product name, version, or ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="border-start-0"
                      style={{ borderColor: 'var(--card-border)' }}
                    />
                  </InputGroup>
                </Col>
                <Col md={2}>
                  <Form.Select
                    value={stageFilter}
                    onChange={(e) => setStageFilter(e.target.value)}
                    className="shadow-sm"
                    style={{ borderColor: 'var(--card-border)' }}
                  >
                    <option value="all">All Stages</option>
                    <option value="draft">Draft</option>
                    <option value="beta">Beta</option>
                    <option value="live">Live</option>
                    <option value="archived">Archived</option>
                  </Form.Select>
                </Col>
                <Col md={2}>
                  <Form.Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="shadow-sm"
                    style={{ borderColor: 'var(--card-border)' }}
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </Form.Select>
                </Col>
                <Col md={4} className="text-end">
                  <div className="d-flex gap-2 justify-content-end">
                    <Button
                      variant="outline-primary"
                      onClick={fetchTemplates}
                      disabled={isLoading}
                      size="sm"
                      className="d-flex align-items-center gap-2"
                    >
                      {isLoading ? (
                        <Spinner animation="border" size="sm" />
                      ) : (
                        <span>🔄</span>
                      )}
                      Refresh
                    </Button>

                    <Button
                      variant="primary"
                      onClick={handleCreateTemplate}
                      size="sm"
                      className="d-flex align-items-center gap-2 fw-semibold"
                    >
                      <span>+</span>
                      Create Template
                    </Button>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div style={{ maxWidth: 'none', width: '100%' }}>
          {/* Loading State */}
          {isLoading && (
            <Row className="mb-2">
              <Col className="text-center">
                <Spinner animation="border" role="status" className="me-2" />
                <span>Loading templates...</span>
              </Col>
            </Row>
          )}

          {/* Error State */}
          {error && (
            <Row className="mb-2">
              <Col>
                <Alert variant="danger" className="d-flex align-items-center">
                  <span className="me-2">⚠️</span>
                  <div>
                    <strong>Error loading templates:</strong> {error}
                    <div className="mt-2">
                      <Button variant="outline-danger" size="sm" onClick={fetchTemplates}>
                        Retry
                      </Button>
                    </div>
                  </div>
                </Alert>
              </Col>
            </Row>
          )}

          {/* Compact Stats Cards */}
          {!isLoading && (
            <Row className="mb-1 g-2">
              <Col md={3}>
                <div className="stats-card">
                  <div className="text-center" style={{ padding: '0.75rem' }}>
                    <div className="stats-number">{groupedTemplates.length}</div>
                    <div className="stats-label">Total Templates</div>
                  </div>
                </div>
              </Col>
              <Col md={3}>
                <div className="stats-card">
                  <div className="text-center" style={{ padding: '0.75rem' }}>
                    <div className="stats-number">
                      {groupedTemplates.filter(t => t.displayTemplate.stage === 'live').length}
                    </div>
                    <div className="stats-label">Live Templates</div>
                  </div>
                </div>
              </Col>
              <Col md={3}>
                <div className="stats-card">
                  <div className="text-center" style={{ padding: '0.75rem' }}>
                    <div className="stats-number">
                      {groupedTemplates.filter(t => t.displayTemplate.stage === 'draft').length}
                    </div>
                    <div className="stats-label">Draft Templates</div>
                  </div>
                </div>
              </Col>
              <Col md={3}>
                <div className="stats-card">
                  <div className="text-center" style={{ padding: '0.75rem' }}>
                    <div className="stats-number">
                      {filteredGroupedTemplates.length}
                    </div>
                    <div className="stats-label">Filtered Results</div>
                  </div>
                </div>
              </Col>
            </Row>
          )}

          {/* Modern Templates Table */}
          <Row>
            <Col>
              <div className="modern-card" style={{ display: 'flex', flexDirection: 'column', height: 'calc(100vh - 180px)' }}>
                <div className="modern-card-header" style={{ flexShrink: 0 }}>
                  <div className="d-flex justify-content-between align-items-center">
                    <div className="d-flex align-items-center gap-2">
                      <span className="text-muted">📋</span>
                      <h5 className="mb-0 fw-semibold">Templates</h5>
                    </div>
                    <div className="d-flex align-items-center gap-3">
                      <small className="text-muted fw-medium">
                        Showing {filteredGroupedTemplates.length} of {groupedTemplates.length} templates
                      </small>
                      {(searchTerm || stageFilter !== 'all' || statusFilter !== 'all') && (
                        <Button
                          variant="outline-secondary"
                          size="sm"
                          onClick={() => {
                            setSearchTerm('');
                            setStageFilter('all');
                            setStatusFilter('all');
                          }}
                          className="border-2"
                          style={{ borderColor: 'var(--gradient-start)' }}
                        >
                          Clear Filters
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
                <div className="modern-card-body p-0" style={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
                  <div style={{ flex: 1, overflow: 'auto' }}>
                    <Table hover responsive className="mb-0 table-sm">
                    <thead style={{ background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)' }}>
                      <tr>
                        <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Product Name</th>
                        <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Latest Version</th>
                        <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Stage</th>
                        <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Products</th>
                        <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Objects</th>
                        <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Versions</th>
                        <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Created</th>
                        <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Status</th>
                        <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {paginatedTemplates.map((groupedTemplate) => (
                        <tr key={groupedTemplate.name}>
                          <td className="ps-3">
                            <div className="fw-medium">
                              {getProductName(groupedTemplate)}
                            </div>
                            <small className="text-muted">Template: {groupedTemplate.name}</small>
                          </td>
                          <td className="ps-3">
                            <Badge bg="light" text="dark">{groupedTemplate.latestVersion}</Badge>
                          </td>
                          <td className="ps-3">
                            <Badge bg={getStageBadgeVariant(groupedTemplate.displayTemplate.stage)}>
                              {groupedTemplate.displayTemplate.stage.toUpperCase()}
                            </Badge>
                          </td>
                          <td className="ps-3">
                            <span className="badge bg-info">{getProductCount(groupedTemplate)}</span>
                          </td>
                          <td className="ps-3">
                            <span className="badge bg-warning text-dark">{getObjectCount(groupedTemplate)}</span>
                          </td>
                          <td className="ps-3">
                            <div className="d-flex align-items-center gap-2">
                              <Badge bg="secondary">{groupedTemplate.totalVersions}</Badge>
                              <small className="text-muted">
                                {groupedTemplate.totalVersions === 1 ? 'version' : 'versions'}
                              </small>
                            </div>
                          </td>
                          <td className="ps-3">
                            <small>{new Date(groupedTemplate.displayTemplate.createdAt).toLocaleDateString()}</small>
                          </td>
                          <td className="ps-3">
                            <Badge bg={groupedTemplate.displayTemplate.isActive ? 'success' : 'danger'}>
                              {groupedTemplate.displayTemplate.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </td>
                          <td className="ps-3">
                            <div className="d-flex gap-2">
                              <Button
                                size="sm"
                                variant="outline-success"
                                onClick={() => handleEditTemplate(groupedTemplate)}
                                title="Edit Template"
                                className="d-flex align-items-center gap-1"
                              >
                                ✏️
                                Edit
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>

                {/* Pagination */}
                {filteredGroupedTemplates.length > 0 && (
                  <div className="d-flex justify-content-between align-items-center" style={{ padding: '1rem', borderTop: '1px solid var(--card-border)', flexShrink: 0 }}>
                      <div className="d-flex align-items-center gap-3">
                        <span className="text-muted small">
                          Showing {startIndex + 1} to {Math.min(endIndex, filteredGroupedTemplates.length)} of {filteredGroupedTemplates.length} entries
                        </span>
                        <div className="d-flex align-items-center gap-2">
                          <span className="text-muted small">Show:</span>
                          <Form.Select
                            size="sm"
                            value={itemsPerPage}
                            onChange={(e) => {
                              setItemsPerPage(Number(e.target.value));
                              setCurrentPage(1);
                            }}
                            style={{ width: 'auto' }}
                          >
                            <option value={5}>5</option>
                            <option value={10}>10</option>
                            <option value={25}>25</option>
                            <option value={50}>50</option>
                          </Form.Select>
                        </div>
                      </div>

                      {totalPages > 1 && (
                        <Pagination size="sm" className="mb-0">
                          <Pagination.First
                            onClick={() => setCurrentPage(1)}
                            disabled={currentPage === 1}
                          />
                          <Pagination.Prev
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                          />

                          {/* Page numbers */}
                          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            let pageNum;
                            if (totalPages <= 5) {
                              pageNum = i + 1;
                            } else if (currentPage <= 3) {
                              pageNum = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                              pageNum = totalPages - 4 + i;
                            } else {
                              pageNum = currentPage - 2 + i;
                            }

                            return (
                              <Pagination.Item
                                key={pageNum}
                                active={pageNum === currentPage}
                                onClick={() => setCurrentPage(pageNum)}
                              >
                                {pageNum}
                              </Pagination.Item>
                            );
                          })}

                          <Pagination.Next
                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                          />
                          <Pagination.Last
                            onClick={() => setCurrentPage(totalPages)}
                            disabled={currentPage === totalPages}
                          />
                        </Pagination>
                      )}
                  </div>
                )}

                {/* Empty States */}
                {filteredGroupedTemplates.length === 0 && groupedTemplates.length > 0 && !isLoading && (
                  <div className="text-center py-5">
                    <div className="mb-4">
                      <span style={{ fontSize: '4rem', opacity: 0.3 }}>🔍</span>
                    </div>
                    <h4 className="text-muted">No Templates Match Your Search</h4>
                    <p className="text-muted">Try adjusting your search terms or filters</p>
                    <Button
                      variant="outline-primary"
                      onClick={() => {
                        setSearchTerm('');
                        setStageFilter('all');
                        setStatusFilter('all');
                      }}
                    >
                      Clear All Filters
                    </Button>
                  </div>
                )}

                {groupedTemplates.length === 0 && !isLoading && !error && (
                  <div className="text-center py-5">
                    <div className="mb-4">
                      <span style={{ fontSize: '4rem', opacity: 0.3 }}>📋</span>
                    </div>
                    <h4 className="text-muted">No Templates Found</h4>
                    <p className="text-muted">Create your first template to get started</p>
                    <Button
                      variant="primary"
                      size="lg"
                      onClick={handleCreateTemplate}
                      className="d-flex align-items-center gap-2 mx-auto"
                    >
                      <span style={{ fontSize: '1.2rem' }}>+</span>
                      Create Your First Template
                    </Button>
                  </div>
                )}
              </div>
            </div>
            </Col>
          </Row>
        </div>
      </main>



      {/* Modern Footer */}
      <footer className="gradient-header text-white py-1 mt-auto">
        <div className="text-center" style={{ padding: '0 1rem' }}>
          <p className="mb-0 opacity-90">&copy; {new Date().getFullYear()} Admin Panel. All rights reserved.</p>
        </div>
      </footer>

      {/* Edit Version Selection Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Select Template Version to Edit</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className="text-muted mb-3">
            Multiple versions found for this product. Select which version you want to edit:
          </p>
          <div className="list-group">
            {selectedProductVersions.map((template, index) => (
              <button
                key={template.id}
                className="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                onClick={() => {
                  setShowEditModal(false);
                  navigateToEdit(template);
                }}
              >
                <div>
                  <div className="fw-medium">Version {template.version}</div>
                  <small className="text-muted">
                    Created: {new Date(template.createdAt).toLocaleDateString()} •
                    Stage: <Badge bg={getStageBadgeVariant(template.stage)} className="ms-1">
                      {template.stage.toUpperCase()}
                    </Badge>
                  </small>
                </div>
                <div className="d-flex align-items-center gap-2">
                  <Badge bg={template.isActive ? 'success' : 'danger'}>
                    {template.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                  {index === 0 && <Badge bg="primary">Latest</Badge>}
                </div>
              </button>
            ))}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};
