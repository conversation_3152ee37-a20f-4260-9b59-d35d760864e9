/**
 * SearchInput Atom
 * Reusable search input component with icon and debounced search
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Form, InputGroup } from 'react-bootstrap';

export interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  debounceMs?: number;
  disabled?: boolean;
  size?: 'sm' | 'lg';
  className?: string;
  icon?: string;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  value,
  onChange,
  placeholder = 'Search...',
  debounceMs = 300,
  disabled = false,
  size,
  className = '',
  icon = '🔍'
}) => {
  const [localValue, setLocalValue] = useState(value);

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Debounced onChange handler
  const debouncedOnChange = useCallback(
    debounceMs > 0
      ? (() => {
          let timeoutId: NodeJS.Timeout;
          return (newValue: string) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => onChange(newValue), debounceMs);
          };
        })()
      : onChange,
    [onChange, debounceMs]
  );

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    debouncedOnChange(newValue);
  };

  // Handle clear
  const handleClear = () => {
    setLocalValue('');
    onChange('');
  };

  return (
    <InputGroup className={`shadow-sm ${className}`} size={size}>
      <InputGroup.Text 
        className="bg-light border-end-0" 
        style={{ borderColor: 'var(--card-border)' }}
      >
        {icon}
      </InputGroup.Text>
      <Form.Control
        type="text"
        placeholder={placeholder}
        value={localValue}
        onChange={handleChange}
        disabled={disabled}
        className="border-start-0"
        style={{ borderColor: 'var(--card-border)' }}
      />
      {localValue && (
        <InputGroup.Text 
          className="bg-light border-start-0 cursor-pointer" 
          style={{ borderColor: 'var(--card-border)' }}
          onClick={handleClear}
          title="Clear search"
        >
          ✕
        </InputGroup.Text>
      )}
    </InputGroup>
  );
};
