using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetAllContexts;

/// <summary>
/// Handler for GetAllContextsQuery
/// </summary>
public class GetAllContextsQueryHandler : IRequestHandler<GetAllContextsQuery, Result<List<ContextDto>>>
{
    private readonly IRepository<Domain.Entities.Context> _contextRepository;
    private readonly ILogger<GetAllContextsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetAllContextsQueryHandler(
        IRepository<Domain.Entities.Context> contextRepository,
        ILogger<GetAllContextsQueryHandler> logger)
    {
        _contextRepository = contextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<List<ContextDto>>> Handle(GetAllContextsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting all contexts with filters: IncludeInactive={IncludeInactive}, Category={Category}, SearchTerm={SearchTerm}",
                request.IncludeInactive, request.Category, request.SearchTerm);

            // Create specification
            var spec = new ContextLookupSpec(
                searchTerm: request.SearchTerm,
                category: request.Category,
                includeInactive: request.IncludeInactive);

            // Execute query using specification
            var contexts = await _contextRepository.ListAsync(spec, cancellationToken);

            // Map to DTOs
            var contextDtos = contexts.Adapt<List<ContextDto>>();

            _logger.LogInformation("Successfully retrieved {Count} contexts", contextDtos.Count);

            return Result<List<ContextDto>>.Success(contextDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all contexts");
            return Result<List<ContextDto>>.Failure($"Error retrieving contexts: {ex.Message}");
        }
    }
}
