using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetContextWithLookups;

/// <summary>
/// Query to get context with its associated lookups by context ID
/// </summary>
public class GetContextWithLookupsQuery : IRequest<Result<ContextWithLookupsDto>>
{
    /// <summary>
    /// Context ID
    /// </summary>
    public Guid ContextId { get; set; }

    /// <summary>
    /// Whether to include inactive lookups
    /// </summary>
    public bool IncludeInactiveLookups { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetContextWithLookupsQuery(Guid contextId, bool includeInactiveLookups = false)
    {
        ContextId = contextId;
        IncludeInactiveLookups = includeInactiveLookups;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetContextWithLookupsQuery()
    {
    }
}
