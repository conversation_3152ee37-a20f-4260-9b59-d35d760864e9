import 'package:flutter/foundation.dart';
import '../models/app_state.dart';
import '../models/nav_item.dart';
import '../models/card_data_model.dart';
import '../models/response_model.dart';
import '../services/api_service.dart';
import '../services/navigation_service.dart';

/// Main application provider for state management
class AppProvider extends ChangeNotifier {
  final AppState _state = AppState();
  final ApiService _apiService = ApiService();

  // Expose state getters
  AppState get state => _state;

  List<NavItem> get navigationItems => _state.navigationItems;
  NavItem? get selectedNavItem => _state.selectedNavItem;
  bool get isNavigationLoading => _state.isNavigationLoading;
  String? get navigationError => _state.navigationError;

  List<Map<String, dynamic>> get instanceData => _state.instanceData;
  bool get isDataLoading => _state.isDataLoading;
  String? get dataError => _state.dataError;

  // Card data for mobile view
  List<CardDataModel> get cardData {
    if (_state.instanceData.isEmpty) return [];

    // Create a mock response model to use the conversion method
    final mockResponse = ResponseModel(
      objectName: _state.selectedNavItem?.objectType ?? 'Unknown',
      viewData: _state.instanceData,
    );

    return _apiService.convertToCardData(mockResponse);
  }

  String get searchQuery => _state.searchQuery;
  bool get isDrawerOpen => _state.isDrawerOpen;
  List<NavItem> get filteredNavigationItems => _state.filteredNavigationItems;

  bool get hasData => _state.hasData;
  bool get isLoading => _state.isLoading;
  bool get hasError => _state.hasError;
  String? get currentError => _state.currentError;

  AppProvider() {
    // Listen to state changes and notify listeners
    _state.addListener(() {
      notifyListeners();
    });

    // Initialize the app
    _initializeApp();
  }

  /// Initialize the application
  Future<void> _initializeApp() async {
    await loadNavigationData();
  }

  /// Load navigation data
  Future<void> loadNavigationData() async {
    try {
      _state.setNavigationLoading(true);
      final items = await NavigationService.loadNavigationData();
      _state.loadNavigationData(items);
    } catch (e) {
      _state.setNavigationError('Failed to load navigation: $e');
    }
  }

  /// Select a navigation item
  Future<void> selectNavigationItem(NavItem? item) async {
    _state.selectNavigationItem(item);

    if (item?.objectType != null) {
      await loadInstanceData(item!.objectType!);
    } else {
      _state.clearInstanceData();
    }
  }

  /// Toggle navigation item expansion state
  void toggleNavItemExpansion(NavItem item) {
    _state.toggleNavItemExpansion(item);
  }

  /// Load instance data for a specific object type
  Future<void> loadInstanceData(String objectType) async {
    try {
      print('Loading instance data for objectType: $objectType');
      _state.setDataLoading(true);

      final response = await _apiService.fetchInstanceData(
        objectType: objectType,
        pageNumber: 1,
        pageSize: 50, // Increased page size for better demo
      );

      print('Received response: $response');

      if (response != null) {
        print('Response was not null, converting to table data');
        final tableData = _apiService.convertToTableData(response);
        print('Converted table data: $tableData');
        _state.loadInstanceData(tableData);
      } else {
        print('Response was null, loading empty data');
        _state.loadInstanceData([]);
      }
    } catch (e) {
      print('Error loading instance data: $e');
      _state.setDataError('Failed to load data: $e');
    }
  }


  /// Update search query
  void updateSearchQuery(String query) {
    _state.updateSearchQuery(query);
  }

  /// Clear search query
  void clearSearchQuery() {
    _state.clearSearchQuery();
  }

  /// Toggle drawer state
  void toggleDrawer() {
    _state.toggleDrawer();
  }

  /// Set drawer state
  void setDrawerOpen(bool open) {
    _state.setDrawerOpen(open);
  }

  /// Retry loading navigation data
  Future<void> retryNavigationLoad() async {
    _state.retryNavigationLoad();
    await loadNavigationData();
  }

  /// Retry loading instance data
  Future<void> retryDataLoad() async {
    _state.retryDataLoad();
    if (_state.selectedNavItem?.objectType != null) {
      await loadInstanceData(_state.selectedNavItem!.objectType!);
    }
  }

  /// Toggle navigation item expansion state
  void toggleNavItemExpanded(NavItem item) {
    _state.toggleNavItemExpansion(item);
  }

  /// Clear all data and reset state
  void reset() {
    _state.reset();
  }


  @override
  void dispose() {
    _state.dispose();
    super.dispose();
  }
}



