// components/ThisEmail.tsx
import React, { useState, useRef } from 'react';
import { Mail, Check, X } from 'lucide-react';

interface ThisEmailProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  allowedDomains?: string[];
  blockedDomains?: string[];
  requireTLD?: boolean;
  allowSubdomains?: boolean;
  allowInternational?: boolean;
  allowPlusAddressing?: boolean;
  allowDotAddressing?: boolean;
  maxLength?: number;
  minLength?: number;
  showIcon?: boolean;
  showValidationIcon?: boolean;
  validateOnBlur?: boolean;
  customValidation?: (value: string) => string | null;
}

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
}

const ThisEmail: React.FC<ThisEmailProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder = 'Enter email address...',
  required = false,
  allowedDomains,
  blockedDomains,
  requireTLD = true,
  allowSubdomains = true,
  allowInternational = true,
  allowPlusAddressing = true,
  allowDotAddressing = true,
  maxLength = 254,
  minLength = 5,
  showIcon = true,
  showValidationIcon = true,
  validateOnBlur = true,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isFocused, setIsFocused] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Email validation regex patterns
  const strictEmailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  const internationalEmailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/u;

  // Helper functions
  const extractDomain = (email: string): string => {
    const parts = email.split('@');
    return parts.length === 2 ? parts[1].toLowerCase() : '';
  };

  const extractLocalPart = (email: string): string => {
    const parts = email.split('@');
    return parts.length === 2 ? parts[0] : '';
  };

  const hasTLD = (domain: string): boolean => {
    const parts = domain.split('.');
    return parts.length >= 2 && parts[parts.length - 1].length >= 2;
  };

  const hasSubdomain = (domain: string): boolean => {
    const parts = domain.split('.');
    return parts.length > 2;
  };

  const hasPlusAddressing = (localPart: string): boolean => {
    return localPart.includes('+');
  };

  const hasDotAddressing = (localPart: string): boolean => {
    return localPart.includes('.');
  };

  const isInternationalDomain = (domain: string): boolean => {
    return /[^\x00-\x7F]/.test(domain);
  };

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // 2. Length validation
    if (minLength > 0) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          return val.length >= minLength;
        },
        message: `${label} must be at least ${minLength} characters`
      });
    }

    if (maxLength > 0) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          return val.length <= maxLength;
        },
        message: `${label} must be at most ${maxLength} characters`
      });
    }

    // 3. Basic email format validation
    rules.push({
      test: (val) => {
        if (val.trim() === '') return !required;
        const regex = allowInternational ? internationalEmailRegex : strictEmailRegex;
        return regex.test(val);
      },
      message: `${label} must be a valid email address`
    });

    // 4. TLD validation
    if (requireTLD) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const domain = extractDomain(val);
          return domain === '' || hasTLD(domain);
        },
        message: `${label} must have a valid top-level domain`
      });
    }

    // 5. Subdomain validation
    if (!allowSubdomains) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const domain = extractDomain(val);
          return domain === '' || !hasSubdomain(domain);
        },
        message: `${label} cannot contain subdomains`
      });
    }

    // 6. International domain validation
    if (!allowInternational) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const domain = extractDomain(val);
          return domain === '' || !isInternationalDomain(domain);
        },
        message: `${label} must use ASCII characters only`
      });
    }

    // 7. Plus addressing validation
    if (!allowPlusAddressing) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const localPart = extractLocalPart(val);
          return localPart === '' || !hasPlusAddressing(localPart);
        },
        message: `${label} cannot contain plus (+) addressing`
      });
    }

    // 8. Dot addressing validation
    if (!allowDotAddressing) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const localPart = extractLocalPart(val);
          return localPart === '' || !hasDotAddressing(localPart);
        },
        message: `${label} cannot contain dots (.) in the local part`
      });
    }

    // 9. Allowed domains validation
    if (allowedDomains && allowedDomains.length > 0) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const domain = extractDomain(val);
          return domain === '' || allowedDomains.some(allowed =>
            domain === allowed.toLowerCase() ||
            (allowSubdomains && domain.endsWith('.' + allowed.toLowerCase()))
          );
        },
        message: `${label} must be from an allowed domain: ${allowedDomains.join(', ')}`
      });
    }

    // 10. Blocked domains validation
    if (blockedDomains && blockedDomains.length > 0) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const domain = extractDomain(val);
          return domain === '' || !blockedDomains.some(blocked =>
            domain === blocked.toLowerCase() ||
            (allowSubdomains && domain.endsWith('.' + blocked.toLowerCase()))
          );
        },
        message: `${label} cannot be from a blocked domain: ${blockedDomains.join(', ')}`
      });
    }

    // 11. Custom validation
    if (customValidation) {
      rules.push({
        test: (val) => {
          const customError = customValidation(val);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || readOnly) return;

    const newValue = e.target.value;
    onChange(newValue);

    // Real-time validation (only if not validating on blur)
    if (!validateOnBlur) {
      const newErrors = validateValue(newValue);
      setErrors(newErrors);
      setIsValidated(newValue.trim().length > 0);
      onValidation?.(newErrors);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);

    // Validate on blur if enabled
    if (validateOnBlur) {
      const newErrors = validateValue(value);
      setErrors(newErrors);
      setIsValidated(value.trim().length > 0);
      onValidation?.(newErrors);
    }
  };

  const getValidationIcon = () => {
    if (!showValidationIcon || !isValidated || value.trim().length === 0) {
      return null;
    }

    const hasErrors = errors.length > 0;
    return hasErrors ? (
      <X size={16} className="validation-icon error" />
    ) : (
      <Check size={16} className="validation-icon success" />
    );
  };

  const hasErrors = errors.length > 0;
  const isValid = isValidated && !hasErrors && value.trim().length > 0;

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Input */}
      <div className="text-input-wrapper">
        <div className={`email-input-container ${hasErrors ? 'has-error' : ''} ${isValid ? 'is-valid' : ''} ${disabled ? 'disabled' : ''} ${isFocused ? 'focused' : ''}`}>
          <input
            ref={inputRef}
            id={id}
            type="email"
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={disabled}
            readOnly={readOnly}
            placeholder={placeholder}
            className="email-input"
            aria-describedby={hasErrors ? `${id}-error` : undefined}
            aria-invalid={hasErrors}
            maxLength={maxLength}
            minLength={minLength}
            autoComplete="email"
            spellCheck={false}
          />

          {showIcon && (
            <div className="email-icon">
              <Mail size={16} />
            </div>
          )}

          {getValidationIcon()}
        </div>

        {/* Helper Text */}
        {!hasErrors && (
          <div className="email-helper">
            {allowedDomains && allowedDomains.length > 0 && (
              <span className="helper-text">
                Allowed domains: {allowedDomains.join(', ')}
              </span>
            )}
            {blockedDomains && blockedDomains.length > 0 && (
              <span className="helper-text">
                Blocked domains: {blockedDomains.join(', ')}
              </span>
            )}
            {!allowPlusAddressing && (
              <span className="helper-text">
                Plus (+) addressing not allowed
              </span>
            )}
            {!allowSubdomains && (
              <span className="helper-text">
                Subdomains not allowed
              </span>
            )}
            {!allowInternational && (
              <span className="helper-text">
                ASCII characters only
              </span>
            )}
          </div>
        )}

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisEmail;
