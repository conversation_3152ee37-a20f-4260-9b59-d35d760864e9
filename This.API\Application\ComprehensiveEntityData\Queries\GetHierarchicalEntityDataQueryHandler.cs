using Abstraction.Database.Repositories;
using Application.ComprehensiveEntityData.DTOs;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.ComprehensiveEntityData.Queries;

/// <summary>
/// UNIFIED: Query handler for hierarchical entity data with proper parent-child nesting and consolidated metadata
/// </summary>
public class GetHierarchicalEntityDataQueryHandler : IRequestHandler<GetHierarchicalEntityDataQuery, Result<UnifiedHierarchicalEntityDataResponseDto>>
{
    private readonly IHierarchicalEntityDataRepository _repository;
    private readonly ILogger<GetHierarchicalEntityDataQueryHandler> _logger;

    public GetHierarchicalEntityDataQueryHandler(
        IHierarchicalEntityDataRepository repository,
        ILogger<GetHierarchicalEntityDataQueryHandler> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the hierarchical entity data query with unified response
    /// </summary>
    public async Task<Result<UnifiedHierarchicalEntityDataResponseDto>> Handle(
        GetHierarchicalEntityDataQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            Console.WriteLine($"DEBUG: GetHierarchicalEntityDataQueryHandler.Handle called with ProductId: {request.ProductId}");
            _logger.LogInformation("Processing hierarchical entity data query - ProductId: {ProductId}, FeatureId: {FeatureId}",
                request.ProductId, request.FeatureId);

            var startTime = DateTime.UtcNow;

            var responseObj = await _repository.GetHierarchicalEntityDataAsync(
                productId: request.ProductId,
                featureId: request.FeatureId,
                searchTerm: request.SearchTerm,
                isActive: request.IsActive,
                onlyVisibleMetadata: request.OnlyVisibleMetadata,
                onlyActiveMetadata: request.OnlyActiveMetadata,
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                cancellationToken: cancellationToken);

            // UNIFIED APPROACH: Return UnifiedHierarchicalEntityDataResponseDto directly
            if (responseObj is UnifiedHierarchicalEntityDataResponseDto unifiedResponse)
            {
                _logger.LogInformation("Returning UNIFIED response format with consolidated metadata");
                return Result<UnifiedHierarchicalEntityDataResponseDto>.Success(unifiedResponse);
            }
            else if (responseObj is HierarchicalEntityDataResponseDto legacyResponse)
            {
                // This shouldn't happen with the current implementation, but handle it just in case
                throw new InvalidOperationException("Legacy response format is no longer supported. Expected UnifiedHierarchicalEntityDataResponseDto.");
            }
            else
            {
                throw new InvalidOperationException($"Unexpected response type: {responseObj?.GetType().Name}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing hierarchical entity data query");
            return Result<UnifiedHierarchicalEntityDataResponseDto>.Failure("Error processing hierarchical entity data");
        }
    }
}
