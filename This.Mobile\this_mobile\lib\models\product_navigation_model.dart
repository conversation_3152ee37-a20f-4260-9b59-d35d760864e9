import 'package:json_annotation/json_annotation.dart';

part 'product_navigation_model.g.dart';

@JsonSerializable()
class ProductResponse {
  final bool succeeded;
  final ProductData data;
  final int statusCode;

  ProductResponse({
    required this.succeeded,
    required this.data,
    required this.statusCode,
  });

  factory ProductResponse.fromJson(Map<String, dynamic> json) =>
      _$ProductResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ProductResponseToJson(this);
}

@JsonSerializable()
class ProductData {
  final List<Product> products;
  final int totalObjectsCount;
  final int totalMetadataCount;
  final int maxHierarchyDepth;

  ProductData({
    required this.products,
    required this.totalObjectsCount,
    required this.totalMetadataCount,
    required this.maxHierarchyDepth,
  });

  factory ProductData.fromJson(Map<String, dynamic> json) =>
      _$ProductDataFromJson(json);
  Map<String, dynamic> toJson() => _$ProductDataToJson(this);
}

@JsonSerializable()
class Product {
  final String id;
  final String name;
  final String version;
  final bool isActive;
  final String applicationUrl;
  final String createdAt;
  final List<RootObject> rootObjects;

  Product({
    required this.id,
    required this.name,
    required this.version,
    required this.isActive,
    required this.applicationUrl,
    required this.createdAt,
    required this.rootObjects,
  });

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
  Map<String, dynamic> toJson() => _$ProductToJson(this);
}

@JsonSerializable()
class RootObject {
  final String id;
  final String name;
  final bool isActive;
  final int hierarchyLevel;
  final List<MetadataWrapper> metadata;
  final List<ChildObject>? childObjects;

  RootObject({
    required this.id,
    required this.name,
    required this.isActive,
    required this.hierarchyLevel,
    required this.metadata,
    this.childObjects,
  });

  factory RootObject.fromJson(Map<String, dynamic> json) =>
      _$RootObjectFromJson(json);
  Map<String, dynamic> toJson() => _$RootObjectToJson(this);
}

@JsonSerializable()
class ChildObject {
  final String id;
  final String name;
  final String parentObjectId;
  final int hierarchyLevel;
  final List<MetadataWrapper> metadata;

  ChildObject({
    required this.id,
    required this.name,
    required this.parentObjectId,
    required this.hierarchyLevel,
    required this.metadata,
  });

  factory ChildObject.fromJson(Map<String, dynamic> json) =>
      _$ChildObjectFromJson(json);
  Map<String, dynamic> toJson() => _$ChildObjectToJson(this);
}

@JsonSerializable()
class MetadataWrapper {
  final Metadata metadata;

  MetadataWrapper({required this.metadata});

  factory MetadataWrapper.fromJson(Map<String, dynamic> json) =>
      _$MetadataWrapperFromJson(json);
  Map<String, dynamic> toJson() => _$MetadataWrapperToJson(this);
}

@JsonSerializable()
class Metadata {
  final String id;
  final String name;
  final String inputType;
  final bool isRequired;

  Metadata({
    required this.id,
    required this.name,
    required this.inputType,
    required this.isRequired,
  });

  factory Metadata.fromJson(Map<String, dynamic> json) =>
      _$MetadataFromJson(json);
  Map<String, dynamic> toJson() => _$MetadataToJson(this);
}
