import React from 'react';
import { Badge } from 'react-bootstrap';
import { Settings, Star } from 'lucide-react';

interface ActionSummary {
  name: string;
  type: 'API' | 'Navigation';
  buttonStyle: string;
  icon: string;
}

interface DisplaySummary {
  name: string;
  displayName: string;
  isDefault: boolean;
  actions: ActionSummary[];
}

interface DisplayActionSummaryProps {
  selectedNode: any;
}

export const DisplayActionSummary: React.FC<DisplayActionSummaryProps> = ({ selectedNode }) => {
  const displays: DisplaySummary[] = selectedNode?.displays || [];

  if (displays.length === 0) {
    return (
      <div className="d-flex align-items-center p-2 mb-3 bg-light rounded border border-warning">
        <Settings size={16} className="text-warning me-2" />
        <small className="text-muted mb-0">No display configuration set up</small>
      </div>
    );
  }

  const defaultDisplay = displays.find(d => d.isDefault);
  const totalActions = displays.reduce((sum, display) => sum + display.actions.length, 0);

  return (
    <div className="d-flex align-items-center justify-content-between p-2 mb-3 bg-light rounded border border-success">
      <div className="d-flex align-items-center">
        <Settings size={16} className="text-success me-2" />
        <div className="d-flex align-items-center gap-2">
          <Badge bg="success" className="d-flex align-items-center">
            {displays.length} display{displays.length !== 1 ? 's' : ''}
          </Badge>
          <Badge bg="secondary" className="d-flex align-items-center">
            {totalActions} action{totalActions !== 1 ? 's' : ''}
          </Badge>
          {defaultDisplay && (
            <Badge bg="warning" text="dark" className="d-flex align-items-center gap-1">
              <Star size={12} fill="currentColor" />
              Default: {defaultDisplay.displayName}
            </Badge>
          )}
        </div>
      </div>
      <div className="d-flex align-items-center gap-1">
        {displays.map((display, index) => (
          <Badge
            key={index}
            bg={display.isDefault ? "primary" : "outline-secondary"}
            text={display.isDefault ? "white" : "dark"}
            className="d-flex align-items-center"
            style={{ fontSize: '10px' }}
            title={`${display.displayName}: ${display.actions.length} actions`}
          >
            {display.displayName} ({display.actions.length})
          </Badge>
        ))}
      </div>
    </div>
  );
};
