using Abstraction.Database.Repositories;
using Application.ActionManagement.DTOs;
using Application.ActionManagement.Specifications;
using Application.DisplayActionManagement.DTOs;
using Application.DisplayActionManagement.Specifications;
using Application.DisplayManagement.DTOs;
using Application.DisplayManagement.Specifications;
using Domain.Entities;
using Finbuckle.MultiTenant;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.DisplayManagement.Commands;

/// <summary>
/// Comprehensive upsert command handler for Display, Action, and DisplayAction entities
/// </summary>
public class UpsertDisplayWithActionCommandHandler : IRequestHandler<UpsertDisplayWithActionCommand, Result<UpsertDisplayWithActionResponseDto>>
{
    private readonly IRepository<Display> _displayRepository;
    private readonly IRepository<Domain.Entities.Action> _actionRepository;
    private readonly IRepository<DisplayAction> _displayActionRepository;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly ITenantInfo _tenantInfo;
    private readonly ILogger<UpsertDisplayWithActionCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertDisplayWithActionCommandHandler(
        IRepository<Display> displayRepository,
        IRepository<Domain.Entities.Action> actionRepository,
        IRepository<DisplayAction> displayActionRepository,
        IRepository<Domain.Entities.Object> objectRepository,
        ITenantInfo tenantInfo,
        ILogger<UpsertDisplayWithActionCommandHandler> logger)
    {
        _displayRepository = displayRepository;
        _actionRepository = actionRepository;
        _displayActionRepository = displayActionRepository;
        _objectRepository = objectRepository;
        _tenantInfo = tenantInfo;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<UpsertDisplayWithActionResponseDto>> Handle(UpsertDisplayWithActionCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting comprehensive upsert operation for Display: {DisplayName}, Action: {ActionName}, Object: {ObjectId}", 
                request.DisplayName, request.ActionName, request.ObjectId);

            // Step 1: Validate that the Object exists
            var existingObject = await _objectRepository.GetByIdAsync(request.ObjectId, cancellationToken);
            if (existingObject == null)
            {
                return Result<UpsertDisplayWithActionResponseDto>.Failure($"Object with ID '{request.ObjectId}' not found.");
            }

            // Step 2: Upsert Display
            var (display, displayWasCreated) = await UpsertDisplayAsync(request, cancellationToken);

            // Step 3: Upsert Action
            var (action, actionWasCreated) = await UpsertActionAsync(request, cancellationToken);

            // Step 4: Upsert DisplayAction relationship
            var (displayAction, displayActionWasCreated) = await UpsertDisplayActionAsync(
                request, display.Id, action.Id, cancellationToken);

            stopwatch.Stop();

            // Step 5: Build response
            var response = new UpsertDisplayWithActionResponseDto
            {
                Display = display.Adapt<DisplayDto>(),
                Action = action.Adapt<ActionDto>(),
                DisplayAction = displayAction.Adapt<DisplayActionDto>(),
                DisplayWasCreated = displayWasCreated,
                ActionWasCreated = actionWasCreated,
                DisplayActionWasCreated = displayActionWasCreated,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds,
                OperationSummary = BuildOperationSummary(displayWasCreated, actionWasCreated, displayActionWasCreated)
            };

            _logger.LogInformation("Comprehensive upsert completed successfully in {ElapsedMs}ms. Display: {DisplayOperation}, Action: {ActionOperation}, DisplayAction: {DisplayActionOperation}",
                stopwatch.ElapsedMilliseconds,
                displayWasCreated ? "Created" : "Updated",
                actionWasCreated ? "Created" : "Updated",
                displayActionWasCreated ? "Created" : "Updated");

            return Result<UpsertDisplayWithActionResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error occurred during comprehensive upsert operation for Display: {DisplayName}, Action: {ActionName}, Object: {ObjectId}",
                request.DisplayName, request.ActionName, request.ObjectId);
            
            return Result<UpsertDisplayWithActionResponseDto>.Failure($"An error occurred during the upsert operation: {ex.Message}");
        }
    }

    /// <summary>
    /// Upsert Display entity
    /// </summary>
    private async Task<(Display display, bool wasCreated)> UpsertDisplayAsync(UpsertDisplayWithActionCommand request, CancellationToken cancellationToken)
    {
        var existingDisplay = await _displayRepository.GetBySpecAsync(
            new DisplayByNameSpec(request.DisplayName), cancellationToken);

        if (existingDisplay != null)
        {
            // Update existing display
            existingDisplay.Description = request.DisplayDescription;
            existingDisplay.DisplayName = request.DisplayDisplayName;
            existingDisplay.IsDefault = request.DisplayIsDefault;
            existingDisplay.RouteTemplate = request.DisplayRouteTemplate;
            existingDisplay.Icon = request.DisplayIcon;
            existingDisplay.SortOrder = request.DisplaySortOrder;
            await _displayRepository.UpdateAsync(existingDisplay, cancellationToken);
            return (existingDisplay, false);
        }
        else
        {
            // Create new display
            var newDisplay = new Display
            {
                Name = request.DisplayName,
                Description = request.DisplayDescription,
                DisplayName = request.DisplayDisplayName,
                IsDefault = request.DisplayIsDefault,
                RouteTemplate = request.DisplayRouteTemplate,
                Icon = request.DisplayIcon,
                SortOrder = request.DisplaySortOrder,
            };

            var createdDisplay = await _displayRepository.AddAsync(newDisplay, cancellationToken);
            return (createdDisplay, true);
        }
    }

    /// <summary>
    /// Upsert Action entity
    /// </summary>
    private async Task<(Domain.Entities.Action action, bool wasCreated)> UpsertActionAsync(UpsertDisplayWithActionCommand request, CancellationToken cancellationToken)
    {
        var existingAction = await _actionRepository.GetBySpecAsync(
            new ActionByNameSpec(request.ActionName), cancellationToken);

        if (existingAction != null)
        {
            // Update existing action
            existingAction.Description = request.ActionDescription;
            existingAction.EndpointTemplate = request.ActionEndpointTemplate;
            existingAction.NavigationTarget = request.ActionNavigationTarget;
            existingAction.Icon = request.ActionIcon;
            existingAction.ButtonStyle = request.ActionButtonStyle;
            existingAction.ConfirmationMessage = request.ActionConfirmationMessage;
            existingAction.SuccessMessage = request.ActionSuccessMessage;
            existingAction.ErrorMessage = request.ActionErrorMessage;
            await _actionRepository.UpdateAsync(existingAction, cancellationToken);
            return (existingAction, false);
        }
        else
        {
            // Create new action
            var newAction = new Domain.Entities.Action
            {
                Name = request.ActionName,
                Description = request.ActionDescription,
                EndpointTemplate = request.ActionEndpointTemplate,
                NavigationTarget = request.ActionNavigationTarget,
                Icon = request.ActionIcon,
                ButtonStyle = request.ActionButtonStyle,
                ConfirmationMessage = request.ActionConfirmationMessage,
                SuccessMessage = request.ActionSuccessMessage,
                ErrorMessage = request.ActionErrorMessage,
            };

            var createdAction = await _actionRepository.AddAsync(newAction, cancellationToken);
            return (createdAction, true);
        }
    }

    /// <summary>
    /// Upsert DisplayAction relationship
    /// </summary>
    private async Task<(DisplayAction displayAction, bool wasCreated)> UpsertDisplayActionAsync(
        UpsertDisplayWithActionCommand request, Guid displayId, Guid actionId, CancellationToken cancellationToken)
    {
        var existingDisplayAction = await _displayActionRepository.GetBySpecAsync(
            new DisplayActionByObjectDisplayActionSpec(request.ObjectId, displayId, actionId), cancellationToken);

        if (existingDisplayAction != null)
        {
            // Update existing DisplayAction
            existingDisplayAction.AccessLevel = request.AccessLevel;
            existingDisplayAction.IsDefault = request.IsDefault;
            existingDisplayAction.SortOrder = request.SortOrder;
            existingDisplayAction.IsVisibleInToolbar = request.IsVisibleInToolbar;
            existingDisplayAction.IsVisibleInContextMenu = request.IsVisibleInContextMenu;
            existingDisplayAction.IsVisibleInRowActions = request.IsVisibleInRowActions;

            await _displayActionRepository.UpdateAsync(existingDisplayAction, cancellationToken);
            return (existingDisplayAction, false);
        }
        else
        {
            // Create new DisplayAction
            var newDisplayAction = new DisplayAction
            {
                ObjectId = request.ObjectId,
                DisplayId = displayId,
                ActionId = actionId,
                AccessLevel = request.AccessLevel,
                IsDefault = request.IsDefault,
                SortOrder = request.SortOrder,
                IsVisibleInToolbar = request.IsVisibleInToolbar,
                IsVisibleInContextMenu = request.IsVisibleInContextMenu,
                IsVisibleInRowActions = request.IsVisibleInRowActions,
            };

            var createdDisplayAction = await _displayActionRepository.AddAsync(newDisplayAction, cancellationToken);
            return (createdDisplayAction, true);
        }
    }

    /// <summary>
    /// Build operation summary message
    /// </summary>
    private static string BuildOperationSummary(bool displayWasCreated, bool actionWasCreated, bool displayActionWasCreated)
    {
        var operations = new List<string>();

        if (displayWasCreated) operations.Add("Display created");
        else operations.Add("Display updated");

        if (actionWasCreated) operations.Add("Action created");
        else operations.Add("Action updated");

        if (displayActionWasCreated) operations.Add("DisplayAction relationship created");
        else operations.Add("DisplayAction relationship updated");

        return string.Join(", ", operations);
    }
}
