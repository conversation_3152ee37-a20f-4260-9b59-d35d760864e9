using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.DataTypes.Commands;

/// <summary>
/// Delete DataType command handler
/// </summary>
public class DeleteDataTypeCommandHandler : IRequestHandler<DeleteDataTypeCommand, Result<bool>>
{
    private readonly IRepository<DataType> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteDataTypeCommandHandler(IRepository<DataType> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteDataTypeCommand request, CancellationToken cancellationToken)
    {
        // Get existing DataType
        var dataType = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (dataType == null)
        {
            return Result<bool>.Failure($"DataType with ID '{request.Id}' not found.");
        }

        // Check if DataType is being used by any Metadata
        if (dataType.Metadata.Any())
        {
            return Result<bool>.Failure("Cannot delete DataType as it is being used by one or more Metadata definitions.");
        }

        // Delete DataType (soft delete)
        await _repository.DeleteAsync(dataType, cancellationToken);

        return Result<bool>.Success(true);
    }
}
