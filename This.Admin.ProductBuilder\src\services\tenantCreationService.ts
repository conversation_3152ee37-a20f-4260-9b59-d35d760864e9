// Tenant Creation Service - Uses Primary API (Port 7222) for upsert, Secondary API (Port 7243) for read operations
import { API_CONFIG, getSecondaryApiUrl } from '../config/apiConfig';

export interface TenantResponse {
  id: string;
  name: string;
  connectionString: string;
  readReplicaConnectionString: string | null;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string | null;
}

export interface TenantsApiResponse {
  succeeded: boolean;
  message: string | null;
  errors: string[] | null;
  data: TenantResponse[];
}

export interface CreateTenantDto {
  id: string;
  name: string;
  connectionString: string;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string;
}

export interface ApiResult<T> {
  succeeded: boolean;
  data: T;
  message: string | null;
  statusCode?: number;
}

export class TenantCreationService {
  private async makeRequest<T>(
    url: string,
    options: RequestInit
  ): Promise<T> {
    try {
      const headers: Record<string, string> = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        ...options.headers as Record<string, string>,
      };

      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText || response.statusText}`);
      }

      // Handle empty responses
      const text = await response.text();
      if (!text) {
        return {} as T;
      }

      try {
        return JSON.parse(text) as T;
      } catch {
        // If response is not JSON, return as string
        return text as unknown as T;
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Network error: ${String(error)}`);
    }
  }

  // Get tenants from secondary API (port 7243)
  async getTenants(): Promise<TenantsApiResponse> {
    const url = getSecondaryApiUrl(API_CONFIG.SECONDARY.ENDPOINTS.TENANTS);

    try {
      console.log('Fetching tenants from secondary API (port 7243):', url);

      const response = await this.makeRequest<TenantsApiResponse>(
        url,
        { method: 'GET' }
      );

      console.log('Tenants creation API response:', response);
      return response;
    } catch (error) {
      console.error('Error fetching tenants from creation API:', error);
      throw error;
    }
  }

  // Helper method to get available tenants for creation
  async getAvailableTenants(): Promise<TenantResponse[]> {
    try {
      const response = await this.getTenants();

      if (response.succeeded && response.data) {
        return response.data.filter(tenant => tenant.isActive);
      }

      return []; // Return empty array if API fails
    } catch (error) {
      console.error('Error fetching available tenants from creation API:', error);
      return []; // Return empty array if API fails
    }
  }

  // Create a new tenant using upsert endpoint
  async createTenant(tenantData: CreateTenantDto): Promise<string> {
    const url = getSecondaryApiUrl(API_CONFIG.SECONDARY.ENDPOINTS.TENANTS_UPSERT);

    try {
      console.log('Creating tenant via upsert API:', url, tenantData);

      const response = await this.makeRequest<ApiResult<string>>(
        url,
        {
          method: 'POST',
          body: JSON.stringify(tenantData),
        }
      );

      if (!response.succeeded) {
        throw new Error(response.message || 'Failed to create tenant');
      }

      console.log('Tenant created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating tenant:', error);
      throw error;
    }
  }

  // Get tenant by ID
  async getTenantById(id: string): Promise<TenantResponse> {
    const url = getSecondaryApiUrl(API_CONFIG.SECONDARY.ENDPOINTS.TENANT_BY_ID(id));

    try {
      console.log('Fetching tenant by ID from secondary API (port 7243):', url);

      const response = await this.makeRequest<ApiResult<TenantResponse>>(
        url,
        { method: 'GET' }
      );

      if (!response.succeeded) {
        throw new Error(response.message || 'Failed to fetch tenant');
      }

      return response.data;
    } catch (error) {
      console.error(`Error fetching tenant ${id}:`, error);
      throw error;
    }
  }

  // Validate tenant exists
  async isValidTenant(tenantName: string): Promise<boolean> {
    try {
      const availableTenants = await this.getAvailableTenants();
      return availableTenants.some(t => t.name === tenantName);
    } catch (error) {
      console.error('Error validating tenant:', error);
      return false;
    }
  }

  // Get tenant names only
  async getAvailableTenantNames(): Promise<string[]> {
    try {
      const tenants = await this.getAvailableTenants();
      return tenants.map(tenant => tenant.name);
    } catch (error) {
      console.error('Error fetching tenant names from creation API:', error);
      return []; // Return empty array if API fails
    }
  }
}

// Export singleton instance
export const tenantCreationService = new TenantCreationService();

// Utility functions for tenant operations
export class TenantUtils {
  // Generate a unique tenant ID
  static generateTenantId(name: string): string {
    const timestamp = Date.now();
    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '');
    return `tenant-${cleanName}-${timestamp}`;
  }

  // Validate tenant name
  static isValidTenantName(name: string): boolean {
    // Tenant name should be alphanumeric with hyphens/underscores
    const nameRegex = /^[a-zA-Z0-9_-]+$/;
    return nameRegex.test(name) && name.length >= 3 && name.length <= 50;
  }

  // Validate email format
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Generate default admin email from tenant name
  static generateAdminEmail(tenantName: string): string {
    const cleanName = tenantName.toLowerCase().replace(/[^a-z0-9]/g, '');
    return `admin@${cleanName}.com`;
  }

  // Generate valid until date (1 year from now)
  static generateValidUntilDate(): string {
    const oneYearFromNow = new Date();
    oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
    return oneYearFromNow.toISOString();
  }

  // Format tenant for display
  static formatTenantForDisplay(tenant: TenantResponse): string {
    return `${tenant.name} (${tenant.adminEmail})`;
  }

  // Check if tenant is expired
  static isTenantExpired(tenant: TenantResponse): boolean {
    const now = new Date();
    const validUpto = new Date(tenant.validUpto);
    return validUpto < now;
  }

  // Get days until tenant expiration
  static getDaysUntilExpiration(tenant: TenantResponse): number {
    const now = new Date();
    const validUpto = new Date(tenant.validUpto);
    const diffTime = validUpto.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
