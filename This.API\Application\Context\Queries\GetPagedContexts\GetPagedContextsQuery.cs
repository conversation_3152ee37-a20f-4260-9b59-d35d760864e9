using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetPagedContexts;

/// <summary>
/// Query to get contexts with pagination
/// </summary>
public class GetPagedContextsQuery : IRequest<PagedResponse<ContextDto, object>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Whether to include inactive contexts
    /// </summary>
    public bool IncludeInactive { get; set; } = false;

    /// <summary>
    /// Category filter
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Search term for name or description
    /// </summary>
    public string? SearchTerm { get; set; }
}
