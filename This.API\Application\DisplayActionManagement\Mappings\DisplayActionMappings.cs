using Application.DisplayActionManagement.DTOs;
using Domain.Entities;
using Mapster;

namespace Application.DisplayActionManagement.Mappings;

/// <summary>
/// Mapping configurations for DisplayAction entity and related DTOs
/// </summary>
public class DisplayActionMappings : IRegister
{
    /// <summary>
    /// Register mappings
    /// </summary>
    public void Register(TypeAdapterConfig config)
    {
        // Map DisplayAction entity to DisplayActionDto
        config.NewConfig<DisplayAction, DisplayActionDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.ObjectId, src => src.ObjectId)
            .Map(dest => dest.DisplayId, src => src.DisplayId)
            .Map(dest => dest.ActionId, src => src.ActionId)
            .Map(dest => dest.AccessLevel, src => src.AccessLevel)
            .Map(dest => dest.IsDefault, src => src.IsDefault)
            .Map(dest => dest.SortOrder, src => src.SortOrder)
            .Map(dest => dest.IsVisibleInToolbar, src => src.IsVisibleInToolbar)
            .Map(dest => dest.IsVisibleInContextMenu, src => src.IsVisibleInContextMenu)
            .Map(dest => dest.IsVisibleInRowActions, src => src.IsVisibleInRowActions)
            .Map(dest => dest.IsActive, src => src.IsActive)
            .Map(dest => dest.IsDeleted, src => src.IsDeleted)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.ModifiedAt, src => src.ModifiedAt)
            .Map(dest => dest.CreatedBy, src => src.CreatedBy)
            .Map(dest => dest.ModifiedBy, src => src.ModifiedBy)
            .Map(dest => dest.Display, src => src.Display)
            .Map(dest => dest.Action, src => src.Action);

        // Map CreateDisplayActionDto to DisplayAction entity
        config.NewConfig<CreateDisplayActionDto, DisplayAction>()
            .Map(dest => dest.ObjectId, src => src.ObjectId)
            .Map(dest => dest.DisplayId, src => src.DisplayId)
            .Map(dest => dest.ActionId, src => src.ActionId)
            .Map(dest => dest.AccessLevel, src => src.AccessLevel)
            .Map(dest => dest.IsDefault, src => src.IsDefault)
            .Map(dest => dest.SortOrder, src => src.SortOrder)
            .Map(dest => dest.IsVisibleInToolbar, src => src.IsVisibleInToolbar)
            .Map(dest => dest.IsVisibleInContextMenu, src => src.IsVisibleInContextMenu)
            .Map(dest => dest.IsVisibleInRowActions, src => src.IsVisibleInRowActions)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.IsActive)
            .Ignore(dest => dest.IsDeleted)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.ModifiedAt)
            .Ignore(dest => dest.CreatedBy)
            .Ignore(dest => dest.ModifiedBy)
            .Ignore(dest => dest.DomainEvents)
            .Ignore(dest => dest.Object)
            .Ignore(dest => dest.Display)
            .Ignore(dest => dest.Action);

        // Map UpdateDisplayActionDto to DisplayAction entity
        config.NewConfig<UpdateDisplayActionDto, DisplayAction>()
            .Map(dest => dest.ObjectId, src => src.ObjectId)
            .Map(dest => dest.DisplayId, src => src.DisplayId)
            .Map(dest => dest.ActionId, src => src.ActionId)
            .Map(dest => dest.AccessLevel, src => src.AccessLevel)
            .Map(dest => dest.IsDefault, src => src.IsDefault)
            .Map(dest => dest.SortOrder, src => src.SortOrder)
            .Map(dest => dest.IsVisibleInToolbar, src => src.IsVisibleInToolbar)
            .Map(dest => dest.IsVisibleInContextMenu, src => src.IsVisibleInContextMenu)
            .Map(dest => dest.IsVisibleInRowActions, src => src.IsVisibleInRowActions)
            .Map(dest => dest.IsActive, src => src.IsActive)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.IsDeleted)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.ModifiedAt)
            .Ignore(dest => dest.CreatedBy)
            .Ignore(dest => dest.ModifiedBy)
            .Ignore(dest => dest.DomainEvents)
            .Ignore(dest => dest.Object)
            .Ignore(dest => dest.Display)
            .Ignore(dest => dest.Action);
    }
}
