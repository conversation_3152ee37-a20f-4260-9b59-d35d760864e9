import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';

/// Dynamic data table widget for displaying instance data
class DataTableWidget extends StatefulWidget {
  final List<Map<String, dynamic>> data;
  final String? title;
  final bool isLoading;
  final VoidCallback? onRefresh;

  const DataTableWidget({
    super.key,
    required this.data,
    this.title,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  State<DataTableWidget> createState() => _DataTableWidgetState();
}

class _DataTableWidgetState extends State<DataTableWidget> {
  int _sortColumnIndex = 0;
  bool _sortAscending = true;
  String _searchQuery = '';
  int _currentPage = 0;
  int _rowsPerPage = 10;

  List<Map<String, dynamic>> get _filteredData {
    print('Filtering data with query: $_searchQuery');
    print('Original data: ${widget.data}');
    
    if (widget.data.isEmpty) {
      print('Data is empty');
      return [];
    }

    if (_searchQuery.isEmpty) {
      print('No search query, returning all data');
      return widget.data;
    }

    final filtered = widget.data.where((row) {
      return row.values.any((value) {
        if (value == null) return false;
        return value.toString().toLowerCase().contains(_searchQuery.toLowerCase());
      });
    }).toList();

    print('Filtered data: $filtered');
    return filtered;
  }

  List<Map<String, dynamic>> get _sortedData {
    final data = List<Map<String, dynamic>>.from(_filteredData);

    if (data.isEmpty) return data;

    final columns = _getColumns();
    if (_sortColumnIndex >= columns.length) return data;

    final sortKey = columns[_sortColumnIndex];

    data.sort((a, b) {
      final aValue = a[sortKey] ?? '';
      final bValue = b[sortKey] ?? '';

      // Try to parse as numbers for proper numeric sorting
      final aNum = double.tryParse(aValue.toString().replaceAll(RegExp(r'[^\d.-]'), ''));
      final bNum = double.tryParse(bValue.toString().replaceAll(RegExp(r'[^\d.-]'), ''));

      int comparison;
      if (aNum != null && bNum != null) {
        comparison = aNum.compareTo(bNum);
      } else {
        comparison = aValue.toString().compareTo(bValue.toString());
      }

      return _sortAscending ? comparison : -comparison;
    });

    return data;
  }

  List<Map<String, dynamic>> get _paginatedData {
    final data = _sortedData;
    final startIndex = _currentPage * _rowsPerPage;
    final endIndex = (startIndex + _rowsPerPage).clamp(0, data.length);

    if (startIndex >= data.length) return [];
    return data.sublist(startIndex, endIndex);
  }

  List<String> _getColumns() {
    if (widget.data.isEmpty) return [];

    final allKeys = <String>{};
    for (final row in widget.data) {
      allKeys.addAll(row.keys);
    }

    return allKeys.toList()..sort();
  }

  void _onSort(int columnIndex, bool ascending) {
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _currentPage = 0; // Reset to first page when searching
    });
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  void _onRowsPerPageChanged(int? rowsPerPage) {
    if (rowsPerPage != null) {
      setState(() {
        _rowsPerPage = rowsPerPage;
        _currentPage = 0; // Reset to first page
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final columns = _getColumns();
    final paginatedData = _paginatedData;
    final totalRows = _filteredData.length;

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and actions
          _buildHeader(),

          // Search bar
          _buildSearchBar(),

          // Data table
          if (columns.isNotEmpty && paginatedData.isNotEmpty)
            _buildDataTable(columns, paginatedData)
          else
            _buildEmptyState(),

          // Pagination
          if (totalRows > _rowsPerPage)
            _buildPagination(totalRows),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingM),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.border),
        ),
      ),
      child: Row(
        children: [
          if (widget.title != null) ...[
            Expanded(
              child: Text(
                widget.title!,
                style: AppTextStyles.titleLarge,
              ),
            ),
          ],
          if (widget.onRefresh != null)
            IconButton(
              onPressed: widget.isLoading ? null : widget.onRefresh,
              icon: widget.isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(LucideIcons.refreshCw),
              tooltip: 'Refresh',
            ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingM),
      child: TextField(
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Search data...',
          prefixIcon: const Icon(LucideIcons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () => _onSearchChanged(''),
                  icon: const Icon(LucideIcons.x),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusM),
          ),
        ),
      ),
    );
  }

  Widget _buildDataTable(List<String> columns, List<Map<String, dynamic>> data) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: MediaQuery.of(context).size.width - 32,
        ),
        child: DataTable(
          sortColumnIndex: _sortColumnIndex,
          sortAscending: _sortAscending,
          columns: columns.asMap().entries.map((entry) {
            final column = entry.value;

            return DataColumn(
              label: Expanded(
                child: Text(
                  column,
                  style: AppTextStyles.labelLarge,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              onSort: (columnIndex, ascending) => _onSort(columnIndex, ascending),
            );
          }).toList(),
          rows: data.map((row) {
            return DataRow(
              cells: columns.map((column) {
                final value = row[column];
                return DataCell(
                  Container(
                    constraints: const BoxConstraints(maxWidth: 200),
                    child: Text(
                      _formatCellValue(value),
                      style: AppTextStyles.bodyMedium,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                );
              }).toList(),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingXL),
      child: Center(
        child: Column(
          children: [
            Icon(
              _searchQuery.isNotEmpty ? LucideIcons.search : LucideIcons.inbox,
              size: AppConstants.iconSizeXL,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: AppConstants.spacingM),
            Text(
              _searchQuery.isNotEmpty
                  ? 'No results found for "$_searchQuery"'
                  : 'No data available',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: AppConstants.spacingM),
              TextButton.icon(
                onPressed: () => _onSearchChanged(''),
                icon: const Icon(LucideIcons.x),
                label: const Text('Clear Search'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPagination(int totalRows) {
    final totalPages = (totalRows / _rowsPerPage).ceil();
    final startRow = _currentPage * _rowsPerPage + 1;
    final endRow = ((_currentPage + 1) * _rowsPerPage).clamp(0, totalRows);

    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingM),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: AppColors.border),
        ),
      ),
      child: Row(
        children: [
          // Rows per page selector
          Text(
            'Rows per page:',
            style: AppTextStyles.bodyMedium,
          ),
          const SizedBox(width: AppConstants.spacingS),
          DropdownButton<int>(
            value: _rowsPerPage,
            items: [5, 10, 25, 50].map((value) {
              return DropdownMenuItem<int>(
                value: value,
                child: Text(value.toString()),
              );
            }).toList(),
            onChanged: _onRowsPerPageChanged,
            underline: const SizedBox(),
          ),

          const Spacer(),

          // Page info
          Text(
            '$startRow-$endRow of $totalRows',
            style: AppTextStyles.bodyMedium,
          ),

          const SizedBox(width: AppConstants.spacingM),

          // Navigation buttons
          IconButton(
            onPressed: _currentPage > 0
                ? () => _onPageChanged(_currentPage - 1)
                : null,
            icon: const Icon(LucideIcons.chevronLeft),
            tooltip: 'Previous page',
          ),
          IconButton(
            onPressed: _currentPage < totalPages - 1
                ? () => _onPageChanged(_currentPage + 1)
                : null,
            icon: const Icon(LucideIcons.chevronRight),
            tooltip: 'Next page',
          ),
        ],
      ),
    );
  }

  String _formatCellValue(dynamic value) {
    if (value == null) return '';

    // Handle boolean values
    if (value is bool) {
      return AppHelpers.formatBoolean(value);
    }

    // Handle numeric values that might be currency
    final stringValue = value.toString();
    if (stringValue.startsWith('\$') || stringValue.startsWith('€') || stringValue.startsWith('£')) {
      return stringValue;
    }

    // Handle other numeric values
    final numValue = double.tryParse(stringValue);
    if (numValue != null && stringValue.contains('.')) {
      return numValue.toStringAsFixed(2);
    }

    return AppHelpers.truncateText(stringValue, 50);
  }
}

/// Compact data table for smaller screens
class CompactDataTableWidget extends StatelessWidget {
  final List<Map<String, dynamic>> data;
  final String? title;

  const CompactDataTableWidget({
    super.key,
    required this.data,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return Card(
        child: Container(
          padding: const EdgeInsets.all(AppConstants.spacingL),
          child: const Center(
            child: Text('No data available'),
          ),
        ),
      );
    }

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null)
            Container(
              padding: const EdgeInsets.all(AppConstants.spacingM),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.border),
                ),
              ),
              child: Text(
                title!,
                style: AppTextStyles.titleLarge,
              ),
            ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: data.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final row = data[index];
              return ExpansionTile(
                title: Text(
                  row.values.first.toString(),
                  style: AppTextStyles.bodyLarge,
                ),
                subtitle: Text(
                  '${row.length} fields',
                  style: AppTextStyles.bodySmall,
                ),
                children: row.entries.map((entry) {
                  return ListTile(
                    dense: true,
                    title: Text(
                      entry.key,
                      style: AppTextStyles.labelMedium,
                    ),
                    trailing: Text(
                      entry.value?.toString() ?? '',
                      style: AppTextStyles.bodyMedium,
                    ),
                  );
                }).toList(),
              );
            },
          ),
        ],
      ),
    );
  }
}
