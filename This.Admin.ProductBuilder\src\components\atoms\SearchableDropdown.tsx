/**
 * SearchableDropdown Atom
 * A reusable dropdown component with search functionality
 */

import React, { useState, useRef, useEffect } from 'react';
import { Form } from 'react-bootstrap';
import { createPortal } from 'react-dom';

export interface DropdownOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SearchableDropdownProps {
  value: string;
  options: DropdownOption[];
  onChange: (value: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  disabled?: boolean;
  size?: 'sm' | 'lg';
  className?: string;
  style?: React.CSSProperties;
  'aria-label'?: string;
  allowClear?: boolean;
  maxHeight?: string;
  noOptionsText?: string;
  showSearch?: boolean;
}

export const SearchableDropdown: React.FC<SearchableDropdownProps> = ({
  value,
  options,
  onChange,
  placeholder = 'Select an option...',
  searchPlaceholder = 'Search options...',
  disabled = false,
  size,
  className = '',
  style,
  'aria-label': ariaLabel,
  allowClear = false,
  maxHeight = '200px',
  noOptionsText = 'No options found',
  showSearch = true,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredOptions(options);
    } else {
      const filtered = options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.value.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredOptions(filtered);
    }
  }, [searchTerm, options]);

  // Close dropdown when clicking outside and handle position updates
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Check if click is on the trigger element
      if (dropdownRef.current && dropdownRef.current.contains(target)) {
        return;
      }

      // Check if click is on the dropdown menu (which is now in a portal)
      const dropdownMenu = document.querySelector('.searchable-dropdown-portal');
      if (dropdownMenu && dropdownMenu.contains(target)) {
        return;
      }

      // If click is outside both trigger and dropdown, close it
      setIsOpen(false);
      setSearchTerm('');
    };

    const handleScroll = () => {
      if (isOpen) {
        updateDropdownPosition();
      }
    };

    const handleResize = () => {
      if (isOpen) {
        updateDropdownPosition();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleResize);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && showSearch && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  }, [isOpen, showSearch]);

  const updateDropdownPosition = () => {
    if (dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  };

  const handleToggle = () => {
    if (!disabled) {
      if (!isOpen) {
        updateDropdownPosition();
        setSearchTerm('');
      }
      setIsOpen(!isOpen);
    }
  };

  const handleOptionSelect = (optionValue: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    onChange(optionValue);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange('');
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
      setSearchTerm('');
    } else if (e.key === 'Enter' && filteredOptions.length > 0) {
      e.preventDefault();
      handleOptionSelect(filteredOptions[0].value);
    }
  };

  // Handle escape key globally when dropdown is open
  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => document.removeEventListener('keydown', handleEscapeKey);
    }
  }, [isOpen]);



  // Get display label for selected value
  const selectedOption = options.find(option => option.value === value);
  const displayLabel = selectedOption ? selectedOption.label : placeholder;

  return (
    <div
      ref={dropdownRef}
      className={`position-relative ${className}`}
      style={style}
    >
        <div
          className={`form-select d-flex align-items-center justify-content-between ${
            size ? `form-select-${size}` : ''
          } ${disabled ? 'disabled' : ''}`}
          style={{
            cursor: disabled ? 'not-allowed' : 'pointer',
            borderColor: 'var(--card-border)',
          }}
          aria-label={ariaLabel}
          onClick={handleToggle}
        >
          <span className={`${!selectedOption ? 'text-muted' : ''}`}>
            {displayLabel}
          </span>
          <div className="d-flex align-items-center">
            {allowClear && value && !disabled && (
              <span
                className="text-muted me-2"
                style={{ cursor: 'pointer', fontSize: '14px' }}
                onClick={handleClear}
                title="Clear selection"
              >
                ✕
              </span>
            )}
            <span className="text-muted">
              {isOpen ? '▲' : '▼'}
            </span>
          </div>
        </div>

        {isOpen && createPortal(
          <div
            className="dropdown-menu show shadow-lg border searchable-dropdown-portal"
            data-dropdown-id={`dropdown-${Math.random().toString(36).substr(2, 9)}`}
            style={{
              position: 'fixed',
              top: `${dropdownPosition.top + 2}px`,
              left: `${dropdownPosition.left}px`,
              width: `${Math.max(dropdownPosition.width, 200)}px`,
              zIndex: 99999,
              maxHeight,
              overflowY: 'auto',
              backgroundColor: 'white',
              border: '1px solid #dee2e6',
              borderRadius: '0.375rem',
              boxShadow: '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
              display: 'block',
              opacity: 1,
              visibility: 'visible',
              pointerEvents: 'auto',
            }}
          >
          {showSearch && (
            <div className="px-3 py-2 border-bottom bg-light">
              <Form.Control
                ref={searchInputRef}
                type="text"
                placeholder={searchPlaceholder}
                value={searchTerm}
                onChange={handleSearchChange}
                onKeyDown={handleSearchKeyDown}
                size="sm"
                className="border-1"
                style={{
                  outline: 'none',
                  boxShadow: 'none',
                  fontSize: '0.875rem'
                }}
                autoComplete="off"
              />
            </div>
          )}

            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-muted text-center">
                <small>{noOptionsText}</small>
              </div>
            ) : (
              filteredOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  className={`dropdown-item ${
                    option.value === value ? 'active' : ''
                  }`}
                  onClick={(e) => handleOptionSelect(option.value, e)}
                  disabled={option.disabled}
                  style={{
                    border: 'none',
                    backgroundColor: option.value === value ? '#0d6efd' : 'transparent',
                    color: option.value === value ? 'white' : '#212529',
                    padding: '0.5rem 1rem',
                    fontSize: '0.875rem',
                    cursor: option.disabled ? 'not-allowed' : 'pointer',
                    textAlign: 'left',
                    width: '100%',
                    display: 'block',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    pointerEvents: 'auto',
                    zIndex: 1,
                  }}
                  onMouseEnter={(e) => {
                    if (!option.disabled && option.value !== value) {
                      e.currentTarget.style.backgroundColor = '#f8f9fa';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!option.disabled && option.value !== value) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  {option.label}
                </button>
              ))
            )}
          </div>,
          document.body
        )}
    </div>
  );
};

export default SearchableDropdown;
