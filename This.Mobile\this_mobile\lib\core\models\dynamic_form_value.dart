class DynamicFormValue {
  final String refId;
  final String metadataKey;
  final dynamic value;
  final List<DynamicFormValue>? children;

  DynamicFormValue({
    required this.refId,
    required this.metadataKey,
    required this.value,
    this.children,
  });

  Map<String, dynamic> toJson() => {
        'refId': refId,
        'metadataKey': metadataKey,
        'value': value,
        if (children != null) 'children': children!.map((e) => e.toJson()).toList(),
      };
}
