using Application.DataTypes.DTOs;
using Application.DataTypes.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.DataTypes.Commands;

/// <summary>
/// Update DataType command handler
/// </summary>
public class UpdateDataTypeCommandHandler : IRequestHandler<UpdateDataTypeCommand, Result<DataTypeDto>>
{
    private readonly IRepository<DataType> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateDataTypeCommandHandler(IRepository<DataType> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<DataTypeDto>> Handle(UpdateDataTypeCommand request, CancellationToken cancellationToken)
    {
        // Get existing DataType
        var dataType = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (dataType == null)
        {
            return Result<DataTypeDto>.Failure($"DataType with ID '{request.Id}' not found.");
        }

        // Check if another DataType with same name already exists
        var existingDataType = await _repository.GetBySpecAsync(new DataTypeByNameSpec(request.Name), cancellationToken);
        if (existingDataType != null && existingDataType.Id != request.Id)
        {
            return Result<DataTypeDto>.Failure($"DataType with name '{request.Name}' already exists.");
        }

        // Update DataType properties
        dataType.Name = request.Name;
        dataType.DisplayName = request.DisplayName ?? request.Name;
        dataType.Category = request.Category ?? "general";
        dataType.UiComponent = request.UiComponent ?? "text";
        dataType.ValidationPattern = request.ValidationPattern;
        dataType.MinLength = request.MinLength;
        dataType.MaxLength = request.MaxLength;
        dataType.MinValue = request.MinValue;
        dataType.MaxValue = request.MaxValue;
        dataType.DecimalPlaces = request.DecimalPlaces;
        dataType.StepValue = request.StepValue;
        dataType.IsRequired = request.IsRequired;
        dataType.InputType = request.InputType;
        dataType.InputMask = request.InputMask;
        dataType.Placeholder = request.Placeholder;
        dataType.HtmlAttributes = request.HtmlAttributes;
        dataType.DefaultOptions = request.DefaultOptions;
        dataType.AllowsMultiple = request.AllowsMultiple;
        dataType.AllowsCustomOptions = request.AllowsCustomOptions;
        dataType.MaxSelections = request.MaxSelections;
        dataType.AllowedFileTypes = request.AllowedFileTypes;
        dataType.MaxFileSizeBytes = request.MaxFileSizeBytes;
        dataType.RequiredErrorMessage = request.RequiredErrorMessage;
        dataType.PatternErrorMessage = request.PatternErrorMessage;
        dataType.MinLengthErrorMessage = request.MinLengthErrorMessage;
        dataType.MaxLengthErrorMessage = request.MaxLengthErrorMessage;
        dataType.MinValueErrorMessage = request.MinValueErrorMessage;
        dataType.MaxValueErrorMessage = request.MaxValueErrorMessage;
        dataType.FileTypeErrorMessage = request.FileTypeErrorMessage;
        dataType.FileSizeErrorMessage = request.FileSizeErrorMessage;
        dataType.IsActive = request.IsActive;

        await _repository.UpdateAsync(dataType, cancellationToken);

        var dto = new DataTypeDto
        {
            Id = dataType.Id,
            Name = dataType.Name,
            DisplayName = dataType.DisplayName,
            Category = dataType.Category,
            UiComponent = dataType.UiComponent,
            ValidationPattern = dataType.ValidationPattern,
            MinLength = dataType.MinLength,
            MaxLength = dataType.MaxLength,
            MinValue = dataType.MinValue,
            MaxValue = dataType.MaxValue,
            DecimalPlaces = dataType.DecimalPlaces,
            StepValue = dataType.StepValue,
            IsRequired = dataType.IsRequired,
            InputType = dataType.InputType,
            InputMask = dataType.InputMask,
            Placeholder = dataType.Placeholder,
            HtmlAttributes = dataType.HtmlAttributes,
            DefaultOptions = dataType.DefaultOptions,
            AllowsMultiple = dataType.AllowsMultiple,
            AllowsCustomOptions = dataType.AllowsCustomOptions,
            MaxSelections = dataType.MaxSelections,
            AllowedFileTypes = dataType.AllowedFileTypes,
            MaxFileSizeBytes = dataType.MaxFileSizeBytes,
            RequiredErrorMessage = dataType.RequiredErrorMessage,
            PatternErrorMessage = dataType.PatternErrorMessage,
            MinLengthErrorMessage = dataType.MinLengthErrorMessage,
            MaxLengthErrorMessage = dataType.MaxLengthErrorMessage,
            MinValueErrorMessage = dataType.MinValueErrorMessage,
            MaxValueErrorMessage = dataType.MaxValueErrorMessage,
            FileTypeErrorMessage = dataType.FileTypeErrorMessage,
            FileSizeErrorMessage = dataType.FileSizeErrorMessage,
            IsActive = dataType.IsActive,
            CreatedAt = dataType.CreatedAt,
            CreatedBy = dataType.CreatedBy ?? Guid.Empty,
            ModifiedAt = dataType.ModifiedAt,
            ModifiedBy = dataType.ModifiedBy
        };

        return Result<DataTypeDto>.Success(dto);
    }
}
