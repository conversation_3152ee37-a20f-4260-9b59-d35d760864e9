import 'package:flutter/material.dart';
import '../models/field_metadata.dart';
import '../services/metadata_service.dart';
import '../services/form_submission_service.dart';
import '../components/widgets/dynamic_form_field.dart';

class DynamicFormScreen extends StatefulWidget {
  final String objectId;
  final String baseUrl;
  final String tenant;

  const DynamicFormScreen({
    Key? key,
    required this.objectId,
    required this.baseUrl,
    required this.tenant,
  }) : super(key: key);

  @override
  State<DynamicFormScreen> createState() => _DynamicFormScreenState();
}

class _DynamicFormScreenState extends State<DynamicFormScreen> {
  late Future<List<FieldMetadata>> _metadataFuture;
  final _formKey = GlobalKey<FormState>();
  final Map<String, dynamic> _formValues = {};
  bool _isSubmitting = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _metadataFuture = MetadataService(
      baseUrl: widget.baseUrl,
      tenant: widget.tenant,
    ).fetchMetadata(widget.objectId);
  }

  void _onFieldChanged(String key, dynamic value) {
    setState(() {
      _formValues[key] = value;
    });
  }

  Future<void> _submit(List<FieldMetadata> metadata) async {
    if (!_formKey.currentState!.validate()) return;
    setState(() {
      _isSubmitting = true;
      _error = null;
    });
    // Build payload here
    final Map<String, dynamic> payload = {}; // TODO: Build payload from _formValues and metadata
    try {
      final response = await FormSubmissionService(
        baseUrl: widget.baseUrl,
        tenant: widget.tenant,
      ).upsertFormData(payload);
      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Form submitted successfully!')),
        );
      } else {
        setState(() {
          _error = 'Submission failed: \\${response.body}';
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Dynamic Form')),
      body: FutureBuilder<List<FieldMetadata>>(
        future: _metadataFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: \\${snapshot.error}'));
          } else if (snapshot.data!.isEmpty) {
            return Center(child: Text('No metadata found.'));
          }
          final metadata = snapshot.data!;
          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Render dynamic fields
                  ...metadata.map((fieldMetadata) => DynamicFormField(
                    metadata: fieldMetadata,
                    value: _formValues[fieldMetadata.id],
                    onChanged: (value) => _onFieldChanged(fieldMetadata.id, value),
                  )),
                  if (_error != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Text(_error!, style: TextStyle(color: Colors.red)),
                    ),
                  SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _isSubmitting ? null : () => _submit(metadata),
                    child: _isSubmitting ? CircularProgressIndicator(color: Colors.white) : Text('Submit'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
