using Application.DisplayManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.DisplayManagement.Queries;

/// <summary>
/// Get display by ID query
/// </summary>
public class GetDisplayByIdQuery : IRequest<Result<DisplayDto>>
{
    /// <summary>
    /// Display ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetDisplayByIdQuery(Guid id)
    {
        Id = id;
    }
}
