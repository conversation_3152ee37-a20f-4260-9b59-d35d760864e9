using System.Globalization;
using System.Text.Json;

namespace Application.DataTransformation.Services;

/// <summary>
/// Service for converting data types according to field mapping specifications
/// </summary>
public static class DataTypeConversionService
{
    /// <summary>
    /// Convert a source value to the target data type
    /// </summary>
    /// <param name="sourceValue">Source value to convert</param>
    /// <param name="sourceType">Expected source data type</param>
    /// <returns>Converted value as string, or null if conversion failed</returns>
    public static (string? convertedValue, string? errorMessage) ConvertValue(object? sourceValue, string sourceType)
    {
        try
        {
            if (sourceValue == null)
            {
                return (null, null);
            }

            var sourceValueString = sourceValue.ToString();
            
            // Handle empty strings
            if (string.IsNullOrWhiteSpace(sourceValueString))
            {
                return (null, null);
            }

            return sourceType.ToLowerInvariant() switch
            {
                "string" => ConvertToString(sourceValue),
                "int" or "integer" => ConvertToInteger(sourceValue),
                "decimal" or "double" or "float" => ConvertToDecimal(sourceValue),
                "datetime" or "date" => ConvertToDateTime(sourceValue),
                "bool" or "boolean" => ConvertToBoolean(sourceValue),
                "guid" => ConvertToGuid(sourceValue),
                "array" => ConvertToArray(sourceValue),
                "object" => ConvertToObject(sourceValue),
                _ => (sourceValueString, null) // Default: return as string
            };
        }
        catch (Exception ex)
        {
            return (null, $"Conversion error: {ex.Message}");
        }
    }

    /// <summary>
    /// Convert to string
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertToString(object sourceValue)
    {
        try
        {
            if (sourceValue is JsonElement jsonElement)
            {
                return jsonElement.ValueKind switch
                {
                    JsonValueKind.String => (jsonElement.GetString(), null),
                    JsonValueKind.Number => (jsonElement.GetDecimal().ToString(CultureInfo.InvariantCulture), null),
                    JsonValueKind.True => ("true", null),
                    JsonValueKind.False => ("false", null),
                    JsonValueKind.Null => (null, null),
                    _ => (jsonElement.GetRawText(), null)
                };
            }

            return (sourceValue.ToString(), null);
        }
        catch (Exception ex)
        {
            return (null, $"String conversion failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Convert to integer
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertToInteger(object sourceValue)
    {
        try
        {
            if (sourceValue is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Number)
                {
                    // Try to get as decimal first, then convert to integer
                    try
                    {
                        var decimalValue = jsonElement.GetDecimal();
                        // Check if it's a whole number
                        if (decimalValue == Math.Floor(decimalValue))
                        {
                            // Convert to long to handle larger integers
                            var longValue = Convert.ToInt64(decimalValue);
                            return (longValue.ToString(), null);
                        }
                        else
                        {
                            // It's a decimal, truncate to integer
                            var truncatedValue = Convert.ToInt64(Math.Floor(decimalValue));
                            return (truncatedValue.ToString(), null);
                        }
                    }
                    catch (OverflowException)
                    {
                        return (null, $"Number '{jsonElement.GetRawText()}' is too large for integer conversion");
                    }
                }
                else if (jsonElement.ValueKind == JsonValueKind.String)
                {
                    var stringValue = jsonElement.GetString();
                    return ConvertStringToInteger(stringValue);
                }
            }
            else if (sourceValue is string stringValue)
            {
                return ConvertStringToInteger(stringValue);
            }
            else if (sourceValue is int or long or short or byte or uint or ulong or ushort or sbyte)
            {
                return (sourceValue.ToString(), null);
            }
            else if (sourceValue is decimal decimalValue)
            {
                var longValue = Convert.ToInt64(Math.Floor(decimalValue));
                return (longValue.ToString(), null);
            }
            else if (sourceValue is double doubleValue)
            {
                var longValue = Convert.ToInt64(Math.Floor(doubleValue));
                return (longValue.ToString(), null);
            }
            else if (sourceValue is float floatValue)
            {
                var longValue = Convert.ToInt64(Math.Floor(floatValue));
                return (longValue.ToString(), null);
            }

            return (null, $"Cannot convert '{sourceValue}' to integer");
        }
        catch (Exception ex)
        {
            return (null, $"Integer conversion failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Convert string to integer
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertStringToInteger(string? stringValue)
    {
        if (string.IsNullOrWhiteSpace(stringValue))
        {
            return (null, null);
        }

        // Try parsing as long first (handles larger numbers)
        if (long.TryParse(stringValue.Trim(), NumberStyles.Integer, CultureInfo.InvariantCulture, out long longValue))
        {
            return (longValue.ToString(), null);
        }

        // Try parsing as decimal and then convert to integer (handles decimal strings)
        if (decimal.TryParse(stringValue.Trim(), NumberStyles.Number, CultureInfo.InvariantCulture, out decimal decimalValue))
        {
            try
            {
                var convertedLong = Convert.ToInt64(Math.Floor(decimalValue));
                return (convertedLong.ToString(), null);
            }
            catch (OverflowException)
            {
                return (null, $"Number '{stringValue}' is too large for integer conversion");
            }
        }

        return (null, $"Cannot convert '{stringValue}' to integer");
    }

    /// <summary>
    /// Convert to decimal
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertToDecimal(object sourceValue)
    {
        try
        {
            if (sourceValue is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Number)
                {
                    var decimalValue = jsonElement.GetDecimal();
                    return (decimalValue.ToString(CultureInfo.InvariantCulture), null);
                }
                else if (jsonElement.ValueKind == JsonValueKind.String)
                {
                    var stringValue = jsonElement.GetString();
                    if (decimal.TryParse(stringValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal parsedDecimal))
                    {
                        return (parsedDecimal.ToString(CultureInfo.InvariantCulture), null);
                    }
                }
            }
            else if (sourceValue is string stringValue)
            {
                if (decimal.TryParse(stringValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal parsedDecimal))
                {
                    return (parsedDecimal.ToString(CultureInfo.InvariantCulture), null);
                }
            }
            else if (sourceValue is decimal or double or float)
            {
                return (Convert.ToDecimal(sourceValue).ToString(CultureInfo.InvariantCulture), null);
            }

            return (null, $"Cannot convert '{sourceValue}' to decimal");
        }
        catch (Exception ex)
        {
            return (null, $"Decimal conversion failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Convert to datetime
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertToDateTime(object sourceValue)
    {
        try
        {
            if (sourceValue is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.String)
                {
                    var stringValue = jsonElement.GetString();
                    if (DateTime.TryParse(stringValue, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDateTime))
                    {
                        return (parsedDateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), null);
                    }
                }
            }
            else if (sourceValue is string stringValue)
            {
                if (DateTime.TryParse(stringValue, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDateTime))
                {
                    return (parsedDateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), null);
                }
            }
            else if (sourceValue is DateTime dateTimeValue)
            {
                return (dateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), null);
            }

            return (null, $"Cannot convert '{sourceValue}' to datetime");
        }
        catch (Exception ex)
        {
            return (null, $"DateTime conversion failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Convert to boolean
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertToBoolean(object sourceValue)
    {
        try
        {
            if (sourceValue is JsonElement jsonElement)
            {
                return jsonElement.ValueKind switch
                {
                    JsonValueKind.True => ("true", null),
                    JsonValueKind.False => ("false", null),
                    JsonValueKind.String => ConvertStringToBoolean(jsonElement.GetString()),
                    JsonValueKind.Number => (jsonElement.GetDecimal() != 0 ? "true" : "false", null),
                    _ => (null, $"Cannot convert JSON {jsonElement.ValueKind} to boolean")
                };
            }
            else if (sourceValue is string stringValue)
            {
                return ConvertStringToBoolean(stringValue);
            }
            else if (sourceValue is bool boolValue)
            {
                return (boolValue.ToString().ToLowerInvariant(), null);
            }

            return (null, $"Cannot convert '{sourceValue}' to boolean");
        }
        catch (Exception ex)
        {
            return (null, $"Boolean conversion failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Convert string to boolean
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertStringToBoolean(string? stringValue)
    {
        if (string.IsNullOrWhiteSpace(stringValue))
        {
            return (null, null);
        }

        var lowerValue = stringValue.ToLowerInvariant().Trim();
        return lowerValue switch
        {
            "true" or "1" or "yes" or "y" or "on" => ("true", null),
            "false" or "0" or "no" or "n" or "off" => ("false", null),
            _ => (null, $"Cannot convert '{stringValue}' to boolean")
        };
    }

    /// <summary>
    /// Convert to GUID
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertToGuid(object sourceValue)
    {
        try
        {
            if (sourceValue is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.String)
            {
                var stringValue = jsonElement.GetString();
                if (Guid.TryParse(stringValue, out Guid parsedGuid))
                {
                    return (parsedGuid.ToString(), null);
                }
            }
            else if (sourceValue is string stringValue)
            {
                if (Guid.TryParse(stringValue, out Guid parsedGuid))
                {
                    return (parsedGuid.ToString(), null);
                }
            }
            else if (sourceValue is Guid guidValue)
            {
                return (guidValue.ToString(), null);
            }

            return (null, $"Cannot convert '{sourceValue}' to GUID");
        }
        catch (Exception ex)
        {
            return (null, $"GUID conversion failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Convert to array (JSON string)
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertToArray(object sourceValue)
    {
        try
        {
            if (sourceValue is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Array)
                {
                    return (jsonElement.GetRawText(), null);
                }
            }
            else if (sourceValue is string stringValue)
            {
                // Try to parse as JSON array
                try
                {
                    var parsed = JsonDocument.Parse(stringValue);
                    if (parsed.RootElement.ValueKind == JsonValueKind.Array)
                    {
                        return (stringValue, null);
                    }
                }
                catch
                {
                    // Not valid JSON, treat as single-item array
                    return ($"[\"{stringValue}\"]", null);
                }
            }

            return (null, $"Cannot convert '{sourceValue}' to array");
        }
        catch (Exception ex)
        {
            return (null, $"Array conversion failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Convert to object (JSON string)
    /// </summary>
    private static (string? convertedValue, string? errorMessage) ConvertToObject(object sourceValue)
    {
        try
        {
            if (sourceValue is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Object)
                {
                    return (jsonElement.GetRawText(), null);
                }
            }
            else if (sourceValue is string stringValue)
            {
                // Try to parse as JSON object
                try
                {
                    var parsed = JsonDocument.Parse(stringValue);
                    if (parsed.RootElement.ValueKind == JsonValueKind.Object)
                    {
                        return (stringValue, null);
                    }
                }
                catch
                {
                    // Not valid JSON object
                    return (null, $"'{stringValue}' is not a valid JSON object");
                }
            }

            return (null, $"Cannot convert '{sourceValue}' to object");
        }
        catch (Exception ex)
        {
            return (null, $"Object conversion failed: {ex.Message}");
        }
    }
}
