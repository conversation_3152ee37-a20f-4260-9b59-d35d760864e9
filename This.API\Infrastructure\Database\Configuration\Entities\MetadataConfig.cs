using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Metadata entity
/// </summary>
public class MetadataConfig : IEntityTypeConfiguration<Metadata>
{
    public void Configure(EntityTypeBuilder<Metadata> builder)
    {
        builder.ToTable("Metadata", "Genp");

        // Properties
        builder.Property(e => e.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.DataTypeId)
            .IsRequired();

        builder.Property(e => e.DisplayLabel)
            .HasMaxLength(255);

        builder.Property(e => e.HelpText)
            .HasColumnType("TEXT");

        builder.Property(e => e.IsVisible)
            .HasDefaultValue(true);

        builder.Property(e => e.IsReadonly)
            .HasDefaultValue(false);

        // Display and Action Management Properties
        builder.Property(e => e.IsEditable)
            .HasDefaultValue(false);

        builder.Property(e => e.IsSearchable)
            .HasDefaultValue(false);

        builder.Property(e => e.IsSortable)
            .HasDefaultValue(false);

        builder.Property(e => e.SortOrder)
            .HasDefaultValue(0);

        builder.Property(e => e.DisplayFormat)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(e => e.DataTypeId)
            .HasDatabaseName("IX_Metadata_DataTypeId");

        builder.HasIndex(e => e.IsVisible)
            .HasDatabaseName("IX_Metadata_IsVisible")
            .HasFilter("\"IsVisible\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.IsEditable)
            .HasDatabaseName("IX_Metadata_IsEditable")
            .HasFilter("\"IsEditable\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.IsSearchable)
            .HasDatabaseName("IX_Metadata_IsSearchable")
            .HasFilter("\"IsSearchable\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.IsSortable)
            .HasDatabaseName("IX_Metadata_IsSortable")
            .HasFilter("\"IsSortable\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.SortOrder)
            .HasDatabaseName("IX_Metadata_SortOrder");

        builder.HasIndex(e => e.ContextId)
            .HasDatabaseName("IX_Metadata_ContextId");

        builder.HasIndex(e => e.TenantContextId)
            .HasDatabaseName("IX_Metadata_TenantContextId");

        builder.HasIndex(e => e.ObjectLookupId)
            .HasDatabaseName("IX_Metadata_ObjectLookupId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.DataType)
            .WithMany(e => e.Metadata)
            .HasForeignKey(e => e.DataTypeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(e => e.TenantInfoMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ProductMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.RoleMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.UserMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.ObjectMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.SubscriptionMetadata)
            .WithOne(e => e.Metadata)
            .HasForeignKey(e => e.MetadataId)
            .OnDelete(DeleteBehavior.Cascade);

        // Lookup override relationships
        builder.HasOne(e => e.Context)
            .WithMany(e => e.MetadataOverrides)
            .HasForeignKey(e => e.ContextId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.TenantContext)
            .WithMany(e => e.MetadataOverrides)
            .HasForeignKey(e => e.TenantContextId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.ObjectLookup)
            .WithMany(e => e.Metadata)
            .HasForeignKey(e => e.ObjectLookupId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
