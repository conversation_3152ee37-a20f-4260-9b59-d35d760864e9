namespace Application.ActionManagement.DTOs;

/// <summary>
/// Action response DTO
/// </summary>
public class ActionDto
{
    /// <summary>
    /// Action ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Action name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the action
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// API endpoint template for API actions
    /// </summary>
    public string? EndpointTemplate { get; set; }

    /// <summary>
    /// Navigation target for Navigation actions
    /// </summary>
    public string? NavigationTarget { get; set; }

    /// <summary>
    /// Icon for the action
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Button style - 'Primary', 'Secondary', 'Danger', etc.
    /// </summary>
    public string? ButtonStyle { get; set; }

    /// <summary>
    /// Confirmation dialog message
    /// </summary>
    public string? ConfirmationMessage { get; set; }

    /// <summary>
    /// Success message to display after action completion
    /// </summary>
    public string? SuccessMessage { get; set; }

    /// <summary>
    /// Error message to display on action failure
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether the action is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the action is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last modification timestamp
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified by user ID
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
