import React, { useState } from 'react';
import { Container, Row, <PERSON>, <PERSON>, But<PERSON>, Form, Alert, Tab, Tabs } from 'react-bootstrap';
import { TenantApiDemo } from '../components/TenantApiDemo';

interface SettingsData {
  general: {
    applicationName: string;
    applicationUrl: string;
    timezone: string;
    language: string;
  };
  api: {
    apiUrl: string;
    timeout: number;
    retries: number;
  };
  notifications: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    smsNotifications: boolean;
  };
}

const defaultSettings: SettingsData = {
  general: {
    applicationName: 'Admin Panel',
    applicationUrl: 'http://localhost:5173',
    timezone: 'UTC',
    language: 'en'
  },
  api: {
    apiUrl: 'http://localhost:3000/api',
    timeout: 30000,
    retries: 3
  },
  notifications: {
    emailNotifications: true,
    pushNotifications: false,
    smsNotifications: false
  }
};

export const Settings: React.FC = () => {
  const [settings, setSettings] = useState<SettingsData>(defaultSettings);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleInputChange = (section: keyof SettingsData, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsSaving(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Here you would typically save to your backend
    console.log('Saving settings:', settings);
    
    setHasChanges(false);
    setIsSaving(false);
    setShowSuccess(true);
    
    // Hide success message after 3 seconds
    setTimeout(() => setShowSuccess(false), 3000);
  };

  const handleReset = () => {
    setSettings(defaultSettings);
    setHasChanges(false);
  };

  return (
    <div className="d-flex flex-column min-vh-100">
      {/* Main Content */}
      <main className="flex-grow-1 py-4">
        <Container>
          {/* Action Buttons */}
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h1 className="h3 mb-0">Settings</h1>
            <div className="d-flex gap-2">
              {hasChanges && (
                <Button
                  variant="outline-secondary"
                  onClick={handleReset}
                  disabled={isSaving}
                >
                  Reset
                </Button>
              )}
              <Button
                variant="primary"
                onClick={handleSave}
                disabled={!hasChanges || isSaving}
                className="fw-semibold"
              >
                {isSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>

          {/* Success Alert */}
          {showSuccess && (
            <Alert variant="success" className="mb-4">
              <strong>Success!</strong> Settings have been saved successfully.
            </Alert>
          )}

          {/* Modern Settings Tabs */}
          <div className="modern-card">
            <div className="modern-card-body">
              <Tabs defaultActiveKey="general" className="mb-4">
                {/* General Settings */}
                <Tab eventKey="general" title="General">
                  <Row>
                    <Col md={8}>
                      <Form.Group className="mb-3">
                        <Form.Label>Application Name</Form.Label>
                        <Form.Control
                          type="text"
                          value={settings.general.applicationName}
                          onChange={(e) => handleInputChange('general', 'applicationName', e.target.value)}
                        />
                      </Form.Group>

                      <Form.Group className="mb-3">
                        <Form.Label>Application URL</Form.Label>
                        <Form.Control
                          type="url"
                          value={settings.general.applicationUrl}
                          onChange={(e) => handleInputChange('general', 'applicationUrl', e.target.value)}
                        />
                      </Form.Group>

                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>Timezone</Form.Label>
                            <Form.Select
                              value={settings.general.timezone}
                              onChange={(e) => handleInputChange('general', 'timezone', e.target.value)}
                            >
                              <option value="UTC">UTC</option>
                              <option value="America/New_York">Eastern Time</option>
                              <option value="America/Chicago">Central Time</option>
                              <option value="America/Denver">Mountain Time</option>
                              <option value="America/Los_Angeles">Pacific Time</option>
                              <option value="Europe/London">London</option>
                              <option value="Europe/Paris">Paris</option>
                              <option value="Asia/Tokyo">Tokyo</option>
                            </Form.Select>
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>Language</Form.Label>
                            <Form.Select
                              value={settings.general.language}
                              onChange={(e) => handleInputChange('general', 'language', e.target.value)}
                            >
                              <option value="en">English</option>
                              <option value="es">Spanish</option>
                              <option value="fr">French</option>
                              <option value="de">German</option>
                              <option value="it">Italian</option>
                              <option value="pt">Portuguese</option>
                            </Form.Select>
                          </Form.Group>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </Tab>

                {/* API Settings */}
                <Tab eventKey="api" title="API Configuration">
                  <Row>
                    <Col md={8}>
                      <Form.Group className="mb-3">
                        <Form.Label>API Base URL</Form.Label>
                        <Form.Control
                          type="url"
                          value={settings.api.apiUrl}
                          onChange={(e) => handleInputChange('api', 'apiUrl', e.target.value)}
                        />
                        <Form.Text className="text-muted">
                          The base URL for your API endpoints.
                        </Form.Text>
                      </Form.Group>

                      <Row>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>Request Timeout (ms)</Form.Label>
                            <Form.Control
                              type="number"
                              value={settings.api.timeout}
                              onChange={(e) => handleInputChange('api', 'timeout', parseInt(e.target.value))}
                              min="1000"
                              max="300000"
                            />
                          </Form.Group>
                        </Col>
                        <Col md={6}>
                          <Form.Group className="mb-3">
                            <Form.Label>Retry Attempts</Form.Label>
                            <Form.Control
                              type="number"
                              value={settings.api.retries}
                              onChange={(e) => handleInputChange('api', 'retries', parseInt(e.target.value))}
                              min="0"
                              max="10"
                            />
                          </Form.Group>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </Tab>

                {/* Notifications */}
                <Tab eventKey="notifications" title="Notifications">
                  <Row>
                    <Col md={8}>
                      <h5 className="mb-3">Notification Preferences</h5>
                      
                      <Form.Check
                        type="switch"
                        id="email-notifications"
                        label="Email Notifications"
                        checked={settings.notifications.emailNotifications}
                        onChange={(e) => handleInputChange('notifications', 'emailNotifications', e.target.checked)}
                        className="mb-3"
                      />

                      <Form.Check
                        type="switch"
                        id="push-notifications"
                        label="Push Notifications"
                        checked={settings.notifications.pushNotifications}
                        onChange={(e) => handleInputChange('notifications', 'pushNotifications', e.target.checked)}
                        className="mb-3"
                      />

                      <Form.Check
                        type="switch"
                        id="sms-notifications"
                        label="SMS Notifications"
                        checked={settings.notifications.smsNotifications}
                        onChange={(e) => handleInputChange('notifications', 'smsNotifications', e.target.checked)}
                        className="mb-3"
                      />

                      <Alert variant="info" className="mt-4">
                        <strong>Note:</strong> Notification settings will take effect immediately after saving.
                      </Alert>
                    </Col>
                  </Row>
                </Tab>

                {/* Tenant API Demo */}
                <Tab eventKey="tenant-api" title="Tenant API Demo">
                  <TenantApiDemo />
                </Tab>
              </Tabs>
            </div>
          </div>
        </Container>
      </main>
    </div>
  );
};