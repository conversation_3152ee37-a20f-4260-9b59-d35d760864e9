using Application.ActionManagement.Commands;
using Application.ActionManagement.DTOs;
using Application.ActionManagement.Queries;
using Application.DisplayManagement.Commands;
using Application.DisplayManagement.DTOs;
using Application.DisplayManagement.Queries;
using Infrastructure.OpenApi;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Common.Response;

namespace Web.Host.Controllers;

/// <summary>
/// Display Management Controller - Comprehensive API for Display, Action, and DisplayAction operations
/// Provides complete CRUD operations with bulk support and proper tenant handling
/// </summary>
[ApiController]
[Route("api/display")]
[Produces("application/json")]
public class DisplayController : BaseApiController
{
    #region Display Management Endpoints

    /// <summary>
    /// Get all displays with filtering and pagination
    /// </summary>
    /// <param name="searchTerm">Search term for filtering (optional)</param>
    /// <param name="isActive">Filter by active status (optional)</param>
    /// <param name="orderBy">Order by field (default: SortOrder)</param>
    /// <returns>List of displays</returns>
    /// <response code="200">Displays retrieved successfully</response>
    /// <response code="400">Invalid request parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpGet]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<List<DisplayDto>>>> GetDisplays(
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = true,
        [FromQuery] string? orderBy = "SortOrder")
    {
        try
        {
            var query = new GetDisplaysQuery
            {
                SearchTerm = searchTerm,
                IsActive = isActive,
                OrderBy = orderBy
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while retrieving displays",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Get display by ID
    /// </summary>
    /// <param name="id">Display ID</param>
    /// <returns>Display details</returns>
    /// <response code="200">Display retrieved successfully</response>
    /// <response code="404">Display not found</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("{id:guid}")]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<DisplayDto>>> GetDisplayById(Guid id)
    {
        try
        {
            var query = new GetDisplayByIdQuery(id);
            var result = await Mediator.Send(query);

            if (!result.Succeeded)
            {
                return NotFound(result);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while retrieving display",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Create new display
    /// </summary>
    /// <param name="command">Create display command</param>
    /// <returns>Created display</returns>
    /// <response code="200">Display created successfully</response>
    /// <response code="400">Invalid request data or validation errors</response>
    /// <response code="500">Internal server error</response>
    [HttpPost]
    [TenantIdHeader]
    public async Task<ActionResult<Result<DisplayDto>>> CreateDisplay([FromBody] CreateDisplayCommand command)
    {
        try
        {
            var result = await Mediator.Send(command);

            if (!result.Succeeded)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while creating display",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Update existing display
    /// </summary>
    /// <param name="id">Display ID</param>
    /// <param name="command">Update display command</param>
    /// <returns>Updated display</returns>
    /// <response code="200">Display updated successfully</response>
    /// <response code="400">Invalid request data or validation errors</response>
    /// <response code="404">Display not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("{id:guid}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<DisplayDto>>> UpdateDisplay(Guid id, [FromBody] UpdateDisplayCommand command)
    {
        try
        {
            command.Id = id;
            var result = await Mediator.Send(command);

            if (!result.Succeeded)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while updating display",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Delete display
    /// </summary>
    /// <param name="id">Display ID</param>
    /// <returns>Deletion result</returns>
    /// <response code="200">Display deleted successfully</response>
    /// <response code="404">Display not found</response>
    /// <response code="500">Internal server error</response>
    [HttpDelete("{id:guid}")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<bool>>> DeleteDisplay(Guid id)
    {
        try
        {
            var command = new DeleteDisplayCommand(id);
            var result = await Mediator.Send(command);

            if (!result.Succeeded)
            {
                return NotFound(result);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while deleting display",
                error = ex.Message
            });
        }
    }

    #endregion

    #region Action Management Endpoints

    /// <summary>
    /// Get all actions with filtering
    /// </summary>
    /// <param name="searchTerm">Search term for filtering (optional)</param>
    /// <param name="isActive">Filter by active status (optional)</param>
    /// <param name="orderBy">Order by field (default: Name)</param>
    /// <returns>List of actions</returns>
    /// <response code="200">Actions retrieved successfully</response>
    /// <response code="400">Invalid request parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("actions")]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<List<ActionDto>>>> GetActions(
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = true,
        [FromQuery] string? orderBy = "Name")
    {
        try
        {
            var query = new GetActionsQuery
            {
                SearchTerm = searchTerm,
                IsActive = isActive,
                OrderBy = orderBy
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while retrieving actions",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Get action by ID
    /// </summary>
    /// <param name="id">Action ID</param>
    /// <returns>Action details</returns>
    /// <response code="200">Action retrieved successfully</response>
    /// <response code="404">Action not found</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("actions/{id:guid}")]
    [TenantIdHeader]
    [AllowAnonymous]
    public async Task<ActionResult<Result<ActionDto>>> GetActionById(Guid id)
    {
        try
        {
            var query = new GetActionByIdQuery(id);
            var result = await Mediator.Send(query);

            if (!result.Succeeded)
            {
                return NotFound(result);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while retrieving action",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Create new action
    /// </summary>
    /// <param name="command">Create action command</param>
    /// <returns>Created action</returns>
    /// <response code="200">Action created successfully</response>
    /// <response code="400">Invalid request data or validation errors</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("actions")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<ActionDto>>> CreateAction([FromBody] CreateActionCommand command)
    {
        try
        {
            var result = await Mediator.Send(command);

            if (!result.Succeeded)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred while creating action",
                error = ex.Message
            });
        }
    }

    #endregion

    #region Hierarchical Upsert Endpoints

    /// <summary>
    /// Upsert Display with nested Actions - hierarchical structure
    /// Creates or updates a Display with its associated Actions and DisplayAction relationships
    /// </summary>
    /// <param name="request">Hierarchical upsert request containing Display with nested Actions</param>
    /// <returns>Comprehensive response with created/updated Display, Actions, and relationships</returns>
    /// <response code="200">Upsert operation completed successfully</response>
    /// <response code="400">Invalid request data or validation errors</response>
    /// <response code="404">Referenced Object not found</response>
    /// <response code="500">Internal server error</response>
    /// <remarks>
    /// **HIERARCHICAL UPSERT OPERATION**: Performs atomic upsert of Display with nested Actions and DisplayAction relationships
    ///
    /// This endpoint handles the complete workflow in a single transaction:
    /// 1. Validates that all specified ObjectIds exist
    /// 2. Creates or updates the Display entity (by name)
    /// 3. For each Action: Creates or updates the Action entity (by name)
    /// 4. For each Action: Creates or updates the DisplayAction relationship
    ///
    /// **Request Example:**
    /// ```json
    /// {
    ///   "name": "ListView",
    ///   "description": "List view for managing items",
    ///   "displayName": "List View",
    ///   "isDefault": true,
    ///   "routeTemplate": "/items/list",
    ///   "icon": "list",
    ///   "sortOrder": 1,
    ///   "isActive": true,
    ///   "actions": [
    ///     {
    ///       "name": "ViewDetails",
    ///       "description": "View item details",
    ///       "endpointTemplate": "/api/items/{id}",
    ///       "navigationTarget": "/items/{id}/details",
    ///       "icon": "eye",
    ///       "buttonStyle": "Primary",
    ///       "objectId": "12345678-1234-1234-1234-123456789012",
    ///       "accessLevel": "Public",
    ///       "isDefault": true,
    ///       "sortOrder": 1,
    ///       "isVisibleInToolbar": true,
    ///       "isVisibleInContextMenu": false,
    ///       "isVisibleInRowActions": true
    ///     }
    ///   ]
    /// }
    /// ```
    /// </remarks>
    [HttpPost("upsert")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<DisplayWithActionsResponseDto>>> UpsertDisplayWithActions(
        [FromBody] DisplayWithActionsDto request)
    {
        try
        {
            var command = request.Adapt<UpsertDisplayWithActionsCommand>();
            var result = await Mediator.Send(command);

            if (!result.Succeeded)
            {
                // Check if it's a not found error for Object
                if (result.Message.Contains("Object with ID") && result.Message.Contains("not found"))
                {
                    return NotFound(result);
                }

                return BadRequest(result);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred during hierarchical upsert operation",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Bulk upsert multiple Displays with nested Actions - hierarchical structure
    /// Creates or updates multiple Displays with their associated Actions and DisplayAction relationships
    /// </summary>
    /// <param name="request">Bulk hierarchical upsert request containing multiple Displays with nested Actions</param>
    /// <returns>Comprehensive bulk response with statistics and results for all processed entities</returns>
    /// <response code="200">Bulk upsert operation completed (may contain partial failures)</response>
    /// <response code="400">Invalid request data or validation errors</response>
    /// <response code="404">Referenced Objects not found</response>
    /// <response code="500">Internal server error</response>
    /// <remarks>
    /// **BULK HIERARCHICAL UPSERT OPERATION**: Performs atomic upsert of multiple Displays with nested Actions
    ///
    /// This endpoint processes multiple displays in a single request:
    /// 1. Validates that all specified ObjectIds exist across all displays
    /// 2. For each Display: Creates or updates the Display entity (by name)
    /// 3. For each Action in each Display: Creates or updates the Action entity (by name)
    /// 4. For each Action in each Display: Creates or updates the DisplayAction relationship
    /// 5. Returns comprehensive statistics and individual results
    ///
    /// **Request Example:**
    /// ```json
    /// {
    ///   "displays": [
    ///     {
    ///       "name": "ListView",
    ///       "description": "List view for managing items",
    ///       "displayName": "List View",
    ///       "actions": [
    ///         {
    ///           "name": "ViewDetails",
    ///           "objectId": "12345678-1234-1234-1234-123456789012",
    ///           "accessLevel": "Public"
    ///         }
    ///       ]
    ///     }
    ///   ]
    /// }
    /// ```
    /// </remarks>
    [HttpPost("bulk-upsert")]
    [TenantIdHeader]
    public async Task<ActionResult<Result<BulkDisplayWithActionsResponseDto>>> BulkUpsertDisplayWithActions(
        [FromBody] BulkDisplayWithActionsDto request)
    {
        try
        {
            var command = request.Adapt<BulkUpsertDisplayWithActionsCommand>();
            var result = await Mediator.Send(command);

            if (!result.Succeeded)
            {
                // Check if it's a not found error for Objects
                if (result.Message.Contains("Object IDs were not found"))
                {
                    return NotFound(result);
                }

                return BadRequest(result);
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                succeeded = false,
                message = "Internal server error occurred during bulk hierarchical upsert operation",
                error = ex.Message
            });
        }
    }

    #endregion
}
