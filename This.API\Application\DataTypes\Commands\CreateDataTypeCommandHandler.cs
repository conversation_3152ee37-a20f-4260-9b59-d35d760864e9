using Application.DataTypes.DTOs;
using Application.DataTypes.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.DataTypes.Commands;

/// <summary>
/// Create DataType command handler
/// </summary>
public class CreateDataTypeCommandHandler : IRequestHandler<CreateDataTypeCommand, Result<DataTypeDto>>
{
    private readonly IRepository<DataType> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateDataTypeCommandHandler(IRepository<DataType> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<DataTypeDto>> Handle(CreateDataTypeCommand request, CancellationToken cancellationToken)
    {
        // Check if DataType with same name already exists
        var existingDataType = await _repository.GetBySpecAsync(new DataTypeByNameSpec(request.Name), cancellationToken);
        if (existingDataType != null)
        {
            return Result<DataTypeDto>.Failure($"DataType with name '{request.Name}' already exists.");
        }

        // Create new DataType
        var dataType = new DataType
        {
            Name = request.Name,
            DisplayName = request.DisplayName ?? request.Name,
            Category = request.Category ?? "general",
            UiComponent = request.UiComponent ?? "text",
            ValidationPattern = request.ValidationPattern,
            MinLength = request.MinLength,
            MaxLength = request.MaxLength,
            MinValue = request.MinValue,
            MaxValue = request.MaxValue,
            DecimalPlaces = request.DecimalPlaces,
            StepValue = request.StepValue,
            IsRequired = request.IsRequired,
            InputType = request.InputType,
            InputMask = request.InputMask,
            Placeholder = request.Placeholder,
            HtmlAttributes = request.HtmlAttributes,
            DefaultOptions = request.DefaultOptions,
            AllowsMultiple = request.AllowsMultiple,
            AllowsCustomOptions = request.AllowsCustomOptions,
            MaxSelections = request.MaxSelections,
            AllowedFileTypes = request.AllowedFileTypes,
            MaxFileSizeBytes = request.MaxFileSizeBytes,
            RequiredErrorMessage = request.RequiredErrorMessage,
            PatternErrorMessage = request.PatternErrorMessage,
            MinLengthErrorMessage = request.MinLengthErrorMessage,
            MaxLengthErrorMessage = request.MaxLengthErrorMessage,
            MinValueErrorMessage = request.MinValueErrorMessage,
            MaxValueErrorMessage = request.MaxValueErrorMessage,
            FileTypeErrorMessage = request.FileTypeErrorMessage,
            FileSizeErrorMessage = request.FileSizeErrorMessage,
            IsActive = request.IsActive
        };

        var createdDataType = await _repository.AddAsync(dataType, cancellationToken);

        var dto = new DataTypeDto
        {
            Id = createdDataType.Id,
            Name = createdDataType.Name,
            DisplayName = createdDataType.DisplayName,
            Category = createdDataType.Category,
            UiComponent = createdDataType.UiComponent,
            ValidationPattern = createdDataType.ValidationPattern,
            MinLength = createdDataType.MinLength,
            MaxLength = createdDataType.MaxLength,
            MinValue = createdDataType.MinValue,
            MaxValue = createdDataType.MaxValue,
            DecimalPlaces = createdDataType.DecimalPlaces,
            StepValue = createdDataType.StepValue,
            IsRequired = createdDataType.IsRequired,
            InputType = createdDataType.InputType,
            InputMask = createdDataType.InputMask,
            Placeholder = createdDataType.Placeholder,
            HtmlAttributes = createdDataType.HtmlAttributes,
            DefaultOptions = createdDataType.DefaultOptions,
            AllowsMultiple = createdDataType.AllowsMultiple,
            AllowsCustomOptions = createdDataType.AllowsCustomOptions,
            MaxSelections = createdDataType.MaxSelections,
            AllowedFileTypes = createdDataType.AllowedFileTypes,
            MaxFileSizeBytes = createdDataType.MaxFileSizeBytes,
            RequiredErrorMessage = createdDataType.RequiredErrorMessage,
            PatternErrorMessage = createdDataType.PatternErrorMessage,
            MinLengthErrorMessage = createdDataType.MinLengthErrorMessage,
            MaxLengthErrorMessage = createdDataType.MaxLengthErrorMessage,
            MinValueErrorMessage = createdDataType.MinValueErrorMessage,
            MaxValueErrorMessage = createdDataType.MaxValueErrorMessage,
            FileTypeErrorMessage = createdDataType.FileTypeErrorMessage,
            FileSizeErrorMessage = createdDataType.FileSizeErrorMessage,
            IsActive = createdDataType.IsActive,
            CreatedAt = createdDataType.CreatedAt,
            CreatedBy = createdDataType.CreatedBy ?? Guid.Empty,
            ModifiedAt = createdDataType.ModifiedAt,
            ModifiedBy = createdDataType.ModifiedBy
        };

        return Result<DataTypeDto>.Success(dto);
    }
}
