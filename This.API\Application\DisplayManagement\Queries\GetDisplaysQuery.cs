using Application.DisplayManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.DisplayManagement.Queries;

/// <summary>
/// Get all displays query
/// </summary>
public class GetDisplaysQuery : IRequest<Result<List<DisplayDto>>>
{
    /// <summary>
    /// Search term for filtering
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; } = true;

    /// <summary>
    /// Order by field
    /// </summary>
    public string? OrderBy { get; set; } = "SortOrder";
}
