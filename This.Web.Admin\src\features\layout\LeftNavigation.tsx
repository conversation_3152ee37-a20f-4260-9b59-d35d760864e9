import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/shared/utils/utils';
import { Button } from '@/shared/components/atoms/Button/Button';
import { routes } from '../../config/routes';
import { useNavigationData } from '../../hooks/useNavigationData';
import { NavigationItem } from './NavigationItem';
import type { NavigationNode } from '../../types/metadata';

interface LeftNavigationProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  className?: string;
}

export const LeftNavigation: React.FC<LeftNavigationProps> = ({
  collapsed: controlledCollapsed,
  onToggleCollapse,
  className
}) => {
  const [internalCollapsed, setInternalCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { nodes, isLoading, error, refresh } = useNavigationData();

  // Use controlled collapsed state if provided, otherwise use internal state
  const collapsed = controlledCollapsed !== undefined ? controlledCollapsed : internalCollapsed;
  const toggleCollapse = onToggleCollapse || (() => setInternalCollapsed(!internalCollapsed));

  // Get the product name from navigation data
  // The first node should be a product (top-level node with type 'product')
  const getNavigationTitle = (): string => {
    if (isLoading) {
      return 'Loading...';
    }

    // Find the first active product node
    const productNode = nodes.find(node => node.type === 'product' && node.isActive);
    if (productNode) {
      return productNode.name || 'Unknown Product';
    }

    // If no active product, find any product node
    const anyProductNode = nodes.find(node => node.type === 'product');
    if (anyProductNode) {
      return anyProductNode.name || 'Unknown Product';
    }

    // Fallback to default title
    return 'Navigation';
  };

  // Filter and restructure navigation nodes to implement flattened Objects hierarchy
  // Only include active, non-deleted products and their active, non-deleted objects
  const getFilteredNavigationNodes = (): NavigationNode[] => {
    const objectNodes: NavigationNode[] = [];

    // Extract all active, non-deleted objects from active, non-deleted products
    nodes.forEach(productNode => {
      // Only process active and non-deleted products
      if (
        productNode.type === 'product' && 
        productNode.isActive !== false && 
        productNode.isDeleted !== true &&
        productNode.children
      ) {
        // Filter and add active, non-deleted objects from this product
        const validObjects = productNode.children.filter(
          objectNode => 
            objectNode.type === 'object' && 
            objectNode.isActive !== false &&
            objectNode.isDeleted !== true
        );

        // Add valid objects to the top level
        validObjects.forEach(objectNode => {
          objectNodes.push({
            ...objectNode,
            // Filter children to only include active and non-deleted ones if they exist
            children: objectNode.children?.filter(
              child => child.isActive !== false && child.isDeleted !== true
            ) || []
          });
        });
      }
    });

    return objectNodes;
  };

  const handleRouteClick = (path: string) => {
    navigate(path);
  };

  return (
    <div className={cn(
      'flex flex-col h-full bg-card border-r border-border transition-all duration-300 ease-in-out',
      collapsed ? 'w-16' : 'w-64',
      'md:relative md:translate-x-0', // Always visible on desktop
      className
    )}>
      {/* Header with title, refresh button, and toggle button */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2 flex-1">
          {!collapsed && (
            <h2 className="text-base font-medium text-foreground">{getNavigationTitle()}</h2>
          )}
          {!collapsed && (
            <Button
              variant="ghost"
              size="icon"
              onClick={refresh}
              disabled={isLoading}
              className="h-6 w-6 hover:bg-muted"
              title="Refresh navigation data"
            >
              <RefreshCw className={cn(
                "h-3 w-3",
                isLoading && "animate-spin"
              )} />
            </Button>
          )}
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleCollapse}
          className="h-8 w-8 hover:bg-muted"
          aria-label={collapsed ? 'Expand navigation' : 'Collapse navigation'}
        >
          {collapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation sections */}
      <div className="flex-1 overflow-y-auto p-2">
        {/* Dynamic Navigation from JSON metadata */}
        <div className="mb-4">
          {/* Error state */}
          {error && !collapsed && (
            <div className="px-3 py-2 mb-2 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-xs text-destructive font-normal">Error loading navigation</p>
              <p className="text-xs text-destructive/80 mt-1">{error}</p>
            </div>
          )}

          {/* Loading state */}
          {isLoading && !collapsed && (
            <div className="px-3 py-2 mb-2">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <RefreshCw className="h-3 w-3 animate-spin" />
                <span>Loading navigation...</span>
              </div>
            </div>
          )}

          {/* Navigation items */}
          {!error && (
            <div className="space-y-1">
              {getFilteredNavigationNodes().length === 0 && !isLoading ? (
                <div className="px-3 py-2 text-xs text-muted-foreground">
                  No items available
                </div>
              ) : (
                getFilteredNavigationNodes().map((node) => (
                  <NavigationItem
                    key={node.id}
                    node={node}
                    collapsed={collapsed}
                    level={0}
                  />
                ))
              )}
            </div>
          )}

          {/* Collapsed state indicator */}
          {collapsed && getFilteredNavigationNodes().length > 0 && (
            <div className="flex justify-center">
              <div className="w-8 h-1 bg-primary/30 rounded-full" />
            </div>
          )}
        </div>

        {/* Separator between dynamic and static navigation */}
        {!collapsed && getFilteredNavigationNodes().length > 0 && (
          <div className="mx-3 my-4 border-t border-border"></div>
        )}

        {/* Static Navigation Routes */}
        <div className="mb-4">
          {!collapsed && (
            <div className="px-3 py-2 mb-2">
              <span className="text-xs font-medium text-muted-foreground">
                Quick Access
              </span>
            </div>
          )}

          {/* Navigation items */}
          <div className="space-y-1">
            {routes.map((route) => {
              const isActive = location.pathname === route.path;

              return (
                <button
                  key={route.key}
                  type="button"
                  onClick={() => handleRouteClick(route.path)}
                  className={cn(
                    'w-full flex items-center gap-3 px-3 py-2 text-xs font-normal rounded-lg transition-all duration-200 ease-in-out',
                    'hover:bg-muted hover:text-foreground',
                    'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1',
                    isActive && 'bg-primary text-primary-foreground hover:bg-primary/90',
                    collapsed && 'justify-center px-2'
                  )}
                  title={collapsed ? route.label : undefined}
                >
                  {/* Icon */}
                  <span className="text-base leading-none flex-shrink-0">
                    {route.icon}
                  </span>

                  {/* Label */}
                  {!collapsed && (
                    <span className="truncate">{route.label}</span>
                  )}

                  {/* Active indicator for collapsed state */}
                  {collapsed && isActive && (
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-primary rounded-l-full" />
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </div>

    </div>
  );
};

export default LeftNavigation;
