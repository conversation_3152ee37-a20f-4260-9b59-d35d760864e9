using MediatR;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertBulkTenantContext;

/// <summary>
/// Command to upsert multiple tenant contexts in bulk
/// </summary>
public class UpsertBulkTenantContextCommand : IRequest<Result<BulkUpsertTenantContextResponse>>
{
    /// <summary>
    /// List of tenant contexts to upsert
    /// </summary>
    public List<TenantContextUpsertItem> TenantContexts { get; set; } = new();

    /// <summary>
    /// Batch size for processing
    /// </summary>
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Individual tenant context item for bulk upsert
/// </summary>
public class TenantContextUpsertItem
{
    /// <summary>
    /// TenantContext ID (optional for insert)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Name of the tenant context
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the tenant context
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Category for grouping tenant contexts
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Whether the tenant context is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Response for bulk upsert operation
/// </summary>
public class BulkUpsertTenantContextResponse
{
    /// <summary>
    /// Number of tenant contexts created
    /// </summary>
    public int CreatedCount { get; set; }

    /// <summary>
    /// Number of tenant contexts updated
    /// </summary>
    public int UpdatedCount { get; set; }

    /// <summary>
    /// Number of tenant contexts failed
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// List of created tenant context IDs
    /// </summary>
    public List<Guid> CreatedIds { get; set; } = new();

    /// <summary>
    /// List of updated tenant context IDs
    /// </summary>
    public List<Guid> UpdatedIds { get; set; } = new();

    /// <summary>
    /// List of errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Total processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}
