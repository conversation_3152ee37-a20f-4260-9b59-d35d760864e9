import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// A customizable slider input widget following the 'this_componentName_input' naming convention
/// This widget handles range slider input with validation based on API configuration
class ThisSliderInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final double value;
  final ValueChanged<double> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;

  // API-based validation parameters
  final String? validationPattern;
  final double? minValue;
  final double? maxValue;
  final int? decimalPlaces;
  final double? stepValue;
  final String? requiredErrorMessage;
  final String? patternErrorMessage;
  final String? minValueErrorMessage;
  final String? maxValueErrorMessage;

  // Slider-specific parameters
  final bool showValue;
  final bool showMinMaxLabels;
  final bool showTicks;
  final String? prefix;
  final String? suffix;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool autoFocus;
  final String? Function(double)? customValidation;

  const ThisSliderInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.validationPattern,
    this.minValue,
    this.maxValue,
    this.decimalPlaces,
    this.stepValue,
    this.requiredErrorMessage,
    this.patternErrorMessage,
    this.minValueErrorMessage,
    this.maxValueErrorMessage,
    this.showValue = true,
    this.showMinMaxLabels = true,
    this.showTicks = false,
    this.prefix,
    this.suffix,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisSliderInput> createState() => _ThisSliderInputState();
}

class _ThisSliderInputState extends State<ThisSliderInput> {
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();

    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  double get _minValue => widget.minValue ?? 0.0;
  double get _maxValue => widget.maxValue ?? 100.0;
  double get _stepValue => widget.stepValue ?? 1.0;
  int get _decimalPlaces => widget.decimalPlaces ?? 0;

  List<String> _validateValue(double value) {
    final errors = <String>[];

    // 1. Required validation (for sliders, this means checking if value is set)
    if (widget.required && value == _minValue && _minValue == 0) {
      errors.add(widget.requiredErrorMessage ?? '${widget.label} is required');
      return errors;
    }

    // 2. Range validation
    if (value < _minValue) {
      errors.add(widget.minValueErrorMessage ?? 'Value cannot be less than $_minValue');
      return errors;
    }

    if (value > _maxValue) {
      errors.add(widget.maxValueErrorMessage ?? 'Value cannot be greater than $_maxValue');
      return errors;
    }

    // 3. Step validation
    final remainder = (value - _minValue) % _stepValue;
    if (remainder.abs() >= 0.0001) {
      errors.add('Value must be in increments of $_stepValue');
      return errors;
    }

    // 4. Pattern validation (if provided)
    if (widget.validationPattern != null) {
      final regex = RegExp(widget.validationPattern!);
      final valueString = _formatValue(value);
      if (!regex.hasMatch(valueString)) {
        errors.add(widget.patternErrorMessage ?? 'Invalid value format');
        return errors;
      }
    }

    // 5. Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(value);
      if (customError != null) {
        errors.add(customError);
        return errors;
      }
    }

    return errors;
  }

  String _formatValue(double value) {
    String formatted;

    if (_decimalPlaces == 0) {
      formatted = value.round().toString();
    } else {
      formatted = value.toStringAsFixed(_decimalPlaces);
    }

    // Add prefix and suffix
    if (widget.prefix != null) {
      formatted = '${widget.prefix}$formatted';
    }
    if (widget.suffix != null) {
      formatted = '$formatted${widget.suffix}';
    }

    return formatted;
  }

  void _handleChange(double value) {
    // Snap to step
    final steppedValue = _minValue + (((value - _minValue) / _stepValue).round() * _stepValue);
    final clampedValue = steppedValue.clamp(_minValue, _maxValue);

    widget.onChanged(clampedValue);

    // Real-time validation (only if not validating on blur)
    if (!widget.validateOnBlur) {
      final errors = _validateValue(clampedValue);
      setState(() {
        _errors = errors;
        _isValidated = true;
      });

      widget.onValidation?.call(errors);
    }
  }

  void _handleChangeEnd(double value) {
    // Validate on change end if enabled
    if (widget.validateOnBlur) {
      final errors = _validateValue(value);
      setState(() {
        _errors = errors;
        _isValidated = true;
      });

      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  int get _divisions {
    if (_stepValue <= 0) return 0;
    return ((_maxValue - _minValue) / _stepValue).round();
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
            const Spacer(),
            if (_getValidationIcon() != null) _getValidationIcon()!,
          ],
        ),
        const SizedBox(height: 8),

        // Value display
        if (widget.showValue)
          Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: hasErrors ? const Color(0xFFC73E1D).withValues(alpha: 0.1) : (isValid ? ColorPalette.green.withValues(alpha: 0.1) : ColorPalette.darkToneInk.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.gray300),
                ),
              ),
              child: Text(
                _formatValue(widget.value),
                style: LexendTextStyles.lexend16Bold.copyWith(
                  color: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.black),
                ),
              ),
            ),
          ),

        const SizedBox(height: 16),

        // Slider
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.black),
            inactiveTrackColor: ColorPalette.gray300.withValues(alpha: 0.3),
            thumbColor: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.black),
            overlayColor: (hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.black)).withValues(alpha: 0.2),
            valueIndicatorColor: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.black),
            valueIndicatorTextStyle: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.darkToneInk,
            ),
            showValueIndicator: ShowValueIndicator.onlyForDiscrete,
            tickMarkShape: widget.showTicks ? const RoundSliderTickMarkShape() : SliderTickMarkShape.noTickMark,
            activeTickMarkColor: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.black),
            inactiveTickMarkColor: ColorPalette.gray300,
          ),
          child: Slider(
            value: widget.value.clamp(_minValue, _maxValue),
            min: _minValue,
            max: _maxValue,
            divisions: _divisions,
            label: _formatValue(widget.value),
            onChanged: widget.disabled || widget.readOnly ? null : _handleChange,
            onChangeEnd: widget.disabled || widget.readOnly ? null : _handleChangeEnd,
            focusNode: _focusNode,
          ),
        ),

        // Min/Max labels
        if (widget.showMinMaxLabels)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatValue(_minValue),
                  style: LexendTextStyles.lexend12Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
                Text(
                  _formatValue(_maxValue),
                  style: LexendTextStyles.lexend12Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ),
          ),

        // Error message
        if (hasErrors)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _errors.first,
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
            ),
          ),

        // Helper text
        if (!hasErrors && _stepValue > 0)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Step: $_stepValue',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          ),
      ],
    );
  }
}
