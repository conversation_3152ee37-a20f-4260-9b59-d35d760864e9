using Ardalis.Specification;
using Domain.Entities;

namespace Application.FieldMappings.Specifications;

/// <summary>
/// Specification to get field mapping with related entity details
/// </summary>
public class FieldMappingWithDetailsSpec : Specification<FieldMapping>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public FieldMappingWithDetailsSpec(Guid id)
    {
        Query.Where(fm => fm.Id == id)
             .Include(fm => fm.ObjectMetadata)
             .Include("ObjectMetadata.Metadata")
             .Include(fm => fm.User)
             .Include(fm => fm.Role);
    }
}
