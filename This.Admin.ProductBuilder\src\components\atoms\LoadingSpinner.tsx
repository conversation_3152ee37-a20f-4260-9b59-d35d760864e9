/**
 * LoadingSpinner Atom
 * Reusable loading spinner component with various sizes and styles
 */

import React from 'react';
import { Spinner } from 'react-bootstrap';

export interface LoadingSpinnerProps {
  size?: 'sm' | 'lg';
  variant?: 'border' | 'grow';
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
  className?: string;
  text?: string;
  centered?: boolean;
  overlay?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size,
  variant = 'border',
  color = 'primary',
  className = '',
  text,
  centered = false,
  overlay = false
}) => {
  const spinnerElement = (
    <div className={`d-flex align-items-center ${centered ? 'justify-content-center' : ''} ${className}`}>
      <Spinner 
        animation={variant} 
        size={size} 
        variant={color}
        role="status"
        aria-hidden="true"
      />
      {text && (
        <span className={`ms-2 ${size === 'sm' ? 'small' : ''}`}>
          {text}
        </span>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div 
        className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
        style={{ 
          backgroundColor: 'rgba(255, 255, 255, 0.8)', 
          zIndex: 1000,
          backdropFilter: 'blur(2px)'
        }}
      >
        {spinnerElement}
      </div>
    );
  }

  if (centered) {
    return (
      <div className="text-center py-4">
        {spinnerElement}
      </div>
    );
  }

  return spinnerElement;
};

// Predefined spinner variants for common use cases
export const TableLoadingSpinner: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <LoadingSpinner 
    size="sm" 
    text={text} 
    centered 
    className="py-5" 
  />
);

export const PageLoadingSpinner: React.FC<{ text?: string }> = ({ text = 'Loading page...' }) => (
  <LoadingSpinner 
    text={text} 
    centered 
    className="py-5" 
  />
);

export const ButtonLoadingSpinner: React.FC = () => (
  <LoadingSpinner 
    size="sm" 
    className="me-2" 
  />
);

export const OverlayLoadingSpinner: React.FC<{ text?: string }> = ({ text = 'Processing...' }) => (
  <LoadingSpinner 
    text={text} 
    overlay 
  />
);
