import { create<PERSON>ontext, useContext, useReducer, use<PERSON><PERSON>back, ReactNode } from 'react';
import { apiService, TenantData, User, Role } from '../services/apiService';

// State interface
interface OnboardingState {
  // Tenant data
  tenantData: TenantData | null;
  tenantLoading: boolean;
  tenantError: string | null;

  // Users data
  users: User[];
  usersLoading: boolean;
  usersError: string | null;

  // Roles data
  roles: Role[];
  rolesLoading: boolean;
  rolesError: string | null;

  // General loading states
  isInitializing: boolean;
}

// Action types
type OnboardingAction =
  | { type: 'SET_INITIALIZING'; payload: boolean }
  | { type: 'SET_TENANT_LOADING'; payload: boolean }
  | { type: 'SET_TENANT_DATA'; payload: TenantData | null }
  | { type: 'SET_TENANT_ERROR'; payload: string | null }
  | { type: 'SET_USERS_LOADING'; payload: boolean }
  | { type: 'SET_USERS_DATA'; payload: User[] }
  | { type: 'SET_USERS_ERROR'; payload: string | null }
  | { type: 'SET_ROLES_LOADING'; payload: boolean }
  | { type: 'SET_ROLES_DATA'; payload: Role[] }
  | { type: 'SET_ROLES_ERROR'; payload: string | null }
  | { type: 'RESET_STATE' };

// Initial state
const initialState: OnboardingState = {
  tenantData: null,
  tenantLoading: false,
  tenantError: null,
  users: [],
  usersLoading: false,
  usersError: null,
  roles: [],
  rolesLoading: false,
  rolesError: null,
  isInitializing: false,
};

// Reducer
function onboardingReducer(state: OnboardingState, action: OnboardingAction): OnboardingState {
  switch (action.type) {
    case 'SET_INITIALIZING':
      return { ...state, isInitializing: action.payload };
    case 'SET_TENANT_LOADING':
      return { ...state, tenantLoading: action.payload };
    case 'SET_TENANT_DATA':
      return { ...state, tenantData: action.payload, tenantError: null };
    case 'SET_TENANT_ERROR':
      return { ...state, tenantError: action.payload, tenantLoading: false };
    case 'SET_USERS_LOADING':
      return { ...state, usersLoading: action.payload };
    case 'SET_USERS_DATA':
      return { ...state, users: action.payload, usersError: null };
    case 'SET_USERS_ERROR':
      return { ...state, usersError: action.payload, usersLoading: false };
    case 'SET_ROLES_LOADING':
      return { ...state, rolesLoading: action.payload };
    case 'SET_ROLES_DATA':
      return { ...state, roles: action.payload, rolesError: null };
    case 'SET_ROLES_ERROR':
      return { ...state, rolesError: action.payload, rolesLoading: false };
    case 'RESET_STATE':
      return initialState;
    default:
      return state;
  }
}

// Context interface - simplified for user import and role assignment only
interface OnboardingContextType {
  state: OnboardingState;

  // User actions
  fetchUsers: (tenantId: string, useCache?: boolean) => Promise<User[]>;
  importUsers: (tenantId: string, tenantData?: any, applicationDetails?: any) => Promise<User[]>;

  // Role actions
  fetchRoles: (tenantId: string, useCache?: boolean) => Promise<Role[]>;
  assignRoles: (tenantId: string, roleAssignments: Record<string, unknown>[], productId?: string) => Promise<void>;

  // Batch actions
  fetchUsersAndRoles: (tenantId: string, useCache?: boolean) => Promise<{ users: User[]; roles: Role[] }>;

  // Utility actions
  resetState: () => void;
  clearCache: (pattern?: string) => void;
  clearSubmissionGuard: (key: string) => void;
}

// Create context
const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

// Provider component
interface OnboardingProviderProps {
  children: ReactNode;
}

export function OnboardingProvider({ children }: OnboardingProviderProps) {
  const [state, dispatch] = useReducer(onboardingReducer, initialState);

  // User actions
  const fetchUsers = useCallback(async (tenantId: string, useCache: boolean = true): Promise<User[]> => {
    try {
      dispatch({ type: 'SET_USERS_LOADING', payload: true });
      dispatch({ type: 'SET_USERS_ERROR', payload: null });

      const users = await apiService.getUsers(tenantId, useCache);
      dispatch({ type: 'SET_USERS_DATA', payload: users });
      return users;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch users';
      dispatch({ type: 'SET_USERS_ERROR', payload: errorMessage });
      return [];
    } finally {
      dispatch({ type: 'SET_USERS_LOADING', payload: false });
    }
  }, []);

  const importUsers = useCallback(async (tenantId: string, tenantData?: any, applicationDetails?: any): Promise<User[]> => {
    const submissionKey = `import_users_${tenantId}`;
    const errorCooldownKey = `import_users_error_${tenantId}`;

    try {
      // Prevent multiple simultaneous submissions
      if (apiService.isSubmissionInProgress(submissionKey)) {
        throw new Error('Import already in progress. Please wait for the current operation to complete.');
      }

      // Check for recent error cooldown (prevent immediate retries after errors)
      if (apiService.isSubmissionInProgress(errorCooldownKey)) {
        throw new Error('Please wait before retrying the import operation.');
      }
      dispatch({ type: 'SET_USERS_LOADING', payload: true });
      dispatch({ type: 'SET_USERS_ERROR', payload: null });

      // Check if users are already imported from applicationDetails
      if (applicationDetails?.isUserImported) {
        return [];
      }

      // Set submission guard to prevent duplicate calls
      apiService.setSubmissionGuard(submissionKey);

      let importedUsers: any[] = [];

      try {
        // Fetch users from Leadrat
        importedUsers = await apiService.importUsersFromLeadrat(tenantId);
      } catch (leadratError) {
        throw new Error(`Failed to fetch users from external system: ${leadratError instanceof Error ? leadratError.message : 'Unknown error'}`);
      }

      if (importedUsers.length === 0) {
        // Return empty array but don't throw error - this is a valid state
        return [];
      }

      // Transform users for bulk creation
      const usersWithPassword = importedUsers.map((user: any) => ({
        firstName: user?.firstName,
        lastName: user?.lastName,
        displayName: `${user?.firstName} ${user?.lastName}`,
        email: user?.email,
        userName: user?.userName,
        phoneNumber: user?.phoneNumber,
        imageUrl: user?.imageUrl,
        isActive: user?.isActive,
        isMFAEnabled: user?.isMFAEnabled,
        password: "123Pa$$word!",
        confirmPassword: "123Pa$$word!",
        integrationSourceId: null,
        externalUserId: user?.userId,
        externalUserData: null,
        objectId: "00000000-0000-0000-0000-000000000000",
      }));

      try {
        const productId = applicationDetails?.id;
        await apiService.bulkCreateUsers(tenantId, usersWithPassword, productId);
      } catch (bulkCreateError) {
        throw new Error(`Failed to create users in system: ${bulkCreateError instanceof Error ? bulkCreateError.message : 'Unknown error'}`);
      }

      // After successful bulk creation, fetch the updated user list from the system
      // This ensures we have the complete user data with system-generated IDs and properties
      try {
        const updatedUsers = await apiService.getUsers(tenantId, false); // Don't use cache to get fresh data

        // Update the context state with the fresh user data
        dispatch({ type: 'SET_USERS_DATA', payload: updatedUsers });

        return updatedUsers;
      } catch (fetchError) {
        console.warn('Failed to fetch updated users after bulk creation:', fetchError);

        const transformedUsers: User[] = importedUsers.map((importedUser) => ({
          ...importedUser,
          phoneNumberConfirmed: false,
          roles: [],
        }));

        // Update context state with fallback data
        dispatch({ type: 'SET_USERS_DATA', payload: transformedUsers });

        return transformedUsers;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to import users';
      dispatch({ type: 'SET_USERS_ERROR', payload: errorMessage });

      apiService.setSubmissionGuard(errorCooldownKey);
      setTimeout(() => {
        apiService.clearSubmissionGuard(errorCooldownKey);
      }, 5000);

      throw error;
    } finally {
      dispatch({ type: 'SET_USERS_LOADING', payload: false });
      apiService.clearSubmissionGuard(submissionKey);
    }
  }, []);

  // Role actions
  const fetchRoles = useCallback(async (tenantId: string, useCache: boolean = true): Promise<Role[]> => {
    try {
      dispatch({ type: 'SET_ROLES_LOADING', payload: true });
      dispatch({ type: 'SET_ROLES_ERROR', payload: null });

      const roles = await apiService.getRoles(tenantId, useCache);
      dispatch({ type: 'SET_ROLES_DATA', payload: roles });
      return roles;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch roles';
      dispatch({ type: 'SET_ROLES_ERROR', payload: errorMessage });
      return [];
    } finally {
      dispatch({ type: 'SET_ROLES_LOADING', payload: false });
    }
  }, []);

  const assignRoles = useCallback(async (tenantId: string, roleAssignments: Record<string, unknown>[], productId?: string): Promise<void> => {
    try {
      await apiService.bulkUpdateRoles(tenantId, roleAssignments, productId);
      // No need to refresh users data after role assignment
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to assign roles';
      throw new Error(errorMessage);
    }
  }, []);

  // Batch actions
  const fetchUsersAndRoles = useCallback(async (tenantId: string, useCache: boolean = true): Promise<{ users: User[]; roles: Role[] }> => {
    try {
      dispatch({ type: 'SET_USERS_LOADING', payload: true });
      dispatch({ type: 'SET_ROLES_LOADING', payload: true });
      dispatch({ type: 'SET_USERS_ERROR', payload: null });
      dispatch({ type: 'SET_ROLES_ERROR', payload: null });

      const { users, roles } = await apiService.getUsersAndRoles(tenantId, useCache);

      dispatch({ type: 'SET_USERS_DATA', payload: users });
      dispatch({ type: 'SET_ROLES_DATA', payload: roles });

      return { users, roles };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch data';
      dispatch({ type: 'SET_USERS_ERROR', payload: errorMessage });
      dispatch({ type: 'SET_ROLES_ERROR', payload: errorMessage });
      return { users: [], roles: [] };
    } finally {
      dispatch({ type: 'SET_USERS_LOADING', payload: false });
      dispatch({ type: 'SET_ROLES_LOADING', payload: false });
    }
  }, []);

  // Utility actions
  const resetState = useCallback(() => {
    dispatch({ type: 'RESET_STATE' });
  }, []);

  const clearCache = useCallback((pattern?: string) => {
    apiService.clearCache(pattern);
  }, []);

  const clearSubmissionGuard = useCallback((key: string) => {
    apiService.clearSubmissionGuard(key);
  }, []);

  const contextValue: OnboardingContextType = {
    state,
    fetchUsers,
    importUsers,
    fetchRoles,
    assignRoles,
    fetchUsersAndRoles,
    resetState,
    clearCache,
    clearSubmissionGuard,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}

// Custom hook to use the context
export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
