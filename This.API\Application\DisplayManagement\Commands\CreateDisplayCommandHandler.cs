using Abstraction.Database.Repositories;
using Application.DisplayManagement.DTOs;
using Application.DisplayManagement.Specifications;
using Domain.Entities;
using Finbuckle.MultiTenant;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.DisplayManagement.Commands;

/// <summary>
/// Create Display command handler
/// </summary>
public class CreateDisplayCommandHandler : IRequestHandler<CreateDisplayCommand, Result<DisplayDto>>
{
    private readonly IRepository<Display> _repository;
    private readonly ITenantInfo _tenantInfo;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateDisplayCommandHandler(
        IRepository<Display> repository,
        ITenantInfo tenantInfo)
    {
        _repository = repository;
        _tenantInfo = tenantInfo;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<DisplayDto>> Handle(CreateDisplayCommand request, CancellationToken cancellationToken)
    {
        // Check if Display with same name already exists for this tenant
        var existingDisplay = await _repository.GetBySpecAsync(
            new DisplayByNameSpec(request.Name), 
            cancellationToken);
        
        if (existingDisplay != null)
        {
            return Result<DisplayDto>.Failure($"Display with name '{request.Name}' already exists.");
        }

        // Create new Display
        var display = request.Adapt<Display>();

        var createdDisplay = await _repository.AddAsync(display, cancellationToken);

        var dto = createdDisplay.Adapt<DisplayDto>();

        return Result<DisplayDto>.Success(dto);
    }
}
