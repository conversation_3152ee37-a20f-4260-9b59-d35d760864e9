﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Migrators.Migrations.Application
{
    /// <inheritdoc />
    public partial class Actions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DisplayFormat",
                schema: "Genp",
                table: "Metadata",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsEditable",
                schema: "Genp",
                table: "Metadata",
                type: "boolean",
                nullable: true,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSearchable",
                schema: "Genp",
                table: "Metadata",
                type: "boolean",
                nullable: true,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSortable",
                schema: "Genp",
                table: "Metadata",
                type: "boolean",
                nullable: true,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "SortOrder",
                schema: "Genp",
                table: "Metadata",
                type: "integer",
                nullable: true,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "Actions",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "TEXT", nullable: true),
                    EndpointTemplate = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    NavigationTarget = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Icon = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ButtonStyle = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ConfirmationMessage = table.Column<string>(type: "TEXT", nullable: true),
                    SuccessMessage = table.Column<string>(type: "TEXT", nullable: true),
                    ErrorMessage = table.Column<string>(type: "TEXT", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Actions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Displays",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "TEXT", nullable: true),
                    DisplayName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    RouteTemplate = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Icon = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    SortOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Displays", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DisplayActions",
                schema: "Genp",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ObjectId = table.Column<Guid>(type: "uuid", nullable: false),
                    DisplayId = table.Column<Guid>(type: "uuid", nullable: false),
                    ActionId = table.Column<Guid>(type: "uuid", nullable: false),
                    AccessLevel = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Public"),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    SortOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsVisibleInToolbar = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsVisibleInContextMenu = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    IsVisibleInRowActions = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DisplayActions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DisplayActions_Actions_ActionId",
                        column: x => x.ActionId,
                        principalSchema: "Genp",
                        principalTable: "Actions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DisplayActions_Displays_DisplayId",
                        column: x => x.DisplayId,
                        principalSchema: "Genp",
                        principalTable: "Displays",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DisplayActions_Objects_ObjectId",
                        column: x => x.ObjectId,
                        principalSchema: "Genp",
                        principalTable: "Objects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Metadata_IsEditable",
                schema: "Genp",
                table: "Metadata",
                column: "IsEditable",
                filter: "\"IsEditable\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Metadata_IsSearchable",
                schema: "Genp",
                table: "Metadata",
                column: "IsSearchable",
                filter: "\"IsSearchable\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Metadata_IsSortable",
                schema: "Genp",
                table: "Metadata",
                column: "IsSortable",
                filter: "\"IsSortable\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Metadata_SortOrder",
                schema: "Genp",
                table: "Metadata",
                column: "SortOrder");

            migrationBuilder.CreateIndex(
                name: "IX_Actions_Id",
                schema: "Genp",
                table: "Actions",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_Actions_IsActive",
                schema: "Genp",
                table: "Actions",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Actions_Name",
                schema: "Genp",
                table: "Actions",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_DisplayActions_AccessLevel",
                schema: "Genp",
                table: "DisplayActions",
                column: "AccessLevel");

            migrationBuilder.CreateIndex(
                name: "IX_DisplayActions_ActionId",
                schema: "Genp",
                table: "DisplayActions",
                column: "ActionId");

            migrationBuilder.CreateIndex(
                name: "IX_DisplayActions_DisplayId",
                schema: "Genp",
                table: "DisplayActions",
                column: "DisplayId");

            migrationBuilder.CreateIndex(
                name: "IX_DisplayActions_Id",
                schema: "Genp",
                table: "DisplayActions",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_DisplayActions_IsActive",
                schema: "Genp",
                table: "DisplayActions",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_DisplayActions_IsDefault",
                schema: "Genp",
                table: "DisplayActions",
                column: "IsDefault",
                filter: "\"IsDefault\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_DisplayActions_ObjectId",
                schema: "Genp",
                table: "DisplayActions",
                column: "ObjectId");

            migrationBuilder.CreateIndex(
                name: "IX_DisplayActions_ObjectId_DisplayId_ActionId",
                schema: "Genp",
                table: "DisplayActions",
                columns: new[] { "ObjectId", "DisplayId", "ActionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DisplayActions_SortOrder",
                schema: "Genp",
                table: "DisplayActions",
                column: "SortOrder");

            migrationBuilder.CreateIndex(
                name: "IX_Displays_Id",
                schema: "Genp",
                table: "Displays",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_Displays_IsActive",
                schema: "Genp",
                table: "Displays",
                column: "IsActive",
                filter: "\"IsActive\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Displays_IsDefault",
                schema: "Genp",
                table: "Displays",
                column: "IsDefault",
                filter: "\"IsDefault\" = true AND \"IsDeleted\" = false");

            migrationBuilder.CreateIndex(
                name: "IX_Displays_Name",
                schema: "Genp",
                table: "Displays",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_Displays_SortOrder",
                schema: "Genp",
                table: "Displays",
                column: "SortOrder");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DisplayActions",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Actions",
                schema: "Genp");

            migrationBuilder.DropTable(
                name: "Displays",
                schema: "Genp");

            migrationBuilder.DropIndex(
                name: "IX_Metadata_IsEditable",
                schema: "Genp",
                table: "Metadata");

            migrationBuilder.DropIndex(
                name: "IX_Metadata_IsSearchable",
                schema: "Genp",
                table: "Metadata");

            migrationBuilder.DropIndex(
                name: "IX_Metadata_IsSortable",
                schema: "Genp",
                table: "Metadata");

            migrationBuilder.DropIndex(
                name: "IX_Metadata_SortOrder",
                schema: "Genp",
                table: "Metadata");

            migrationBuilder.DropColumn(
                name: "DisplayFormat",
                schema: "Genp",
                table: "Metadata");

            migrationBuilder.DropColumn(
                name: "IsEditable",
                schema: "Genp",
                table: "Metadata");

            migrationBuilder.DropColumn(
                name: "IsSearchable",
                schema: "Genp",
                table: "Metadata");

            migrationBuilder.DropColumn(
                name: "IsSortable",
                schema: "Genp",
                table: "Metadata");

            migrationBuilder.DropColumn(
                name: "SortOrder",
                schema: "Genp",
                table: "Metadata");
        }
    }
}
