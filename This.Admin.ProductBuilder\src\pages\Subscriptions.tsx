import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Table, Badge, Form, InputGroup, Alert, Pagination, Spinner } from 'react-bootstrap';
import { AddSubscriptionModal } from '../components/AddSubscriptionModal';
import { SearchableDropdown } from '../components/atoms/SearchableDropdown';

import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { subscriptionService, SubscriptionUtils } from '../services/subscriptionService';

// Local TenantResponse interface to avoid import issues
interface TenantResponse {
  id: string;
  name: string;
  connectionString: string;
  readReplicaConnectionString: string | null;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string | null;
}

// Local type definitions to avoid import issues
interface SubscriptionSummary {
  activeSubscriptions: number;
  expiredSubscriptions: number;
  expiringIn30Days: number;
  uniqueTenants: number;
  uniqueProducts: number;
  subscriptionsByStatus: Record<string, number>;
  subscriptionsByType: Record<string, number>;
}

// Local subscription interface based on API response
interface Subscription {
  id: string;
  tenantId: string;
  tenantName: string | null;
  productId: string;
  productName: string | null;
  subscriptionType: string;
  status: string;
  startDate: string;
  endDate: string | null;
  autoRenew: boolean;
  pricingTier: string | null;
  version: string;
  templateJson: string;
  isActive: boolean;
  metadataCount: number;
  createdAt: string;
  createdBy: string;
  modifiedAt: string | null;
  modifiedBy: string | null;
  isExpired: boolean;
  daysUntilExpiration: number | null;
  // Additional computed properties for UI compatibility
  name?: string;
  type?: string;
  userCount?: number;
  price?: string;
}

export const Subscriptions: React.FC = () => {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingSubscription, setEditingSubscription] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTenant, setSelectedTenant] = useState(''); // Start with empty string for "All Tenants"
  const [summary, setSummary] = useState<SubscriptionSummary | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Available tenants - start with empty array, load from API
  const [availableTenants, setAvailableTenants] = useState<TenantResponse[]>([]);

  // Load available tenants
  const loadAvailableTenants = async () => {
    try {
      const tenants = await subscriptionService.getAvailableTenants();
      setAvailableTenants(tenants);
      // Keep selectedTenant as empty string for "All Tenants" by default
    } catch (error) {
      console.error('Error loading available tenants:', error);
      // Fallback to default tenant
      const fallbackTenant: TenantResponse = {
        id: 'lrbnewqa',
        name: 'lrbnewqa',
        connectionString: '',
        readReplicaConnectionString: null,
        adminEmail: '<EMAIL>',
        isActive: true,
        validUpto: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        issuer: null
      };
      setAvailableTenants([fallbackTenant]);
      // Keep selectedTenant as empty string for "All Tenants" by default
    }
  };

  // Fetch subscriptions from comprehensive API
  const fetchSubscriptions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Build comprehensive API parameters
      const params: any = {
        pageNumber: currentPage,
        pageSize: itemsPerPage,
        isActive: statusFilter === 'all' ? undefined : statusFilter === 'active',
        searchTerm: searchTerm || undefined,
        status: statusFilter === 'all' ? undefined : statusFilter,
        includeSummary: true
      };

      // Only include tenantId if a specific tenant is selected (not "All Tenants")
      if (selectedTenant && selectedTenant.trim() !== '') {
        params.tenantId = selectedTenant;
      }

      console.log('🔍 Fetching subscriptions with params:', {
        ...params,
        tenantFilter: selectedTenant ? `Specific tenant: ${selectedTenant}` : 'All Tenants (no filter)'
      });

      const response = await subscriptionService.getComprehensiveSubscriptions(params);

      if (response.succeeded && response.data) {
        setSubscriptions(response.data.subscriptions);
        setTotalItems(response.data.totalCount);
        setTotalPages(response.data.totalPages);

        // Set summary if available from comprehensive API
        if (response.data.summary) {
          setSummary(response.data.summary);
        }
      } else {
        throw new Error(response.message || 'Failed to fetch subscriptions');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching subscriptions:', err);

      // Show user-friendly error message
      if (errorMessage.includes('fetch')) {
        toast.error('Unable to connect to the server. Please check if the API is running.');
      } else {
        toast.error(`Failed to load subscriptions: ${errorMessage}`);
      }

      // Set empty state
      setSubscriptions([]);
      setTotalItems(0);
      setTotalPages(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch summary statistics
  const fetchSummary = async () => {
    try {
      const summaryData = await subscriptionService.getSubscriptionSummary();
      setSummary(summaryData);
    } catch (error) {
      console.error('Error fetching summary:', error);
      // Don't show error for summary, it's not critical
      // Set default summary to avoid null errors
      setSummary({
        activeSubscriptions: 0,
        expiredSubscriptions: 0,
        expiringIn30Days: 0,
        uniqueTenants: 0,
        uniqueProducts: 0,
        subscriptionsByStatus: {},
        subscriptionsByType: {}
      });
    }
  };

  // Load initial data
  useEffect(() => {
    loadAvailableTenants();
    fetchSummary();
  }, []);

  // Fetch subscriptions on component mount and when dependencies change
  useEffect(() => {
    fetchSubscriptions();
  }, [currentPage, itemsPerPage, statusFilter, selectedTenant]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        fetchSubscriptions();
      } else {
        setCurrentPage(1); // This will trigger fetchSubscriptions via the above useEffect
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // All filtering and pagination is handled by the API
  const filteredSubscriptions = subscriptions;
  const paginatedSubscriptions = subscriptions; // API handles pagination

  // Pagination calculations for display
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

  // Handle tenant change
  const handleTenantChange = (tenant: string) => {
    setSelectedTenant(tenant);
    setCurrentPage(1);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchSubscriptions();
  };

  // Handle adding new subscription
  const handleAddSubscription = (newSubscription: any) => {
    // Convert the modal's subscription format to our local format
    const subscription: Subscription = {
      id: newSubscription.id,
      tenantId: newSubscription.tenantId || '',
      tenantName: newSubscription.tenantName || null,
      productId: newSubscription.productId || '',
      productName: newSubscription.name || newSubscription.productName || null,
      subscriptionType: newSubscription.type || newSubscription.subscriptionType || 'basic',
      status: newSubscription.status || 'active',
      startDate: newSubscription.startDate,
      endDate: newSubscription.endDate || null,
      autoRenew: newSubscription.autoRenew || false,
      pricingTier: newSubscription.type || newSubscription.pricingTier || null,
      version: newSubscription.version || '1.0.0',
      templateJson: newSubscription.templateJson || '{}',
      isActive: newSubscription.status === 'active',
      metadataCount: newSubscription.metadataCount || 0,
      createdAt: new Date().toISOString(),
      createdBy: 'system',
      modifiedAt: null,
      modifiedBy: null,
      isExpired: false,
      daysUntilExpiration: null,
      name: newSubscription.name,
      type: newSubscription.type,
      userCount: newSubscription.userCount || 1,
      price: newSubscription.price || '0'
    };
    setSubscriptions(prev => [subscription, ...prev]);
  };

  // Handle editing subscription
  const handleEditSubscription = (subscription: any) => {
    setEditingSubscription(subscription);
    setShowEditModal(true);
  };

  // Handle updating subscription
  const handleUpdateSubscription = (updatedSubscription: any) => {
    setSubscriptions(prev =>
      prev.map(sub =>
        sub.id === updatedSubscription.id ? { ...sub, ...updatedSubscription } : sub
      )
    );
    setShowEditModal(false);
    setEditingSubscription(null);
  };

  // Handle opening add modal
  const handleShowAddModal = () => {
    setShowAddModal(true);
  };

  // Handle closing add modal
  const handleCloseAddModal = () => {
    setShowAddModal(false);
  };

  // Handle closing edit modal
  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setEditingSubscription(null);
  };

  // Helper function to get status badge variant
  const getStatusBadgeVariant = (status: string): string => {
    return SubscriptionUtils.getStatusBadgeVariant(status);
  };

  // Helper function to get type badge variant
  const getTypeBadgeVariant = (type: string): string => {
    return SubscriptionUtils.getTypeBadgeVariant(type);
  };

  return (
    <div className="d-flex flex-column min-vh-100">
      {/* Main Content */}
      <main className="flex-grow-1" style={{ padding: '0.25rem 1rem 0.25rem 1rem', overflow: 'visible', position: 'relative' }}>
        <div style={{ maxWidth: 'none', width: '100%', overflow: 'visible', position: 'relative' }}>


          {/* Search and Filters with Action Buttons */}
          <div className="modern-card mb-1" style={{ overflow: 'visible', zIndex: 1, position: 'relative' }}>
            <div className="modern-card-body" style={{ padding: '1rem', overflow: 'visible' }}>
              <Row className="g-3 align-items-center" style={{ position: 'relative', zIndex: 10 }}>
                <Col md={3}>
                  <InputGroup className="shadow-sm">
                    <InputGroup.Text className="bg-light border-end-0" style={{ borderColor: 'var(--card-border)' }}>
                      🔍
                    </InputGroup.Text>
                    <Form.Control
                      type="text"
                      placeholder="Search subscriptions by name or ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="border-start-0"
                      style={{ borderColor: 'var(--card-border)' }}
                    />
                  </InputGroup>
                </Col>
                <Col md={2}>
                  <SearchableDropdown
                    value={selectedTenant}
                    onChange={handleTenantChange}
                    options={[
                      { value: '', label: 'All Tenants' },
                      ...availableTenants.map((tenant) => ({
                        value: tenant.name,
                        label: tenant.name
                      }))
                    ]}
                    placeholder="All Tenants"
                    disabled={availableTenants.length === 0}
                    showSearch={true}
                    searchPlaceholder="Search tenants..."
                    className="shadow-sm"
                  />
                </Col>
                <Col md={2}>
                  <SearchableDropdown
                    value={statusFilter}
                    onChange={setStatusFilter}
                    options={[
                      { value: 'all', label: 'All Status' },
                      { value: 'active', label: 'Active' },
                      { value: 'inactive', label: 'Inactive' },
                      { value: 'expired', label: 'Expired' }
                    ]}
                    placeholder="All Status"
                    showSearch={false}
                    className="shadow-sm"
                  />
                </Col>
                <Col md={5} className="text-end">
                  <div className="d-flex gap-2 justify-content-end">
                    <Button
                      variant="outline-primary"
                      onClick={handleRefresh}
                      disabled={isLoading}
                      size="sm"
                      className="d-flex align-items-center gap-2"
                    >
                      {isLoading ? (
                        <Spinner animation="border" size="sm" />
                      ) : (
                        <span>🔄</span>
                      )}
                      Refresh
                    </Button>
                    <Button
                      variant="primary"
                      className="d-flex align-items-center gap-2 fw-semibold"
                      onClick={handleShowAddModal}
                    >
                      <span>+</span>
                      Add Subscription
                    </Button>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
          {/* Compact Stats Cards */}
          <Row className="mb-1 g-2" style={{ zIndex: 1, position: 'relative' }}>
            <Col md={3}>
              <div className="stats-card">
                <div className="text-center" style={{ padding: '0.75rem' }}>
                  <div className="stats-number">{totalItems}</div>
                  <div className="stats-label">Total Subscriptions</div>
                </div>
              </div>
            </Col>
            <Col md={3}>
              <div className="stats-card">
                <div className="text-center" style={{ padding: '0.75rem' }}>
                  <div className="stats-number">
                    {summary?.activeSubscriptions || subscriptions.filter(s => s.status === 'active').length}
                  </div>
                  <div className="stats-label">Active</div>
                </div>
              </div>
            </Col>
            <Col md={3}>
              <div className="stats-card">
                <div className="text-center" style={{ padding: '0.75rem' }}>
                  <div className="stats-number">
                    {summary?.expiredSubscriptions || subscriptions.filter(s => s.status === 'expired').length}
                  </div>
                  <div className="stats-label">Expired</div>
                </div>
              </div>
            </Col>
            <Col md={3}>
              <div className="stats-card">
                <div className="text-center" style={{ padding: '0.75rem' }}>
                  <div className="stats-number">
                    {summary?.expiringIn30Days || subscriptions.filter(s => {
                      const daysUntilExpiry = s.daysUntilExpiration;
                      return daysUntilExpiry !== null && daysUntilExpiry <= 30 && daysUntilExpiry > 0;
                    }).length}
                  </div>
                  <div className="stats-label">Expiring Soon</div>
                </div>
              </div>
            </Col>
          </Row>

          {/* Error Alert */}
          {error && (
            <Alert variant="danger" className="mb-3" dismissible onClose={() => setError(null)}>
              <Alert.Heading>Error Loading Subscriptions</Alert.Heading>
              <p>{error}</p>
              <Button variant="outline-danger" size="sm" onClick={handleRefresh}>
                Try Again
              </Button>
            </Alert>
          )}

          {/* Modern Subscriptions Table */}
          <div className="modern-card" style={{ display: 'flex', flexDirection: 'column', height: 'calc(100vh - 180px)', overflow: 'visible', zIndex: 1, position: 'relative' }}>
            <div className="modern-card-header" style={{ flexShrink: 0 }}>
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0 fw-semibold">Subscriptions</h5>
                <div className="d-flex align-items-center gap-2">
                  {isLoading && <Spinner animation="border" size="sm" />}
                  <Badge bg="secondary">{totalItems} total</Badge>
                </div>
              </div>
            </div>
            <div className="modern-card-body p-0" style={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'visible' }}>
              {isLoading && subscriptions.length === 0 ? (
                <div className="text-center py-5">
                  <Spinner animation="border" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </Spinner>
                  <p className="mt-2 text-muted">Loading subscriptions...</p>
                </div>
              ) : filteredSubscriptions.length === 0 ? (
                <div className="text-center py-5">
                  <div className="text-muted">
                    <div className="h4 mb-3">📋</div>
                    <p>No subscriptions found matching your criteria</p>
                  </div>
                </div>
              ) : (
                <div style={{ flex: 1, overflow: 'auto' }}>
                  <Table hover responsive className="mb-0 table-sm">
                  <thead style={{ background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)' }}>
                    <tr>
                      <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Product Name</th>
                      <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Tenant</th>
                      <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Type</th>
                      <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Status</th>
                      <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Period</th>
                      <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Version</th>
                      <th className="fw-semibold text-dark py-2 ps-3" style={{ borderTop: 'none', borderLeft: 'none', borderRight: 'none', borderBottom: '2px solid #e9ecef' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedSubscriptions.map((subscription) => (
                      <tr key={subscription.id}>
                        <td className="ps-3">
                          <div>
                            <div className="fw-medium">{subscription.productName || 'Unknown Product'}</div>
                            <div className="text-muted small">ID: {subscription.id.substring(0, 8)}...</div>
                          </div>
                        </td>
                        <td className="ps-3">
                          <div>
                            <div className="fw-medium">{subscription.tenantName || subscription.tenantId}</div>
                            {subscription.tenantName && subscription.tenantName !== subscription.tenantId && (
                              <div className="text-muted small">ID: {subscription.tenantId}</div>
                            )}
                          </div>
                        </td>
                        <td className="ps-3">
                          <Badge bg={SubscriptionUtils.getTypeBadgeVariant(subscription.subscriptionType)}>
                            {subscription.subscriptionType.charAt(0).toUpperCase() + subscription.subscriptionType.slice(1)}
                          </Badge>
                        </td>
                        <td className="ps-3">
                          <Badge bg={SubscriptionUtils.getStatusBadgeVariant(subscription.status)}>
                            {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                          </Badge>
                        </td>
                        <td className="ps-3">
                          <div className="small">
                            <div className="fw-medium">
                              {new Date(subscription.startDate).toLocaleDateString()} - {subscription.endDate ? new Date(subscription.endDate).toLocaleDateString() : 'No end date'}
                            </div>
                            {subscription.daysUntilExpiration !== null && subscription.daysUntilExpiration <= 30 && (
                              <div className="text-warning small">
                                {subscription.daysUntilExpiration > 0
                                  ? `${subscription.daysUntilExpiration} days left`
                                  : 'Expired'
                                }
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="ps-3">
                          <div className="small">
                            <div className="fw-medium">v{subscription.version}</div>
                          </div>
                        </td>
                        <td className="ps-3">
                          <div className="d-flex gap-2">
                            <Button
                              variant="outline-primary"
                              size="sm"
                              title="View Details"
                              onClick={() => console.log('View subscription:', subscription.id)}
                            >
                              👁️
                            </Button>
                            <Button
                              variant="outline-secondary"
                              size="sm"
                              title="Edit Subscription"
                              onClick={() => handleEditSubscription(subscription)}
                            >
                              ✏️
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
                </div>
              )}

            {/* Pagination */}
            {filteredSubscriptions.length > 0 && (
              <div className="d-flex justify-content-between align-items-center" style={{ padding: '1rem', borderTop: '1px solid var(--card-border)', flexShrink: 0 }}>
                  <div className="d-flex align-items-center gap-3">
                    <span className="text-muted small">
                      Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} entries
                    </span>
                    <div className="d-flex align-items-center gap-2">
                      <span className="text-muted small">Show:</span>
                      <SearchableDropdown
                        value={itemsPerPage.toString()}
                        onChange={(value) => {
                          setItemsPerPage(Number(value));
                          setCurrentPage(1);
                        }}
                        options={[
                          { value: '5', label: '5' },
                          { value: '10', label: '10' },
                          { value: '25', label: '25' },
                          { value: '50', label: '50' }
                        ]}
                        size="sm"
                        showSearch={false}
                        className="d-inline-block"
                        style={{ width: '80px' }}
                      />
                    </div>
                  </div>

                  {totalPages > 1 && (
                    <Pagination size="sm" className="mb-0">
                      <Pagination.First
                        onClick={() => setCurrentPage(1)}
                        disabled={currentPage === 1}
                      />
                      <Pagination.Prev
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      />

                      {/* Page numbers */}
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <Pagination.Item
                            key={pageNum}
                            active={pageNum === currentPage}
                            onClick={() => setCurrentPage(pageNum)}
                          >
                            {pageNum}
                          </Pagination.Item>
                        );
                      })}

                      <Pagination.Next
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                      />
                      <Pagination.Last
                        onClick={() => setCurrentPage(totalPages)}
                        disabled={currentPage === totalPages}
                      />
                    </Pagination>
                  )}
              </div>
            )}
          </div>
        </div>
        </div>
      </main>

      {/* Add Subscription Modal */}
      <AddSubscriptionModal
        show={showAddModal}
        onHide={handleCloseAddModal}
        onSubscriptionAdded={handleAddSubscription}
      />

      {/* Edit Subscription Modal */}
      <AddSubscriptionModal
        show={showEditModal}
        onHide={handleCloseEditModal}
        mode="edit"
        editSubscription={editingSubscription}
        onSubscriptionUpdated={handleUpdateSubscription}
        onSubscriptionAdded={handleAddSubscription} // Required prop, but not used in edit mode
      />

      {/* Toast Container for notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </div>
  );
};