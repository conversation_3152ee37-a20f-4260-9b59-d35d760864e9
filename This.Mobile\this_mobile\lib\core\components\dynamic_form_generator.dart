import 'package:flutter/material.dart';
import '../models/field_metadata.dart';
import '../services/metadata_service.dart';
import 'widgets/dynamic_form_field.dart';

/// Dynamic form generator that creates forms based on metadata
class DynamicFormGenerator extends StatefulWidget {
  final String objectType;
  final Map<String, dynamic>? initialData;
  final Function(Map<String, dynamic>) onSubmit;
  final VoidCallback? onCancel;
  final VoidCallback? onSuccess;
  final bool isEditMode;
  final String? title;
  final MetadataService metadataService;
  final String? objectId; // Object ID for the specific object type
  final Map<String, dynamic>? existingData; // Existing data for edit mode

  const DynamicFormGenerator({
    super.key,
    required this.objectType,
    required this.onSubmit,
    required this.metadataService,
    this.initialData,
    this.onCancel,
    this.onSuccess,
    this.isEditMode = false,
    this.title,
    this.objectId,
    this.existingData,
  });

  @override
  State<DynamicFormGenerator> createState() => _DynamicFormGeneratorState();
}

class _DynamicFormGeneratorState extends State<DynamicFormGenerator> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final Map<String, dynamic> _formData = {};
  final Map<String, List<String>> _validationErrors = {};

  List<FieldMetadata> _metadata = [];
  bool _isLoading = true;
  String? _error;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _loadMetadata();
    _initializeFormData();
  }

  /// Load metadata for the object type
  Future<void> _loadMetadata() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final metadata = await widget.metadataService.fetchMetadata(widget.objectType);
      print('Original metadata count: ${metadata.length}');

      // Deduplicate fields to prevent duplicate form inputs (like ServingSize/servingSize)
      final deduplicatedMetadata = _deduplicateFields(metadata);
      print('After deduplication count: ${deduplicatedMetadata.length}');

      // Sort metadata by category and name for consistent display
      final sortedMetadata = deduplicatedMetadata.toList()
        ..sort((a, b) {
          final categoryCompare = a.category.compareTo(b.category);
          if (categoryCompare != 0) return categoryCompare;
          return a.displayLabel.compareTo(b.displayLabel);
        });

      // Filter metadata to only show visible fields
      final visibleMetadata = sortedMetadata.where((field) => field.isVisible).toList();

      setState(() {
        _metadata = visibleMetadata;
        _isLoading = false;
      });

      _initializeFormData();
    } catch (e) {
      setState(() {
        _error = 'Failed to load form metadata: $e';
        _isLoading = false;
      });
    }
  }

  /// Initialize form data with initial values or defaults
  void _initializeFormData() {
    _formData.clear();

    if (widget.initialData != null) {
      _formData.addAll(widget.initialData!);
    }

    // Set default values from metadata
    for (final field in _metadata) {
      if (!_formData.containsKey(field.name)) {
        _formData[field.name] = _getDefaultValue(field);
      }
    }
  }

  /// Get default value for a field based on its type
  dynamic _getDefaultValue(FieldMetadata field) {
    switch (field.inputType.toLowerCase()) {
      case 'checkbox':
        // For multi-select checkboxes, return empty list
        // For single boolean checkbox, return false
        if (field.values != null && field.values!.isNotEmpty) {
          return <String>[]; // Multi-select checkbox
        }
        return false; // Single boolean checkbox

      case 'radio':
      case 'dropdown':
      case 'select':
        return null; // No selection by default

      case 'number':
      case 'currency':
      case 'percentage':
        return null; // Let the field handle empty state

      case 'date':
      case 'datetime-local':
      case 'time':
        return null; // No date/time selected by default

      case 'year':
      case 'month':
      case 'day':
        return null; // No numeric value selected by default

      case 'email':
      case 'phone':
      case 'text':
      case 'textarea':
      default:
        return ''; // Empty string for text fields
    }
  }

  /// Handle field value changes
  void _onFieldChanged(String fieldName, dynamic value) {
    setState(() {
      _formData[fieldName] = value;
      // Clear validation errors for this field
      _validationErrors.remove(fieldName);
    });
  }

  /// Handle field validation
  void _onFieldValidation(String fieldName, List<String> errors) {
    // Defer setState to avoid calling it during build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          if (errors.isEmpty) {
            _validationErrors.remove(fieldName);
          } else {
            _validationErrors[fieldName] = errors;
          }
        });
      }
    });
  }

  /// Validate the entire form
  bool _validateForm() {
    bool isValid = true;
    final errors = <String, List<String>>{};

    for (final field in _metadata) {
      final value = _formData[field.name];
      final fieldErrors = <String>[];

      // Required field validation
      if (field.isRequired && _isValueEmpty(value)) {
        fieldErrors.add('${field.displayLabel} is required');
      }

      // Type-specific validation
      if (!_isValueEmpty(value)) {
        final typeErrors = _validateFieldType(field, value);
        fieldErrors.addAll(typeErrors);
      }

      if (fieldErrors.isNotEmpty) {
        errors[field.name] = fieldErrors;
        isValid = false;
      }
    }

    setState(() {
      _validationErrors.clear();
      _validationErrors.addAll(errors);
    });

    // Debug: Log validation errors in debug mode
    if (!isValid) {
      assert(() {
        debugPrint('Validation failed with errors:');
        for (final entry in errors.entries) {
          debugPrint('  ${entry.key}: ${entry.value}');
        }
        return true;
      }());
    }

    return isValid;
  }

  /// Check if a value is empty
  bool _isValueEmpty(dynamic value) {
    if (value == null) return true;
    if (value is String) return value.trim().isEmpty;
    if (value is List) return value.isEmpty;
    if (value is bool) return false; // Boolean values are never considered empty
    if (value is num) return false; // Numeric values (including 0) are never considered empty
    return false;
  }

  /// Validate field based on its type
  List<String> _validateFieldType(FieldMetadata field, dynamic value) {
    final errors = <String>[];

    // Skip validation for null/empty values (required validation is handled separately)
    if (_isValueEmpty(value)) {
      return errors;
    }

    final stringValue = value.toString();

    switch (field.inputType.toLowerCase()) {
      case 'email':
        if (!_isValidEmail(stringValue)) {
          errors.add('Please enter a valid email address');
        }
        break;

      case 'phone':
        if (!_isValidPhone(stringValue)) {
          errors.add('Please enter a valid phone number');
        }
        break;

      case 'number':
      case 'currency':
      case 'percentage':
        if (value is! num && double.tryParse(stringValue) == null) {
          errors.add('Please enter a valid number');
        }
        break;

      case 'text':
      case 'textarea':
        if (field.minLength != null && stringValue.length < field.minLength!) {
          errors.add('Minimum length is ${field.minLength} characters');
        }
        if (field.maxLength != null && stringValue.length > field.maxLength!) {
          errors.add('Maximum length is ${field.maxLength} characters');
        }
        if (field.validationPattern != null && field.validationPattern!.isNotEmpty) {
          try {
            final regex = RegExp(field.validationPattern!);
            if (!regex.hasMatch(stringValue)) {
              errors.add('Please enter a valid format');
            }
          } catch (e) {
            // Invalid regex pattern, skip validation
          }
        }
        break;

      case 'year':
        if (value is! int && int.tryParse(stringValue) == null) {
          errors.add('Please enter a valid year');
        } else {
          final year = value is int ? value : int.parse(stringValue);
          if (year < 1900 || year > 2100) {
            errors.add('Year must be between 1900 and 2100');
          }
        }
        break;

      case 'month':
        if (value is! int && int.tryParse(stringValue) == null) {
          errors.add('Please enter a valid month');
        } else {
          final month = value is int ? value : int.parse(stringValue);
          if (month < 1 || month > 12) {
            errors.add('Month must be between 1 and 12');
          }
        }
        break;

      case 'day':
        if (value is! int && int.tryParse(stringValue) == null) {
          errors.add('Please enter a valid day');
        } else {
          final day = value is int ? value : int.parse(stringValue);
          if (day < 1 || day > 31) {
            errors.add('Day must be between 1 and 31');
          }
        }
        break;
    }

    return errors;
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }

  /// Validate phone format (basic validation)
  bool _isValidPhone(String phone) {
    return RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(phone);
  }

  /// Submit the form
  Future<void> _submitForm() async {
    if (_isSubmitting) return;

    if (!_validateForm()) {
      // Count total errors
      final totalErrors = _validationErrors.values.fold<int>(
        0, (sum, errors) => sum + errors.length,
      );

      // Show specific error message
      final errorFields = _validationErrors.keys.toList();
      final fieldNames = errorFields.map((fieldName) {
        final field = _metadata.firstWhere(
          (f) => f.name == fieldName,
          orElse: () => FieldMetadata(
            id: fieldName,
            name: fieldName,
            displayLabel: fieldName,
            inputType: 'text',
            isRequired: false,
            dataTypeName: 'text',
            category: 'General',
            uiComponent: 'ThisTextInput',
          ),
        );
        return field.displayLabel;
      }).join(', ');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            totalErrors == 1
                ? 'Please fix the error in: $fieldNames'
                : 'Please fix $totalErrors errors in: $fieldNames',
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Prepare metadata payload for API submission
      final metadataProperties = _prepareMetadataPayload();
      widget.onSubmit(metadataProperties);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error submitting form: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  /// Prepare metadata payload for API submission
  Map<String, dynamic> _prepareMetadataPayload() {
    final metadataProperties = <String, dynamic>{};

    // Add ObjectId if this is for a specific object type
    if (widget.objectId != null) {
      metadataProperties['ObjectId'] = widget.objectId;
    }

    // Add RefId if this is an edit operation
    if (widget.isEditMode && widget.existingData != null) {
      final refId = widget.existingData!['refId'] ?? widget.existingData!['id'];
      if (refId != null) {
        metadataProperties['RefId'] = refId;
      }
    }

    // Add form field data using metadata keys
    for (final field in _metadata) {
      final value = _formData[field.name];

      // Skip empty values unless they are required
      if (!field.isRequired && _isValueEmpty(value)) {
        continue;
      }
      // Use metadataKey for API payload, falling back to field name if not specified
      final key = field.metadataKey ?? field.name;
      final convertedValue = _convertValueForSubmission(field, value);

      // Only include non-empty values and required fields
      if (!_isValueEmpty(convertedValue) || field.isRequired) {
        metadataProperties[key] = convertedValue;
      }
    }

    return metadataProperties;
  }

  /// Convert value to appropriate type for submission
  dynamic _convertValueForSubmission(FieldMetadata field, dynamic value) {
    if (value == null) return null;

    switch (field.inputType.toLowerCase()) {
      case 'number':
        if (value is num) return value;
        if (value is String && value.isNotEmpty) {
          return double.tryParse(value) ?? int.tryParse(value);
        }
        return null;

      case 'currency':
      case 'percentage':
        if (value is num) return value.toDouble();
        if (value is String && value.isNotEmpty) {
          // Remove currency symbols and percentage signs
          final cleanValue = value.replaceAll(RegExp(r'[^0-9.-]'), '');
          return double.tryParse(cleanValue);
        }
        return null;

      case 'checkbox':
        if (value is List) return value;
        if (value is bool) return value;
        if (value is String) {
          if (value.toLowerCase() == 'true') return true;
          if (value.toLowerCase() == 'false') return false;
          // Handle comma-separated values for multi-select
          return value.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
        }
        return value;

      case 'date':
      case 'datetime-local':
        if (value is DateTime) return value.toIso8601String();
        if (value is String && value.isNotEmpty) {
          final date = DateTime.tryParse(value);
          return date?.toIso8601String();
        }
        return null;

      case 'time':
        if (value is TimeOfDay) {
          return '${value.hour.toString().padLeft(2, '0')}:${value.minute.toString().padLeft(2, '0')}';
        }
        return value;

      case 'year':
      case 'month':
      case 'day':
        if (value is int) return value;
        if (value is String && value.isNotEmpty) {
          return int.tryParse(value);
        }
        return null;

      default:
        return value;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? '${widget.isEditMode ? 'Edit' : 'New'} ${widget.objectType}'),
        actions: [
          // Debug: Validate button
          TextButton(
            onPressed: () {
              _validateForm();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Validation ${_validationErrors.isEmpty ? 'passed' : 'failed'}'),
                  backgroundColor: _validationErrors.isEmpty ? Colors.green : Colors.red,
                ),
              );
            },
            child: const Text('Check'),
          ),
          if (widget.onCancel != null)
            TextButton(
              onPressed: widget.onCancel,
              child: const Text('Cancel'),
            ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: _isSubmitting ? null : _submitForm,
            child: _isSubmitting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(widget.isEditMode ? 'Update' : 'Create'),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading form...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadMetadata,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_metadata.isEmpty) {
      return const Center(
        child: Text('No form fields available'),
      );
    }

    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Validation summary (if there are errors)
          if (_validationErrors.isNotEmpty) _buildValidationSummary(),

          // Group fields by category
          ..._buildFieldsByCategory(),

          const SizedBox(height: 32),

          // Submit button (mobile)
          if (MediaQuery.of(context).size.width < 600) ...[
            ElevatedButton(
              onPressed: _isSubmitting ? null : _submitForm,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isSubmitting
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 12),
                        Text('Submitting...'),
                      ],
                    )
                  : Text(widget.isEditMode ? 'Update ${widget.objectType}' : 'Create ${widget.objectType}'),
            ),
            const SizedBox(height: 16),
            if (widget.onCancel != null)
              OutlinedButton(
                onPressed: widget.onCancel,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Cancel'),
              ),
          ],
        ],
      ),
    );
  }

  /// Build fields grouped by category
  List<Widget> _buildFieldsByCategory() {
    final fieldsByCategory = <String, List<FieldMetadata>>{};

    // Group fields by category
    for (final field in _metadata) {
      final category = field.category.isEmpty ? 'General' : field.category;
      fieldsByCategory.putIfAbsent(category, () => []).add(field);
    }

    final widgets = <Widget>[];

    for (final entry in fieldsByCategory.entries) {
      final category = entry.key;
      final fields = entry.value;

      // Add category header
      if (fieldsByCategory.length > 1) {
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(top: 24, bottom: 12),
            child: Text(
              category,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        );
      }

      // Add fields
      for (final field in fields) {
        widgets.add(_buildFormField(field));

        // Add validation errors
        final errors = _validationErrors[field.name];
        if (errors != null && errors.isNotEmpty) {
          widgets.add(
            Container(
              margin: const EdgeInsets.only(left: 12, top: 4, bottom: 8),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Error${errors.length > 1 ? 's' : ''}:',
                        style: const TextStyle(
                          color: Colors.red,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  ...errors.map((error) => Padding(
                    padding: const EdgeInsets.only(left: 20),
                    child: Text(
                      '• $error',
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  )),
                ],
              ),
            ),
          );
        }
      }
    }

    return widgets;
  }

  /// Build validation summary widget
  Widget _buildValidationSummary() {
    final totalErrors = _validationErrors.values.fold<int>(
      0, (sum, errors) => sum + errors.length,
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        border: Border.all(color: Colors.red.withValues(alpha: 0.5)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error,
                color: Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Form has $totalErrors error${totalErrors > 1 ? 's' : ''}',
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Please fix the following issues before submitting:',
            style: TextStyle(
              color: Colors.red.shade700,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          ...(_validationErrors.entries.map((entry) {
            final fieldName = entry.key;
            final errors = entry.value;
            final field = _metadata.firstWhere(
              (f) => f.name == fieldName,
              orElse: () => FieldMetadata(
                id: fieldName,
                name: fieldName,
                displayLabel: fieldName,
                inputType: 'text',
                isRequired: false,
                dataTypeName: 'text',
                category: 'General',
                uiComponent: 'ThisTextInput',
              ),
            );

            return Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '• ${field.displayLabel}: ${errors.join(', ')}',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 13,
                ),
              ),
            );
          })),
        ],
      ),
    );
  }

  /// Build individual form field
  Widget _buildFormField(FieldMetadata field) {
    return DynamicFormField(
      metadata: field,
      value: _formData[field.name],
      onChanged: (value) => _onFieldChanged(field.name, value),
      onValidation: (errors) => _onFieldValidation(field.name, errors),
      disabled: _isSubmitting,
    );
  }

  /// Deduplicate fields to prevent duplicate form inputs
  /// This handles cases like 'ServingSize' and 'servingSize' appearing as separate fields
  List<FieldMetadata> _deduplicateFields(List<FieldMetadata> metadata) {
    final Map<String, FieldMetadata> uniqueFields = {};
    final Set<String> seenNormalizedNames = {};

    for (final field in metadata) {
      // Create normalized versions of the field name for comparison
      final normalizedName = field.name.toLowerCase().trim();
      final normalizedDisplayLabel = field.displayLabel.toLowerCase().trim();

      // Check if we've already seen this field (by name or display label)
      final isDuplicate = seenNormalizedNames.contains(normalizedName) ||
                         seenNormalizedNames.contains(normalizedDisplayLabel);

      if (!isDuplicate) {
        // This is a unique field, add it
        uniqueFields[field.name] = field;
        seenNormalizedNames.add(normalizedName);
        seenNormalizedNames.add(normalizedDisplayLabel);
      } else {
        // This is a duplicate, decide which one to keep
        final existingField = uniqueFields.values.firstWhere(
          (existing) =>
            existing.name.toLowerCase() == normalizedName ||
            existing.displayLabel.toLowerCase() == normalizedDisplayLabel,
          orElse: () => field,
        );

        // Prefer the field with better naming convention (camelCase over PascalCase)
        if (_isBetterFieldName(field.name, existingField.name)) {
          // Replace the existing field with this better one
          uniqueFields.remove(existingField.name);
          uniqueFields[field.name] = field;
        }

        print('Duplicate field detected: "${field.name}" (${field.displayLabel}) - keeping "${uniqueFields[field.name]?.name ?? existingField.name}"');
      }
    }

    final duplicatesRemoved = metadata.length - uniqueFields.length;
    if (duplicatesRemoved > 0) {
      print('Removed $duplicatesRemoved duplicate fields from ${widget.objectType} form');
    }

    return uniqueFields.values.toList();
  }

  /// Determine if one field name is better than another
  /// Prefers camelCase over PascalCase, and more descriptive names
  bool _isBetterFieldName(String newName, String existingName) {
    // Prefer camelCase over PascalCase
    final newIsCamelCase = newName.isNotEmpty && newName[0].toLowerCase() == newName[0];
    final existingIsCamelCase = existingName.isNotEmpty && existingName[0].toLowerCase() == existingName[0];

    if (newIsCamelCase && !existingIsCamelCase) {
      return true; // New name is camelCase, existing is PascalCase
    }

    if (!newIsCamelCase && existingIsCamelCase) {
      return false; // Existing name is camelCase, new is PascalCase
    }

    // If both are same case style, prefer the shorter or more standard name
    return newName.length <= existingName.length;
  }
}
