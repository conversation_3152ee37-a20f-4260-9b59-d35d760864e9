// config/routes.ts
export interface RouteConfig {
  path: string;
  key: string;
  label: string;
  icon: string;
  title: string;
}

export const routes: RouteConfig[] = [
  {
    path: '/users',
    key: 'users',
    label: 'Users',
    icon: '👥',
    title: 'User Management'
  },
  {
    path: '/roles',
    key: 'roles',
    label: 'Roles',
    icon: '🛡️',
    title: 'Role Management'
  },
  {
    path: '/settings',
    key: 'settings',
    label: 'Settings',
    icon: '⚙️',
    title: 'Application Settings'
  }
];

export const defaultRoute = '/settings';
