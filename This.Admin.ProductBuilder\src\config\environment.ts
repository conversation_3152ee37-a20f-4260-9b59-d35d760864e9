/**
 * Environment Configuration
 * Centralized access to environment variables with type safety
 */

export interface EnvironmentConfig {
  // Dual API Base URLs
  apiBaseUrl: string;           // Primary API - Standard data operations
  apiLeadratBaseUrl: string;    // Secondary API - Tenant management operations
  
  // API Configuration
  apiProtocol: string;
  apiHost: string;
  apiSubscriptionDataPort: number;
  apiTenantDataPort: number;
  apiSubscriptionCreationPort: number;
  
  // Default Headers
  apiDefaultTenant: string;
  apiContentType: string;
  apiAccept: string;
  
  // Request Configuration
  apiTimeout: number;
  apiRetryAttempts: number;
  apiRetryDelay: number;
  
  // Environment
  nodeEnv: string;
  
  // Feature Flags
  enableApiLogging: boolean;
  enableErrorReporting: boolean;
  enablePerformanceMonitoring: boolean;
}

/**
 * Get environment variable with fallback
 */
function getEnvVar(key: string, fallback: string = ''): string {
  return import.meta.env[key] || fallback;
}

/**
 * Get environment variable as number with fallback
 */
function getEnvVarAsNumber(key: string, fallback: number): number {
  const value = import.meta.env[key];
  return value ? parseInt(value, 10) : fallback;
}

/**
 * Get environment variable as boolean with fallback
 */
function getEnvVarAsBoolean(key: string, fallback: boolean): boolean {
  const value = import.meta.env[key];
  return value ? value.toLowerCase() === 'true' : fallback;
}

/**
 * Environment configuration object
 */
export const environment: EnvironmentConfig = {
  // Dual API Base URLs
  apiBaseUrl: getEnvVar('VITE_API_BaseUrl', 'https://localhost:7222/api'),
  apiLeadratBaseUrl: getEnvVar('VITE_API_Leadrat_BaseUrl', 'https://localhost:7243/api'),
  
  // API Configuration
  apiProtocol: getEnvVar('VITE_API_PROTOCOL', 'https'),
  apiHost: getEnvVar('VITE_API_HOST', 'localhost'),
  apiSubscriptionDataPort: getEnvVarAsNumber('VITE_API_SUBSCRIPTION_DATA_PORT', 7222),
  apiTenantDataPort: getEnvVarAsNumber('VITE_API_TENANT_DATA_PORT', 7243),
  apiSubscriptionCreationPort: getEnvVarAsNumber('VITE_API_SUBSCRIPTION_CREATION_PORT', 7222),
  
  // Default Headers
  apiDefaultTenant: getEnvVar('VITE_API_DEFAULT_TENANT', 'lrbnewqa'),
  apiContentType: getEnvVar('VITE_API_CONTENT_TYPE', 'application/json'),
  apiAccept: getEnvVar('VITE_API_ACCEPT', 'application/json'),
  
  // Request Configuration
  apiTimeout: getEnvVarAsNumber('VITE_API_TIMEOUT', 30000),
  apiRetryAttempts: getEnvVarAsNumber('VITE_API_RETRY_ATTEMPTS', 3),
  apiRetryDelay: getEnvVarAsNumber('VITE_API_RETRY_DELAY', 1000),
  
  // Environment
  nodeEnv: getEnvVar('VITE_NODE_ENV', 'development'),
  
  // Feature Flags
  enableApiLogging: getEnvVarAsBoolean('VITE_ENABLE_API_LOGGING', true),
  enableErrorReporting: getEnvVarAsBoolean('VITE_ENABLE_ERROR_REPORTING', true),
  enablePerformanceMonitoring: getEnvVarAsBoolean('VITE_ENABLE_PERFORMANCE_MONITORING', false),
};

/**
 * Validate environment configuration
 */
export function validateEnvironment(): void {
  const requiredVars = [
    'apiSubscriptionDataBaseUrl',
    'apiTenantDataBaseUrl',
    'apiSubscriptionCreationBaseUrl'
  ];
  
  const missingVars = requiredVars.filter(key => !environment[key as keyof EnvironmentConfig]);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
}

/**
 * Get API base URL by service type
 */
export function getApiBaseUrl(serviceType: 'primary' | 'secondary'): string {
  switch (serviceType) {
    case 'primary':
      return environment.apiBaseUrl;
    case 'secondary':
      return environment.apiLeadratBaseUrl;
    default:
      throw new Error(`Unknown API service type: ${serviceType}`);
  }
}

/**
 * Check if development environment
 */
export const isDevelopment = (): boolean => environment.nodeEnv === 'development';

/**
 * Check if production environment
 */
export const isProduction = (): boolean => environment.nodeEnv === 'production';
