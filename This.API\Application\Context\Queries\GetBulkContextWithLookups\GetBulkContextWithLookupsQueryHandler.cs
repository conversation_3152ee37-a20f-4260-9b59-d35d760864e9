using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetBulkContextWithLookups;

/// <summary>
/// Handler for GetBulkContextWithLookupsQuery
/// </summary>
public class GetBulkContextWithLookupsQueryHandler : IRequestHandler<GetBulkContextWithLookupsQuery, Result<BulkContextWithLookupsDto>>
{
    private readonly IRepository<Domain.Entities.Context> _contextRepository;
    private readonly IRepository<Lookup> _lookupRepository;
    private readonly ILogger<GetBulkContextWithLookupsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetBulkContextWithLookupsQueryHandler(
        IRepository<Domain.Entities.Context> contextRepository,
        IRepository<Lookup> lookupRepository,
        ILogger<GetBulkContextWithLookupsQueryHandler> logger)
    {
        _contextRepository = contextRepository;
        _lookupRepository = lookupRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<BulkContextWithLookupsDto>> Handle(GetBulkContextWithLookupsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting bulk contexts with lookups for {Count} ContextIds, IncludeInactiveLookups: {IncludeInactiveLookups}",
                request.ContextIds.Count, request.IncludeInactiveLookups);

            if (!request.ContextIds.Any())
            {
                return Result<BulkContextWithLookupsDto>.Failure("No context IDs provided");
            }

            var result = new BulkContextWithLookupsDto();

            // Get all contexts by IDs
            var contexts = await _contextRepository.ListAsync(
                new ContextsByIdsSpec(request.ContextIds), 
                cancellationToken);

            // Track which contexts were found
            var foundContextIds = contexts.Select(c => c.Id).ToList();
            var notFoundContextIds = request.ContextIds.Except(foundContextIds).ToList();
            var deletedContextIds = contexts.Where(c => c.IsDeleted).Select(c => c.Id).ToList();

            // Filter out deleted contexts for processing
            var activeContexts = contexts.Where(c => !c.IsDeleted).ToList();

            // Get all lookups for the active contexts in one query
            if (activeContexts.Any())
            {
                var activeContextIds = activeContexts.Select(c => c.Id).ToList();
                var lookupSpec = new LookupsByContextIdsSpec(
                    contextIds: activeContextIds,
                    includeInactive: request.IncludeInactiveLookups);

                var allLookups = await _lookupRepository.ListAsync(lookupSpec, cancellationToken);

                // Group lookups by context ID for efficient processing
                var lookupsByContextId = allLookups.GroupBy(l => l.ContextId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // Build the result for each active context
                foreach (var context in activeContexts)
                {
                    var contextDto = context.Adapt<ContextDto>();
                    var contextLookups = lookupsByContextId.GetValueOrDefault(context.Id, new List<Lookup>());
                    var lookupDtos = contextLookups.Adapt<List<LookupDto>>();

                    var contextWithLookups = new ContextWithLookupsDto
                    {
                        Context = contextDto,
                        Lookups = lookupDtos
                    };

                    result.ContextsWithLookups.Add(contextWithLookups);
                }
            }

            // Set tracking information
            result.NotFoundContextIds = notFoundContextIds;
            result.DeletedContextIds = deletedContextIds;

            _logger.LogInformation("Successfully retrieved {FoundCount} contexts with {TotalLookups} total lookups. " +
                "NotFound: {NotFoundCount}, Deleted: {DeletedCount}",
                result.TotalContextsCount, result.TotalLookupsCount, 
                notFoundContextIds.Count, deletedContextIds.Count);

            return Result<BulkContextWithLookupsDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving bulk contexts with lookups for {Count} ContextIds", 
                request.ContextIds.Count);
            return Result<BulkContextWithLookupsDto>.Failure($"Error retrieving bulk contexts with lookups: {ex.Message}");
        }
    }
}
