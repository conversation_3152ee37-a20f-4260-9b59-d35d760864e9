using Shared.Common;

namespace Abstraction.Database.Repositories;

/// <summary>
/// Interface for Dapper repository
/// </summary>
public interface IDapperRepository : ITransientRepository
{
    /// <summary>
    /// Execute a query and return the first result
    /// </summary>
    Task<T> QueryFirstOrDefaultAsync<T>(string sql, object? param = null, System.Data.IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
        where T : class;

    /// <summary>
    /// Execute a query and return all results
    /// </summary>
    Task<IEnumerable<T>> QueryAsync<T>(string sql, object? param = null, System.Data.IDbTransaction? transaction = null, CancellationToken cancellationToken = default)
        where T : class;

    /// <summary>
    /// Query object instance view data with pagination
    /// </summary>
    Task<(IEnumerable<Dictionary<string, object?>> Data, int TotalCount)> QueryObjectInstanceViewAsync(string objectName, string tenantId, bool createView = true, int pageNumber = 1, int pageSize = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create object instance view using stored procedure
    /// </summary>
    Task<string> CreateObjectInstanceViewAsync(string objectName, string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Query specific instance by RefId from view
    /// </summary>
    Task<Dictionary<string, object?>?> QueryInstanceByRefIdAsync(string viewName, Guid refId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Query all values for a specific metadata key from object instance view with RefId
    /// </summary>
    Task<(IEnumerable<MetadataKeyValueResult> Values, int TotalCount)> QueryMetadataKeyValuesAsync(string objectName, string metadataKey, string tenantId, bool createView = true, int pageNumber = 1, int pageSize = 10, CancellationToken cancellationToken = default);
}

/// <summary>
/// Result model for metadata key value with RefId
/// </summary>
public class MetadataKeyValueResult
{
    /// <summary>
    /// The actual value of the metadata key
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// The RefId of the instance containing this value
    /// </summary>
    public Guid RefId { get; set; }
}
