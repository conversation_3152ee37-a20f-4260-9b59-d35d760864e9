namespace Abstraction.Identity.Dtos;

/// <summary>
/// Request for updating a role
/// </summary>
public class UpdateRoleRequest
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Role name
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// Product ID this role belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Role description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is a system role
    /// </summary>
    public bool IsSystemRole { get; set; } = false;

    /// <summary>
    /// Permissions as JSON
    /// </summary>
    public string Permissions { get; set; } = "{}";

    /// <summary>
    /// Whether the role is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
