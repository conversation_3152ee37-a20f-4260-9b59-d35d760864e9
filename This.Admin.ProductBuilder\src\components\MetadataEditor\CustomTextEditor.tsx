import React, { useState } from 'react';
import type { MetadataItem } from '../../utils/index';

interface RenderEditCellProps {
  row: any;
  column: any;
  onRowChange: (row: any) => void;
  onClose: (commitChanges?: boolean) => void;
  updateTempMetadataRow: (rowId: string, updates: Partial<MetadataItem>) => void;
}

export const CustomTextEditor: React.FC<RenderEditCellProps> = ({ 
  row, 
  column, 
  onRowChange, 
  onClose, 
  updateTempMetadataRow 
}) => {
  const [value, setValue] = useState(row[column.key] || '');

  const handleSave = () => {
    updateTempMetadataRow(row._internalId, { [column.key]: value });
    onRowChange({ ...row, [column.key]: value });
    onClose(true);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSave();
    } else if (event.key === 'Escape') {
      onClose(false);
    }
  };

  const handleBlur = () => {
    handleSave();
  };

  return (
    <input
      type="text"
      value={value}
      onChange={(e) => setValue(e.target.value)}
      onKeyDown={handleKeyDown}
      onBlur={handleBlur}
      autoFocus
      style={{
        width: '100%',
        height: '100%',
        border: 'none',
        outline: 'none',
        padding: '8px',
        fontSize: 'inherit',
        fontFamily: 'inherit'
      }}
    />
  );
};
