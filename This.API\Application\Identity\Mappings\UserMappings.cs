using Application.Identity.DTOs;
using Abstraction.Identity.Dtos;
using Domain.Entities;
using Mapster;

namespace Application.Identity.Mappings;

/// <summary>
/// Mapping configurations for User entity and related DTOs
/// </summary>
public class UserMappings : IRegister
{
    /// <summary>
    /// Register mappings
    /// </summary>
    public void Register(TypeAdapterConfig config)
    {
        // Map User entity to UserDto
        config.NewConfig<User, UserDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.UserName, src => src.UserName)
            .Map(dest => dest.FirstName, src => src.FirstName)
            .Map(dest => dest.LastName, src => src.LastName)
            .Map(dest => dest.NormalizedUserName, src => src.NormalizedUserName)
            .Map(dest => dest.Email, src => src.Email)
            .Map(dest => dest.NormalizedEmail, src => src.NormalizedEmail)
            .Map(dest => dest.EmailConfirmed, src => src.EmailConfirmed)
            .Map(dest => dest.PhoneNumber, src => src.PhoneNumber)
            .Map(dest => dest.PhoneNumberConfirmed, src => src.PhoneNumberConfirmed)
            .Map(dest => dest.TwoFactorEnabled, src => src.TwoFactorEnabled)
            .Map(dest => dest.LockoutEnd, src => src.LockoutEnd)
            .Map(dest => dest.LockoutEnabled, src => src.LockoutEnabled)
            .Map(dest => dest.AccessFailedCount, src => src.AccessFailedCount)
            .Map(dest => dest.ExternalUserId, src => src.ExternalUserId)
            .Map(dest => dest.IsActive, src => src.IsActive)
            .Map(dest => dest.LastLoginAt, src => src.LastLoginAt)
            .Map(dest => dest.RefreshToken, src => src.RefreshToken)
            .Map(dest => dest.RefreshTokenExpiryTime, src => src.RefreshTokenExpiryTime)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.CreatedBy, src => src.CreatedBy)
            .Map(dest => dest.ModifiedAt, src => src.ModifiedAt)
            .Map(dest => dest.ModifiedBy, src => src.ModifiedBy)
            .Map(dest => dest.IsDeleted, src => src.IsDeleted)
            .Ignore(dest => dest.Roles); // Roles will be mapped separately

        // Map User entity to UserDetailsDto
        config.NewConfig<User, UserDetailsDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.UserName, src => src.UserName)
            .Map(dest => dest.FirstName, src => src.FirstName)
            .Map(dest => dest.LastName, src => src.LastName)
            .Map(dest => dest.NormalizedUserName, src => src.NormalizedUserName)
            .Map(dest => dest.Email, src => src.Email)
            .Map(dest => dest.NormalizedEmail, src => src.NormalizedEmail)
            .Map(dest => dest.EmailConfirmed, src => src.EmailConfirmed)
            .Map(dest => dest.PhoneNumber, src => src.PhoneNumber)
            .Map(dest => dest.PhoneNumberConfirmed, src => src.PhoneNumberConfirmed)
            .Map(dest => dest.TwoFactorEnabled, src => src.TwoFactorEnabled)
            .Map(dest => dest.LockoutEnd, src => src.LockoutEnd)
            .Map(dest => dest.LockoutEnabled, src => src.LockoutEnabled)
            .Map(dest => dest.AccessFailedCount, src => src.AccessFailedCount)
            .Map(dest => dest.ExternalUserId, src => src.ExternalUserId)
            .Map(dest => dest.IsActive, src => src.IsActive)
            .Map(dest => dest.LastLoginAt, src => src.LastLoginAt)
            .Map(dest => dest.RefreshToken, src => src.RefreshToken)
            .Map(dest => dest.RefreshTokenExpiryTime, src => src.RefreshTokenExpiryTime)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.CreatedBy, src => src.CreatedBy)
            .Map(dest => dest.ModifiedAt, src => src.ModifiedAt)
            .Map(dest => dest.ModifiedBy, src => src.ModifiedBy)
            .Map(dest => dest.IsDeleted, src => src.IsDeleted)
            .Ignore(dest => dest.Roles); // Roles will be mapped separately

        // Map CreateUserRequest to User entity
        config.NewConfig<CreateUserRequest, User>()
            .Map(dest => dest.UserName, src => src.UserName)
            .Map(dest => dest.FirstName, src => src.FirstName)
            .Map(dest => dest.LastName, src => src.LastName)
            .Map(dest => dest.Email, src => src.Email)
            .Map(dest => dest.PhoneNumber, src => src.PhoneNumber)
            .Map(dest => dest.IsActive, src => src.IsActive)
            .Map(dest => dest.ExternalUserId, src => src.ExternalUserId)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.NormalizedUserName)
            .Ignore(dest => dest.NormalizedEmail)
            .Ignore(dest => dest.EmailConfirmed)
            .Ignore(dest => dest.PasswordHash)
            .Ignore(dest => dest.SecurityStamp)
            .Ignore(dest => dest.ConcurrencyStamp)
            .Ignore(dest => dest.PhoneNumberConfirmed)
            .Ignore(dest => dest.TwoFactorEnabled)
            .Ignore(dest => dest.LockoutEnd)
            .Ignore(dest => dest.LockoutEnabled)
            .Ignore(dest => dest.AccessFailedCount)
            .Ignore(dest => dest.LastLoginAt)
            .Ignore(dest => dest.RefreshToken)
            .Ignore(dest => dest.RefreshTokenExpiryTime)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.CreatedBy)
            .Ignore(dest => dest.ModifiedAt)
            .Ignore(dest => dest.ModifiedBy)
            .Ignore(dest => dest.IsDeleted)
            .Ignore(dest => dest.UserRoles)
            .Ignore(dest => dest.UserMetadata)
            .Ignore(dest => dest.FieldMappings)
            .Ignore(dest => dest.ConflictResolutions);

        // Map UpdateUserRequest to User entity
        config.NewConfig<UpdateUserRequest, User>()
            .Map(dest => dest.FirstName, src => src.FirstName)
            .Map(dest => dest.LastName, src => src.LastName)
            .Map(dest => dest.Email, src => src.Email)
            .Map(dest => dest.PhoneNumber, src => src.PhoneNumber)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.UserName)
            .Ignore(dest => dest.NormalizedUserName)
            .Ignore(dest => dest.NormalizedEmail)
            .Ignore(dest => dest.EmailConfirmed)
            .Ignore(dest => dest.PasswordHash)
            .Ignore(dest => dest.SecurityStamp)
            .Ignore(dest => dest.ConcurrencyStamp)
            .Ignore(dest => dest.PhoneNumberConfirmed)
            .Ignore(dest => dest.TwoFactorEnabled)
            .Ignore(dest => dest.LockoutEnd)
            .Ignore(dest => dest.LockoutEnabled)
            .Ignore(dest => dest.AccessFailedCount)
            .Ignore(dest => dest.ExternalUserId)
            .Ignore(dest => dest.IsActive)
            .Ignore(dest => dest.LastLoginAt)
            .Ignore(dest => dest.RefreshToken)
            .Ignore(dest => dest.RefreshTokenExpiryTime)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.CreatedBy)
            .Ignore(dest => dest.ModifiedAt)
            .Ignore(dest => dest.ModifiedBy)
            .Ignore(dest => dest.IsDeleted)
            .Ignore(dest => dest.UserRoles)
            .Ignore(dest => dest.UserMetadata)
            .Ignore(dest => dest.FieldMappings)
            .Ignore(dest => dest.ConflictResolutions);
    }
}
