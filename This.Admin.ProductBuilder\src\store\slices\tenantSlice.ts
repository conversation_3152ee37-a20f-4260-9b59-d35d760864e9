/**
 * Tenant Slice
 * Redux Toolkit slice for tenant state management
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import { tenantApiService } from '../../services/api';
import type { 
  TenantDto, 
  TenantApiResponse, 
  GetTenantsParams,
  CreateTenantRequest,
  UpdateTenantRequest
} from '../../services/api';

// State interface
export interface TenantState {
  // Data
  tenants: TenantDto[];
  activeTenants: TenantDto[];
  currentTenant: TenantDto | null;
  dropdownOptions: Array<{ value: string; label: string; tenant: TenantDto }>;
  
  // Pagination
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  
  // Filters
  filters: {
    searchTerm: string;
    isActive: boolean | null;
    adminEmail: string;
  };
  
  // Loading states
  loading: {
    list: boolean;
    create: boolean;
    update: boolean;
    active: boolean;
    dropdown: boolean;
    validate: boolean;
  };
  
  // Error states
  errors: {
    list: string | null;
    create: string | null;
    update: string | null;
    active: string | null;
    dropdown: string | null;
    validate: string | null;
  };
  
  // UI state
  selectedTenantId: string | null;
  lastUpdated: string | null;
  
  // Stats
  stats: {
    totalTenants: number;
    activeTenants: number;
    inactiveTenants: number;
  };
}

// Initial state
const initialState: TenantState = {
  tenants: [],
  activeTenants: [],
  currentTenant: null,
  dropdownOptions: [],
  totalCount: 0,
  pageNumber: 1,
  pageSize: 10,
  filters: {
    searchTerm: '',
    isActive: null,
    adminEmail: '',
  },
  loading: {
    list: false,
    create: false,
    update: false,
    active: false,
    dropdown: false,
    validate: false,
  },
  errors: {
    list: null,
    create: null,
    update: null,
    active: null,
    dropdown: null,
    validate: null,
  },
  selectedTenantId: null,
  lastUpdated: null,
  stats: {
    totalTenants: 0,
    activeTenants: 0,
    inactiveTenants: 0,
  },
};

// Async thunks
export const fetchTenants = createAsyncThunk(
  'tenants/fetchTenants',
  async (params: GetTenantsParams = {}, { rejectWithValue }) => {
    try {
      const response = await tenantApiService.getTenants(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tenants');
    }
  }
);

export const fetchTenantById = createAsyncThunk(
  'tenants/fetchTenantById',
  async (id: string, { rejectWithValue }) => {
    try {
      const tenant = await tenantApiService.getTenantById(id);
      return tenant;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tenant');
    }
  }
);

export const createTenant = createAsyncThunk(
  'tenants/createTenant',
  async (data: CreateTenantRequest, { rejectWithValue }) => {
    try {
      const tenantId = await tenantApiService.createTenant(data);
      // Fetch the created tenant to get full data
      const tenant = await tenantApiService.getTenantById(tenantId);
      return tenant;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create tenant');
    }
  }
);

export const updateTenant = createAsyncThunk(
  'tenants/updateTenant',
  async (data: UpdateTenantRequest, { rejectWithValue }) => {
    try {
      const tenantId = await tenantApiService.updateTenant(data);
      // Fetch the updated tenant to get full data
      const tenant = await tenantApiService.getTenantById(tenantId);
      return tenant;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update tenant');
    }
  }
);

export const fetchActiveTenants = createAsyncThunk(
  'tenants/fetchActiveTenants',
  async (_, { rejectWithValue }) => {
    try {
      const tenants = await tenantApiService.getActiveTenants();
      return tenants;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch active tenants');
    }
  }
);

export const fetchTenantDropdownOptions = createAsyncThunk(
  'tenants/fetchTenantDropdownOptions',
  async (_, { rejectWithValue }) => {
    try {
      // Use the specific method for subscription modal that uses Secondary API (Port 7243)
      const options = await tenantApiService.getTenantDropdownOptionsForSubscription();
      return options;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tenant dropdown options');
    }
  }
);

export const validateTenantConnection = createAsyncThunk(
  'tenants/validateTenantConnection',
  async (tenantId: string, { rejectWithValue }) => {
    try {
      const isValid = await tenantApiService.validateTenantConnection(tenantId);
      return { tenantId, isValid };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to validate tenant connection');
    }
  }
);

export const fetchTenantStats = createAsyncThunk(
  'tenants/fetchTenantStats',
  async (_, { rejectWithValue }) => {
    try {
      const stats = await tenantApiService.getTenantStats();
      return stats;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch tenant stats');
    }
  }
);

// Slice
const tenantSlice = createSlice({
  name: 'tenants',
  initialState,
  reducers: {
    // Filter actions
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.filters.searchTerm = action.payload;
    },
    setActiveFilter: (state, action: PayloadAction<boolean | null>) => {
      state.filters.isActive = action.payload;
    },
    setAdminEmailFilter: (state, action: PayloadAction<string>) => {
      state.filters.adminEmail = action.payload;
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Pagination actions
    setPageNumber: (state, action: PayloadAction<number>) => {
      state.pageNumber = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.pageNumber = 1;
    },
    
    // Selection actions
    setSelectedTenantId: (state, action: PayloadAction<string | null>) => {
      state.selectedTenantId = action.payload;
    },
    
    // Current tenant
    setCurrentTenant: (state, action: PayloadAction<TenantDto | null>) => {
      state.currentTenant = action.payload;
    },
    
    // Error clearing
    clearError: (state, action: PayloadAction<keyof TenantState['errors']>) => {
      state.errors[action.payload] = null;
    },
    clearAllErrors: (state) => {
      state.errors = initialState.errors;
    },
    
    // Reset state
    resetState: () => initialState,
  },
  extraReducers: (builder) => {
    // Fetch tenants
    builder
      .addCase(fetchTenants.pending, (state) => {
        state.loading.list = true;
        state.errors.list = null;
      })
      .addCase(fetchTenants.fulfilled, (state, action) => {
        state.loading.list = false;
        if (action.payload.succeeded && action.payload.data) {
          state.tenants = action.payload.data;
          state.totalCount = action.payload.data.length;
          state.lastUpdated = new Date().toISOString();
          
          // Update stats
          state.stats.totalTenants = action.payload.data.length;
          state.stats.activeTenants = action.payload.data.filter(t => t.isActive).length;
          state.stats.inactiveTenants = state.stats.totalTenants - state.stats.activeTenants;
        }
      })
      .addCase(fetchTenants.rejected, (state, action) => {
        state.loading.list = false;
        state.errors.list = action.payload as string;
      });

    // Fetch tenant by ID
    builder
      .addCase(fetchTenantById.fulfilled, (state, action) => {
        state.currentTenant = action.payload;
        // Update in list if exists
        const index = state.tenants.findIndex(tenant => tenant.id === action.payload.id);
        if (index > -1) {
          state.tenants[index] = action.payload;
        }
      });

    // Create tenant
    builder
      .addCase(createTenant.pending, (state) => {
        state.loading.create = true;
        state.errors.create = null;
      })
      .addCase(createTenant.fulfilled, (state, action) => {
        state.loading.create = false;
        state.tenants.unshift(action.payload);
        state.totalCount += 1;
        state.stats.totalTenants += 1;
        if (action.payload.isActive) {
          state.stats.activeTenants += 1;
          state.activeTenants.unshift(action.payload);
        } else {
          state.stats.inactiveTenants += 1;
        }
      })
      .addCase(createTenant.rejected, (state, action) => {
        state.loading.create = false;
        state.errors.create = action.payload as string;
      });

    // Update tenant
    builder
      .addCase(updateTenant.pending, (state) => {
        state.loading.update = true;
        state.errors.update = null;
      })
      .addCase(updateTenant.fulfilled, (state, action) => {
        state.loading.update = false;
        const index = state.tenants.findIndex(tenant => tenant.id === action.payload.id);
        if (index > -1) {
          const oldTenant = state.tenants[index];
          state.tenants[index] = action.payload;
          
          // Update active tenants list
          const activeIndex = state.activeTenants.findIndex(t => t.id === action.payload.id);
          if (action.payload.isActive && activeIndex === -1) {
            state.activeTenants.push(action.payload);
          } else if (!action.payload.isActive && activeIndex > -1) {
            state.activeTenants.splice(activeIndex, 1);
          } else if (action.payload.isActive && activeIndex > -1) {
            state.activeTenants[activeIndex] = action.payload;
          }
          
          // Update stats if status changed
          if (oldTenant.isActive !== action.payload.isActive) {
            if (action.payload.isActive) {
              state.stats.activeTenants += 1;
              state.stats.inactiveTenants -= 1;
            } else {
              state.stats.activeTenants -= 1;
              state.stats.inactiveTenants += 1;
            }
          }
        }
        if (state.currentTenant?.id === action.payload.id) {
          state.currentTenant = action.payload;
        }
      })
      .addCase(updateTenant.rejected, (state, action) => {
        state.loading.update = false;
        state.errors.update = action.payload as string;
      });

    // Fetch active tenants
    builder
      .addCase(fetchActiveTenants.pending, (state) => {
        state.loading.active = true;
        state.errors.active = null;
      })
      .addCase(fetchActiveTenants.fulfilled, (state, action) => {
        state.loading.active = false;
        state.activeTenants = action.payload;
      })
      .addCase(fetchActiveTenants.rejected, (state, action) => {
        state.loading.active = false;
        state.errors.active = action.payload as string;
      });

    // Fetch tenant dropdown options
    builder
      .addCase(fetchTenantDropdownOptions.pending, (state) => {
        state.loading.dropdown = true;
        state.errors.dropdown = null;
      })
      .addCase(fetchTenantDropdownOptions.fulfilled, (state, action) => {
        state.loading.dropdown = false;
        state.dropdownOptions = action.payload;
      })
      .addCase(fetchTenantDropdownOptions.rejected, (state, action) => {
        state.loading.dropdown = false;
        state.errors.dropdown = action.payload as string;
      });

    // Validate tenant connection
    builder
      .addCase(validateTenantConnection.pending, (state) => {
        state.loading.validate = true;
        state.errors.validate = null;
      })
      .addCase(validateTenantConnection.fulfilled, (state, action) => {
        state.loading.validate = false;
        // Could store validation results if needed
      })
      .addCase(validateTenantConnection.rejected, (state, action) => {
        state.loading.validate = false;
        state.errors.validate = action.payload as string;
      });

    // Fetch tenant stats
    builder
      .addCase(fetchTenantStats.fulfilled, (state, action) => {
        state.stats = action.payload;
      });
  },
});

// Export actions
export const {
  setSearchTerm,
  setActiveFilter,
  setAdminEmailFilter,
  clearFilters,
  setPageNumber,
  setPageSize,
  setSelectedTenantId,
  setCurrentTenant,
  clearError,
  clearAllErrors,
  resetState,
} = tenantSlice.actions;

// Selectors
export const selectTenants = (state: { tenants: TenantState }) => state.tenants.tenants;
export const selectActiveTenants = (state: { tenants: TenantState }) => state.tenants.activeTenants;
export const selectCurrentTenant = (state: { tenants: TenantState }) => state.tenants.currentTenant;
export const selectTenantDropdownOptions = (state: { tenants: TenantState }) => state.tenants.dropdownOptions;
export const selectTenantFilters = (state: { tenants: TenantState }) => state.tenants.filters;
export const selectTenantLoading = (state: { tenants: TenantState }) => state.tenants.loading;
export const selectTenantErrors = (state: { tenants: TenantState }) => state.tenants.errors;
export const selectSelectedTenantId = (state: { tenants: TenantState }) => state.tenants.selectedTenantId;
export const selectTenantStats = (state: { tenants: TenantState }) => state.tenants.stats;
export const selectTenantPagination = (state: { tenants: TenantState }) => ({
  pageNumber: state.tenants.pageNumber,
  pageSize: state.tenants.pageSize,
  totalCount: state.tenants.totalCount,
});

// Export reducer
export default tenantSlice.reducer;
