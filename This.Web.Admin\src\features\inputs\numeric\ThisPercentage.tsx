// components/ThisPercentage.tsx
import React, { useState, useRef } from 'react';


interface ThisPercentageProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  min?: number;
  max?: number;
  decimals?: number;
  allowNegative?: boolean;
  showIcon?: boolean;
  displayMode?: 'decimal' | 'fraction';
  autoConvert?: boolean;
  customValidation?: (value: string) => string | null;
}

interface ValidationRule {
  test: (value: string) => boolean;
  message: string;
}

const ThisPercentage: React.FC<ThisPercentageProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder = 'Enter percentage...',
  required = false,
  min = 0,
  max = 100,
  decimals = 2,
  allowNegative = false,
  showIcon = true,
  displayMode = 'decimal',
  autoConvert = false,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => val.trim().length > 0,
        message: `${label} is required`
      });
    }

    // 2. Valid number validation
    rules.push({
      test: (val) => {
        if (val.trim() === '') return !required;
        const numericValue = parseFloat(val);
        return !isNaN(numericValue);
      },
      message: `${label} must be a valid number`
    });

    // 3. Negative value validation
    if (!allowNegative) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const numericValue = parseFloat(val);
          return isNaN(numericValue) || numericValue >= 0;
        },
        message: `${label} cannot be negative`
      });
    }

    // 4. Minimum value validation
    rules.push({
      test: (val) => {
        if (val.trim() === '') return !required;
        const numericValue = parseFloat(val);
        return isNaN(numericValue) || numericValue >= min;
      },
      message: `${label} must be at least ${min}%`
    });

    // 5. Maximum value validation
    rules.push({
      test: (val) => {
        if (val.trim() === '') return !required;
        const numericValue = parseFloat(val);
        return isNaN(numericValue) || numericValue <= max;
      },
      message: `${label} must be at most ${max}%`
    });

    // 6. Decimal places validation
    if (decimals >= 0) {
      rules.push({
        test: (val) => {
          if (val.trim() === '') return !required;
          const decimalPart = val.split('.')[1];
          return !decimalPart || decimalPart.length <= decimals;
        },
        message: `${label} can have at most ${decimals} decimal place${decimals !== 1 ? 's' : ''}`
      });
    }

    // 7. Custom validation
    if (customValidation) {
      rules.push({
        test: (val) => {
          const customError = customValidation(val);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateValue = (val: string): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  const formatValue = (val: string): string => {
    if (val.trim() === '') return val;

    // Remove any non-numeric characters except decimal point and minus sign
    let cleaned = val.replace(/[^\d.-]/g, '');

    // Handle negative sign
    if (!allowNegative) {
      cleaned = cleaned.replace(/-/g, '');
    } else {
      // Ensure only one minus sign at the beginning
      const hasNegative = cleaned.startsWith('-');
      cleaned = cleaned.replace(/-/g, '');
      if (hasNegative) cleaned = '-' + cleaned;
    }

    // Handle decimal point
    const parts = cleaned.split('.');
    if (parts.length > 2) {
      cleaned = parts[0] + '.' + parts.slice(1).join('');
    }

    // Limit decimal places
    if (decimals >= 0 && parts.length === 2) {
      cleaned = parts[0] + '.' + parts[1].substring(0, decimals);
    }

    return cleaned;
  };

  const convertToDecimal = (val: string): string => {
    if (!autoConvert || val.trim() === '') return val;

    const numericValue = parseFloat(val);
    if (isNaN(numericValue)) return val;

    // If value is greater than 1 and displayMode is decimal, assume it's already a percentage
    if (displayMode === 'decimal' && numericValue > 1) {
      return (numericValue / 100).toFixed(decimals || 2);
    }

    return val;
  };

  const convertToPercentage = (val: string): string => {
    if (!autoConvert || val.trim() === '') return val;

    const numericValue = parseFloat(val);
    if (isNaN(numericValue)) return val;

    // If value is less than or equal to 1 and displayMode is fraction, convert to percentage
    if (displayMode === 'fraction' && numericValue <= 1) {
      return (numericValue * 100).toFixed(decimals || 2);
    }

    return val;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || readOnly) return;

    let newValue = e.target.value;

    // Format the value
    newValue = formatValue(newValue);

    // Auto-convert if enabled
    if (autoConvert) {
      if (displayMode === 'decimal') {
        newValue = convertToDecimal(newValue);
      } else {
        newValue = convertToPercentage(newValue);
      }
    }

    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);

    // Format value on blur
    if (value.trim() !== '') {
      const numericValue = parseFloat(value);
      if (!isNaN(numericValue)) {
        const formattedValue = numericValue.toFixed(decimals || 2);
        if (formattedValue !== value) {
          onChange(formattedValue);
        }
      }
    }
  };

  const handleClick = (e: React.MouseEvent<HTMLInputElement>) => {
    // Allow normal cursor positioning on click
    e.stopPropagation();
  };

  const getDisplayValue = (): string => {
    if (displayMode === 'decimal') {
      return value;
    } else {
      // For fraction mode, show as percentage
      const numericValue = parseFloat(value);
      if (!isNaN(numericValue) && numericValue <= 1) {
        return (numericValue * 100).toString();
      }
      return value;
    }
  };

  const getPlaceholderText = (): string => {
    if (displayMode === 'decimal') {
      return placeholder;
    } else {
      return placeholder.replace('percentage', 'value (0-100)');
    }
  };

  const hasErrors = errors.length > 0;

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Input */}
      <div className="text-input-wrapper">
        <div className={`percentage-input-container ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''} ${isFocused ? 'focused' : ''}`}>
          <input
            ref={inputRef}
            id={id}
            type="text"
            value={getDisplayValue()}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onClick={handleClick}
            disabled={disabled}
            readOnly={readOnly}
            placeholder={getPlaceholderText()}
            className="percentage-input"
            aria-describedby={hasErrors ? `${id}-error` : undefined}
            aria-invalid={hasErrors}
            inputMode="decimal"
          />

          {showIcon && (
            <div className="percentage-mode">
              <span className="percentage-mode-text">%</span>
            </div>
          )}
        </div>

        {/* Helper Text */}
        {displayMode === 'fraction' && !hasErrors && (
          <div className="percentage-helper">
            <span className="helper-text">
              Enter as decimal (0.5 = 50%) or percentage (50)
            </span>
          </div>
        )}

        {/* Range Display */}
        {(min !== 0 || max !== 100) && !hasErrors && (
          <div className="percentage-range">
            <span className="range-text">
              Valid range: {min}% - {max}%
            </span>
          </div>
        )}

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisPercentage;
