using Application.DataTransformation.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.DataTransformation.Commands;

/// <summary>
/// Command to transform incoming JSON data according to field mappings
/// </summary>
public class TransformDataCommand : IRequest<Result<DataTransformationResultDto>>
{
    /// <summary>
    /// API name to get field mappings for
    /// </summary>
    public string ApiName { get; set; } = string.Empty;

    /// <summary>
    /// JSON data to transform
    /// </summary>
    public string JsonData { get; set; } = string.Empty;

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// User ID for audit purposes (optional)
    /// </summary>
    public Guid? UserId { get; set; }
}
