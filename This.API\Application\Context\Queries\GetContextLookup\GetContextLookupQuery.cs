using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetContextLookup;

/// <summary>
/// Query to get context lookup data for dropdowns
/// </summary>
public class GetContextLookupQuery : IRequest<Result<List<ContextLookupDto>>>
{
    /// <summary>
    /// Category filter
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Search term for name
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Whether to include inactive contexts
    /// </summary>
    public bool IncludeInactive { get; set; } = false;
}
