/**
 * Redux Store Hooks
 * Custom hooks for accessing Redux state and actions
 */

import { useCallback, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from './index';
import type { RootState } from './index';

// Subscription hooks
import {
  fetchSubscriptions,
  fetchSubscriptionById,
  createSubscription,
  updateSubscription,
  deleteSubscription,
  fetchSubscriptionStats,
  setSearchTerm as setSubscriptionSearchTerm,
  setStatusFilter,
  setTenantFilter,
  setPageNumber,
  setPageSize,
  selectSubscriptions,
  selectCurrentSubscription,
  selectSubscriptionSummary,
  selectSubscriptionFilters,
  selectSubscriptionLoading,
  selectSubscriptionErrors,
  selectSubscriptionPagination,
} from './slices/subscriptionSlice';

// Template hooks
import {
  fetchTemplates,
  fetchAllTemplates,
  fetchTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  fetchLiveTemplates,
  setSearchTerm as setTemplateSearchTerm,
  setStageFilter as setTemplateStageFilter,
  setViewMode as setTemplateViewMode,
  selectTemplates,
  selectGroupedTemplates,
  selectCurrentTemplate,
  selectLiveTemplates,
  selectTemplateFilters,
  selectTemplateLoading,
  selectTemplateErrors,
  selectTemplateViewMode,
} from './slices/templateSlice';

// Tenant hooks
import {
  fetchTenants,
  fetchTenantById,
  createTenant,
  updateTenant,
  fetchActiveTenants,
  fetchTenantDropdownOptions,
  setSelectedTenantId,
  selectTenants,
  selectActiveTenants,
  selectCurrentTenant,
  selectTenantDropdownOptions,
  selectTenantLoading,
  selectTenantErrors,
  selectSelectedTenantId,
} from './slices/tenantSlice';

// UI hooks
import {
  addNotification,
  openModal,
  closeModal,
  setGlobalLoading,
  toggleSidebar,
  setSidebarCollapsed,
  selectNotifications,
  selectModals,
  selectGlobalLoading,
  selectSidebarCollapsed,
} from './slices/uiSlice';

// Subscription hooks
export const useSubscriptions = () => {
  const dispatch = useAppDispatch();
  const subscriptions = useAppSelector(selectSubscriptions);
  const currentSubscription = useAppSelector(selectCurrentSubscription);
  const summary = useAppSelector(selectSubscriptionSummary);
  const filters = useAppSelector(selectSubscriptionFilters);
  const loading = useAppSelector(selectSubscriptionLoading);
  const errors = useAppSelector(selectSubscriptionErrors);
  const pagination = useAppSelector(selectSubscriptionPagination);

  const loadSubscriptions = useCallback((params = {}) => {
    dispatch(fetchSubscriptions(params));
  }, [dispatch]);

  const loadSubscriptionById = useCallback((id: string, tenant?: string) => {
    dispatch(fetchSubscriptionById({ id, tenant }));
  }, [dispatch]);

  const createNewSubscription = useCallback((data: any, tenant?: string) => {
    return dispatch(createSubscription({ data, tenant }));
  }, [dispatch]);

  const updateExistingSubscription = useCallback((data: any, tenant?: string) => {
    return dispatch(updateSubscription({ data, tenant }));
  }, [dispatch]);

  const removeSubscription = useCallback((id: string, tenant?: string) => {
    return dispatch(deleteSubscription({ id, tenant }));
  }, [dispatch]);

  const loadStats = useCallback((tenant?: string) => {
    dispatch(fetchSubscriptionStats(tenant));
  }, [dispatch]);

  const setSearch = useCallback((searchTerm: string) => {
    dispatch(setSubscriptionSearchTerm(searchTerm));
    // Auto-reload with new search term
    setTimeout(() => dispatch(fetchSubscriptions()), 0);
  }, [dispatch]);

  const setStatus = useCallback((status: string) => {
    dispatch(setStatusFilter(status));
    // Auto-reload with new status filter
    setTimeout(() => dispatch(fetchSubscriptions()), 0);
  }, [dispatch]);

  const setTenant = useCallback((tenantId: string) => {
    dispatch(setTenantFilter(tenantId));
    // Auto-reload with new tenant filter
    setTimeout(() => dispatch(fetchSubscriptions()), 0);
  }, [dispatch]);

  const setPage = useCallback((pageNumber: number) => {
    dispatch(setPageNumber(pageNumber));
  }, [dispatch]);

  const setSize = useCallback((pageSize: number) => {
    dispatch(setPageSize(pageSize));
  }, [dispatch]);

  return {
    subscriptions,
    currentSubscription,
    summary,
    filters,
    loading,
    errors,
    pagination,
    loadSubscriptions,
    loadSubscriptionById,
    createNewSubscription,
    updateExistingSubscription,
    removeSubscription,
    loadStats,
    setSearch,
    setStatus,
    setTenant,
    setPage,
    setSize,
  };
};

// Template hooks
export const useTemplates = () => {
  const dispatch = useAppDispatch();
  const templates = useAppSelector(selectTemplates);
  const groupedTemplates = useAppSelector(selectGroupedTemplates);
  const currentTemplate = useAppSelector(selectCurrentTemplate);
  const liveTemplates = useAppSelector(selectLiveTemplates);
  const filters = useAppSelector(selectTemplateFilters);
  const loading = useAppSelector(selectTemplateLoading);
  const errors = useAppSelector(selectTemplateErrors);
  const viewMode = useAppSelector(selectTemplateViewMode);

  const loadTemplates = useCallback((params = {}) => {
    dispatch(fetchTemplates(params));
  }, [dispatch]);

  const loadAllTemplates = useCallback(() => {
    dispatch(fetchAllTemplates());
  }, [dispatch]);

  const loadTemplateById = useCallback((id: string) => {
    dispatch(fetchTemplateById(id));
  }, [dispatch]);

  const createNewTemplate = useCallback((data: any) => {
    return dispatch(createTemplate(data));
  }, [dispatch]);

  const updateExistingTemplate = useCallback((data: any) => {
    return dispatch(updateTemplate(data));
  }, [dispatch]);

  const removeTemplate = useCallback((id: string) => {
    return dispatch(deleteTemplate(id));
  }, [dispatch]);

  const loadLiveTemplates = useCallback(() => {
    dispatch(fetchLiveTemplates());
  }, [dispatch]);

  const setSearch = useCallback((searchTerm: string) => {
    dispatch(setTemplateSearchTerm(searchTerm));
  }, [dispatch]);

  const setStage = useCallback((stage: string) => {
    dispatch(setTemplateStageFilter(stage));
  }, [dispatch]);

  const setView = useCallback((viewMode: 'list' | 'grouped') => {
    dispatch(setTemplateViewMode(viewMode));
  }, [dispatch]);

  return {
    templates,
    groupedTemplates,
    currentTemplate,
    liveTemplates,
    filters,
    loading,
    errors,
    viewMode,
    loadTemplates,
    loadAllTemplates,
    loadTemplateById,
    createNewTemplate,
    updateExistingTemplate,
    removeTemplate,
    loadLiveTemplates,
    setSearch,
    setStage,
    setView,
  };
};

// Tenant hooks
export const useTenants = () => {
  const dispatch = useAppDispatch();
  const tenants = useAppSelector(selectTenants);
  const activeTenants = useAppSelector(selectActiveTenants);
  const currentTenant = useAppSelector(selectCurrentTenant);
  const dropdownOptions = useAppSelector(selectTenantDropdownOptions);
  const loading = useAppSelector(selectTenantLoading);
  const errors = useAppSelector(selectTenantErrors);
  const selectedTenantId = useAppSelector(selectSelectedTenantId);

  const loadTenants = useCallback((params = {}) => {
    dispatch(fetchTenants(params));
  }, [dispatch]);

  const loadTenantById = useCallback((id: string) => {
    dispatch(fetchTenantById(id));
  }, [dispatch]);

  const createNewTenant = useCallback((data: any) => {
    return dispatch(createTenant(data));
  }, [dispatch]);

  const updateExistingTenant = useCallback((data: any) => {
    return dispatch(updateTenant(data));
  }, [dispatch]);

  const loadActiveTenants = useCallback(() => {
    dispatch(fetchActiveTenants());
  }, [dispatch]);

  const loadDropdownOptions = useCallback(() => {
    dispatch(fetchTenantDropdownOptions());
  }, [dispatch]);

  const selectTenant = useCallback((tenantId: string | null) => {
    dispatch(setSelectedTenantId(tenantId));
  }, [dispatch]);

  return {
    tenants,
    activeTenants,
    currentTenant,
    dropdownOptions,
    loading,
    errors,
    selectedTenantId,
    loadTenants,
    loadTenantById,
    createNewTenant,
    updateExistingTenant,
    loadActiveTenants,
    loadDropdownOptions,
    selectTenant,
  };
};

// UI hooks
export const useUI = () => {
  const dispatch = useAppDispatch();
  const notifications = useAppSelector(selectNotifications);
  const modals = useAppSelector(selectModals);
  const globalLoading = useAppSelector(selectGlobalLoading);
  const sidebarCollapsed = useAppSelector(selectSidebarCollapsed);

  const showNotification = useCallback((notification: any) => {
    dispatch(addNotification(notification));
  }, [dispatch]);

  const showModal = useCallback((type: string, data?: any, size?: 'sm' | 'lg' | 'xl') => {
    dispatch(openModal({ type, data, size }));
  }, [dispatch]);

  const hideModal = useCallback((type: string) => {
    dispatch(closeModal(type));
  }, [dispatch]);

  const setLoading = useCallback((loading: boolean, message?: string) => {
    dispatch(setGlobalLoading({ loading, message }));
  }, [dispatch]);

  const toggleSidebarCollapse = useCallback(() => {
    dispatch(toggleSidebar());
  }, [dispatch]);

  const setSidebarCollapse = useCallback((collapsed: boolean) => {
    dispatch(setSidebarCollapsed(collapsed));
  }, [dispatch]);

  return {
    notifications,
    modals,
    globalLoading,
    sidebarCollapsed,
    showNotification,
    showModal,
    hideModal,
    setLoading,
    toggleSidebarCollapse,
    setSidebarCollapse,
  };
};

// Combined hook for common operations
export const useAppData = () => {
  const subscriptions = useSubscriptions();
  const templates = useTemplates();
  const tenants = useTenants();
  const ui = useUI();

  // Auto-load data on mount
  useEffect(() => {
    tenants.loadActiveTenants();
    tenants.loadDropdownOptions();
    templates.loadLiveTemplates();
  }, []);

  const refreshAll = useCallback(() => {
    subscriptions.loadSubscriptions();
    templates.loadTemplates();
    tenants.loadTenants();
    subscriptions.loadStats();
  }, [subscriptions, templates, tenants]);

  const isAnyLoading = useCallback(() => {
    return (
      subscriptions.loading.list ||
      templates.loading.list ||
      tenants.loading.list ||
      ui.globalLoading
    );
  }, [subscriptions.loading, templates.loading, tenants.loading, ui.globalLoading]);

  return {
    subscriptions,
    templates,
    tenants,
    ui,
    refreshAll,
    isAnyLoading,
  };
};

// Error handling hook
export const useErrorHandler = () => {
  const { showNotification } = useUI();

  const handleError = useCallback((error: any, context?: string) => {
    const message = error?.message || error || 'An unexpected error occurred';
    const title = context ? `${context} Error` : 'Error';
    
    showNotification({
      type: 'error',
      title,
      message,
      duration: 5000,
    });
  }, [showNotification]);

  const handleSuccess = useCallback((message: string, title = 'Success') => {
    showNotification({
      type: 'success',
      title,
      message,
      duration: 3000,
    });
  }, [showNotification]);

  return {
    handleError,
    handleSuccess,
  };
};
