import React from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';

interface SaveFormData {
  templateName: string;
  version: string;
  stage: 'draft' | 'beta' | 'live' | 'archived';
  description: string;
}

interface TemplateSaveModalProps {
  show: boolean;
  onHide: () => void;
  saveFormData: SaveFormData;
  setSaveFormData: React.Dispatch<React.SetStateAction<SaveFormData>>;
  onSave: () => void;
  isSaving: boolean;
}

export const TemplateSaveModal: React.FC<TemplateSaveModalProps> = ({
  show,
  onHide,
  saveFormData,
  setSaveFormData,
  onSave,
  isSaving
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave();
  };

  return (
    <Modal show={show} onHide={onHide} backdrop="static">
      <Modal.Header closeButton>
        <Modal.Title>Save Template</Modal.Title>
      </Modal.Header>

      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          <Form.Group className="mb-3">
            <Form.Label>Template Name <span className="text-danger">*</span></Form.Label>
            <Form.Control
              type="text"
              value={saveFormData.templateName}
              onChange={(e) => setSaveFormData(prev => ({ ...prev, templateName: e.target.value }))}
              placeholder="Enter template name..."
              required
            />
            <Form.Text className="text-muted">
              This will be used to group different versions of the same template
            </Form.Text>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Version <span className="text-danger">*</span></Form.Label>
            <Form.Control
              type="text"
              value={saveFormData.version}
              onChange={(e) => setSaveFormData(prev => ({ ...prev, version: e.target.value }))}
              placeholder="e.g., 1.0.0"
              required
            />
            <Form.Text className="text-muted">
              Use semantic versioning (e.g., 1.0.0, 1.1.0, 2.0.0)
            </Form.Text>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Stage <span className="text-danger">*</span></Form.Label>
            <Form.Select
              value={saveFormData.stage}
              onChange={(e) => setSaveFormData(prev => ({ ...prev, stage: e.target.value as any }))}
              required
            >
              <option value="draft">Draft</option>
              <option value="beta">Beta</option>
              <option value="live">Live</option>
              <option value="archived">Archived</option>
            </Form.Select>
            <Form.Text className="text-muted">
              Draft: Work in progress, Beta: Testing, Live: Production ready, Archived: No longer active
            </Form.Text>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              value={saveFormData.description}
              onChange={(e) => setSaveFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what this template is for..."
            />
            <Form.Text className="text-muted">
              Optional description for your template
            </Form.Text>
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="outline-secondary" onClick={onHide} disabled={isSaving}>
            Cancel
          </Button>
          <Button
            variant="success"
            type="submit"
            disabled={isSaving}
            className="d-flex align-items-center gap-2"
          >
            {isSaving ? (
              <>
                <Spinner animation="border" size="sm" />
                Saving...
              </>
            ) : (
              <>
                💾 Save Template
              </>
            )}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
