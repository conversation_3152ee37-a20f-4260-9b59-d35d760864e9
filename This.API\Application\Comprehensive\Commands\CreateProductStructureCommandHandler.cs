using Abstraction.Database.Repositories;
using Application.ActionManagement.Specifications;
using Application.Common.DataType;
using Application.Comprehensive.DTOs;
using Application.Comprehensive.Specifications;
using Application.DisplayActionManagement.Specifications;
using Application.DisplayManagement.Specifications;
using Application.MetadataManagement.Specifications;
using Application.Objects.DTOs;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.Comprehensive.Commands;

/// <summary>
/// Handler for creating comprehensive product structure
/// </summary>
public partial class CreateProductStructureCommandHandler : IRequestHandler<CreateProductStructureCommand, Result<ProductStructureCreationResult>>
{
    private readonly IRepositoryWithEvents<Product> _productRepository;
    private readonly IRepositoryWithEvents<Domain.Entities.Object> _objectRepository;
    private readonly IRepositoryWithEvents<Metadata> _metadataRepository;
    private readonly IRepositoryWithEvents<DataType> _dataTypeRepository;
    private readonly IRepositoryWithEvents<ObjectMetadata> _objectMetadataRepository;
    private readonly IRepositoryWithEvents<Display> _displayRepository;
    private readonly IRepositoryWithEvents<Domain.Entities.Action> _actionRepository;
    private readonly IRepositoryWithEvents<DisplayAction> _displayActionRepository;
    private readonly IRepositoryWithEvents<Role> _roleRepository;
    private readonly ILogger<CreateProductStructureCommandHandler> _logger;

    public CreateProductStructureCommandHandler(
        IRepositoryWithEvents<Product> productRepository,
        IRepositoryWithEvents<Domain.Entities.Object> objectRepository,
        IRepositoryWithEvents<Metadata> metadataRepository,
        IRepositoryWithEvents<DataType> dataTypeRepository,
        IRepositoryWithEvents<ObjectMetadata> objectMetadataRepository,
        IRepositoryWithEvents<Display> displayRepository,
        IRepositoryWithEvents<Domain.Entities.Action> actionRepository,
        IRepositoryWithEvents<DisplayAction> displayActionRepository,
        IRepositoryWithEvents<Role> roleRepository,
        ILogger<CreateProductStructureCommandHandler> logger)
    {
        _productRepository = productRepository;
        _objectRepository = objectRepository;
        _metadataRepository = metadataRepository;
        _dataTypeRepository = dataTypeRepository;
        _objectMetadataRepository = objectMetadataRepository;
        _displayRepository = displayRepository;
        _actionRepository = actionRepository;
        _displayActionRepository = displayActionRepository;
        _roleRepository = roleRepository;
        _logger = logger;
    }

    public async Task<Result<ProductStructureCreationResult>> Handle(
        CreateProductStructureCommand request, 
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ProductStructureCreationResult();

        try
        {
            _logger.LogInformation("Starting comprehensive product structure creation for {ProductCount} products",
                request.Products?.Count ?? 0);

            // Phase 1: Validation
            var validationResult = await ValidateProductStructureAsync(request.Products, cancellationToken);
            if (!validationResult.Success)
            {
                result.Success = false;
                result.Errors.AddRange(validationResult.Errors);
                result.Message = "Validation failed";
                return Result<ProductStructureCreationResult>.Failure(result.Message);
            }

            // Phase 2: Initialize processing context
            var context = new ProductStructureProcessingContext
            {
                AllDataTypes = await GetAllDataTypesAsync(cancellationToken),
                AllMetadata = new Dictionary<string, Metadata>(),
                CreatedProducts = new List<Product>(),
                CreatedObjects = new List<Domain.Entities.Object>(),
                CreatedMetadata = new List<Metadata>(),
                CreatedObjectMetadata = new List<ObjectMetadata>(),
                DatabaseQueriesCount = 1 // For GetAllDataTypesAsync
            };

            // Phase 3: Process all products
            foreach (var productDto in request.Products)
            {
                await ProcessProductAsync(productDto, context, cancellationToken);
            }

            // Phase 4: Batch database operations
            await ExecuteBatchOperationsAsync(context, cancellationToken);

            // Phase 5: Build success response
            BuildSuccessResponse(result, context);

            stopwatch.Stop();
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            result.Metrics.TotalProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Product structure creation completed successfully. " +
                "Products: {ProductCount}, Objects: {ObjectCount}, Metadata: {MetadataCount}, Roles: {RoleCount}, Time: {TimeMs}ms",
                result.TotalProductsCreated, result.TotalObjectsCreated,
                result.TotalMetadataCreated, result.TotalRolesCreated, result.ProcessingTimeMs);

            return Result<ProductStructureCreationResult>.Success(result, result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during product structure creation");

            result.Success = false;
            result.Errors.Add($"Internal error: {ex.Message}");
            result.Message = "Processing failed due to internal error";

            stopwatch.Stop();
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return Result<ProductStructureCreationResult>.Failure(result.Message);
        }
    }

    #region Private Helper Methods

    private async Task<ProductStructureCreationResult> ValidateProductStructureAsync(
        List<ProductStructureDto> products,
        CancellationToken cancellationToken)
    {
        var result = new ProductStructureCreationResult();
        var errors = new List<string>();

        try
        {
            // Validate request structure
            if (products == null || !products.Any())
            {
                errors.Add("At least one product must be provided");
            }
            else
            {
                // Validate each product
                foreach (var product in products)
                {
                    ValidateProductStructure(product, errors);
                }

                // Validate unique product names
                var productNames = products.Select(p => p.Name.ToLowerInvariant()).ToList();
                var duplicateNames = productNames.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key);
                foreach (var duplicateName in duplicateNames)
                {
                    errors.Add($"Duplicate product name found: '{duplicateName}'");
                }
            }

            result.Success = !errors.Any();
            result.Errors = errors;
            result.Message = result.Success ? "Validation passed" : "Validation failed";

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during product structure validation");
            result.Success = false;
            result.Errors.Add($"Validation error: {ex.Message}");
            result.Message = "Validation failed due to internal error";
            return result;
        }
    }

    private async Task<Dictionary<string, DataType>> GetAllDataTypesAsync(CancellationToken cancellationToken)
    {
        var dataTypes = await _dataTypeRepository.ListAsync(cancellationToken);
        return dataTypes.ToDictionary(dt => dt.Name.ToLowerInvariant(), dt => dt);
    }

    private async Task ProcessProductAsync(
        ProductStructureDto productDto,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        Product productEntity;
        bool wasCreated;

        // Check if product with same name already exists
        var existingProductSpec = new ProductByNameSpec(productDto.Name);
        var existingProduct = await _productRepository.GetBySpecAsync(existingProductSpec, cancellationToken);
        context.DatabaseQueriesCount++;

        if (existingProduct != null)
        {
            // Use existing product
            productEntity = existingProduct;
            wasCreated = false;
            context.ExistingProducts.Add(existingProduct);

            _logger.LogInformation("Product '{ProductName}' already exists, using existing ID: {ProductId}",
                productDto.Name, existingProduct.Id);
        }
        else
        {
            // Create new product with new GUID
            var productId = Guid.NewGuid();
            productEntity = new Product
            {
                Id = productId,
                Name = productDto.Name,
                Description = productDto.Description,
                Version = productDto.Version ?? "1.0.0",
                IsActive = productDto.IsActive,
                CreatedAt = DateTime.UtcNow,
                ModifiedAt = DateTime.UtcNow
            };

            context.CreatedProducts.Add(productEntity);
            wasCreated = true;

            _logger.LogInformation("Creating new product '{ProductName}' with ID: {ProductId}",
                productDto.Name, productEntity.Id);
        }

        // Create processing info for this product
        var productProcessingInfo = new ProductProcessingInfo
        {
            Product = productEntity,
            WasCreated = wasCreated
        };

        // Process product metadata
        if (productDto.Metadata?.Any() == true)
        {
            foreach (var metadataDto in productDto.Metadata)
            {
                var metadataProcessingInfo = await ProcessMetadataAsync(metadataDto, context, cancellationToken);
                if (metadataProcessingInfo != null)
                {
                    productProcessingInfo.Metadata.Add(metadataProcessingInfo);
                }
            }
        }

        // Process product objects
        if (productDto.Objects?.Any() == true)
        {
            foreach (var objectDto in productDto.Objects)
            {
                var objectProcessingInfo = await ProcessObjectAsync(objectDto, productEntity.Id, null, 0, context, cancellationToken);
                if (objectProcessingInfo != null)
                {
                    productProcessingInfo.Objects.Add(objectProcessingInfo);
                }
            }
        }

        context.ProcessedProducts.Add(productProcessingInfo);
    }

    private async Task<ObjectProcessingInfo?> ProcessObjectAsync(
        ObjectStructureDto objectDto,
        Guid productId,
        Guid? parentObjectId,
        int level,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        Domain.Entities.Object objectEntity;
        bool wasCreated;

        // Check if object with same name already exists under this product
        var existingObjectSpec = new ObjectByNameAndProductSpec(objectDto.Name, productId);
        var existingObject = await _objectRepository.GetBySpecAsync(existingObjectSpec, cancellationToken);
        context.DatabaseQueriesCount++;

        if (existingObject != null)
        {
            // Use existing object
            objectEntity = existingObject;
            wasCreated = false;
            context.ExistingObjects.Add(existingObject);

            _logger.LogInformation("Object '{ObjectName}' already exists under product {ProductId}, using existing ID: {ObjectId}",
                objectDto.Name, productId, existingObject.Id);
        }
        else
        {
            // Create new object with new GUID
            var objectId = Guid.NewGuid();
            objectEntity = new Domain.Entities.Object
            {
                Id = objectId,
                Name = objectDto.Name,
                Description = objectDto.Description,
                ProductId = productId,
                ParentObjectId = parentObjectId,
                IsActive = objectDto.IsActive,
                CreatedAt = DateTime.UtcNow,
                ModifiedAt = DateTime.UtcNow
            };

            context.CreatedObjects.Add(objectEntity);
            wasCreated = true;

            _logger.LogInformation("Creating new object '{ObjectName}' under product {ProductId} with ID: {ObjectId}",
                objectDto.Name, productId, objectEntity.Id);
        }

        // Create processing info for this object
        var objectProcessingInfo = new ObjectProcessingInfo
        {
            Object = objectEntity,
            WasCreated = wasCreated,
            Level = level
        };

        // Process object metadata (always check for existing metadata)
        if (objectDto.Metadata?.Any() == true)
        {
            foreach (var metadataDto in objectDto.Metadata)
            {
                var metadataProcessingInfo = await ProcessMetadataAsync(metadataDto, context, cancellationToken);
                if (metadataProcessingInfo != null)
                {
                    objectProcessingInfo.Metadata.Add(metadataProcessingInfo);

                    // Check if ObjectMetadata relationship already exists
                    var existingObjectMetadataSpec = new ObjectMetadataExistsSpec(objectEntity.Id, metadataProcessingInfo.Metadata.Id);
                    var existingObjectMetadata = await _objectMetadataRepository.GetBySpecAsync(existingObjectMetadataSpec, cancellationToken);
                    context.DatabaseQueriesCount++;

                    if (existingObjectMetadata != null)
                    {
                        // Relationship already exists
                        context.ExistingObjectMetadata.Add(existingObjectMetadata);
                        _logger.LogInformation("ObjectMetadata relationship already exists for Object {ObjectId} and Metadata {MetadataId}",
                            objectEntity.Id, metadataProcessingInfo.Metadata.Id);
                    }
                    else
                    {
                        // Create new ObjectMetadata link
                        var objectMetadata = new ObjectMetadata
                        {
                            Id = Guid.NewGuid(),
                            ObjectId = objectEntity.Id,
                            MetadataId = metadataProcessingInfo.Metadata.Id,
                            IsUnique = false,
                            IsActive = true,
                            IsVisibleInList = true,
                            IsVisibleInEdit = true,
                            IsVisibleInCreate = true,
                            IsVisibleInView = true,
                            IsCalculated = true,
                            CreatedAt = DateTime.UtcNow,
                            ModifiedAt = DateTime.UtcNow
                        };

                        context.CreatedObjectMetadata.Add(objectMetadata);
                        var objectMetadataKey = $"{objectEntity.Id}_{metadataProcessingInfo.Metadata.Id}";
                        context.CreatedObjectMetadataKeys.Add(objectMetadataKey);

                        _logger.LogInformation("Creating new ObjectMetadata relationship for Object {ObjectId} and Metadata {MetadataId}",
                            objectEntity.Id, metadataProcessingInfo.Metadata.Id);
                    }
                }
            }
        }

        // Process displays for this object
        if (objectDto.Displays?.Any() == true)
        {
            foreach (var displayDto in objectDto.Displays)
            {
                var displayProcessingInfo = await ProcessDisplayAsync(displayDto, objectEntity.Id, context, cancellationToken);
                if (displayProcessingInfo != null)
                {
                    objectProcessingInfo.Displays.Add(displayProcessingInfo);
                }
            }
        }

        // Process child objects recursively
        if (objectDto.Objects?.Any() == true)
        {
            foreach (var childObjectDto in objectDto.Objects)
            {
                var childObjectProcessingInfo = await ProcessObjectAsync(childObjectDto, productId, objectEntity.Id, level + 1, context, cancellationToken);
                if (childObjectProcessingInfo != null)
                {
                    objectProcessingInfo.Children.Add(childObjectProcessingInfo);
                }
            }
        }

        return objectProcessingInfo;
    }

    private async Task<MetadataProcessingInfo?> ProcessMetadataAsync(
        MetadataStructureDto metadataDto,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        Metadata metadataEntity;
        bool wasCreated;

        // Check if metadata with same name already exists
        var existingMetadataSpec = new MetadataByNameSpec(metadataDto.Name);
        var existingMetadata = await _metadataRepository.GetBySpecAsync(existingMetadataSpec, cancellationToken);
        context.DatabaseQueriesCount++;

        if (existingMetadata != null)
        {
            // Use existing metadata
            metadataEntity = existingMetadata;
            wasCreated = false;
            context.ExistingMetadata.Add(existingMetadata);

            _logger.LogInformation("Metadata '{MetadataName}' already exists, using existing ID: {MetadataId}",
                metadataDto.Name, existingMetadata.Id);
        }
        else
        {
            // Create new metadata with new GUID

            // Determine data type for new metadata
            var dataTypeName = MapDataTypeName(metadataDto.Type);
            if (!context.AllDataTypes.TryGetValue(dataTypeName.ToLowerInvariant(), out var dataType))
            {
                _logger.LogWarning("DataType '{DataTypeName}' not found for metadata '{MetadataName}', using 'text' as fallback",
                    dataTypeName, metadataDto.Name);
                dataType = context.AllDataTypes.GetValueOrDefault("text") ?? context.AllDataTypes.Values.First();
            }

            // Always generate new GUID for Metadata entities
            var metadataId = Guid.NewGuid();

            // Create new metadata
            metadataEntity = new Metadata
            {
                Id = metadataId,
                Name = metadataDto.Name,
                DataTypeId = dataType.Id,
                DisplayLabel = metadataDto.Name,
                HelpText = metadataDto.Description,
                IsVisible = metadataDto.IsVisible,
                IsReadonly = metadataDto.IsReadonly,
                CreatedAt = DateTime.UtcNow,
                ModifiedAt = DateTime.UtcNow
            };

            context.CreatedMetadata.Add(metadataEntity);
            wasCreated = true;

            _logger.LogInformation("Creating new metadata '{MetadataName}' with ID: {MetadataId}",
                metadataDto.Name, metadataEntity.Id);
        }

        // Create processing info for this metadata
        var metadataProcessingInfo = new MetadataProcessingInfo
        {
            Metadata = metadataEntity,
            WasCreated = wasCreated
        };

        return metadataProcessingInfo;
    }

    private async Task<DisplayProcessingInfo?> ProcessDisplayAsync(
        DisplayStructureDto displayDto,
        Guid objectId,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        Display displayEntity;
        bool wasCreated;

        // Check if display with same name already exists
        var existingDisplaySpec = new DisplayByNameSpec(displayDto.Name);
        var existingDisplay = await _displayRepository.GetBySpecAsync(existingDisplaySpec, cancellationToken);
        context.DatabaseQueriesCount++;

        if (existingDisplay != null)
        {
            // Use existing display
            displayEntity = existingDisplay;
            wasCreated = false;
            context.ExistingDisplays.Add(existingDisplay);

            _logger.LogInformation("Display '{DisplayName}' already exists, using existing ID: {DisplayId}",
                displayDto.Name, existingDisplay.Id);
        }
        else
        {
            // Create new display with new GUID
            var displayId = Guid.NewGuid();
            displayEntity = new Display
            {
                Id = displayId,
                Name = displayDto.Name,
                Description = displayDto.Description,
                DisplayName = displayDto.DisplayName,
                IsDefault = displayDto.IsDefault,
                RouteTemplate = displayDto.RouteTemplate,
                Icon = displayDto.Icon,
                SortOrder = displayDto.SortOrder,
                IsActive = displayDto.IsActive,
                CreatedAt = DateTime.UtcNow,
                ModifiedAt = DateTime.UtcNow
            };

            context.CreatedDisplays.Add(displayEntity);
            wasCreated = true;

            _logger.LogInformation("Creating new display '{DisplayName}' with ID: {DisplayId}",
                displayDto.Name, displayEntity.Id);
        }

        // Create processing info for this display
        var displayProcessingInfo = new DisplayProcessingInfo
        {
            Display = displayEntity,
            WasCreated = wasCreated
        };

        // Process actions for this display
        if (displayDto.Actions?.Any() == true)
        {
            foreach (var actionDto in displayDto.Actions)
            {
                var actionProcessingInfo = await ProcessActionAsync(actionDto, displayEntity.Id, objectId, context, cancellationToken);
                if (actionProcessingInfo != null)
                {
                    displayProcessingInfo.Actions.Add(actionProcessingInfo);
                }
            }
        }

        return displayProcessingInfo;
    }

    private async Task<ActionProcessingInfo?> ProcessActionAsync(
        ActionStructureDto actionDto,
        Guid displayId,
        Guid objectId,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        Domain.Entities.Action actionEntity;
        bool wasCreated;

        // Check if action with same name already exists
        var existingActionSpec = new ActionByNameSpec(actionDto.Name);
        var existingAction = await _actionRepository.GetBySpecAsync(existingActionSpec, cancellationToken);
        context.DatabaseQueriesCount++;

        if (existingAction != null)
        {
            // Use existing action
            actionEntity = existingAction;
            wasCreated = false;
            context.ExistingActions.Add(existingAction);

            _logger.LogInformation("Action '{ActionName}' already exists, using existing ID: {ActionId}",
                actionDto.Name, existingAction.Id);
        }
        else
        {
            // Create new action with new GUID
            var actionId = Guid.NewGuid();
            actionEntity = new Domain.Entities.Action
            {
                Id = actionId,
                Name = actionDto.Name,
                Description = actionDto.Description,
                EndpointTemplate = actionDto.EndpointTemplate,
                NavigationTarget = actionDto.NavigationTarget,
                Icon = actionDto.Icon,
                ButtonStyle = actionDto.ButtonStyle,
                ConfirmationMessage = actionDto.ConfirmationMessage,
                SuccessMessage = actionDto.SuccessMessage,
                ErrorMessage = actionDto.ErrorMessage,
                IsActive = actionDto.IsActive,
                CreatedAt = DateTime.UtcNow,
                ModifiedAt = DateTime.UtcNow
            };

            context.CreatedActions.Add(actionEntity);
            wasCreated = true;

            _logger.LogInformation("Creating new action '{ActionName}' with ID: {ActionId}",
                actionDto.Name, actionEntity.Id);
        }

        // Create DisplayAction relationship
        var displayActionProcessingInfo = await ProcessDisplayActionAsync(
            displayId, actionEntity.Id, objectId, actionDto, context, cancellationToken);

        // Create processing info for this action
        var actionProcessingInfo = new ActionProcessingInfo
        {
            Action = actionEntity,
            WasCreated = wasCreated,
            DisplayAction = displayActionProcessingInfo.DisplayAction,
            DisplayActionWasCreated = displayActionProcessingInfo.WasCreated
        };

        return actionProcessingInfo;
    }

    private async Task<(DisplayAction DisplayAction, bool WasCreated)> ProcessDisplayActionAsync(
        Guid displayId,
        Guid actionId,
        Guid objectId,
        ActionStructureDto actionDto,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        // Check if DisplayAction relationship already exists
        var existingDisplayActionSpec = new DisplayActionByObjectDisplayActionSpec(objectId, displayId, actionId);
        var existingDisplayAction = await _displayActionRepository.GetBySpecAsync(existingDisplayActionSpec, cancellationToken);
        context.DatabaseQueriesCount++;

        if (existingDisplayAction != null)
        {
            // Relationship already exists
            context.ExistingDisplayActions.Add(existingDisplayAction);
            _logger.LogInformation("DisplayAction relationship already exists for Display {DisplayId}, Action {ActionId}, Object {ObjectId}",
                displayId, actionId, objectId);

            return (existingDisplayAction, false);
        }
        else
        {
            // Create new DisplayAction relationship
            var displayAction = new DisplayAction
            {
                Id = Guid.NewGuid(),
                DisplayId = displayId,
                ActionId = actionId,
                ObjectId = objectId,
                AccessLevel = actionDto.AccessLevel,
                IsDefault = actionDto.IsDefault,
                SortOrder = actionDto.SortOrder,
                IsVisibleInToolbar = actionDto.IsVisibleInToolbar,
                IsVisibleInContextMenu = actionDto.IsVisibleInContextMenu,
                IsActive = actionDto.IsActive,
                CreatedAt = DateTime.UtcNow,
                ModifiedAt = DateTime.UtcNow
            };

            context.CreatedDisplayActions.Add(displayAction);

            _logger.LogInformation("Creating new DisplayAction relationship for Display {DisplayId}, Action {ActionId}, Object {ObjectId}",
                displayId, actionId, objectId);

            return (displayAction, true);
        }
    }

    private void ValidateProductStructure(ProductStructureDto product, List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(product.Name))
        {
            errors.Add("Product name is required");
        }

        // Validate objects if present
        if (product.Objects?.Any() == true)
        {
            ValidateObjectsStructure(product.Objects, $"Product '{product.Name}'", 0, errors);
        }
    }

    private void ValidateObjectsStructure(List<ObjectStructureDto> objects, string parentPath, int depth, List<string> errors)
    {
        if (depth > 10) // Prevent excessive nesting
        {
            errors.Add($"Maximum hierarchy depth (10) exceeded at path '{parentPath}'");
            return;
        }

        var names = new HashSet<string>();
        foreach (var obj in objects)
        {
            if (string.IsNullOrWhiteSpace(obj.Name))
            {
                errors.Add($"Object name is required at path '{parentPath}'");
                continue;
            }

            if (!names.Add(obj.Name.ToLowerInvariant()))
            {
                errors.Add($"Duplicate object name '{obj.Name}' found at path '{parentPath}'");
            }

            // Validate child objects recursively
            if (obj.Objects?.Any() == true)
            {
                var currentPath = $"{parentPath}/{obj.Name}";
                ValidateObjectsStructure(obj.Objects, currentPath, depth + 1, errors);
            }
        }
    }

    private async Task ExecuteBatchOperationsAsync(
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        // Add metadata first (dependencies) - handle duplicates gracefully
        if (context.CreatedMetadata.Any())
        {
            try
            {
                await _metadataRepository.AddRangeAsync(context.CreatedMetadata, cancellationToken);
                context.DatabaseQueriesCount++;
            }
            catch (Exception ex) when (ex.Message.Contains("duplicate key") || ex.Message.Contains("IX_Metadata_MetadataKey"))
            {
                _logger.LogWarning("Duplicate key error when saving metadata batch, attempting individual saves");

                // If batch fails due to duplicates, try saving individually
                var successfulMetadata = new List<Metadata>();
                foreach (var metadata in context.CreatedMetadata)
                {
                    try
                    {
                        await _metadataRepository.AddAsync(metadata, cancellationToken);
                        successfulMetadata.Add(metadata);
                        context.DatabaseQueriesCount++;
                    }
                    catch (Exception individualEx) when (individualEx.Message.Contains("duplicate key") || individualEx.Message.Contains("IX_Metadata_MetadataKey"))
                    {
                        _logger.LogWarning("Skipping duplicate metadata '{MetadataName}'", metadata.Name);
                        context.DatabaseQueriesCount++;
                    }
                }

                // Update the created metadata list to only include successful ones
                context.CreatedMetadata.Clear();
                context.CreatedMetadata.AddRange(successfulMetadata);
            }
        }
        // Add products
        if (context.CreatedProducts.Any())
        {
            await _productRepository.AddRangeAsync(context.CreatedProducts, cancellationToken);
            context.DatabaseQueriesCount++;

            // Create default roles for each new product
            await CreateDefaultRolesForProductsAsync(context.CreatedProducts, context, cancellationToken);
        }

        // Add objects
        if (context.CreatedObjects.Any())
        {
            await _objectRepository.AddRangeAsync(context.CreatedObjects, cancellationToken);
            context.DatabaseQueriesCount++;
        }

        // Add object-metadata links - handle duplicates gracefully
        if (context.CreatedObjectMetadata.Any())
        {
            try
            {
                await _objectMetadataRepository.AddRangeAsync(context.CreatedObjectMetadata, cancellationToken);
                context.DatabaseQueriesCount++;
            }
            catch (Exception ex) when (ex.Message.Contains("duplicate key") || ex.Message.Contains("IX_ObjectMetadata_ObjectId_MetadataId"))
            {
                _logger.LogWarning("Duplicate key error when saving ObjectMetadata batch, attempting individual saves");

                // If batch fails due to duplicates, try saving individually
                var successfulObjectMetadata = new List<ObjectMetadata>();
                foreach (var objectMetadata in context.CreatedObjectMetadata)
                {
                    try
                    {
                        await _objectMetadataRepository.AddAsync(objectMetadata, cancellationToken);
                        successfulObjectMetadata.Add(objectMetadata);
                        context.DatabaseQueriesCount++;
                    }
                    catch (Exception individualEx) when (individualEx.Message.Contains("duplicate key") || individualEx.Message.Contains("IX_ObjectMetadata_ObjectId_MetadataId"))
                    {
                        _logger.LogWarning("Skipping duplicate ObjectMetadata for Object '{ObjectId}' and Metadata '{MetadataId}'",
                            objectMetadata.ObjectId, objectMetadata.MetadataId);
                        context.DatabaseQueriesCount++;
                    }
                }

                // Update the created object metadata list to only include successful ones
                context.CreatedObjectMetadata.Clear();
                context.CreatedObjectMetadata.AddRange(successfulObjectMetadata);
            }
        }

        // Add displays
        if (context.CreatedDisplays.Any())
        {
            await _displayRepository.AddRangeAsync(context.CreatedDisplays, cancellationToken);
            context.DatabaseQueriesCount++;
        }

        // Add actions
        if (context.CreatedActions.Any())
        {
            await _actionRepository.AddRangeAsync(context.CreatedActions, cancellationToken);
            context.DatabaseQueriesCount++;
        }

        // Add display-action relationships - handle duplicates gracefully
        if (context.CreatedDisplayActions.Any())
        {
            try
            {
                await _displayActionRepository.AddRangeAsync(context.CreatedDisplayActions, cancellationToken);
                context.DatabaseQueriesCount++;
            }
            catch (Exception ex) when (ex.Message.Contains("duplicate key") || ex.Message.Contains("IX_DisplayAction"))
            {
                _logger.LogWarning("Duplicate key error when saving DisplayAction batch, attempting individual saves");

                // If batch fails due to duplicates, try saving individually
                var successfulDisplayActions = new List<DisplayAction>();
                foreach (var displayAction in context.CreatedDisplayActions)
                {
                    try
                    {
                        await _displayActionRepository.AddAsync(displayAction, cancellationToken);
                        successfulDisplayActions.Add(displayAction);
                        context.DatabaseQueriesCount++;
                    }
                    catch (Exception individualEx) when (individualEx.Message.Contains("duplicate key") || individualEx.Message.Contains("IX_DisplayAction"))
                    {
                        _logger.LogWarning("Skipping duplicate DisplayAction for Display '{DisplayId}', Action '{ActionId}', Object '{ObjectId}'",
                            displayAction.DisplayId, displayAction.ActionId, displayAction.ObjectId);
                        context.DatabaseQueriesCount++;
                    }
                }

                // Update the created display action list to only include successful ones
                context.CreatedDisplayActions.Clear();
                context.CreatedDisplayActions.AddRange(successfulDisplayActions);
            }
        }

        // Add roles
        if (context.CreatedRoles.Any())
        {
            await _roleRepository.AddRangeAsync(context.CreatedRoles, cancellationToken);
            context.DatabaseQueriesCount++;
        }
    }

    private void BuildSuccessResponse(
        ProductStructureCreationResult result,
        ProductStructureProcessingContext context)
    {
        result.Success = true;
        result.Message = "Product structure processed successfully";

        // Set counts for created entities
        result.TotalProductsCreated = context.CreatedProducts.Count;
        result.TotalObjectsCreated = context.CreatedObjects.Count;
        result.TotalMetadataCreated = context.CreatedMetadata.Count;
        result.TotalObjectMetadataCreated = context.CreatedObjectMetadata.Count;
        result.TotalDisplaysCreated = context.CreatedDisplays.Count;
        result.TotalActionsCreated = context.CreatedActions.Count;
        result.TotalDisplayActionsCreated = context.CreatedDisplayActions.Count;
        result.TotalRolesCreated = context.CreatedRoles.Count;

        // Set counts for existing entities
        result.TotalProductsExisting = context.ExistingProducts.Count;
        result.TotalObjectsExisting = context.ExistingObjects.Count;
        result.TotalMetadataExisting = context.ExistingMetadata.Count;
        result.TotalObjectMetadataExisting = context.ExistingObjectMetadata.Count;
        result.TotalDisplaysExisting = context.ExistingDisplays.Count;
        result.TotalActionsExisting = context.ExistingActions.Count;
        result.TotalDisplayActionsExisting = context.ExistingDisplayActions.Count;

        // Build product response structure from processed products
        foreach (var productProcessingInfo in context.ProcessedProducts)
        {
            var productInfo = BuildCreatedProductInfo(productProcessingInfo, context);
            result.CreatedProducts.Add(productInfo);
        }

        // Set metrics
        result.Metrics.DatabaseQueriesCount = context.DatabaseQueriesCount;
        result.Metrics.MaxHierarchyDepth = CalculateMaxDepthFromProcessedProducts(context.ProcessedProducts);
    }

    private CreatedProductInfo BuildCreatedProductInfo(
        ProductProcessingInfo productProcessingInfo,
        ProductStructureProcessingContext context)
    {
        var product = productProcessingInfo.Product;
        var rootObjects = productProcessingInfo.Objects.Where(o => o.Level == 0).ToList();

        var productInfo = new CreatedProductInfo
        {
            ProductId = product.Id,
            Name = product.Name,
            WasCreated = productProcessingInfo.WasCreated,
            ObjectsCreated = productProcessingInfo.Objects.Count(o => o.WasCreated),
            ObjectsExisting = productProcessingInfo.Objects.Count(o => !o.WasCreated),
            MetadataFieldsCreated = productProcessingInfo.Metadata.Count(m => m.WasCreated),
            MetadataFieldsExisting = productProcessingInfo.Metadata.Count(m => !m.WasCreated)
        };

        // Build root objects hierarchy
        foreach (var rootObjectInfo in rootObjects)
        {
            var objectInfo = BuildCreatedObjectInfo(rootObjectInfo);
            productInfo.RootObjects.Add(objectInfo);
        }

        return productInfo;
    }

    private ProductStructureCreatedObjectInfo BuildCreatedObjectInfo(
        ObjectProcessingInfo objectProcessingInfo)
    {
        var objectEntity = objectProcessingInfo.Object;

        var objectInfo = new ProductStructureCreatedObjectInfo
        {
            ObjectId = objectEntity.Id,
            Name = objectEntity.Name,
            WasCreated = objectProcessingInfo.WasCreated,
            ParentObjectId = objectEntity.ParentObjectId,
            Level = objectProcessingInfo.Level,
            MetadataFieldsCreated = objectProcessingInfo.Metadata.Count(m => m.WasCreated),
            MetadataFieldsExisting = objectProcessingInfo.Metadata.Count(m => !m.WasCreated),
            DisplaysCreated = objectProcessingInfo.Displays.Count(d => d.WasCreated),
            DisplaysExisting = objectProcessingInfo.Displays.Count(d => !d.WasCreated),
            ActionsCreated = objectProcessingInfo.Displays.SelectMany(d => d.Actions).Count(a => a.WasCreated),
            ActionsExisting = objectProcessingInfo.Displays.SelectMany(d => d.Actions).Count(a => !a.WasCreated),
            DisplayActionsCreated = objectProcessingInfo.Displays.SelectMany(d => d.Actions).Count(a => a.DisplayActionWasCreated),
            DisplayActionsExisting = objectProcessingInfo.Displays.SelectMany(d => d.Actions).Count(a => !a.DisplayActionWasCreated)
        };

        // Build child objects recursively
        foreach (var childObjectInfo in objectProcessingInfo.Children)
        {
            var childInfo = BuildCreatedObjectInfo(childObjectInfo);
            objectInfo.Children.Add(childInfo);
        }

        return objectInfo;
    }

    private static int CalculateMaxDepthFromProcessedProducts(List<ProductProcessingInfo> processedProducts)
    {
        if (!processedProducts.Any()) return 0;

        var maxDepth = 0;
        foreach (var productInfo in processedProducts)
        {
            var productMaxDepth = CalculateMaxDepthFromObjects(productInfo.Objects);
            maxDepth = Math.Max(maxDepth, productMaxDepth);
        }

        return maxDepth;
    }

    private static int CalculateMaxDepthFromObjects(List<ObjectProcessingInfo> objects)
    {
        if (!objects.Any()) return 0;

        return objects.Max(obj => obj.Level) + 1;
    }

    private static int CalculateMaxDepth(List<Domain.Entities.Object> objects)
    {
        if (!objects.Any()) return 0;

        var rootObjects = objects.Where(o => o.ParentObjectId == null).ToList();
        return rootObjects.Any() ? rootObjects.Max(root => CalculateDepth(root, objects, 1)) : 0;
    }

    private static int CalculateDepth(Domain.Entities.Object obj, List<Domain.Entities.Object> allObjects, int currentDepth)
    {
        var children = allObjects.Where(o => o.ParentObjectId == obj.Id).ToList();
        return children.Any() ? children.Max(child => CalculateDepth(child, allObjects, currentDepth + 1)) : currentDepth;
    }

    private async Task CreateDefaultRolesForProductsAsync(
        List<Product> createdProducts,
        ProductStructureProcessingContext context,
        CancellationToken cancellationToken)
    {
        var defaultRoleNames = new[] { "Admin", "CustomerAdmin", "User" };

        foreach (var product in createdProducts)
        {
            foreach (var roleName in defaultRoleNames)
            {
                var role = new Role
                {
                    Id = Guid.NewGuid(),
                    Name = $"{roleName}",
                    NormalizedName = $"{product.Name}_{roleName}".ToUpperInvariant(),
                    ProductId = product.Id,
                    Description = $"{roleName} role for {product.Name} product",
                    IsSystemRole = true,
                    IsActive = true,
                    Permissions = "{}",
                    CreatedAt = DateTime.UtcNow,
                    ModifiedAt = DateTime.UtcNow
                };

                context.CreatedRoles.Add(role);

                _logger.LogInformation("Creating default role '{RoleName}' for product '{ProductName}' with ID: {RoleId}",
                    role.Name, product.Name, role.Id);
            }
        }
    }

    private static string MapDataTypeName(string inputType)
    {
        return inputType?.ToLowerInvariant() switch
        {
            "string" or "text" => "text",
            "int" or "integer" or "number" => "number",
            "bool" or "boolean" => "boolean",
            "datetime" or "date" => "datetime",
            "email" => "email",
            "phone" => "phone",
            "url" => "url",
            "address" => "address",
            "currency" => "currency",
            "percentage" => "percentage",
            "file" => "file",
            "image" => "image",
            "select" => "select",
            "multiselect" => "multiselect",
            "radio" => "radio",
            "checkbox" => "checkbox",
            "textarea" => "textarea",
            "richtext" => "richtext",
            "color" => "color",
            "slider" => "slider",
            "rating" => "rating",
            "tag" => "tag",
            "guid" => "guid",
            "year" => "year",
            "month" => "month",
            "day" => "day",
            "time" => "time",
            _ => "text" // Default fallback
        };
    }

    #endregion
}

internal class ProductStructureProcessingContext
{
    public Dictionary<string, DataType> AllDataTypes { get; set; } = new();
    public Dictionary<string, Metadata> AllMetadata { get; set; } = new();

    // Created entities
    public List<Product> CreatedProducts { get; set; } = new();
    public List<Domain.Entities.Object> CreatedObjects { get; set; } = new();
    public List<Metadata> CreatedMetadata { get; set; } = new();
    public List<ObjectMetadata> CreatedObjectMetadata { get; set; } = new();
    public List<Display> CreatedDisplays { get; set; } = new();
    public List<Domain.Entities.Action> CreatedActions { get; set; } = new();
    public List<DisplayAction> CreatedDisplayActions { get; set; } = new();
    public List<Role> CreatedRoles { get; set; } = new();

    // Existing entities
    public List<Product> ExistingProducts { get; set; } = new();
    public List<Domain.Entities.Object> ExistingObjects { get; set; } = new();
    public List<Metadata> ExistingMetadata { get; set; } = new();
    public List<ObjectMetadata> ExistingObjectMetadata { get; set; } = new();
    public List<Display> ExistingDisplays { get; set; } = new();
    public List<Domain.Entities.Action> ExistingActions { get; set; } = new();
    public List<DisplayAction> ExistingDisplayActions { get; set; } = new();

    // Processed entities (for response building)
    public List<ProductProcessingInfo> ProcessedProducts { get; set; } = new();

    public HashSet<string> CreatedObjectMetadataKeys { get; set; } = new();
    public int DatabaseQueriesCount { get; set; }
}

internal class ProductProcessingInfo
{
    public Product Product { get; set; } = null!;
    public bool WasCreated { get; set; }
    public List<ObjectProcessingInfo> Objects { get; set; } = new();
    public List<MetadataProcessingInfo> Metadata { get; set; } = new();
}

internal class ObjectProcessingInfo
{
    public Domain.Entities.Object Object { get; set; } = null!;
    public bool WasCreated { get; set; }
    public int Level { get; set; }
    public List<MetadataProcessingInfo> Metadata { get; set; } = new();
    public List<ObjectProcessingInfo> Children { get; set; } = new();
    public List<DisplayProcessingInfo> Displays { get; set; } = new();
}

internal class MetadataProcessingInfo
{
    public Metadata Metadata { get; set; } = null!;
    public bool WasCreated { get; set; }
}

internal class DisplayProcessingInfo
{
    public Display Display { get; set; } = null!;
    public bool WasCreated { get; set; }
    public List<ActionProcessingInfo> Actions { get; set; } = new();
}

internal class ActionProcessingInfo
{
    public Domain.Entities.Action Action { get; set; } = null!;
    public bool WasCreated { get; set; }
    public DisplayAction DisplayAction { get; set; } = null!;
    public bool DisplayActionWasCreated { get; set; }
}

/// <summary>
/// Handler for validating product structure
/// </summary>
public class ValidateProductStructureQueryHandler : IRequestHandler<ValidateProductStructureQuery, Result<ProductStructureCreationResult>>
{
    private readonly ILogger<ValidateProductStructureQueryHandler> _logger;

    public ValidateProductStructureQueryHandler(
        ILogger<ValidateProductStructureQueryHandler> logger)
    {
        _logger = logger;
    }

    public async Task<Result<ProductStructureCreationResult>> Handle(
        ValidateProductStructureQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing product structure validation for {ProductCount} products",
                request.Products?.Count ?? 0);

            var result = new ProductStructureCreationResult();
            var errors = new List<string>();

            // Validate request structure
            if (request.Products == null || !request.Products.Any())
            {
                errors.Add("At least one product must be provided");
            }
            else
            {
                // Validate each product
                foreach (var product in request.Products)
                {
                    ValidateProductStructure(product, errors);
                }

                // Validate unique product names
                var productNames = request.Products.Select(p => p.Name.ToLowerInvariant()).ToList();
                var duplicateNames = productNames.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key);
                foreach (var duplicateName in duplicateNames)
                {
                    errors.Add($"Duplicate product name found: '{duplicateName}'");
                }
            }

            result.Success = !errors.Any();
            result.Errors = errors;
            result.Message = result.Success ? "Validation passed" : "Validation failed";

            if (result.Success)
            {
                _logger.LogInformation("Product structure validation completed successfully");
                return Result<ProductStructureCreationResult>.Success(result, "Validation completed successfully");
            }
            else
            {
                _logger.LogWarning("Product structure validation failed. Errors: {Errors}",
                    string.Join(", ", result.Errors));

                return Result<ProductStructureCreationResult>.Success(result, "Validation completed with errors");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during product structure validation");

            return Result<ProductStructureCreationResult>.Failure("An unexpected error occurred during validation");
        }
    }

    private void ValidateProductStructure(ProductStructureDto product, List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(product.Name))
        {
            errors.Add("Product name is required");
        }

        // Validate objects if present
        if (product.Objects?.Any() == true)
        {
            ValidateObjectsStructure(product.Objects, $"Product '{product.Name}'", 0, errors);
        }
    }

    private void ValidateObjectsStructure(List<ObjectStructureDto> objects, string parentPath, int depth, List<string> errors)
    {
        if (depth > 10) // Prevent excessive nesting
        {
            errors.Add($"Maximum hierarchy depth (10) exceeded at path '{parentPath}'");
            return;
        }

        var names = new HashSet<string>();
        foreach (var obj in objects)
        {
            if (string.IsNullOrWhiteSpace(obj.Name))
            {
                errors.Add($"Object name is required at path '{parentPath}'");
                continue;
            }

            if (!names.Add(obj.Name.ToLowerInvariant()))
            {
                errors.Add($"Duplicate object name '{obj.Name}' found at path '{parentPath}'");
            }

            // Validate child objects recursively
            if (obj.Objects?.Any() == true)
            {
                var currentPath = $"{parentPath}/{obj.Name}";
                ValidateObjectsStructure(obj.Objects, currentPath, depth + 1, errors);
            }
        }
    }
}
