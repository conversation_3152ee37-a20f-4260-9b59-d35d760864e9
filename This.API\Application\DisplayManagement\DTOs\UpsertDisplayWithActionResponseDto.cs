using Application.ActionManagement.DTOs;
using Application.DisplayActionManagement.DTOs;

namespace Application.DisplayManagement.DTOs;

/// <summary>
/// Comprehensive upsert response DTO containing created/updated Display, Action, and DisplayAction
/// </summary>
public class UpsertDisplayWithActionResponseDto
{
    /// <summary>
    /// Created or updated Display information
    /// </summary>
    public DisplayDto Display { get; set; } = null!;

    /// <summary>
    /// Created or updated Action information
    /// </summary>
    public ActionDto Action { get; set; } = null!;

    /// <summary>
    /// Created or updated DisplayAction relationship
    /// </summary>
    public DisplayActionDto DisplayAction { get; set; } = null!;

    /// <summary>
    /// Indicates whether the Display was created (true) or updated (false)
    /// </summary>
    public bool DisplayWasCreated { get; set; }

    /// <summary>
    /// Indicates whether the Action was created (true) or updated (false)
    /// </summary>
    public bool ActionWasCreated { get; set; }

    /// <summary>
    /// Indicates whether the DisplayAction was created (true) or updated (false)
    /// </summary>
    public bool DisplayActionWasCreated { get; set; }

    /// <summary>
    /// Summary of the operation performed
    /// </summary>
    public string OperationSummary { get; set; } = string.Empty;

    /// <summary>
    /// Total processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}
