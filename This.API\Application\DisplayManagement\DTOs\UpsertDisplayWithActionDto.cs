using System.ComponentModel.DataAnnotations;

namespace Application.DisplayManagement.DTOs;

/// <summary>
/// Comprehensive upsert request DTO for Display, Action, and DisplayAction
/// </summary>
public class UpsertDisplayWithActionDto
{
    #region Display Properties

    /// <summary>
    /// Display name - 'List', 'View', 'Update', 'Create', 'Card', or custom names
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Description of the display
    /// </summary>
    public string? DisplayDescription { get; set; }

    /// <summary>
    /// Display name for UI
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string DisplayDisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default display for this type
    /// </summary>
    public bool DisplayIsDefault { get; set; } = false;

    /// <summary>
    /// URL route template for navigation
    /// </summary>
    [MaxLength(500)]
    public string? DisplayRouteTemplate { get; set; }

    /// <summary>
    /// Icon for the display
    /// </summary>
    [MaxLength(100)]
    public string? DisplayIcon { get; set; }

    /// <summary>
    /// Sort order for display ordering
    /// </summary>
    public int DisplaySortOrder { get; set; } = 0;

    #endregion

    #region Action Properties

    /// <summary>
    /// Action name
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string ActionName { get; set; } = string.Empty;

    /// <summary>
    /// Description of the action
    /// </summary>
    public string? ActionDescription { get; set; }

    /// <summary>
    /// API endpoint template for API actions
    /// </summary>
    [MaxLength(500)]
    public string? ActionEndpointTemplate { get; set; }

    /// <summary>
    /// Navigation target for Navigation actions
    /// </summary>
    [MaxLength(500)]
    public string? ActionNavigationTarget { get; set; }

    /// <summary>
    /// Icon for the action
    /// </summary>
    [MaxLength(100)]
    public string? ActionIcon { get; set; }

    /// <summary>
    /// Button style - 'Primary', 'Secondary', 'Danger', etc.
    /// </summary>
    [MaxLength(50)]
    public string? ActionButtonStyle { get; set; }

    /// <summary>
    /// Confirmation dialog message
    /// </summary>
    public string? ActionConfirmationMessage { get; set; }

    /// <summary>
    /// Success message to display after action completion
    /// </summary>
    public string? ActionSuccessMessage { get; set; }

    /// <summary>
    /// Error message to display on action failure
    /// </summary>
    public string? ActionErrorMessage { get; set; }

    #endregion

    #region DisplayAction Properties

    /// <summary>
    /// Object ID - Foreign key to Object entity (required for DisplayAction relationship)
    /// </summary>
    [Required]
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Access level - 'Public', 'Protected', 'Private'
    /// </summary>
    [MaxLength(50)]
    public string AccessLevel { get; set; } = "Public";

    /// <summary>
    /// Whether this is a default action
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Sort order for action ordering
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// Whether the action is visible in toolbar
    /// </summary>
    public bool IsVisibleInToolbar { get; set; } = true;

    /// <summary>
    /// Whether the action is visible in context menu
    /// </summary>
    public bool IsVisibleInContextMenu { get; set; } = false;

    /// <summary>
    /// Whether the action is visible in row actions
    /// </summary>
    public bool IsVisibleInRowActions { get; set; } = false;

    #endregion
}
