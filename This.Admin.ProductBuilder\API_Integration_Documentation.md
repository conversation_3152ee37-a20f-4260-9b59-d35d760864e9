# Comprehensive Subscriptions API Integration

## Overview

This document describes the integration between the This.Admin.ProductBuilder frontend and the comprehensive-entity/subscriptions API endpoint.

## API Endpoint Details

### Base Information
- **Endpoint**: `GET /api/comprehensive-entity/subscriptions`
- **Base URL**: `https://localhost:7222/api`
- **Authentication**: None required (AllowAnonymous)
- **Content-Type**: `application/json`

### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `tenantId` | string | - | Filter by specific tenant ID |
| `productId` | Guid | - | Filter by product ID |
| `status` | string | - | Filter by subscription status |
| `subscriptionType` | string | - | Filter by subscription type |
| `isActive` | boolean | - | Filter by active status |
| `isExpired` | boolean | - | Filter by expired status |
| `pricingTier` | string | - | Filter by pricing tier |
| `searchTerm` | string | - | Search in subscription type, product name, tenant name |
| `startDateFrom` | DateTime | - | Filter by start date from |
| `startDateTo` | DateTime | - | Filter by start date to |
| `endDateFrom` | DateTime | - | Filter by end date from |
| `endDateTo` | DateTime | - | Filter by end date to |
| `expiringWithinDays` | int | - | Filter subscriptions expiring within N days |
| `pageNumber` | int | 1 | Page number for pagination |
| `pageSize` | int | 50 | Page size (max: 1000) |
| `orderBy` | string | "CreatedAt" | Order by field |
| `orderDirection` | string | "desc" | Order direction (asc/desc) |
| `includeSummary` | boolean | true | Include summary statistics |

### Response Structure

```typescript
interface ApiResult<ComprehensiveSubscriptionResponse> {
  succeeded: boolean;
  data: {
    subscriptions: ComprehensiveSubscriptionDto[];
    totalCount: number;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    summary: ComprehensiveSubscriptionSummary;
  };
  message: string;
  errors?: string[];
}
```

### Subscription Data Structure

```typescript
interface ComprehensiveSubscriptionDto {
  id: string;
  tenantId: string;
  tenantName: string | null;
  productId: string;
  productName: string | null;
  subscriptionType: string;
  status: string;
  startDate: string;
  endDate: string | null;
  autoRenew: boolean;
  pricingTier: string | null;
  version: string;
  templateJson: string;
  isActive: boolean;
  metadataCount: number;
  createdAt: string;
  createdBy: string;
  modifiedAt: string | null;
  modifiedBy: string | null;
  isExpired: boolean;
  daysUntilExpiration: number | null;
}
```

## Frontend Integration

### Service Layer (`subscriptionService.ts`)

The service provides both new comprehensive methods and legacy compatibility:

#### New Methods:
- `getComprehensiveSubscriptions(params)` - Direct API call
- `getSubscriptionSummary()` - Get summary statistics
- `getAvailableTenants()` - Get list of available tenants

#### Legacy Methods (Backward Compatible):
- `getSubscriptions(params, tenant)` - Converts to comprehensive format
- `getSubscriptionById(id, tenant)` - Uses comprehensive endpoint

### UI Components (`Subscriptions.tsx`)

#### Features Implemented:
1. **Real-time Data Loading**: Fetches data from comprehensive API
2. **Advanced Filtering**: Status, tenant, search term filters
3. **Pagination**: Server-side pagination with page size controls
4. **Summary Statistics**: Real-time stats cards with API data
5. **Error Handling**: Comprehensive error states and retry mechanisms
6. **Loading States**: Spinners and loading indicators
7. **Responsive Design**: Mobile-friendly table and controls

#### Data Binding:
- **Stats Cards**: Use summary data from API
- **Table Rows**: Display comprehensive subscription data
- **Filters**: Send parameters to API for server-side filtering
- **Pagination**: Controlled by API response metadata

## Testing

### Manual Testing Steps:

1. **Basic Functionality**:
   ```bash
   # Open browser console and run:
   fetch('https://localhost:7222/api/comprehensive-entity/subscriptions?pageSize=5')
     .then(r => r.json())
     .then(console.log)
   ```

2. **Filter Testing**:
   ```bash
   # Test with filters:
   fetch('https://localhost:7222/api/comprehensive-entity/subscriptions?isActive=true&status=active')
     .then(r => r.json())
     .then(console.log)
   ```

3. **Pagination Testing**:
   ```bash
   # Test pagination:
   fetch('https://localhost:7222/api/comprehensive-entity/subscriptions?pageNumber=1&pageSize=2')
     .then(r => r.json())
     .then(console.log)
   ```

### Automated Testing:

Use the provided test script:
```javascript
// In browser console:
// Load and run: src/test-api-integration.js
```

## Error Scenarios

### Network Errors:
- **Connection Failed**: Shows retry button
- **Timeout**: Automatic retry with exponential backoff
- **Invalid Response**: Graceful fallback to empty state

### API Errors:
- **400 Bad Request**: Shows parameter validation errors
- **500 Server Error**: Shows generic error with retry option
- **Empty Response**: Shows "No data available" message

### Data Validation:
- **Missing Fields**: Uses fallback values
- **Invalid Dates**: Shows "Invalid date" placeholder
- **Null Values**: Handles gracefully with default displays

## Performance Optimizations

1. **Debounced Search**: 500ms delay on search input
2. **Efficient Pagination**: Server-side pagination reduces data transfer
3. **Summary Caching**: Summary data cached for 5 minutes
4. **Lazy Loading**: Only loads visible data
5. **Error Boundaries**: Prevents crashes from API errors

## Configuration

### Environment Variables:
```env
REACT_APP_API_BASE_URL=https://localhost:7222/api
REACT_APP_DEFAULT_PAGE_SIZE=10
REACT_APP_MAX_PAGE_SIZE=1000
```

### Default Settings:
- Page Size: 10 items
- Default Tenant: 'lrbnewqa'
- Refresh Interval: Manual only
- Cache Duration: 5 minutes for summary data

## Testing the Integration

### Quick Test Steps:

1. **Start the API Server**: Ensure the backend API is running on `https://localhost:7222`
2. **Start the Frontend**: Run the React app with `npm start`
3. **Navigate to Subscriptions**: Go to the Subscriptions page
4. **Run API Test**: Click the "Run API Tests" button in the test component
5. **Check Results**: Verify all tests pass and data is displayed

### Expected Test Results:
- ✅ Comprehensive subscriptions API: SUCCESS
- ✅ Legacy subscriptions API: SUCCESS
- ✅ Summary API: SUCCESS
- ✅ Available tenants: SUCCESS

### Manual Verification:
1. **Stats Cards**: Should show real numbers from API summary
2. **Subscription Table**: Should display actual subscription data
3. **Tenant Filter**: Should populate with real tenant IDs
4. **Pagination**: Should work with server-side data
5. **Search**: Should filter results via API
6. **Error Handling**: Should show friendly messages when API is down

## Troubleshooting

### Common Issues:

1. **Import Errors**:
   - Fixed by using local type definitions
   - Removed circular dependencies
   - Simplified imports

2. **CORS Errors**:
   - Ensure API allows frontend origin (localhost:3000)
   - Add CORS headers in API configuration

3. **SSL Certificate**:
   - Use valid HTTPS certificate for localhost
   - Or configure API to use HTTP for development

4. **Network Timeouts**:
   - Check API server status
   - Verify endpoint URLs are correct

5. **Empty Data**:
   - Verify database has subscription records
   - Check API returns valid response structure

6. **TypeScript Errors**:
   - All types are now defined locally
   - No external type dependencies

### Debug Mode:
Enable detailed console logging:
```javascript
// In browser console
localStorage.setItem('debug', 'true');
```

### API Test Component:
The page includes a test component that verifies:
- API connectivity
- Response structure
- Error handling
- Data transformation

### Import Issues Fixed:
If you encounter TypeScript import errors:
```typescript
// Instead of importing TemplateResponse directly:
// import { templateService, TemplateResponse, TemplateUtils } from '../services/templateService';

// Use local interface definition:
import { templateService, TemplateUtils } from '../services/templateService';

interface TemplateResponse {
  id: string;
  name: string;
  version: string;
  stage: string;
  templateJson: any;
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  isActive: boolean;
  isDeleted: boolean;
}
```

## Templates API Integration

### Template Dropdown Integration

#### **API Endpoint Details:**
- **Endpoint**: `GET /api/templates`
- **Base URL**: `https://localhost:7222/api`
- **Authentication**: None required
- **Content-Type**: `application/json`

#### **Query Parameters for Live Templates:**
| Parameter | Value | Description |
|-----------|-------|-------------|
| `stage` | "live" | Filter templates by live stage |
| `isActive` | true | Only active templates |
| `pageSize` | 1000 | Get all available templates |
| `pageNumber` | 1 | First page |

#### **Template Response Structure:**
```typescript
interface TemplateDto {
  id: string;
  name: string;
  version: string;
  stage: string;
  templateJson: string;
  createdAt: string;
  publishedAt?: string;
  isActive: boolean;
  isDeleted: boolean;
}
```

#### **Dropdown Implementation:**
1. **Format**: `"{templateName} ({stage}) - v{version}"`
2. **Example**: `"KitchenSync (live) - v2.1.0"`
3. **Sorting**:
   - Primary: Template name (alphabetical ascending)
   - Secondary: Version (descending - newest first)
4. **Filtering**: Only `stage="live"` and `isActive=true`

#### **Service Methods:**
```typescript
// Get live templates for dropdown
templateService.getLiveTemplates(): Promise<TemplateResponse[]>

// Get templates by stage with sorting
templateService.getTemplatesByStage(stage: string): Promise<TemplateResponse[]>

// Format template for dropdown display
TemplateUtils.formatTemplateForDropdown(template): string

// Sort templates for dropdown
TemplateUtils.sortTemplatesForDropdown(templates): TemplateResponse[]
```

#### **Add Subscription Modal Integration:**
- ✅ **Dynamic Loading**: Templates loaded when modal opens
- ✅ **Loading States**: Spinner while fetching templates
- ✅ **Error Handling**: User-friendly error messages
- ✅ **Empty State**: Message when no live templates available
- ✅ **Proper Sorting**: Name (A-Z), Version (newest first)
- ✅ **Formatted Display**: "Name (Stage) - vVersion" format

#### **Testing the Integration:**
1. **Template API Test Component**: Included for verification
2. **Manual Testing**: Use "Run Template API Tests" button
3. **Expected Results**:
   - ✅ All templates API: SUCCESS
   - ✅ Live templates API: SUCCESS
   - ✅ Template sorting: SUCCESS
   - ✅ Template formatting: SUCCESS
   - ✅ Stage filtering: SUCCESS

## Three-Step Subscription Creation API Integration

### **Complete Implementation Overview**

The "Create Subscription" button now executes a comprehensive three-step API integration process:

#### **Step 1: Tenant Creation**
- **API Endpoint**: `POST /api/tenants`
- **Payload Structure**:
```typescript
{
  id: string;           // Generated tenant ID
  name: string;         // From tenant dropdown selection
  adminEmail: string;   // From tenant dropdown selection
  isActive: boolean;    // Set to true
  validUpto: string;    // 1 year from creation date
  issuer?: string;      // Optional, set to null
}
```
- **Response**: Returns created tenant ID for subsequent steps

#### **Step 2: Product Structure Creation**
- **API Endpoint**: `POST /api/comprehensive-entity/create-product-structure`
- **Headers**: `X-Tenant-Id` (from Step 1)
- **Payload Structure**:
```typescript
{
  products: [{
    id?: string;                    // Optional, from template
    name: string;                   // From template
    type?: string;                  // From template
    description?: string;           // From template
    version?: string;               // From template
    isActive?: boolean;             // From template
    metadata?: MetadataDto[];       // From template
    objects?: ObjectStructureDto[]; // From template (features)
  }]
}
```
- **Response**: Returns created product ID and structure details

#### **Step 3: Subscription Creation**
- **API Endpoint**: `POST /api/subscriptions`
- **Headers**: `X-Tenant-Id` (from Step 1)
- **Payload Structure**:
```typescript
{
  productId: string;        // From Step 2
  subscriptionType: string; // From form (basic/premium/enterprise)
  status: string;           // From form (active/inactive)
  startDate: string;        // From form
  endDate?: string;         // From form
  autoRenew: boolean;       // Hardcoded to false
  pricingTier?: string;     // Derived from subscription type
  version: string;          // From selected template
  templateJson: string;     // From selected template (stringified)
  isActive: boolean;        // Based on status
}
```
- **Response**: Returns created subscription details

### **Implementation Features**

#### **✅ Progress Visualization**
- Real-time step indicators showing current progress
- Visual progress bar with percentage completion
- Step-by-step status updates (⏳ Pending, 🔄 Processing, ✅ Complete)
- Processing time tracking and display

#### **✅ Error Handling & Rollback**
- **Step 1 Failure**: Shows tenant creation error, stops process
- **Step 2 Failure**: Shows product structure error, considers tenant cleanup
- **Step 3 Failure**: Shows subscription error, considers product/tenant cleanup
- Comprehensive error messages with specific step identification
- Validation before starting the process

#### **✅ User Experience**
- Loading states with step-specific messages
- Disabled form during creation process
- Success confirmation with processing time
- Automatic modal closure on success
- Toast notifications for success/failure

#### **✅ Data Integration**
- Template JSON parsing and structure conversion
- Pricing tier derivation from subscription type
- Tenant data extraction from dropdown selection
- Form validation before API calls

### **Service Architecture**

#### **SubscriptionCreationService Class**
```typescript
class SubscriptionCreationService {
  // Step 1: Create tenant
  async createTenant(tenantData: TenantDto): Promise<string>

  // Step 2: Create product structure from template
  async createProductStructure(templateJson: any, tenantId: string): Promise<string>

  // Step 3: Create subscription
  async createSubscription(subscriptionData: CreateSubscriptionDto, tenantId: string): Promise<string>

  // Main orchestration method
  async createCompleteSubscription(request: SubscriptionCreationRequest): Promise<SubscriptionCreationResult>
}
```

#### **Helper Methods**
- `parseTemplateToProductStructure()`: Converts template JSON to product structure
- `derivePricingTier()`: Maps subscription type to pricing tier
- `makeRequest()`: Centralized HTTP request handling with tenant headers

### **Testing & Validation**

#### **SubscriptionCreationTestComponent**
- Comprehensive test suite for three-step process
- Mock data generation for testing
- API endpoint validation
- Error scenario testing
- Performance metrics tracking

#### **Test Coverage**
- ✅ **Step 1**: Tenant creation with valid data
- ✅ **Step 2**: Product structure creation with template JSON
- ✅ **Step 3**: Subscription creation with all parameters
- ✅ **Error Handling**: Each step failure scenario
- ✅ **Integration**: End-to-end process validation

### **API Response Handling**

#### **Success Flow**
1. **Tenant Created**: Extract tenant ID from response
2. **Product Created**: Extract product ID from creation result
3. **Subscription Created**: Extract subscription ID and details
4. **UI Update**: Add new subscription to list, show success message

#### **Error Flow**
1. **Identify Failed Step**: Tenant, Product, or Subscription
2. **Extract Error Details**: API error messages and codes
3. **User Notification**: Step-specific error messages
4. **State Reset**: Return form to editable state

### **Performance Metrics**
- **Processing Time**: Tracks total time for all three steps
- **Step Timing**: Individual step performance monitoring
- **Success Rate**: Track completion vs failure rates
- **Error Analysis**: Categorize failures by step

## Future Enhancements

1. **Real-time Updates**: WebSocket integration for live data
2. **Export Functionality**: CSV/Excel export of subscription data
3. **Advanced Filters**: Date range pickers, multi-select filters
4. **Bulk Operations**: Multi-select and bulk actions
5. **Subscription Details**: Modal with detailed subscription view
6. **Template Versioning**: Support for template version selection
7. **Template Preview**: Show template details in modal
8. **Rollback Mechanism**: Automatic cleanup of created entities on failure
9. **Retry Logic**: Automatic retry for failed steps
10. **Audit Trail**: Track all creation steps for debugging
