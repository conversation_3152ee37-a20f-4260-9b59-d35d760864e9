// components/ThisFile.tsx
import React, { useState, useRef, useCallback } from 'react';
import {
  Upload,
  File,
  X,
  Check,
  AlertCircle,
  Download
} from 'lucide-react';

interface ThisFileProps {
  id: string;
  label: string;
  value: File[];
  onChange: (files: File[]) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  multiple?: boolean;
  maxFiles?: number;
  minFiles?: number;
  maxFileSize?: number; // in bytes
  minFileSize?: number; // in bytes
  allowedTypes?: string[]; // MIME types
  blockedTypes?: string[]; // MIME types
  allowedExtensions?: string[]; // file extensions
  blockedExtensions?: string[]; // file extensions
  maxTotalSize?: number; // total size of all files in bytes
  showFileList?: boolean;
  showFileSize?: boolean;
  showFileType?: boolean;
  showPreview?: boolean;
  allowDownload?: boolean;
  allowRemove?: boolean;
  dragAndDrop?: boolean;
  customValidation?: (files: File[]) => string | null;
}

interface ValidationRule {
  test: (files: File[]) => boolean;
  message: string;
}

const ThisFile: React.FC<ThisFileProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder = 'Choose files or drag and drop',
  required = false,
  multiple = false,
  maxFiles = 10,
  minFiles = 0,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  minFileSize = 0,
  allowedTypes,
  blockedTypes,
  allowedExtensions,
  blockedExtensions,
  maxTotalSize = 100 * 1024 * 1024, // 100MB
  showFileList = true,
  showFileSize = true,
  showFileType = true,

  allowDownload = false,
  allowRemove = true,
  dragAndDrop = true,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Helper functions
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileExtension = (filename: string): string => {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
  };

  const getTotalFileSize = (files: File[]): number => {
    return files.reduce((total, file) => total + file.size, 0);
  };

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (files) => files.length > 0,
        message: `${label} is required`
      });
    }

    // 2. Minimum files validation
    if (minFiles > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.length >= minFiles;
        },
        message: `${label} must have at least ${minFiles} file${minFiles > 1 ? 's' : ''}`
      });
    }

    // 3. Maximum files validation
    if (maxFiles > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.length <= maxFiles;
        },
        message: `${label} can have at most ${maxFiles} file${maxFiles > 1 ? 's' : ''}`
      });
    }

    // 4. File size validation (individual files)
    if (maxFileSize > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => file.size <= maxFileSize);
        },
        message: `Each file must be smaller than ${formatFileSize(maxFileSize)}`
      });
    }

    if (minFileSize > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => file.size >= minFileSize);
        },
        message: `Each file must be larger than ${formatFileSize(minFileSize)}`
      });
    }

    // 5. Total size validation
    if (maxTotalSize > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return getTotalFileSize(files) <= maxTotalSize;
        },
        message: `Total file size must be smaller than ${formatFileSize(maxTotalSize)}`
      });
    }

    // 6. File type validation (MIME types)
    if (allowedTypes && allowedTypes.length > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => allowedTypes.includes(file.type));
        },
        message: `Only these file types are allowed: ${allowedTypes.join(', ')}`
      });
    }

    if (blockedTypes && blockedTypes.length > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return !files.some(file => blockedTypes.includes(file.type));
        },
        message: `These file types are not allowed: ${blockedTypes.join(', ')}`
      });
    }

    // 7. File extension validation
    if (allowedExtensions && allowedExtensions.length > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => allowedExtensions.includes(getFileExtension(file.name)));
        },
        message: `Only these file extensions are allowed: ${allowedExtensions.join(', ')}`
      });
    }

    if (blockedExtensions && blockedExtensions.length > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return !files.some(file => blockedExtensions.includes(getFileExtension(file.name)));
        },
        message: `These file extensions are not allowed: ${blockedExtensions.join(', ')}`
      });
    }

    // 8. Custom validation
    if (customValidation) {
      rules.push({
        test: (files) => {
          const customError = customValidation(files);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateFiles = (files: File[]): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(files)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleFileChange = useCallback((newFiles: File[]) => {
    const validationErrors = validateFiles(newFiles);
    setErrors(validationErrors);
    setIsValidated(true);
    onValidation?.(validationErrors);
    onChange(newFiles);
  }, [onChange, onValidation]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const combinedFiles = multiple ? [...value, ...files] : files;
    handleFileChange(combinedFiles);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled && !readOnly && dragAndDrop) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    if (disabled || readOnly || !dragAndDrop) return;

    const files = Array.from(event.dataTransfer.files);
    const combinedFiles = multiple ? [...value, ...files] : files;
    handleFileChange(combinedFiles);
  };

  const removeFile = (index: number) => {
    if (disabled || readOnly || !allowRemove) return;

    const newFiles = value.filter((_, i) => i !== index);
    handleFileChange(newFiles);
  };

  const downloadFile = (file: File) => {
    if (!allowDownload) return;

    const url = URL.createObjectURL(file);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const openFileDialog = () => {
    if (!disabled && !readOnly) {
      fileInputRef.current?.click();
    }
  };

  const hasErrors = errors.length > 0;
  const totalSize = getTotalFileSize(value);

  return (
    <div className="file-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* File Input Wrapper */}
      <div className="text-input-wrapper">
        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          id={id}
          multiple={multiple}
          disabled={disabled}
          onChange={handleInputChange}
          className="hidden"
          accept={allowedTypes?.join(',') || allowedExtensions?.map(ext => `.${ext}`).join(',')}
        />

        {/* Drop Zone */}
        <div
          className={`file-drop-zone ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''} ${isDragOver ? 'drag-over' : ''} ${readOnly ? 'readonly' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={openFileDialog}
        >
          <div className="file-drop-content">
            <Upload className="file-upload-icon" size={24} />
            <p className="file-drop-text">
              {value.length > 0 ? `${value.length} file${value.length > 1 ? 's' : ''} selected` : placeholder}
            </p>
            {dragAndDrop && !readOnly && !disabled && (
              <p className="file-drop-hint">
                Click to browse or drag and drop files here
              </p>
            )}
            {!dragAndDrop && !readOnly && !disabled && (
              <p className="file-drop-hint">
                Click to browse files
              </p>
            )}
          </div>
        </div>

        {/* File List */}
        {showFileList && value.length > 0 && (
          <div className="file-list">
            {value.map((file, index) => (
              <div key={`${file.name}-${index}`} className="file-item">
                <div className="file-info">
                  <File className="file-icon" size={16} />
                  <div className="file-details">
                    <span className="file-name">{file.name}</span>
                    <div className="file-meta">
                      {showFileSize && (
                        <span className="file-size">{formatFileSize(file.size)}</span>
                      )}
                      {showFileType && (
                        <span className="file-type">{file.type || 'Unknown'}</span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="file-actions">
                  {allowDownload && (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadFile(file);
                      }}
                      className="file-action-button"
                      title="Download file"
                    >
                      <Download size={14} />
                    </button>
                  )}
                  {allowRemove && !readOnly && !disabled && (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(index);
                      }}
                      className="file-action-button remove"
                      title="Remove file"
                    >
                      <X size={14} />
                    </button>
                  )}
                </div>
              </div>
            ))}

            {/* Total Size Display */}
            {value.length > 1 && (
              <div className="file-total-size">
                Total: {formatFileSize(totalSize)}
                {maxTotalSize > 0 && ` / ${formatFileSize(maxTotalSize)}`}
              </div>
            )}
          </div>
        )}

        {/* Validation Icon */}
        {isValidated && (
          <div className="validation-icon">
            {!hasErrors ? (
              <Check className="text-green-500" size={16} />
            ) : (
              <AlertCircle className="text-red-500" size={16} />
            )}
          </div>
        )}

        {/* Error Messages */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisFile;
