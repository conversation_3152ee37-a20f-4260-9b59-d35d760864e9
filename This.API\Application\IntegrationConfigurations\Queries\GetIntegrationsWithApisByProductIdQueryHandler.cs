using Application.IntegrationConfigurations.DTOs;
using Application.IntegrationConfigurations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Queries;

/// <summary>
/// Handler for GetIntegrationsWithApisByProductIdQuery
/// </summary>
public class GetIntegrationsWithApisByProductIdQueryHandler : IRequestHandler<GetIntegrationsWithApisByProductIdQuery, Result<List<IntegrationWithApiInfoDto>>>
{
    private readonly IReadRepository<Integration> _integrationRepository;
    private readonly IReadRepository<IntegrationConfiguration> _configurationRepository;
    private readonly IReadRepository<Product> _productRepository;
    private readonly ILogger<GetIntegrationsWithApisByProductIdQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationsWithApisByProductIdQueryHandler(
        IReadRepository<Integration> integrationRepository,
        IReadRepository<IntegrationConfiguration> configurationRepository,
        IReadRepository<Product> productRepository,
        ILogger<GetIntegrationsWithApisByProductIdQueryHandler> logger)
    {
        _integrationRepository = integrationRepository;
        _configurationRepository = configurationRepository;
        _productRepository = productRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<List<IntegrationWithApiInfoDto>>> Handle(
        GetIntegrationsWithApisByProductIdQuery request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting integrations with API info for Product {ProductId}", request.ProductId);

        try
        {
            // Step 1: Validate Product exists
            var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
            if (product == null)
            {
                return Result<List<IntegrationWithApiInfoDto>>.Failure($"Product with ID {request.ProductId} not found");
            }

            // Step 2: Get all integrations for the product
            var integrations = await GetIntegrationsForProductAsync(request.ProductId, request.IsActive, cancellationToken);

            if (!integrations.Any())
            {
                _logger.LogInformation("No integrations found for Product {ProductId}", request.ProductId);
                return Result<List<IntegrationWithApiInfoDto>>.Success(new List<IntegrationWithApiInfoDto>());
            }

            // Step 3: Get all configurations for these integrations
            var integrationIds = integrations.Select(i => i.Id).ToList();
            var configurations = await GetConfigurationsForIntegrationsAsync(integrationIds, cancellationToken);

            // Step 4: Build response DTOs
            var result = BuildIntegrationWithApiInfoDtos(integrations, configurations, product);

            _logger.LogInformation("Successfully retrieved {IntegrationCount} integrations with API info for Product {ProductId}", 
                result.Count, request.ProductId);

            return Result<List<IntegrationWithApiInfoDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting integrations with API info for Product {ProductId}", request.ProductId);
            return Result<List<IntegrationWithApiInfoDto>>.Failure($"Failed to get integrations with API info: {ex.Message}");
        }
    }

    /// <summary>
    /// Get integrations for the specified product
    /// </summary>
    private async Task<List<Integration>> GetIntegrationsForProductAsync(Guid productId, bool? isActive, CancellationToken cancellationToken)
    {
        var spec = new IntegrationsForProductSpec(productId, isActive);
        return await _integrationRepository.ListAsync(spec, cancellationToken);
    }

    /// <summary>
    /// Get configurations for the specified integrations with related data
    /// </summary>
    private async Task<List<IntegrationConfiguration>> GetConfigurationsForIntegrationsAsync(List<Guid> integrationIds, CancellationToken cancellationToken)
    {
        var spec = new IntegrationConfigurationsForIntegrationsSpec(integrationIds);
        return await _configurationRepository.ListAsync(spec, cancellationToken);
    }

    /// <summary>
    /// Build the response DTOs
    /// </summary>
    private static List<IntegrationWithApiInfoDto> BuildIntegrationWithApiInfoDtos(
        List<Integration> integrations,
        List<IntegrationConfiguration> configurations,
        Product product)
    {
        var result = new List<IntegrationWithApiInfoDto>();

        foreach (var integration in integrations)
        {
            // Get configurations for this integration
            var integrationConfigs = configurations.Where(c => c.IntegrationId == integration.Id).ToList();

            var integrationDto = new IntegrationWithApiInfoDto
            {
                IntegrationId = integration.Id,
                IntegrationName = integration.Name,
                ProductId = integration.ProductId,
                ProductName = product.Name,
                AuthType = integration.AuthType,
                IsIntegrationActive = integration.IsActive,
                SyncFrequency = integration.SyncFrequency,
                LastSyncAt = integration.LastSyncAt,
                IntegrationCreatedAt = integration.CreatedAt,
                IntegrationCreatedBy = integration.CreatedBy,
                ConfiguredApis = integrationConfigs.Select(config => new ApiConfigurationInfoDto
                {
                    ApiId = config.IntegrationApiId,
                    ApiName = config.IntegrationApi?.Name ?? string.Empty,
                    EndpointUrl = config.IntegrationApi?.EndpointUrl ?? string.Empty,
                    Schema = config.IntegrationApi?.Schema,
                    IsApiActive = config.IntegrationApi?.IsActive ?? false,
                    ConfigurationId = config.Id,
                    ObjectId = config.ObjectId,
                    ObjectName = config.Object?.Name ?? string.Empty,
                    Direction = config.Direction,
                    IsConfigActive = config.IsActive,
                    ConfigCreatedAt = config.CreatedAt,
                    ConfigCreatedBy = config.CreatedBy
                }).ToList()
            };

            result.Add(integrationDto);
        }

        return result;
    }
}
