using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.DisplayManagement.Commands;

/// <summary>
/// Delete Display command handler
/// </summary>
public class DeleteDisplayCommandHandler : IRequestHandler<DeleteDisplayCommand, Result<bool>>
{
    private readonly IRepository<Display> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteDisplayCommandHandler(IRepository<Display> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteDisplayCommand request, CancellationToken cancellationToken)
    {
        // Get existing display
        var existingDisplay = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (existingDisplay == null)
        {
            return Result<bool>.Failure($"Display with ID '{request.Id}' not found.");
        }

        // Soft delete the display
        await _repository.DeleteAsync(existingDisplay, cancellationToken);

        return Result<bool>.Success(true);
    }
}
