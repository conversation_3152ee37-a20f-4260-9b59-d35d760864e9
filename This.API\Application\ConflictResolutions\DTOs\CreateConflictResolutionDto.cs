namespace Application.ConflictResolutions.DTOs;

/// <summary>
/// Create Conflict Resolution DTO
/// </summary>
public class CreateConflictResolutionDto
{
    /// <summary>
    /// Integration ID this conflict resolution applies to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Object ID this conflict resolution applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Resolution strategy (e.g., "SourceWins", "TargetWins", "Manual", "Merge")
    /// </summary>
    public string ResolutionStrategy { get; set; } = string.Empty;

    /// <summary>
    /// Merge rules configuration stored as JSON
    /// </summary>
    public string? MergeRules { get; set; }
}
