import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../utils/constants.dart';
import '../widgets/app_drawer.dart';
import '../widgets/data_table_widget.dart';
import '../widgets/mobile_card_view_widget.dart';
import '../widgets/loading_widget.dart';
import '../widgets/error_widget.dart';
import '../models/card_data_model.dart';
import 'mobile_form_screen.dart';

/// Main application screen with responsive layout
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _isSearchExpanded = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _toggleSearch() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
      if (!_isSearchExpanded) {
        _searchController.clear();
        context.read<AppProvider>().clearSearchQuery();
      }
    });
  }

  void _openDrawer() {
    _scaffoldKey.currentState?.openDrawer();
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveUtils.isMobile(context);
    final shouldUseModalDrawer = ResponsiveUtils.shouldUseModalDrawer(context);

    return Scaffold(
      key: _scaffoldKey,
      appBar: _buildAppBar(context, isMobile),
      drawer: shouldUseModalDrawer ? const AppDrawer() : null,
      body: Row(
        children: [
          // Persistent drawer for larger screens
          if (!shouldUseModalDrawer)
            const SizedBox(
              width: AppConstants.drawerWidth,
              child: AppDrawer(),
            ),

          // Main content area
          Expanded(
            child: _buildMainContent(context),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, bool isMobile) {
    return AppBar(
      title: _isSearchExpanded && !isMobile ? _buildExpandedSearchBar() : const Text(AppConstants.appName),
      leading: ResponsiveUtils.shouldUseModalDrawer(context)
          ? IconButton(
              onPressed: _openDrawer,
              icon: const Icon(LucideIcons.menu),
              tooltip: 'Open navigation menu',
            )
          : null,
      automaticallyImplyLeading: ResponsiveUtils.shouldUseModalDrawer(context),
      actions: _buildAppBarActions(context, isMobile),
      elevation: AppConstants.elevationS,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: AppColors.textPrimary,
    );
  }

  Widget _buildExpandedSearchBar() {
    return TextField(
      controller: _searchController,
      autofocus: true,
      decoration: const InputDecoration(
        hintText: 'Search...',
        border: InputBorder.none,
        hintStyle: TextStyle(color: AppColors.textSecondary),
      ),
      style: AppTextStyles.titleMedium,
      onChanged: (query) {
        context.read<AppProvider>().updateSearchQuery(query);
      },
    );
  }

  List<Widget> _buildAppBarActions(BuildContext context, bool isMobile) {
    return [
      // Search button
      if (!_isSearchExpanded || isMobile)
        IconButton(
          onPressed: _toggleSearch,
          icon: Icon(_isSearchExpanded ? LucideIcons.x : LucideIcons.search),
          tooltip: _isSearchExpanded ? 'Close search' : 'Search',
        ),

      // Add new button
      Consumer<AppProvider>(
        builder: (context, provider, child) {
          if (provider.selectedNavItem == null) return const SizedBox.shrink();

          return IconButton(
            onPressed: () => _handleAddRecord(context, provider),
            icon: const Icon(LucideIcons.plus),
            tooltip: 'Add new ${provider.selectedNavItem!.name}',
          );
        },
      ),

      // More options menu
      PopupMenuButton<String>(
        onSelected: (value) => _handleMenuAction(context, value),
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'refresh',
            child: ListTile(
              leading: Icon(LucideIcons.refreshCw),
              title: Text('Refresh'),
              dense: true,
            ),
          ),
          const PopupMenuItem(
            value: 'settings',
            child: ListTile(
              leading: Icon(LucideIcons.settings),
              title: Text('Settings'),
              dense: true,
            ),
          ),
          const PopupMenuItem(
            value: 'about',
            child: ListTile(
              leading: Icon(LucideIcons.info),
              title: Text('About'),
              dense: true,
            ),
          ),
        ],
        icon: const Icon(Icons.more_vert),
        tooltip: 'More options',
      ),
    ];
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'refresh':
        context.read<AppProvider>().retryDataLoad();
        break;
      case 'settings':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings coming soon!'),
            behavior: SnackBarBehavior.floating,
          ),
        );
        break;
      case 'about':
        _showAboutDialog(context);
        break;
    }
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(AppConstants.radiusM),
        ),
        child: const Icon(
          LucideIcons.package,
          color: Colors.white,
          size: AppConstants.iconSizeL,
        ),
      ),
      children: [
        const Text(
          'A modern Flutter application for dynamic inventory management with hierarchical navigation and responsive design.',
        ),
      ],
    );
  }

  Widget _buildMainContent(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ResponsiveUtils.getContentPadding(context)),
      child: Consumer<AppProvider>(
        builder: (context, provider, child) {
          // Show search bar on mobile when expanded
          if (_isSearchExpanded && ResponsiveUtils.isMobile(context)) {
            return Column(
              children: [
                _buildMobileSearchBar(provider),
                const SizedBox(height: AppConstants.spacingM),
                Expanded(child: _buildContentArea(provider)),
              ],
            );
          }

          return _buildContentArea(provider);
        },
      ),
    );
  }

  Widget _buildMobileSearchBar(AppProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: TextField(
          controller: _searchController,
          autofocus: true,
          decoration: InputDecoration(
            hintText: 'Search navigation and data...',
            prefixIcon: const Icon(LucideIcons.search),
            suffixIcon: IconButton(
              onPressed: () {
                _searchController.clear();
                provider.clearSearchQuery();
                _toggleSearch();
              },
              icon: const Icon(LucideIcons.x),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusM),
            ),
          ),
          onChanged: provider.updateSearchQuery,
        ),
      ),
    );
  }

  Widget _buildContentArea(AppProvider provider) {
    // Loading state
    if (provider.isDataLoading && provider.instanceData.isEmpty) {
      return const LoadingWidget(
        message: 'Loading data...',
      );
    }

    // Error state
    if (provider.dataError != null && provider.instanceData.isEmpty) {
      return DataErrorWidget(
        message: provider.dataError!,
        onRetry: provider.retryDataLoad,
      );
    }

    // No item selected state
    if (provider.selectedNavItem == null) {
      return const WelcomeStateWidget();
    }

    // No data available state
    if (provider.instanceData.isEmpty && !provider.isDataLoading) {
      return NoDataWidget(
        itemName: provider.selectedNavItem!.name,
      );
    }

    // Data display state
    return _buildDataDisplay(provider);
  }

  Widget _buildDataDisplay(AppProvider provider) {
    final isMobile = ResponsiveUtils.isMobile(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Breadcrumb and title
        _buildBreadcrumb(provider),

        const SizedBox(height: AppConstants.spacingM),

        // Data display
        Expanded(
          child: isMobile
              ? MobileCardViewWidget(
                  cards: provider.cardData,
                  title: provider.selectedNavItem?.name,
                  isLoading: provider.isDataLoading,
                  onRefresh: provider.retryDataLoad,
                  onCardTap: (card) => _handleEditRecord(context, provider, card),
                )
              : DataTableWidget(
                  data: provider.instanceData,
                  title: provider.selectedNavItem?.name,
                  isLoading: provider.isDataLoading,
                  onRefresh: provider.retryDataLoad,
                ),
        ),
      ],
    );
  }

  Widget _buildBreadcrumb(AppProvider provider) {
    final selectedItem = provider.selectedNavItem;
    if (selectedItem == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Row(
          children: [
            Icon(
              selectedItem.iconData,
              size: AppConstants.iconSizeM,
              color: AppColors.primary,
            ),
            const SizedBox(width: AppConstants.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    selectedItem.name,
                    style: AppTextStyles.titleLarge,
                  ),
                  if (selectedItem.objectType != null)
                    Text(
                      'Object Type: ${selectedItem.objectType}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                ],
              ),
            ),
            if (provider.isDataLoading)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
      ),
    );
  }



  /// Handle adding a new record
  Future<void> _handleAddRecord(BuildContext context, AppProvider provider) async {
    if (provider.selectedNavItem == null) return;

    final result = await MobileFormScreenHelper.navigateToCreate(
      context,
      provider.selectedNavItem!.objectType ?? 'unknown',
    );

    if (result == true) {
      // Form was submitted successfully, refresh data
      provider.retryDataLoad();
    }
  }

  /// Handle editing an existing record
  Future<void> _handleEditRecord(
    BuildContext context,
    AppProvider provider,
    dynamic cardOrData,
  ) async {
    if (provider.selectedNavItem == null) return;

    // Extract data from card or raw data
    Map<String, dynamic> initialData;
    String recordId;

    if (cardOrData is CardDataModel) {
      // Convert card data back to raw data format
      initialData = {};
      recordId = cardOrData.id;

      // First, try to get original data from metadata
      if (cardOrData.metadata.containsKey('originalData')) {
        final originalData = cardOrData.metadata['originalData'] as Map<String, dynamic>;
        initialData.addAll(originalData);
      } else {
        // Fallback: convert fields back to data
        for (final field in cardOrData.fields) {
          initialData[field.key] = field.value;
        }
      }
    } else if (cardOrData is Map<String, dynamic>) {
      initialData = Map<String, dynamic>.from(cardOrData);
      recordId = initialData['id']?.toString() ?? '';
    } else {
      return; // Invalid data type
    }

    final result = await MobileFormScreenHelper.navigateToEdit(
      context,
      provider.selectedNavItem!.objectType ?? 'unknown',
      recordId,
      initialData,
    );

    if (result == true) {
      // Form was submitted successfully, refresh data
      provider.retryDataLoad();
    }
  }
}
