using System.ComponentModel.DataAnnotations;

namespace Application.DataTypes.DTOs;

/// <summary>
/// Create DataType DTO
/// </summary>
public class CreateDataTypeDto
{
    /// <summary>
    /// Name of the data type
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Display name for UI
    /// </summary>
    [MaxLength(255)]
    public string? DisplayName { get; set; }

    /// <summary>
    /// Category of the data type
    /// </summary>
    [MaxLength(100)]
    public string? Category { get; set; }

    /// <summary>
    /// UI component to use for rendering
    /// </summary>
    [MaxLength(100)]
    public string? UiComponent { get; set; }

    /// <summary>
    /// Validation pattern (regex)
    /// </summary>
    [MaxLength(500)]
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// Minimum length for text fields
    /// </summary>
    public int? MinLength { get; set; }

    /// <summary>
    /// Maximum length for text fields
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Minimum value for numeric fields
    /// </summary>
    public decimal? MinValue { get; set; }

    /// <summary>
    /// Maximum value for numeric fields
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// Number of decimal places for numeric fields
    /// </summary>
    public int? DecimalPlaces { get; set; }

    /// <summary>
    /// Step value for numeric inputs
    /// </summary>
    public decimal? StepValue { get; set; }

    /// <summary>
    /// Whether the field is required by default
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// HTML input type
    /// </summary>
    [MaxLength(50)]
    public string? InputType { get; set; }

    /// <summary>
    /// Input mask for formatted fields
    /// </summary>
    [MaxLength(100)]
    public string? InputMask { get; set; }

    /// <summary>
    /// Placeholder text
    /// </summary>
    [MaxLength(255)]
    public string? Placeholder { get; set; }

    /// <summary>
    /// Additional HTML attributes as JSON
    /// </summary>
    public string? HtmlAttributes { get; set; }

    /// <summary>
    /// Default options for choice fields
    /// </summary>
    public string? DefaultOptions { get; set; }

    /// <summary>
    /// Whether multiple selections are allowed
    /// </summary>
    public bool AllowsMultiple { get; set; } = false;

    /// <summary>
    /// Whether custom options can be added
    /// </summary>
    public bool AllowsCustomOptions { get; set; } = false;

    /// <summary>
    /// Maximum number of selections for multi-select fields
    /// </summary>
    public int? MaxSelections { get; set; }

    /// <summary>
    /// Allowed file types
    /// </summary>
    [MaxLength(500)]
    public string? AllowedFileTypes { get; set; }

    /// <summary>
    /// Maximum file size in bytes
    /// </summary>
    public long? MaxFileSizeBytes { get; set; }

    /// <summary>
    /// Error message for required field validation
    /// </summary>
    [MaxLength(255)]
    public string? RequiredErrorMessage { get; set; }

    /// <summary>
    /// Error message for pattern validation
    /// </summary>
    [MaxLength(255)]
    public string? PatternErrorMessage { get; set; }

    /// <summary>
    /// Error message for minimum length validation
    /// </summary>
    [MaxLength(255)]
    public string? MinLengthErrorMessage { get; set; }

    /// <summary>
    /// Error message for maximum length validation
    /// </summary>
    [MaxLength(255)]
    public string? MaxLengthErrorMessage { get; set; }

    /// <summary>
    /// Error message for minimum value validation
    /// </summary>
    [MaxLength(255)]
    public string? MinValueErrorMessage { get; set; }

    /// <summary>
    /// Error message for maximum value validation
    /// </summary>
    [MaxLength(255)]
    public string? MaxValueErrorMessage { get; set; }

    /// <summary>
    /// Error message for file type validation
    /// </summary>
    [MaxLength(255)]
    public string? FileTypeErrorMessage { get; set; }

    /// <summary>
    /// Error message for file size validation
    /// </summary>
    [MaxLength(255)]
    public string? FileSizeErrorMessage { get; set; }

    /// <summary>
    /// General error message
    /// </summary>
    [MaxLength(255)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Label to be displayed for the field
    /// </summary>
    [MaxLength(255)]
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// Additional help or guidance text for the field
    /// </summary>
    [MaxLength(500)]
    public string? HelpText { get; set; }

    /// <summary>
    /// The order in which the field should be displayed
    /// </summary>
    public int? FieldOrder { get; set; }

    /// <summary>
    /// Indicates whether the field is visible
    /// </summary>
    public bool? IsVisible { get; set; }

    /// <summary>
    /// Indicates whether the field is read-only
    /// </summary>
    public bool? IsReadonly { get; set; }

    /// <summary>
    /// Whether the data type is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
