import 'package:flutter/material.dart';
import '../../models/form_section_data.dart';
import 'dynamic_form_field.dart';

class FormSection extends StatelessWidget {
  final FormSectionData sectionData;
  final Map<String, dynamic> values;
  final Function(String, dynamic) onFieldChanged;

  const FormSection({
    Key? key,
    required this.sectionData,
    required this.values,
    required this.onFieldChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              sectionData.sectionName,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            ...sectionData.fields.map((field) => DynamicFormField(
                  metadata: field,
                  value: values[field.name],
                  onChanged: (val) => onFieldChanged(field.name, val),
                )),
            if (sectionData.children != null)
              ...sectionData.children!.map((childSection) => FormSection(
                    sectionData: childSection,
                    values: values,
                    onFieldChanged: onFieldChanged,
                  )),
          ],
        ),
      ),
    );
  }
}
