{"name": "productbuilder", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^2.2.7", "@types/react-router-dom": "^5.3.3", "bootstrap": "^5.3.6", "lucide-react": "^0.516.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-bootstrap-icons": "^1.11.6", "react-data-grid": "^7.0.0-beta.55", "react-dom": "^19.1.0", "react-redux": "^9.1.2", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.10", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}