/**
 * TemplateTable Organism
 * Complete template table with filtering, actions, and data management
 */

import React from 'react';
import { Card, Badge, ButtonGroup, Button } from 'react-bootstrap';
import { DataTable, TableColumn, TableAction } from '../molecules/DataTable';
import { FilterBar, createTemplateFilters } from '../molecules/FilterBar';
import { ActionBar, createTemplateActions } from '../molecules/ActionBar';
import { StatusBadge } from '../atoms/StatusBadge';
import { useTemplates } from '../../store/hooks';
import type { TemplateDto, GroupedTemplate } from '../../services/api';

export interface TemplateTableProps {
  onViewTemplate?: (template: TemplateDto) => void;
  onEditTemplate?: (template: TemplateDto) => void;
  onDeleteTemplate?: (template: TemplateDto) => void;
  onCreateTemplate?: () => void;
  onImportTemplate?: () => void;
  className?: string;
  maxHeight?: string | number;
}

export const TemplateTable: React.FC<TemplateTableProps> = ({
  onViewTemplate,
  onEditTemplate,
  onDeleteTemplate,
  onCreateTemplate,
  onImportTemplate,
  className = '',
  maxHeight = '600px'
}) => {
  const {
    templates,
    groupedTemplates,
    filters,
    loading,
    viewMode,
    loadTemplates,
    setSearch,
    setStage,
    setStatus,
    setView
  } = useTemplates();

  // Handle filter changes
  const handleFilterChange = (filterKey: string, value: string) => {
    switch (filterKey) {
      case 'stage':
        setStage(value);
        break;
      case 'status':
        setStatus(value);
        break;
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    loadTemplates();
  };

  // Handle view mode change
  const handleViewModeChange = (mode: 'list' | 'grouped') => {
    setView(mode);
  };

  // Table columns for individual templates
  const templateColumns: TableColumn<TemplateDto>[] = [
    {
      key: 'name',
      title: 'Template Name',
      dataIndex: 'name',
      render: (value, record) => (
        <div>
          <div className="fw-medium">
            {value || `Template ${record.id.substring(0, 8)}`}
          </div>
          <small className="text-muted">
            ID: {record.id.substring(0, 8)}...
          </small>
        </div>
      ),
      width: '250px'
    },
    {
      key: 'version',
      title: 'Version',
      dataIndex: 'version',
      render: (value) => (
        <Badge bg="secondary" className="font-monospace">
          v{value}
        </Badge>
      ),
      width: '100px'
    },
    {
      key: 'stage',
      title: 'Stage',
      dataIndex: 'stage',
      render: (value) => (
        <StatusBadge 
          status={value} 
          variant="template"
        />
      ),
      width: '120px'
    },
    {
      key: 'status',
      title: 'Status',
      dataIndex: 'isActive',
      render: (value) => (
        <StatusBadge 
          status={value ? 'Active' : 'Inactive'} 
          variant="custom"
          customVariant={value ? 'success' : 'secondary'}
        />
      ),
      width: '100px'
    },
    {
      key: 'created',
      title: 'Created',
      dataIndex: 'createdAt',
      render: (value, record) => (
        <div>
          <div className="small">
            {new Date(value).toLocaleDateString()}
          </div>
          <small className="text-muted">
            by {record.createdBy || 'System'}
          </small>
        </div>
      ),
      width: '140px'
    },
    {
      key: 'published',
      title: 'Published',
      dataIndex: 'publishedAt',
      render: (value) => (
        <div className="small">
          {value ? new Date(value).toLocaleDateString() : '—'}
        </div>
      ),
      width: '120px'
    }
  ];

  // Table columns for grouped templates
  const groupedColumns: TableColumn<GroupedTemplate>[] = [
    {
      key: 'templateName',
      title: 'Template Name',
      dataIndex: 'templateName',
      render: (value, record) => (
        <div>
          <div className="fw-medium">{value}</div>
          <small className="text-muted">
            {record.totalVersions} version{record.totalVersions !== 1 ? 's' : ''}
          </small>
        </div>
      ),
      width: '250px'
    },
    {
      key: 'latestVersion',
      title: 'Latest Version',
      dataIndex: 'latestVersion',
      render: (value) => (
        <Badge bg="primary" className="font-monospace">
          v{value}
        </Badge>
      ),
      width: '140px'
    },
    {
      key: 'stage',
      title: 'Stage',
      render: (_, record) => (
        <StatusBadge 
          status={record.displayTemplate.stage} 
          variant="template"
        />
      ),
      width: '120px'
    },
    {
      key: 'status',
      title: 'Status',
      render: (_, record) => (
        <StatusBadge 
          status={record.displayTemplate.isActive ? 'Active' : 'Inactive'} 
          variant="custom"
          customVariant={record.displayTemplate.isActive ? 'success' : 'secondary'}
        />
      ),
      width: '100px'
    },
    {
      key: 'created',
      title: 'Created',
      render: (_, record) => (
        <div>
          <div className="small">
            {new Date(record.displayTemplate.createdAt).toLocaleDateString()}
          </div>
          <small className="text-muted">
            by {record.displayTemplate.createdBy || 'System'}
          </small>
        </div>
      ),
      width: '140px'
    }
  ];

  // Table actions for individual templates
  const templateActions: TableAction<TemplateDto>[] = [
    {
      key: 'view',
      icon: '👁️',
      variant: 'outline-primary',
      onClick: (record) => onViewTemplate?.(record),
      tooltip: 'View Template'
    },
    {
      key: 'edit',
      icon: '✏️',
      variant: 'outline-secondary',
      onClick: (record) => onEditTemplate?.(record),
      tooltip: 'Edit Template'
    },
    {
      key: 'delete',
      icon: '🗑️',
      variant: 'outline-danger',
      onClick: (record) => onDeleteTemplate?.(record),
      tooltip: 'Delete Template'
    }
  ].filter(action => {
    switch (action.key) {
      case 'view':
        return !!onViewTemplate;
      case 'edit':
        return !!onEditTemplate;
      case 'delete':
        return !!onDeleteTemplate;
      default:
        return true;
    }
  });

  // Table actions for grouped templates
  const groupedActions: TableAction<GroupedTemplate>[] = [
    {
      key: 'view',
      icon: '👁️',
      variant: 'outline-primary',
      onClick: (record) => onViewTemplate?.(record.displayTemplate),
      tooltip: 'View Latest Template'
    },
    {
      key: 'edit',
      icon: '✏️',
      variant: 'outline-secondary',
      onClick: (record) => onEditTemplate?.(record.displayTemplate),
      tooltip: 'Edit Latest Template'
    }
  ].filter(action => {
    switch (action.key) {
      case 'view':
        return !!onViewTemplate;
      case 'edit':
        return !!onEditTemplate;
      default:
        return true;
    }
  });

  // Filter configuration
  const filterConfigs = createTemplateFilters(filters.stage, filters.status);

  // Action configuration
  const actionConfigs = createTemplateActions(
    handleRefresh,
    onCreateTemplate || (() => {}),
    onImportTemplate,
    loading.list
  );

  // Get current data based on view mode
  const currentData = viewMode === 'grouped' ? groupedTemplates : templates;
  const currentColumns = viewMode === 'grouped' ? groupedColumns : templateColumns;
  const currentActions = viewMode === 'grouped' ? groupedActions : templateActions;

  return (
    <Card className={`modern-card ${className}`}>
      <Card.Header className="bg-transparent border-0 pb-0">
        <div className="d-flex justify-content-between align-items-start">
          <div>
            <h6 className="mb-0 fw-semibold">Templates</h6>
            <small className="text-muted">
              {currentData.length} {viewMode === 'grouped' ? 'template group' : 'template'}
              {currentData.length !== 1 ? 's' : ''} found
            </small>
          </div>
          
          <div className="d-flex gap-2 align-items-center">
            {/* View Mode Toggle */}
            <ButtonGroup size="sm">
              <Button
                variant={viewMode === 'list' ? 'primary' : 'outline-primary'}
                onClick={() => handleViewModeChange('list')}
              >
                📋 List
              </Button>
              <Button
                variant={viewMode === 'grouped' ? 'primary' : 'outline-primary'}
                onClick={() => handleViewModeChange('grouped')}
              >
                📁 Grouped
              </Button>
            </ButtonGroup>
            
            {/* Actions */}
            <ActionBar
              actions={onCreateTemplate ? actionConfigs : actionConfigs.filter(a => a.key !== 'create')}
              loading={loading.list}
            />
          </div>
        </div>
      </Card.Header>

      <Card.Body className="pt-3">
        <FilterBar
          searchValue={filters.searchTerm}
          onSearchChange={setSearch}
          searchPlaceholder="Search templates..."
          filters={filterConfigs}
          onFilterChange={handleFilterChange}
          className="mb-3"
          disabled={loading.list}
        />

        <DataTable
          data={currentData}
          columns={currentColumns}
          actions={currentActions}
          loading={loading.list}
          hover
          maxHeight={maxHeight}
          empty={{
            icon: '📋',
            title: 'No templates found',
            description: 'No templates match your current search and filter criteria'
          }}
        />
      </Card.Body>
    </Card>
  );
};
