using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertTenantContext;

/// <summary>
/// Handler for UpsertTenantContextCommand
/// </summary>
public class UpsertTenantContextCommandHandler : IRequestHandler<UpsertTenantContextCommand, Result<Guid>>
{
    private readonly IRepository<TenantContext> _tenantContextRepository;
    private readonly ILogger<UpsertTenantContextCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertTenantContextCommandHandler(
        IRepository<TenantContext> tenantContextRepository,
        ILogger<UpsertTenantContextCommandHandler> logger)
    {
        _tenantContextRepository = tenantContextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<Guid>> Handle(UpsertTenantContextCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Upserting tenant context: {Name}", request.Name);

            TenantContext tenantContext;

            if (request.Id.HasValue && request.Id.Value != Guid.Empty)
            {
                // Update existing tenant context
                tenantContext = await _tenantContextRepository.GetByIdAsync(request.Id.Value, cancellationToken);
                if (tenantContext == null)
                {
                    return Result<Guid>.Failure($"TenantContext with ID {request.Id.Value} not found");
                }

                tenantContext.Name = request.Name;
                tenantContext.Description = request.Description;
                tenantContext.Category = request.Category;
                tenantContext.IsActive = request.IsActive;

                await _tenantContextRepository.UpdateAsync(tenantContext, cancellationToken);
                _logger.LogInformation("Updated tenant context with ID: {Id}", tenantContext.Id);
            }
            else
            {
                // Create new tenant context
                tenantContext = new TenantContext
                {
                    Id = Guid.NewGuid(),
                    Name = request.Name,
                    Description = request.Description,
                    Category = request.Category,
                    IsActive = request.IsActive,
                    IsDeleted = false
                };

                await _tenantContextRepository.AddAsync(tenantContext, cancellationToken);
                _logger.LogInformation("Created new tenant context with ID: {Id}", tenantContext.Id);
            }

            return Result<Guid>.Success(tenantContext.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while upserting tenant context: {Name}", request.Name);
            return Result<Guid>.Failure($"Error upserting tenant context: {ex.Message}");
        }
    }
}
