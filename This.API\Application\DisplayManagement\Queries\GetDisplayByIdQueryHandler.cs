using Abstraction.Database.Repositories;
using Application.DisplayManagement.DTOs;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.DisplayManagement.Queries;

/// <summary>
/// Get display by ID query handler
/// </summary>
public class GetDisplayByIdQueryHandler : IRequestHandler<GetDisplayByIdQuery, Result<DisplayDto>>
{
    private readonly IRepository<Display> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetDisplayByIdQueryHandler(IRepository<Display> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<DisplayDto>> Handle(GetDisplayByIdQuery request, CancellationToken cancellationToken)
    {
        var display = await _repository.GetByIdAsync(request.Id, cancellationToken);

        if (display == null)
        {
            return Result<DisplayDto>.Failure($"Display with ID '{request.Id}' not found.");
        }

        var dto = display.Adapt<DisplayDto>();

        return Result<DisplayDto>.Success(dto);
    }
}
