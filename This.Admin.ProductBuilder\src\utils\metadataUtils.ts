// Available data types
export const DATA_TYPES = [
  'Rating', 'Day', 'Time', 'Color', 'Textarea', 'Multiselect', 'Text', 'Url',
  'Boolean', 'Currency', 'Month', 'Select', 'File', 'Richtext', 'Image', 'Year',
  'Radio', 'Tag', 'Number', 'Datetime', 'Phone', 'Slider', 'Checkbox',
  'Percentage', 'Address', 'Email', 'Date', 'Guid'
];

// Helper function to generate hexadecimal GUID
export const generateGuid = (): string => {
  const hex = () => Math.floor(Math.random() * 16).toString(16);
  const s4 = () => hex() + hex() + hex() + hex();
  const s8 = () => s4() + s4();
  const s12 = () => s4() + s4() + s4();

  const uuid = [
    s8(),
    s4(),
    '4' + hex() + hex() + hex(),
    (8 + Math.floor(Math.random() * 4)).toString(16) + hex() + hex() + hex(),
    s12()
  ].join('-');

  return uuid.toUpperCase();
};

// Base metadata type
export type BaseMetadata = {
  name: string;
  type: string;
  description: string;
  required: boolean;
  defaultValue?: string;
  isActive: boolean;
  _internalId?: string;
};

// Specific metadata types
export type ProductMetadata = BaseMetadata & {
  name: 'Name' | 'Description' | 'Version' | 'IsActive';
};

export type ObjectMetadata = BaseMetadata & {
  name: 'ParentObjectId' | 'Name' | 'Description' | 'IsActive';
};

export type RoleMetadata = BaseMetadata & {
  name: 'Name' | 'Description' | 'IsActive';
};

export type ActionMetadata = BaseMetadata & {
  name: 'Name' | 'Description' | 'IsActive';
};

export type PermissionMetadata = BaseMetadata & {
  name: 'Name' | 'Description' | 'IsActive';
};

export type MetadataItem = ProductMetadata | ObjectMetadata | RoleMetadata | ActionMetadata | PermissionMetadata;

// Default metadata for each type
export const DEFAULT_PRODUCT_METADATA: ProductMetadata[] = [
  { name: 'Name', type: 'Text', description: 'Product name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Product description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'Version', type: 'Text', description: 'Product version', required: true, isActive: true, defaultValue: '1.0.0', _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the product is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];

export const DEFAULT_OBJECT_METADATA: ObjectMetadata[] = [
  { name: 'ParentObjectId', type: 'Text', description: 'Parent object identifier', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'Name', type: 'Text', description: 'Object name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Object description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the object is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];

export const DEFAULT_ROLE_METADATA: RoleMetadata[] = [
  { name: 'Name', type: 'Text', description: 'Role name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Role description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the role is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];

export const DEFAULT_ACTION_METADATA: ActionMetadata[] = [
  { name: 'Name', type: 'Text', description: 'Action name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Action description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the action is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];

export const DEFAULT_PERMISSION_METADATA: PermissionMetadata[] = [
  { name: 'Name', type: 'Text', description: 'Permission name', required: true, isActive: true, _internalId: generateGuid() },
  { name: 'Description', type: 'Textarea', description: 'Permission description', required: false, isActive: true, _internalId: generateGuid() },
  { name: 'IsActive', type: 'Boolean', description: 'Whether the permission is active', required: true, isActive: true, defaultValue: 'true', _internalId: generateGuid() },
];

// Get default metadata based on node type
export const getDefaultMetadata = (nodeType: string): MetadataItem[] => {
  switch (nodeType) {
    case 'product':
      return []; // No default metadata for products
    case 'object':
      return []; // No default metadata for objects
    case 'role':
      return [...DEFAULT_ROLE_METADATA];
    case 'action':
      return [...DEFAULT_ACTION_METADATA];
    case 'permission':
      return [...DEFAULT_PERMISSION_METADATA];
    default:
      return [];
  }
};
