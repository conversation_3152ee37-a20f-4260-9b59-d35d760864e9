import 'package:flutter/foundation.dart';
import 'nav_item.dart';

/// Application state model for centralized state management
class AppState extends ChangeNotifier {
  // Navigation state
  List<NavItem> _navigationItems = [];
  NavItem? _selectedNavItem;
  bool _isNavigationLoading = false;
  String? _navigationError;

  // Data state
  List<Map<String, dynamic>> _instanceData = [];
  bool _isDataLoading = false;
  String? _dataError;

  // UI state
  String _searchQuery = '';
  bool _isDrawerOpen = false;

  // Getters
  List<NavItem> get navigationItems => _navigationItems;
  NavItem? get selectedNavItem => _selectedNavItem;
  bool get isNavigationLoading => _isNavigationLoading;
  String? get navigationError => _navigationError;

  List<Map<String, dynamic>> get instanceData => _instanceData;
  bool get isDataLoading => _isDataLoading;
  String? get dataError => _dataError;

  String get searchQuery => _searchQuery;
  bool get isDrawerOpen => _isDrawerOpen;

  // Filtered navigation items based on search query
  List<NavItem> get filteredNavigationItems {
    if (_searchQuery.isEmpty) {
      return _navigationItems;
    }

    return _navigationItems
        .where((item) => item.matchesSearch(_searchQuery))
        .toList();
  }

  // Check if there's any data to display
  bool get hasData => _instanceData.isNotEmpty;

  // Check if we're in a loading state
  bool get isLoading => _isNavigationLoading || _isDataLoading;

  // Check if there are any errors
  bool get hasError => _navigationError != null || _dataError != null;

  // Get the current error message
  String? get currentError => _navigationError ?? _dataError;

  /// Load navigation data
  void loadNavigationData(List<NavItem> items) {
    _isNavigationLoading = true;
    _navigationError = null;
    notifyListeners();

    try {
      _navigationItems = items;
      _isNavigationLoading = false;
      notifyListeners();
    } catch (e) {
      _navigationError = 'Failed to load navigation data: $e';
      _isNavigationLoading = false;
      notifyListeners();
    }
  }

  /// Set navigation loading state
  void setNavigationLoading(bool loading) {
    _isNavigationLoading = loading;
    notifyListeners();
  }

  /// Set navigation error
  void setNavigationError(String? error) {
    _navigationError = error;
    _isNavigationLoading = false;
    notifyListeners();
  }

  /// Select a navigation item
  void selectNavigationItem(NavItem? item) {
    _selectedNavItem = item;
    _dataError = null; // Clear any previous data errors
    notifyListeners();
  }

  /// Load instance data
  void loadInstanceData(List<Map<String, dynamic>> data) {
    _isDataLoading = true;
    _dataError = null;
    notifyListeners();

    try {
      _instanceData = data;
      _isDataLoading = false;
      notifyListeners();
    } catch (e) {
      _dataError = 'Failed to load instance data: $e';
      _isDataLoading = false;
      notifyListeners();
    }
  }

  /// Set data loading state
  void setDataLoading(bool loading) {
    _isDataLoading = loading;
    notifyListeners();
  }

  /// Set data error
  void setDataError(String? error) {
    _dataError = error;
    _isDataLoading = false;
    notifyListeners();
  }

  /// Clear instance data
  void clearInstanceData() {
    _instanceData = [];
    _dataError = null;
    notifyListeners();
  }

  /// Update search query
  void updateSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  /// Clear search query
  void clearSearchQuery() {
    _searchQuery = '';
    notifyListeners();
  }

  /// Toggle drawer state
  void toggleDrawer() {
    _isDrawerOpen = !_isDrawerOpen;
    notifyListeners();
  }

  /// Set drawer state
  void setDrawerOpen(bool open) {
    _isDrawerOpen = open;
    notifyListeners();
  }

  /// Retry loading navigation data
  void retryNavigationLoad() {
    _navigationError = null;
    notifyListeners();
  }

  /// Retry loading instance data
  void retryDataLoad() {
    _dataError = null;
    notifyListeners();
  }

  /// Clear all errors
  void clearErrors() {
    _navigationError = null;
    _dataError = null;
    notifyListeners();
  }

  /// Reset the entire state
  void reset() {
    _navigationItems = [];
    _selectedNavItem = null;
    _isNavigationLoading = false;
    _navigationError = null;
    _instanceData = [];
    _isDataLoading = false;
    _dataError = null;
    _searchQuery = '';
    _isDrawerOpen = false;
    notifyListeners();
  }

  /// Toggle expansion state of a navigation item
  void toggleNavItemExpansion(NavItem item) {
    _navigationItems = _updateNavItemExpansion(_navigationItems, item);
    notifyListeners();
  }

  /// Helper method to recursively update navigation item expansion
  List<NavItem> _updateNavItemExpansion(List<NavItem> items, NavItem targetItem) {
    return items.map((item) {
      if (item.id == targetItem.id) {
        return item.copyWith(isExpanded: !item.isExpanded);
      } else if (item.hasChildren) {
        return item.copyWith(
          children: _updateNavItemExpansion(item.children!, targetItem),
        );
      }
      return item;
    }).toList();
  }
}
