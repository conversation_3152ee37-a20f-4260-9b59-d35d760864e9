using Domain.Common.Contracts;
using System.ComponentModel.DataAnnotations;

namespace Domain.Entities;

/// <summary>
/// RoleActions entity - Many-to-many relationship between Role and Action entities
/// </summary>
public class RoleActions : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Role ID - Foreign key to Role entity
    /// </summary>
    [Required]
    public Guid RoleId { get; set; }

    /// <summary>
    /// Action ID - Foreign key to Action entity
    /// </summary>
    [Required]
    public Guid ActionId { get; set; }

    /// <summary>
    /// Whether this role-action relationship is active
    /// </summary>
    public new bool IsActive { get; set; } = true;

    // Navigation Properties
    /// <summary>
    /// Role associated with this relationship
    /// </summary>
    public virtual Role Role { get; set; } = null!;

    /// <summary>
    /// Action associated with this relationship
    /// </summary>
    public virtual Action Action { get; set; } = null!;
}
