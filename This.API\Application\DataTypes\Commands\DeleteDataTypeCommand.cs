using MediatR;
using Shared.Common.Response;

namespace Application.DataTypes.Commands;

/// <summary>
/// Delete DataType command
/// </summary>
public class DeleteDataTypeCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// DataType ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteDataTypeCommand(Guid id)
    {
        Id = id;
    }
}
