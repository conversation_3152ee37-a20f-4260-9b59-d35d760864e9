using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for RoleActions entity
/// </summary>
public class RoleActionsConfig : IEntityTypeConfiguration<RoleActions>
{
    public void Configure(EntityTypeBuilder<RoleActions> builder)
    {
        builder.ToTable("RoleActions", "Genp");

        // Primary key
        builder.HasKey(e => e.Id);

        // Properties
        builder.Property(e => e.RoleId)
            .IsRequired();

        builder.Property(e => e.ActionId)
            .IsRequired();

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.Id)
            .HasDatabaseName("IX_RoleActions_Id");

        builder.HasIndex(e => e.RoleId)
            .HasDatabaseName("IX_RoleActions_RoleId");

        builder.HasIndex(e => e.ActionId)
            .HasDatabaseName("IX_RoleActions_ActionId");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_RoleActions_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Unique constraint for RoleId and ActionId combination
        builder.HasIndex(e => new { e.RoleId, e.ActionId })
            .IsUnique()
            .HasDatabaseName("IX_RoleActions_RoleId_ActionId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Role)
            .WithMany()
            .HasForeignKey(e => e.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Action)
            .WithMany()
            .HasForeignKey(e => e.ActionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
