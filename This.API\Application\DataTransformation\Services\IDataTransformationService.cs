using Application.DataTransformation.DTOs;
using Shared.Common.Response;

namespace Application.DataTransformation.Services;

/// <summary>
/// Interface for data transformation service
/// </summary>
public interface IDataTransformationService
{
    /// <summary>
    /// Transform incoming JSON data according to field mappings for a specific API
    /// </summary>
    /// <param name="apiName">Name of the API to get field mappings for</param>
    /// <param name="jsonData">Incoming JSON data to transform</param>
    /// <param name="tenantId">Tenant ID for multi-tenancy</param>
    /// <param name="userId">User ID for audit purposes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transformation result with created ObjectValue records</returns>
    Task<Result<DataTransformationResultDto>> TransformDataAsync(
        string apiName,
        string jsonData,
        string tenantId,
        Guid? userId = null,
        CancellationToken cancellationToken = default);


}
