import React from 'react';
import { Container, Badge, Button } from 'react-bootstrap';

interface ProductBuilderHeaderProps {
  templateInfo: any;
  locationState: any;
  onSave: () => void;
  onBackToTemplates: () => void;
  isSaving: boolean;
}

export const ProductBuilderHeader: React.FC<ProductBuilderHeaderProps> = ({
  templateInfo,
  locationState,
  onSave,
  onBackToTemplates,
  isSaving
}) => {
  return (
    <header className="bg-dark text-white py-3 shadow">
      <Container>
        <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex align-items-center">
            <h1 className="h3 mb-0 me-3">Admin Panel</h1>
            {templateInfo && (
              <div className="d-flex align-items-center">
                <Badge bg="primary" className="me-2">
                  {templateInfo.templateJson?.products?.[0]?.name || 'Template'}
                </Badge>
                <Badge bg="secondary" className="me-2">
                  v{templateInfo.version}
                </Badge>
                <Badge bg={templateInfo.stage === 'live' ? 'success' : 'warning'}>
                  {templateInfo.stage?.toUpperCase()}
                </Badge>
              </div>
            )}
          </div>
          <div className="d-flex align-items-center gap-2">
            {templateInfo && (
              <small className="text-light opacity-75">
                Mode: {locationState?.mode || 'edit'}
              </small>
            )}
            <Button
              variant="success"
              size="sm"
              onClick={onSave}
              disabled={isSaving}
              className="d-flex align-items-center gap-1"
            >
              💾
              {templateInfo?.id && !templateInfo.id.startsWith('template-') ? 'Update Template' : 'Save Template'}
            </Button>
            <Button
              variant="outline-light"
              size="sm"
              onClick={onBackToTemplates}
            >
              Back to Templates
            </Button>
          </div>
        </div>
      </Container>
    </header>
  );
};
