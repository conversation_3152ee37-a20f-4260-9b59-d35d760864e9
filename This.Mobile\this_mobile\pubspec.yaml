name: this_mobile
description: "A new Flutter project."


version: 1.0.0+1

environment:
  sdk: ^3.6.1


dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8

  # State Management
  provider: ^6.1.2

  # HTTP requests
  http: ^1.2.1

  # Icons
  lucide_icons_flutter: ^1.0.0

  # JSON serialization
  json_annotation: ^4.9.0
  intl: ^0.20.2
  file_picker: ^10.2.0
  image_picker: ^1.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  build_runner: ^2.4.9
  json_serializable: ^6.7.1


flutter:

  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
