namespace Abstraction.Identity.Dtos;

/// <summary>
/// Data transfer object for creating a user
/// </summary>
public class CreateUserRequestDto
{
    /// <summary>
    /// First name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Display name
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// Email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Username
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Password
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Confirm password
    /// </summary>
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Profile image URL
    /// </summary>
    public string? ImageUrl { get; set; }

    /// <summary>
    /// Whether the user is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Whether MFA is enabled
    /// </summary>
    public bool IsMFAEnabled { get; set; } = false;

    /// <summary>
    /// Integration source ID
    /// </summary>
    public Guid? IntegrationSourceId { get; set; }

    /// <summary>
    /// External user ID
    /// </summary>
    public string? ExternalUserId { get; set; }

    /// <summary>
    /// External user data as JSON
    /// </summary>
    public string? ExternalUserData { get; set; }

    /// <summary>
    /// Whether the user must change password on next login
    /// </summary>
    public bool MustChangePassword { get; set; } = false;

    /// <summary>
    /// External object ID
    /// </summary>
    public string? ObjectId { get; set; }

    /// <summary>
    /// List of role names to assign to the user
    /// </summary>
    public List<string> Roles { get; set; } = new();
}
