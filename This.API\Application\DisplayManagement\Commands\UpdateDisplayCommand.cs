using Application.DisplayManagement.DTOs;
using MediatR;
using Shared.Common.Response;
using System.ComponentModel.DataAnnotations;

namespace Application.DisplayManagement.Commands;

/// <summary>
/// Update Display command
/// </summary>
public class UpdateDisplayCommand : IRequest<Result<DisplayDto>>
{
    /// <summary>
    /// Display ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Display name - 'List', 'View', 'Update', 'Create', 'Card', or custom names
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the display
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Display name for UI
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default display for this type
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// URL route template for navigation
    /// </summary>
    [MaxLength(500)]
    public string? RouteTemplate { get; set; }

    /// <summary>
    /// Icon for the display
    /// </summary>
    [MaxLength(100)]
    public string? Icon { get; set; }

    /// <summary>
    /// Sort order for display ordering
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// Whether the display is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
