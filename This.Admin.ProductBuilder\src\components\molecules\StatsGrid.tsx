/**
 * StatsGrid Molecule
 * Reusable statistics grid component
 */

import React from 'react';
import { Row, Col } from 'react-bootstrap';
import { StatsCard, StatsCardProps } from '../atoms/StatsCard';

export interface StatsGridItem extends StatsCardProps {
  key: string;
  colSize?: number;
  hidden?: boolean;
}

export interface StatsGridProps {
  items: StatsGridItem[];
  loading?: boolean;
  className?: string;
  gutter?: number;
  responsive?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

export const StatsGrid: React.FC<StatsGridProps> = ({
  items,
  loading = false,
  className = '',
  gutter = 2,
  responsive = { md: 3 }
}) => {
  const visibleItems = items.filter(item => !item.hidden);

  // Calculate default column size if not specified
  const defaultColSize = Math.floor(12 / visibleItems.length);

  return (
    <Row className={`g-${gutter} ${className}`}>
      {visibleItems.map(({ key, colSize, hidden, ...statsProps }) => (
        <Col 
          key={key}
          {...responsive}
          md={colSize || responsive.md || defaultColSize}
        >
          <StatsCard
            {...statsProps}
            loading={loading || statsProps.loading}
          />
        </Col>
      ))}
    </Row>
  );
};

// Predefined stats grid configurations
export const createSubscriptionStats = (
  data: {
    total: number;
    active: number;
    expired: number;
    expiring: number;
  },
  onStatClick?: (type: string) => void
): StatsGridItem[] => [
  {
    key: 'total',
    title: 'Total Subscriptions',
    value: data.total,
    variant: 'primary',
    icon: '📊',
    onClick: onStatClick ? () => onStatClick('total') : undefined
  },
  {
    key: 'active',
    title: 'Active',
    value: data.active,
    variant: 'success',
    icon: '✅',
    onClick: onStatClick ? () => onStatClick('active') : undefined
  },
  {
    key: 'expired',
    title: 'Expired',
    value: data.expired,
    variant: 'danger',
    icon: '❌',
    onClick: onStatClick ? () => onStatClick('expired') : undefined
  },
  {
    key: 'expiring',
    title: 'Expiring Soon',
    value: data.expiring,
    variant: 'warning',
    icon: '⚠️',
    onClick: onStatClick ? () => onStatClick('expiring') : undefined
  }
];

export const createTemplateStats = (
  data: {
    total: number;
    live: number;
    draft: number;
    beta: number;
  },
  onStatClick?: (type: string) => void
): StatsGridItem[] => [
  {
    key: 'total',
    title: 'Total Templates',
    value: data.total,
    variant: 'primary',
    icon: '📋',
    onClick: onStatClick ? () => onStatClick('total') : undefined
  },
  {
    key: 'live',
    title: 'Live',
    value: data.live,
    variant: 'success',
    icon: '🟢',
    onClick: onStatClick ? () => onStatClick('live') : undefined
  },
  {
    key: 'draft',
    title: 'Draft',
    value: data.draft,
    variant: 'secondary',
    icon: '📝',
    onClick: onStatClick ? () => onStatClick('draft') : undefined
  },
  {
    key: 'beta',
    title: 'Beta',
    value: data.beta,
    variant: 'warning',
    icon: '🧪',
    onClick: onStatClick ? () => onStatClick('beta') : undefined
  }
];

export const createTenantStats = (
  data: {
    total: number;
    active: number;
    inactive: number;
  },
  onStatClick?: (type: string) => void
): StatsGridItem[] => [
  {
    key: 'total',
    title: 'Total Tenants',
    value: data.total,
    variant: 'primary',
    icon: '🏢',
    onClick: onStatClick ? () => onStatClick('total') : undefined
  },
  {
    key: 'active',
    title: 'Active',
    value: data.active,
    variant: 'success',
    icon: '✅',
    onClick: onStatClick ? () => onStatClick('active') : undefined
  },
  {
    key: 'inactive',
    title: 'Inactive',
    value: data.inactive,
    variant: 'warning',
    icon: '⏸️',
    onClick: onStatClick ? () => onStatClick('inactive') : undefined
  }
];
