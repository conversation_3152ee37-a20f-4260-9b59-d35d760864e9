using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetPagedTenantContexts;

/// <summary>
/// Handler for GetPagedTenantContextsQuery
/// </summary>
public class GetPagedTenantContextsQueryHandler : IRequestHandler<GetPagedTenantContextsQuery, PagedResponse<TenantContextDto, object>>
{
    private readonly IRepository<TenantContext> _tenantContextRepository;
    private readonly ILogger<GetPagedTenantContextsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetPagedTenantContextsQueryHandler(
        IRepository<TenantContext> tenantContextRepository,
        ILogger<GetPagedTenantContextsQueryHandler> logger)
    {
        _tenantContextRepository = tenantContextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<PagedResponse<TenantContextDto, object>> Handle(GetPagedTenantContextsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting paged tenant contexts: Page={PageNumber}, Size={PageSize}, IncludeInactive={IncludeInactive}, Category={Category}, SearchTerm={SearchTerm}",
                request.PageNumber, request.PageSize, request.IncludeInactive, request.Category, request.SearchTerm);

            // Calculate pagination
            var skip = (request.PageNumber - 1) * request.PageSize;

            // Create specifications for data and count - tenant filtering is handled automatically by the repository
            var dataSpec = new TenantContextsWithFiltersSpec(
                searchTerm: request.SearchTerm,
                category: request.Category,
                includeInactive: request.IncludeInactive,
                skip: skip,
                take: request.PageSize);

            var countSpec = new TenantContextsCountSpec(
                searchTerm: request.SearchTerm,
                category: request.Category,
                includeInactive: request.IncludeInactive);

            // Execute queries using specifications
            var tenantContexts = await _tenantContextRepository.ListAsync(dataSpec, cancellationToken);
            var totalCount = await _tenantContextRepository.CountAsync(countSpec, cancellationToken);

            // Map to DTOs
            var tenantContextDtos = tenantContexts.Adapt<List<TenantContextDto>>();

            _logger.LogInformation("Successfully retrieved {Count} tenant contexts out of {TotalCount} total", tenantContextDtos.Count, totalCount);

            return PagedResponse<TenantContextDto, object>.Success(tenantContextDtos, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting paged tenant contexts");
            return PagedResponse<TenantContextDto, object>.Failure($"Error retrieving paged tenant contexts: {ex.Message}");
        }
    }
}
