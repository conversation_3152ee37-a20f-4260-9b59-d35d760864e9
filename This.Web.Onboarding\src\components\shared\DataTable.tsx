import React, { useState } from 'react';
import { Edit, Trash2 } from 'lucide-react';
import { TableProps } from '../../types';
import Toggle from './Toggle';

function DataTable<T extends { id: string }>({
  data,
  columns,
  isSelectable = true,
  selectedItems = [],
  onSelect,
  onSelectAll,
  onEdit,
  onDelete,
}: TableProps<T>) {
  const [selected, setSelected] = useState<string[]>(selectedItems);

  const handleSelect = (id: string) => {
    const updatedSelected = selected.includes(id)
      ? selected.filter((itemId) => itemId !== id)
      : [...selected, id];
    
    setSelected(updatedSelected);
    if (onSelect) onSelect(updatedSelected);
  };

  const handleSelectAll = () => {
    const allSelected = selected.length === data.length;
    const newSelected = allSelected ? [] : data.map((item) => item.id);
    
    setSelected(newSelected);
    if (onSelectAll) onSelectAll(!allSelected);
  };

  return (
    <div className="table-container">
      <table className="data-table">
        <thead>
          <tr>
            {isSelectable && (
              <th scope="col" className="data-table-cell">
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  checked={data.length > 0 && selected.length === data.length}
                  onChange={handleSelectAll}
                />
              </th>
            )}
            {columns.map((column) => (
              <th
                key={column.key as string}
                scope="col"
                className="data-table-header data-table-cell"
                style={{ width: column.width }}
              >
                {column.header}
              </th>
            ))}
            <th scope="col" className="data-table-header data-table-cell">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {data.length === 0 ? (
            <tr>
              <td
                colSpan={isSelectable ? columns.length + 2 : columns.length + 1}
                className="data-table-cell text-center py-8 text-gray-500"
              >
                No data available
              </td>
            </tr>
          ) : (
            data.map((item) => (
              <tr key={item.id} className="data-table-row">
                {isSelectable && (
                  <td className="data-table-cell">
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      checked={selected.includes(item.id)}
                      onChange={() => handleSelect(item.id)}
                    />
                  </td>
                )}
                {columns.map((column) => {
                  const key = column.key as keyof T;
                  return (
                    <td key={column.key as string} className="data-table-cell">
                      {column.render
                        ? column.render(item)
                        : key === 'isActive' 
                          ? <Toggle 
                              checked={Boolean(item[key])} 
                              onChange={() => {}} 
                            />
                          : String(item[key] || '--')}
                    </td>
                  );
                })}
                <td className="data-table-cell">
                  <div className="flex space-x-2">
                    {onEdit && (
                      <button
                        onClick={() => onEdit(item.id)}
                        className="text-gray-600 hover:text-primary-600"
                      >
                        <Edit size={18} />
                      </button>
                    )}
                    {onDelete && (
                      <button
                        onClick={() => onDelete(item.id)}
                        className="text-gray-600 hover:text-red-600"
                      >
                        <Trash2 size={18} />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}

export default DataTable;