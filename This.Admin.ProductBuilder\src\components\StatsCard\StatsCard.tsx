import React from 'react';
import { Card } from 'react-bootstrap';

interface StatsCardProps {
  value: number | string;
  label: string;
  className?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({ 
  value, 
  label, 
  className = '' 
}) => {
  return (
    <div className={`stats-card ${className}`}>
      <Card.Body>
        <div className="stats-number">{value}</div>
        <div className="stats-label">{label}</div>
      </Card.Body>
    </div>
  );
};
