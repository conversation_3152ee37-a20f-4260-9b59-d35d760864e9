using System.ComponentModel.DataAnnotations;

namespace Application.ActionManagement.DTOs;

/// <summary>
/// Update Action DTO
/// </summary>
public class UpdateActionDto
{
    /// <summary>
    /// Action name
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the action
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// API endpoint template for API actions
    /// </summary>
    [MaxLength(500)]
    public string? EndpointTemplate { get; set; }

    /// <summary>
    /// Navigation target for Navigation actions
    /// </summary>
    [MaxLength(500)]
    public string? NavigationTarget { get; set; }

    /// <summary>
    /// Icon for the action
    /// </summary>
    [MaxLength(100)]
    public string? Icon { get; set; }

    /// <summary>
    /// Button style - 'Primary', 'Secondary', 'Danger', etc.
    /// </summary>
    [MaxLength(50)]
    public string? ButtonStyle { get; set; }

    /// <summary>
    /// Confirmation dialog message
    /// </summary>
    public string? ConfirmationMessage { get; set; }

    /// <summary>
    /// Success message to display after action completion
    /// </summary>
    public string? SuccessMessage { get; set; }

    /// <summary>
    /// Error message to display on action failure
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether the action is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
