using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.Context.Commands.UpsertBulkTenantContext;

/// <summary>
/// Handler for UpsertBulkTenantContextCommand
/// </summary>
public class UpsertBulkTenantContextCommandHandler : IRequestHandler<UpsertBulkTenantContextCommand, Result<BulkUpsertTenantContextResponse>>
{
    private readonly IRepository<TenantContext> _tenantContextRepository;
    private readonly ILogger<UpsertBulkTenantContextCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertBulkTenantContextCommandHandler(
        IRepository<TenantContext> tenantContextRepository,
        ILogger<UpsertBulkTenantContextCommandHandler> logger)
    {
        _tenantContextRepository = tenantContextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<BulkUpsertTenantContextResponse>> Handle(UpsertBulkTenantContextCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new BulkUpsertTenantContextResponse();

        try
        {
            _logger.LogInformation("Starting bulk upsert of {Count} tenant contexts", request.TenantContexts.Count);

            foreach (var tenantContextItem in request.TenantContexts)
            {
                try
                {
                    TenantContext tenantContext;

                    if (tenantContextItem.Id.HasValue && tenantContextItem.Id.Value != Guid.Empty)
                    {
                        // Update existing tenant context
                        tenantContext = await _tenantContextRepository.GetByIdAsync(tenantContextItem.Id.Value, cancellationToken);
                        if (tenantContext == null)
                        {
                            response.Errors.Add($"TenantContext with ID {tenantContextItem.Id.Value} not found");
                            response.FailedCount++;
                            continue;
                        }

                        tenantContext.Name = tenantContextItem.Name;
                        tenantContext.Description = tenantContextItem.Description;
                        tenantContext.Category = tenantContextItem.Category;
                        tenantContext.IsActive = tenantContextItem.IsActive;

                        await _tenantContextRepository.UpdateAsync(tenantContext, cancellationToken);
                        response.UpdatedCount++;
                        response.UpdatedIds.Add(tenantContext.Id);
                    }
                    else
                    {
                        // Create new tenant context
                        tenantContext = new TenantContext
                        {
                            Id = Guid.NewGuid(),
                            Name = tenantContextItem.Name,
                            Description = tenantContextItem.Description,
                            Category = tenantContextItem.Category,
                            IsActive = tenantContextItem.IsActive,
                            IsDeleted = false
                        };

                        await _tenantContextRepository.AddAsync(tenantContext, cancellationToken);
                        response.CreatedCount++;
                        response.CreatedIds.Add(tenantContext.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing tenant context: {Name}", tenantContextItem.Name);
                    response.Errors.Add($"Error processing tenant context '{tenantContextItem.Name}': {ex.Message}");
                    response.FailedCount++;
                }
            }

            stopwatch.Stop();
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Bulk upsert completed: Created={Created}, Updated={Updated}, Failed={Failed}, Time={Time}ms",
                response.CreatedCount, response.UpdatedCount, response.FailedCount, response.ProcessingTimeMs);

            return Result<BulkUpsertTenantContextResponse>.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            _logger.LogError(ex, "Bulk upsert failed after {Time}ms", response.ProcessingTimeMs);
            return Result<BulkUpsertTenantContextResponse>.Failure($"Bulk upsert failed: {ex.Message}");
        }
    }
}
