using MediatR;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertContext;

/// <summary>
/// Command to upsert a single context
/// </summary>
public class UpsertContextCommand : IRequest<Result<Guid>>
{
    /// <summary>
    /// Context ID (optional for insert)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Name of the context
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the context
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Category for grouping contexts
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Whether the context is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
