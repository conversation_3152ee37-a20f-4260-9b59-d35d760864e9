import 'package:flutter/material.dart';
import '../utils/constants.dart';

/// Loading widget with different states and animations
class LoadingWidget extends StatelessWidget {
  final String? message;
  final double? size;
  final Color? color;

  const LoadingWidget({
    super.key,
    this.message,
    this.size,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size ?? AppConstants.iconSizeXL,
            height: size ?? AppConstants.iconSizeXL,
            child: CircularProgressIndicator(
              color: color ?? Theme.of(context).colorScheme.primary,
              strokeWidth: 3.0,
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: AppConstants.spacingM),
            Text(
              message!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Shimmer loading effect for list items
class ShimmerLoading extends StatefulWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;

  const ShimmerLoading({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
  });

  @override
  State<ShimmerLoading> createState() => _ShimmerLoadingState();
}

class _ShimmerLoadingState extends State<ShimmerLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ??
                BorderRadius.circular(AppConstants.radiusM),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: const [
                Color(0xFFE0E0E0),
                Color(0xFFF5F5F5),
                Color(0xFFE0E0E0),
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ],
            ),
          ),
        );
      },
    );
  }
}

/// Navigation drawer shimmer loading
class NavigationShimmerLoading extends StatelessWidget {
  const NavigationShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header shimmer
        Container(
          padding: const EdgeInsets.all(AppConstants.spacingM),
          child: const ShimmerLoading(
            width: double.infinity,
            height: 60,
          ),
        ),

        // Search bar shimmer
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.spacingM,
            vertical: AppConstants.spacingS,
          ),
          child: const ShimmerLoading(
            width: double.infinity,
            height: 48,
          ),
        ),

        const SizedBox(height: AppConstants.spacingM),

        // Navigation items shimmer
        Expanded(
          child: ListView.builder(
            itemCount: 8,
            itemBuilder: (context, index) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.spacingM,
                  vertical: AppConstants.spacingS,
                ),
                child: Row(
                  children: [
                    ShimmerLoading(
                      width: AppConstants.iconSizeM,
                      height: AppConstants.iconSizeM,
                      borderRadius: BorderRadius.circular(AppConstants.radiusS),
                    ),
                    const SizedBox(width: AppConstants.spacingM),
                    Expanded(
                      child: ShimmerLoading(
                        width: double.infinity,
                        height: 20,
                        borderRadius: BorderRadius.circular(AppConstants.radiusS),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Data table shimmer loading
class DataTableShimmerLoading extends StatelessWidget {
  final int rowCount;
  final int columnCount;

  const DataTableShimmerLoading({
    super.key,
    this.rowCount = 5,
    this.columnCount = 4,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              children: List.generate(
                columnCount,
                (index) => Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(right: AppConstants.spacingM),
                    child: const ShimmerLoading(
                      width: double.infinity,
                      height: 24,
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: AppConstants.spacingM),

            // Data rows
            ...List.generate(
              rowCount,
              (rowIndex) => Container(
                margin: const EdgeInsets.only(bottom: AppConstants.spacingS),
                child: Row(
                  children: List.generate(
                    columnCount,
                    (colIndex) => Expanded(
                      child: Container(
                        margin: const EdgeInsets.only(right: AppConstants.spacingM),
                        child: const ShimmerLoading(
                          width: double.infinity,
                          height: 20,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Skeleton loading for cards
class CardSkeletonLoading extends StatelessWidget {
  final double? width;
  final double? height;

  const CardSkeletonLoading({
    super.key,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Container(
        width: width,
        height: height ?? 200,
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ShimmerLoading(
              width: double.infinity,
              height: 24,
            ),
            const SizedBox(height: AppConstants.spacingM),
            const ShimmerLoading(
              width: double.infinity,
              height: 16,
            ),
            const SizedBox(height: AppConstants.spacingS),
            ShimmerLoading(
              width: MediaQuery.of(context).size.width * 0.6,
              height: 16,
            ),
            const Spacer(),
            Row(
              children: [
                const ShimmerLoading(
                  width: 80,
                  height: 32,
                ),
                const Spacer(),
                ShimmerLoading(
                  width: AppConstants.iconSizeM,
                  height: AppConstants.iconSizeM,
                  borderRadius: BorderRadius.circular(AppConstants.iconSizeM / 2),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Loading overlay for the entire screen
class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? loadingMessage;

  const LoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.loadingMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: LoadingWidget(
              message: loadingMessage,
            ),
          ),
      ],
    );
  }
}
