using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationApis.Commands;

/// <summary>
/// Delete integration API command handler
/// </summary>
public class DeleteIntegrationApiCommandHandler : IRequestHandler<DeleteIntegrationApiCommand, Result<bool>>
{
    private readonly IRepositoryWithEvents<IntegrationApi> _integrationApiRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteIntegrationApiCommandHandler(IRepositoryWithEvents<IntegrationApi> integrationApiRepository)
    {
        _integrationApiRepository = integrationApiRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteIntegrationApiCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing integration API
            var integrationApi = await _integrationApiRepository.GetByIdAsync(request.Id, cancellationToken);
            if (integrationApi == null || integrationApi.IsDeleted)
            {
                return Result<bool>.Failure($"Integration API with ID {request.Id} not found.");
            }

            // Check if integration API is being used by any integration configurations
            // For now, we'll skip this check since we need to implement a proper specification
            // TODO: Implement proper check for integration configurations using this API

            // Soft delete
            integrationApi.IsDeleted = true;
            integrationApi.ModifiedAt = DateTime.UtcNow;

            await _integrationApiRepository.UpdateAsync(integrationApi, cancellationToken);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure($"Failed to delete integration API: {ex.Message}");
        }
    }
}
