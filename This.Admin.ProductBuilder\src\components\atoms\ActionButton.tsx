/**
 * ActionButton Atom
 * Reusable action button component with consistent styling and loading states
 */

import React from 'react';
import { Button, ButtonProps } from 'react-bootstrap';
import { LoadingSpinner } from './LoadingSpinner';

export interface ActionButtonProps extends Omit<ButtonProps, 'onClick'> {
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>;
  loading?: boolean;
  icon?: string | React.ReactNode;
  iconPosition?: 'left' | 'right';
  loadingText?: string;
  confirmAction?: boolean;
  confirmMessage?: string;
  tooltip?: string;
}

export const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  loading = false,
  icon,
  iconPosition = 'left',
  loadingText,
  confirmAction = false,
  confirmMessage = 'Are you sure?',
  tooltip,
  disabled,
  className = '',
  ...buttonProps
}) => {
  const [isProcessing, setIsProcessing] = React.useState(false);

  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!onClick || loading || isProcessing) return;

    // Confirm action if required
    if (confirmAction && !window.confirm(confirmMessage)) {
      return;
    }

    try {
      setIsProcessing(true);
      await onClick(event);
    } catch (error) {
      console.error('Action button error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const isLoading = loading || isProcessing;
  const isDisabled = disabled || isLoading;

  const renderIcon = () => {
    if (isLoading) {
      return <LoadingSpinner size="sm" />;
    }
    
    if (typeof icon === 'string') {
      return <span>{icon}</span>;
    }
    
    return icon;
  };

  const renderContent = () => {
    const text = isLoading && loadingText ? loadingText : children;
    const iconElement = renderIcon();

    if (!iconElement) {
      return text;
    }

    if (iconPosition === 'right') {
      return (
        <>
          {text}
          {iconElement && <span className="ms-2">{iconElement}</span>}
        </>
      );
    }

    return (
      <>
        {iconElement && <span className="me-2">{iconElement}</span>}
        {text}
      </>
    );
  };

  return (
    <Button
      {...buttonProps}
      onClick={handleClick}
      disabled={isDisabled}
      className={`d-flex align-items-center ${className}`}
      title={tooltip}
    >
      {renderContent()}
    </Button>
  );
};

// Predefined action button variants
export const RefreshButton: React.FC<Omit<ActionButtonProps, 'icon' | 'variant'>> = (props) => (
  <ActionButton
    variant="outline-primary"
    icon="🔄"
    loadingText="Refreshing..."
    {...props}
  >
    Refresh
  </ActionButton>
);

export const AddButton: React.FC<Omit<ActionButtonProps, 'icon' | 'variant'>> = (props) => (
  <ActionButton
    variant="primary"
    icon="+"
    {...props}
  >
    {props.children || 'Add'}
  </ActionButton>
);

export const EditButton: React.FC<Omit<ActionButtonProps, 'icon' | 'variant' | 'size'>> = (props) => (
  <ActionButton
    variant="outline-secondary"
    size="sm"
    icon="✏️"
    tooltip="Edit"
    {...props}
  />
);

export const ViewButton: React.FC<Omit<ActionButtonProps, 'icon' | 'variant' | 'size'>> = (props) => (
  <ActionButton
    variant="outline-primary"
    size="sm"
    icon="👁️"
    tooltip="View Details"
    {...props}
  />
);

export const DeleteButton: React.FC<Omit<ActionButtonProps, 'icon' | 'variant' | 'size' | 'confirmAction'>> = (props) => (
  <ActionButton
    variant="outline-danger"
    size="sm"
    icon="🗑️"
    tooltip="Delete"
    confirmAction
    confirmMessage="Are you sure you want to delete this item?"
    loadingText="Deleting..."
    {...props}
  />
);
