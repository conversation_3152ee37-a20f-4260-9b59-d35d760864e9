using Application.Identity.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Identity.Commands;

/// <summary>
/// Command for updating roles for multiple users in bulk
/// </summary>
public class BulkUpdateUserRolesCommand : IRequest<ApiResponse<BulkUpdateUserRolesResponse>>
{
    /// <summary>
    /// List of user role update requests
    /// </summary>
    public List<UserRoleUpdateRequest> UserRoleUpdates { get; set; } = new();

    /// <summary>
    /// Whether to continue processing if some users fail to update
    /// </summary>
    public bool ContinueOnError { get; set; } = true;

    /// <summary>
    /// Whether to validate all users and roles before updating any
    /// </summary>
    public bool ValidateBeforeUpdate { get; set; } = true;

    /// <summary>
    /// For update the prodect
    /// </summary>
    public Guid ProductId { get; set; }
}

/// <summary>
/// Individual user role update request
/// </summary>
public class UserRoleUpdateRequest
{
    /// <summary>
    /// User ID whose roles need to be updated
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// List of role IDs to assign to the user (replaces existing roles)
    /// </summary>
    public List<string> RoleIds { get; set; } = new();
}

/// <summary>
/// Response for bulk user role updates
/// </summary>
public class BulkUpdateUserRolesResponse
{
    /// <summary>
    /// Total number of users requested to be updated
    /// </summary>
    public int TotalRequested { get; set; }

    /// <summary>
    /// Number of users successfully updated
    /// </summary>
    public int SuccessfullyUpdated { get; set; }

    /// <summary>
    /// Number of users that failed to update
    /// </summary>
    public int Failed { get; set; }

    /// <summary>
    /// List of successfully updated users with their new roles
    /// </summary>
    public List<UserRoleUpdateResult> UpdatedUsers { get; set; } = new();

    /// <summary>
    /// List of errors for failed user role updates
    /// </summary>
    public List<BulkUserRoleUpdateError> Errors { get; set; } = new();

    /// <summary>
    /// Overall success message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Result for a successful user role update
/// </summary>
public class UserRoleUpdateResult
{
    /// <summary>
    /// User ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// User's email
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's username
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// List of roles assigned to the user after update
    /// </summary>
    public List<UserRoleDto> AssignedRoles { get; set; } = new();

    /// <summary>
    /// Update message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Error information for failed user role update
/// </summary>
public class BulkUserRoleUpdateError
{
    /// <summary>
    /// Index of the user in the original request
    /// </summary>
    public int UserIndex { get; set; }

    /// <summary>
    /// User ID that failed to update
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// List of role IDs that were attempted to be assigned
    /// </summary>
    public List<string> AttemptedRoleIds { get; set; } = new();

    /// <summary>
    /// Error message
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// Error details
    /// </summary>
    public string? ErrorDetails { get; set; }
}
