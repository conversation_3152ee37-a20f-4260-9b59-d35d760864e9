// Comprehensive Subscription API Service
// DEPRECATED: Use subscriptionApiService from './api/subscriptionApiService' instead
import { API_CONFIG } from '../config/apiConfig';

const API_BASE_URL = API_CONFIG.PRIMARY.BASE_URL;

export interface ComprehensiveSubscriptionDto {
  id: string;
  tenantId: string;
  tenantName: string | null;
  productId: string;
  productName: string | null;
  subscriptionType: string;
  status: string;
  startDate: string;
  endDate: string | null;
  autoRenew: boolean;
  pricingTier: string | null;
  version: string;
  templateJson: string;
  isActive: boolean;
  metadataCount: number;
  createdAt: string;
  createdBy: string;
  modifiedAt: string | null;
  modifiedBy: string | null;
  isExpired: boolean;
  daysUntilExpiration: number | null;
}

// Legacy interface for backward compatibility
export interface SubscriptionResponse extends ComprehensiveSubscriptionDto {
  // Additional computed properties for UI compatibility
  name?: string;
  type?: string;
  userCount?: number;
  price?: string;
}

export interface GetComprehensiveSubscriptionsParams {
  tenantId?: string;
  productId?: string;
  status?: string;
  subscriptionType?: string;
  isActive?: boolean;
  isExpired?: boolean;
  pricingTier?: string;
  searchTerm?: string;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
  expiringWithinDays?: number;
  pageNumber?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: string;
  includeSummary?: boolean;
}

export interface ComprehensiveSubscriptionSummary {
  activeSubscriptions: number;
  expiredSubscriptions: number;
  expiringIn30Days: number;
  uniqueTenants: number;
  uniqueProducts: number;
  subscriptionsByStatus: Record<string, number>;
  subscriptionsByType: Record<string, number>;
}

export interface ComprehensiveSubscriptionResponse {
  subscriptions: ComprehensiveSubscriptionDto[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  summary: ComprehensiveSubscriptionSummary;
}

export interface ApiResult<T> {
  succeeded: boolean;
  data: T;
  message: string;
  errors?: string[];
}

// Legacy interface for backward compatibility
export interface GetSubscriptionsParams extends GetComprehensiveSubscriptionsParams {
  PageNumber?: number;
  PageSize?: number;
  IsActive?: boolean;
}

export interface PaginatedSubscriptionResponse {
  succeeded: boolean;
  data: SubscriptionResponse[];
  message: string;
  statusCode: number;
  page: number;
  firstPage: number;
  lastPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export interface TenantResponse {
  id: string;
  name: string;
  connectionString: string;
  readReplicaConnectionString: string | null;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string | null;
}

export interface TenantsApiResponse {
  succeeded: boolean;
  message: string | null;
  errors: string[] | null;
  data: TenantResponse[];
}

// Additional interfaces for comprehensive API
export interface GetComprehensiveSubscriptionsParams {
  tenantId?: string;
  productId?: string;
  status?: string;
  subscriptionType?: string;
  isActive?: boolean;
  isExpired?: boolean;
  pricingTier?: string;
  searchTerm?: string;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
  expiringWithinDays?: number;
  pageNumber?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: string;
  includeSummary?: boolean;
}

export interface ApiResult<T> {
  succeeded: boolean;
  message: string;
  data: T;
}

export interface LegacyComprehensiveSubscriptionResponse {
  subscriptions: SubscriptionResponse[];
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  summary: ComprehensiveSubscriptionSummary;
}

export interface ComprehensiveSubscriptionSummary {
  activeSubscriptions: number;
  expiredSubscriptions: number;
  expiringIn30Days: number;
  uniqueTenants: number;
  uniqueProducts: number;
  subscriptionsByStatus: Record<string, number>;
  subscriptionsByType: Record<string, number>;
}

export class SubscriptionService {
  private async makeRequest<T>(
    url: string,
    options: RequestInit,
    tenant?: string
  ): Promise<T> {
    try {
      const headers: Record<string, string> = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        ...options.headers as Record<string, string>,
      };

      // Add tenant header only if provided (comprehensive endpoint doesn't require it)
      if (tenant) {
        headers['tenant'] = tenant;
      }

      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText || response.statusText}`);
      }

      // Handle empty responses
      const text = await response.text();
      if (!text) {
        return {} as T;
      }

      try {
        return JSON.parse(text) as T;
      } catch {
        // If response is not JSON, return as string
        return text as unknown as T;
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Network error: ${String(error)}`);
    }
  }

  async getComprehensiveSubscriptions(
    params: GetComprehensiveSubscriptionsParams = {}
  ): Promise<ApiResult<ComprehensiveSubscriptionResponse>> {
    const queryParams = new URLSearchParams();

    // Add all comprehensive subscription parameters (only if they have values)
    if (params.tenantId && params.tenantId.trim() !== '') queryParams.append('tenantId', params.tenantId);
    if (params.productId) queryParams.append('productId', params.productId);
    if (params.status) queryParams.append('status', params.status);
    if (params.subscriptionType) queryParams.append('subscriptionType', params.subscriptionType);
    if (params.isActive !== undefined) queryParams.append('isActive', params.isActive.toString());
    if (params.isExpired !== undefined) queryParams.append('isExpired', params.isExpired.toString());
    if (params.pricingTier) queryParams.append('pricingTier', params.pricingTier);
    if (params.searchTerm) queryParams.append('searchTerm', params.searchTerm);
    if (params.startDateFrom) queryParams.append('startDateFrom', params.startDateFrom);
    if (params.startDateTo) queryParams.append('startDateTo', params.startDateTo);
    if (params.endDateFrom) queryParams.append('endDateFrom', params.endDateFrom);
    if (params.endDateTo) queryParams.append('endDateTo', params.endDateTo);
    if (params.expiringWithinDays !== undefined) queryParams.append('expiringWithinDays', params.expiringWithinDays.toString());

    // Set default pagination if not provided
    queryParams.append('pageNumber', (params.pageNumber || 1).toString());
    queryParams.append('pageSize', (params.pageSize || 10).toString());

    if (params.orderBy) queryParams.append('orderBy', params.orderBy);
    if (params.orderDirection) queryParams.append('orderDirection', params.orderDirection);

    // Default to include summary
    queryParams.append('includeSummary', (params.includeSummary !== false).toString());

    const url = `${API_BASE_URL}/comprehensive-entity/subscriptions?${queryParams.toString()}`;

    console.log('🌐 Comprehensive subscriptions API call:', {
      url,
      params,
      tenantFilter: params.tenantId ? `Applied (${params.tenantId})` : 'None (All tenants)',
      queryString: queryParams.toString()
    });

    try {
      console.log('🌐 Fetching comprehensive subscriptions from:', url);

      const response = await this.makeRequest<ApiResult<ComprehensiveSubscriptionResponse>>(
        url,
        { method: 'GET' }
      );

      console.log('📋 Comprehensive subscriptions API response:', {
        succeeded: response.succeeded,
        message: response.message,
        dataExists: !!response.data,
        subscriptionCount: response.data?.subscriptions?.length || 0,
        totalCount: response.data?.totalCount || 0,
        fullResponse: response
      });

      // Validate response structure
      if (response.succeeded && response.data) {
        if (!response.data.subscriptions) {
          console.warn('⚠️ Response data missing subscriptions array, setting empty array');
          response.data.subscriptions = [];
        }
        if (typeof response.data.totalCount !== 'number') {
          console.warn('⚠️ Response data missing totalCount, setting to 0');
          response.data.totalCount = 0;
        }
      }

      return response;
    } catch (error) {
      console.error('❌ Error fetching comprehensive subscriptions:', {
        url,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  // Legacy method for backward compatibility
  async getSubscriptions(
    params: GetSubscriptionsParams = {},
    tenant?: string
  ): Promise<PaginatedSubscriptionResponse> {
    try {
      // Convert legacy params to comprehensive params
      const comprehensiveParams: GetComprehensiveSubscriptionsParams = {
        // Only include tenantId if explicitly provided and not empty
        ...(tenant && tenant.trim() !== '' && { tenantId: tenant }),
        pageNumber: params.PageNumber || params.pageNumber || 1,
        pageSize: params.PageSize || params.pageSize || 10,
        isActive: params.IsActive !== undefined ? params.IsActive : params.isActive,
        searchTerm: params.searchTerm,
        status: params.status,
        includeSummary: true
      };

      console.log('🔍 Legacy getSubscriptions called with:', {
        originalParams: params,
        tenant: tenant,
        comprehensiveParams: comprehensiveParams,
        willFilterByTenant: !!(tenant && tenant.trim() !== '')
      });

      console.log('🔍 Calling comprehensive subscriptions with params:', comprehensiveParams);
      const comprehensiveResponse = await this.getComprehensiveSubscriptions(comprehensiveParams);
      console.log('📋 Comprehensive response received:', {
        succeeded: comprehensiveResponse.succeeded,
        message: comprehensiveResponse.message,
        hasData: !!comprehensiveResponse.data,
        dataKeys: comprehensiveResponse.data ? Object.keys(comprehensiveResponse.data) : [],
        errors: comprehensiveResponse.errors
      });

      if (!comprehensiveResponse.succeeded) {
        console.error('❌ Comprehensive subscriptions API failed:', {
          succeeded: comprehensiveResponse.succeeded,
          message: comprehensiveResponse.message,
          errors: comprehensiveResponse.errors,
          fullResponse: comprehensiveResponse
        });

        // Try to provide a more user-friendly error message
        let errorMessage = comprehensiveResponse.message || 'Failed to fetch subscriptions';
        if (errorMessage.includes('Error processing comprehensive subscription data')) {
          errorMessage = 'Unable to load subscription data. This may be due to data processing issues on the server.';
        }

        throw new Error(errorMessage);
      }

      // Validate response data structure
      if (!comprehensiveResponse.data) {
        console.error('❌ No data in comprehensive response:', comprehensiveResponse);
        throw new Error('No data received from comprehensive subscriptions API');
      }

      if (!comprehensiveResponse.data.subscriptions) {
        console.error('❌ No subscriptions array in response data:', comprehensiveResponse.data);
        throw new Error('Invalid response structure: missing subscriptions array');
      }

      console.log('✅ Processing subscriptions data:', {
        subscriptionCount: comprehensiveResponse.data.subscriptions.length,
        totalCount: comprehensiveResponse.data.totalCount
      });

      // Transform comprehensive response to legacy format
      const legacyData: SubscriptionResponse[] = comprehensiveResponse.data.subscriptions.map((sub, index) => {
        try {
          return {
            ...sub,
            name: sub.productName || `Subscription ${sub.id.substring(0, 8)}`,
            type: sub.subscriptionType,
            userCount: SubscriptionUtils.calculateUserCount(sub.metadataCount),
            price: SubscriptionUtils.formatPrice(sub.pricingTier || '0')
          };
        } catch (error) {
          console.error(`❌ Error transforming subscription at index ${index}:`, { sub, error });
          throw new Error(`Failed to transform subscription data at index ${index}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      });

      return {
        succeeded: true,
        data: legacyData,
        message: comprehensiveResponse.message,
        statusCode: 200,
        page: comprehensiveResponse.data.pageNumber,
        firstPage: 1,
        lastPage: comprehensiveResponse.data.totalPages,
        totalPages: comprehensiveResponse.data.totalPages,
        totalItems: comprehensiveResponse.data.totalCount,
        pageSize: comprehensiveResponse.data.pageSize,
        hasPreviousPage: comprehensiveResponse.data.hasPreviousPage,
        hasNextPage: comprehensiveResponse.data.hasNextPage
      };
    } catch (error) {
      console.error('❌ Error in comprehensive subscriptions, trying fallback:', error);

      // Fallback to basic subscriptions API if comprehensive fails
      try {
        console.log('🔄 Attempting fallback to basic subscriptions API...');

        const fallbackParams = new URLSearchParams();
        if (params.PageNumber || params.pageNumber) fallbackParams.append('PageNumber', String(params.PageNumber || params.pageNumber));
        if (params.PageSize || params.pageSize) fallbackParams.append('PageSize', String(params.PageSize || params.pageSize));
        if (params.IsActive !== undefined) fallbackParams.append('IsActive', String(params.IsActive));
        if (params.isActive !== undefined) fallbackParams.append('IsActive', String(params.isActive));
        if (params.searchTerm) fallbackParams.append('searchTerm', params.searchTerm);
        if (params.status) fallbackParams.append('status', params.status);

        const fallbackUrl = `${API_BASE_URL}/subscriptions?${fallbackParams.toString()}`;
        console.log('🌐 Fallback API URL:', fallbackUrl);

        const fallbackResponse = await this.makeRequest<PaginatedSubscriptionResponse>(
          fallbackUrl,
          { method: 'GET' },
          tenant
        );

        console.log('✅ Fallback API response received:', fallbackResponse);
        return fallbackResponse;

      } catch (fallbackError) {
        console.error('❌ Fallback API also failed:', fallbackError);
        throw new Error(`Both comprehensive and basic subscription APIs failed. Original error: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  async getSubscriptionById(
    id: string,
    tenant: string = 'lrbnewqa'
  ): Promise<SubscriptionResponse> {
    // Use comprehensive endpoint to get single subscription
    try {
      const response = await this.getComprehensiveSubscriptions({
        tenantId: tenant,
        pageSize: 1000 // Get all to find the specific one
      });

      if (!response.succeeded) {
        throw new Error(response.message || 'Failed to fetch subscription');
      }

      const subscription = response.data.subscriptions.find(sub => sub.id === id);
      if (!subscription) {
        throw new Error(`Subscription with ID ${id} not found`);
      }

      return {
        ...subscription,
        name: subscription.productName || `Subscription ${subscription.id.substring(0, 8)}`,
        type: subscription.subscriptionType,
        userCount: SubscriptionUtils.calculateUserCount(subscription.metadataCount),
        price: SubscriptionUtils.formatPrice(subscription.pricingTier || '0')
      };
    } catch (error) {
      console.error(`Error fetching subscription ${id}:`, error);
      throw error;
    }
  }

  // Get tenants from dedicated API endpoint
  async getTenants(): Promise<TenantsApiResponse> {
    const url = `${API_BASE_URL}/tenants`;

    try {
      console.log('Fetching tenants from:', url);

      const response = await this.makeRequest<TenantsApiResponse>(
        url,
        { method: 'GET' },
        'lrbnewqa' // Default tenant for fetching tenant list
      );

      console.log('Tenants API response:', response);
      return response;
    } catch (error) {
      console.error('Error fetching tenants:', error);
      throw error;
    }
  }

  // Helper method to get available tenants from dedicated API
  async getAvailableTenants(): Promise<TenantResponse[]> {
    try {
      const response = await this.getTenants();

      if (response.succeeded && response.data) {
        return response.data.filter(tenant => tenant.isActive);
      }

      return []; // Return empty array if API fails
    } catch (error) {
      console.error('Error fetching available tenants:', error);
      return []; // Return empty array if API fails
    }
  }

  // Fallback method to get tenant names only
  async getAvailableTenantNames(): Promise<string[]> {
    try {
      const tenants = await this.getAvailableTenants();
      return tenants.map(tenant => tenant.name);
    } catch (error) {
      console.error('Error fetching tenant names:', error);
      return ['lrbnewqa']; // fallback
    }
  }

  // Helper method to validate tenant
  async isValidTenant(tenant: string): Promise<boolean> {
    const availableTenants = await this.getAvailableTenants();
    return availableTenants.some(t => t.name === tenant);
  }

  // Get subscription summary statistics
  async getSubscriptionSummary(): Promise<ComprehensiveSubscriptionSummary> {
    try {
      const response = await this.getComprehensiveSubscriptions({
        pageSize: 1,
        includeSummary: true
      });

      if (response.succeeded) {
        return response.data.summary;
      }

      throw new Error(response.message || 'Failed to fetch subscription summary');
    } catch (error) {
      console.error('Error fetching subscription summary:', error);
      throw error;
    }
  }
}

// Create a singleton instance
export const subscriptionService = new SubscriptionService();

// Utility functions for subscription data
export class SubscriptionUtils {
  static getStatusBadgeVariant(status: string): string {
    switch (status.toLowerCase()) {
      case 'active': return 'success';
      case 'inactive': return 'secondary';
      case 'expired': return 'danger';
      case 'pending': return 'warning';
      case 'cancelled': return 'dark';
      default: return 'secondary';
    }
  }

  static getTypeBadgeVariant(type: string): string {
    switch (type.toLowerCase()) {
      case 'enterprise': return 'primary';
      case 'premium': return 'warning';
      case 'basic': return 'info';
      case 'trial': return 'secondary';
      default: return 'info';
    }
  }

  static formatPrice(pricingTier: string): string {
    // Extract price from pricing tier string or return as is
    if (pricingTier && pricingTier.includes('$')) {
      return pricingTier;
    }
    return `$${pricingTier || '0.00'}`;
  }

  static calculateUserCount(metadataCount: number): number {
    // This might need adjustment based on how user count is determined
    return metadataCount || 0;
  }

  static isSubscriptionActive(subscription: SubscriptionResponse): boolean {
    const now = new Date();
    if (!subscription.endDate) {
      return subscription.isActive && subscription.status.toLowerCase() === 'active';
    }
    const endDate = new Date(subscription.endDate);
    return subscription.isActive && subscription.status.toLowerCase() === 'active' && endDate > now;
  }

  static getDaysUntilExpiry(subscription: SubscriptionResponse): number {
    if (!subscription.endDate) {
      return Infinity; // No expiry date
    }
    const now = new Date();
    const endDate = new Date(subscription.endDate);
    const diffTime = endDate.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
