/**
 * Components Index
 * Export all components following atomic design principles
 */

// Legacy components (to be migrated)
export { Layout } from './Layout';
export { GradientHeader } from './GradientHeader';
export { ModernCard, ModernCardHeader, ModernCardBody } from './ModernCard';
export { AddSubscriptionModal } from './AddSubscriptionModal';

// New components
export { TenantSelector } from './TenantSelector';
export { TenantApiDemo } from './TenantApiDemo';

// Atoms (smallest reusable components)
export * from './atoms';

// Molecules (combinations of atoms)
export * from './molecules';

// Organisms (combinations of molecules and atoms)
export * from './organisms';

// Re-export commonly used components for convenience
export { SearchInput } from './atoms/SearchInput';
export { StatusBadge } from './atoms/StatusBadge';
export { LoadingSpinner } from './atoms/LoadingSpinner';
export { ActionButton } from './atoms/ActionButton';
export { StatsCard as NewStatsCard } from './atoms/StatsCard'; // Renamed to avoid conflict

export { FilterBar } from './molecules/FilterBar';
export { ActionBar } from './molecules/ActionBar';
export { StatsGrid } from './molecules/StatsGrid';
export { DataTable } from './molecules/DataTable';
export { PaginationBar } from './molecules/PaginationBar';

export { SubscriptionTable } from './organisms/SubscriptionTable';
export { TemplateTable } from './organisms/TemplateTable';
export { SubscriptionModal } from './organisms/SubscriptionModal';

// Keep legacy StatsCard export for backward compatibility
export { StatsCard } from './StatsCard';
