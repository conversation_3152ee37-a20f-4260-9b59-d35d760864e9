// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NavigationResponse _$NavigationResponseFromJson(Map<String, dynamic> json) =>
    NavigationResponse(
      navigationItems: (json['navigationItems'] as List<dynamic>?)
          ?.map((e) => NavigationItemDto.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: (json['totalCount'] as num?)?.toInt(),
      maxDepth: (json['maxDepth'] as num?)?.toInt(),
    );

Map<String, dynamic> _$NavigationResponseToJson(NavigationResponse instance) =>
    <String, dynamic>{
      'navigationItems': instance.navigationItems,
      'totalCount': instance.totalCount,
      'maxDepth': instance.maxDepth,
    };

NavigationItemDto _$NavigationItemDtoFromJson(Map<String, dynamic> json) =>
    NavigationItemDto(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String?,
      description: json['description'] as String?,
      objectType: json['objectType'] as String?,
      parentId: json['parentId'] as String?,
      hierarchyLevel: (json['hierarchyLevel'] as num?)?.toInt(),
      hierarchyPath: json['hierarchyPath'] as String?,
      iconName: json['iconName'] as String?,
      iconType: json['iconType'] as String?,
      isActive: json['isActive'] as bool?,
      isVisible: json['isVisible'] as bool?,
      sortOrder: (json['sortOrder'] as num?)?.toInt(),
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      children: (json['children'] as List<dynamic>?)
          ?.map((e) => NavigationItemDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$NavigationItemDtoToJson(NavigationItemDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'description': instance.description,
      'objectType': instance.objectType,
      'parentId': instance.parentId,
      'hierarchyLevel': instance.hierarchyLevel,
      'hierarchyPath': instance.hierarchyPath,
      'iconName': instance.iconName,
      'iconType': instance.iconType,
      'isActive': instance.isActive,
      'isVisible': instance.isVisible,
      'sortOrder': instance.sortOrder,
      'permissions': instance.permissions,
      'metadata': instance.metadata,
      'children': instance.children,
    };

ObjectMetadataResponse _$ObjectMetadataResponseFromJson(
        Map<String, dynamic> json) =>
    ObjectMetadataResponse(
      objectTypes: (json['objectTypes'] as List<dynamic>?)
          ?.map((e) => ObjectTypeDto.fromJson(e as Map<String, dynamic>))
          .toList(),
      categories: (json['categories'] as List<dynamic>?)
          ?.map((e) => CategoryDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ObjectMetadataResponseToJson(
        ObjectMetadataResponse instance) =>
    <String, dynamic>{
      'objectTypes': instance.objectTypes,
      'categories': instance.categories,
    };

ObjectTypeDto _$ObjectTypeDtoFromJson(Map<String, dynamic> json) =>
    ObjectTypeDto(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String?,
      description: json['description'] as String?,
      category: json['category'] as String?,
      iconName: json['iconName'] as String?,
      isActive: json['isActive'] as bool?,
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$ObjectTypeDtoToJson(ObjectTypeDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'description': instance.description,
      'category': instance.category,
      'iconName': instance.iconName,
      'isActive': instance.isActive,
      'permissions': instance.permissions,
    };

CategoryDto _$CategoryDtoFromJson(Map<String, dynamic> json) => CategoryDto(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String?,
      iconName: json['iconName'] as String?,
      sortOrder: (json['sortOrder'] as num?)?.toInt(),
    );

Map<String, dynamic> _$CategoryDtoToJson(CategoryDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'iconName': instance.iconName,
      'sortOrder': instance.sortOrder,
    };
