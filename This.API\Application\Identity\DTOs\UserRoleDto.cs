namespace Application.Identity.DTOs;

/// <summary>
/// User role data transfer object
/// </summary>
public class UserRoleDto
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Role name
    /// </summary>
    public string? RoleName { get; set; }

    /// <summary>
    /// Role description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the role is enabled for the user
    /// </summary>
    public bool Enabled { get; set; }
}

/// <summary>
/// Request for updating user roles
/// </summary>
public class UpdateUserRolesRequest
{
    /// <summary>
    /// List of user roles with their enabled status
    /// </summary>
    public List<UserRoleDto> UserRoles { get; set; } = new();
}

/// <summary>
/// Request for assigning roles to a user
/// </summary>
public class AssignRolesToUserRequest
{
    /// <summary>
    /// User ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// List of role names to assign
    /// </summary>
    public List<string> RoleNames { get; set; } = new();
}

/// <summary>
/// Request for removing roles from a user
/// </summary>
public class RemoveRolesFromUserRequest
{
    /// <summary>
    /// User ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// List of role names to remove
    /// </summary>
    public List<string> RoleNames { get; set; } = new();
}

/// <summary>
/// Role data transfer object
/// </summary>
public class RoleDto
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Role name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Normalized role name
    /// </summary>
    public string? NormalizedName { get; set; }

    /// <summary>
    /// Concurrency stamp
    /// </summary>
    public string? ConcurrencyStamp { get; set; }

    /// <summary>
    /// Product ID this role belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Role description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is a system role
    /// </summary>
    public bool IsSystemRole { get; set; } = false;

    /// <summary>
    /// Permissions as JSON
    /// </summary>
    public string Permissions { get; set; } = "{}";

    /// <summary>
    /// Whether the role is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// When the role was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the role
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// When the role was last modified
    /// </summary>
    public DateTime ModifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who last modified the role
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Whether the role is soft deleted
    /// </summary>
    public bool IsDeleted { get; set; } = false;
}

/// <summary>
/// Request for creating a new role
/// </summary>
public class CreateRoleRequest
{
    /// <summary>
    /// Role name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product ID this role belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Role description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is a system role
    /// </summary>
    public bool IsSystemRole { get; set; } = false;

    /// <summary>
    /// Permissions as JSON
    /// </summary>
    public string Permissions { get; set; } = "{}";

    /// <summary>
    /// Whether the role is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
