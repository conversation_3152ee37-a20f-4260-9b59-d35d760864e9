// Service for handling object values operations (upsert, fetch, etc.)
import NavigationService from './navigationService';
import type { NavigationNode } from '../types/metadata';

// API Request/Response interfaces
export interface UpsertObjectWithMetadataRequest {
  metadataProperties: Record<string, any>;
}

export interface UpsertObjectWithMetadataResponse {
  objectId: string;
  refId: string;
  totalProcessed: number;
  insertedCount: number;
  updatedCount: number;
  skippedCount: number;
  objectValues: ObjectValueResponseData[];
  skippedProperties: SkippedPropertyInfo[];
  message: string;
}

export interface ObjectValueResponseData {
  id: string;
  objectMetadataId: string;
  refId: string;
  value: string;
  createdAt: string;
  modifiedAt: string;
  wasInserted: boolean;
}

export interface SkippedPropertyInfo {
  propertyName: string;
  reason: string;
}

export interface ObjectInstanceResponse {
  refId: string;
  instanceData: Record<string, any>;
  found: boolean;
  message?: string;
}

export class ObjectValuesService {
  private static instance: ObjectValuesService;

  // API Configuration
  private readonly API_BASE_URL = 'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net';
  private readonly API_HEADERS = {
    'accept': 'application/json',
    'Content-Type': 'application/json',
    'tenant': 'kitchsync'
  };
  private readonly REQUEST_TIMEOUT = 30000; // 30 seconds

  static getInstance(): ObjectValuesService {
    if (!ObjectValuesService.instance) {
      ObjectValuesService.instance = new ObjectValuesService();
    }
    return ObjectValuesService.instance;
  }

  /**
   * Get ObjectId from navigation data by object name
   */
  private getObjectIdByName(objectName: string): string | null {
    try {
      const navigationService = NavigationService.getInstance();
      const navigationData = navigationService.getCurrentData();

      if (!navigationData || !navigationData.nodes || navigationData.nodes.length === 0) {
        console.warn('No navigation data available');
        return null;
      }

      // Search through the navigation hierarchy to find the object
      const findObjectInNodes = (nodes: NavigationNode[]): string | null => {
        for (const node of nodes) {
          if (node.type === 'object' && node.name && node.name.toLowerCase() === objectName.toLowerCase()) {
            return node.id || null;
          }
          if (node.children && node.children.length > 0) {
            const found = findObjectInNodes(node.children);
            if (found) return found;
          }
        }
        return null;
      };

      return findObjectInNodes(navigationData.nodes);
    } catch (error) {
      console.error('Error getting ObjectId from navigation data:', error);
      return null;
    }
  }

  /**
   * Generate a new RefId (GUID)
   */
  private generateRefId(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Transform form data to API payload format
   */
  private transformFormDataToApiPayload(
    formData: Record<string, any>,
    objectId: string,
    refId?: string,
    parentObjectValueId?: string
  ): UpsertObjectWithMetadataRequest {
    // Create the metadata properties dictionary
    const metadataProperties: Record<string, any> = {
      ObjectId: objectId,
      RefId: refId || this.generateRefId(),
      ...(parentObjectValueId && { ParentObjectValueId: parentObjectValueId })
    };

    // Add all form fields as metadata properties
    Object.entries(formData).forEach(([key, value]) => {
      // Include all values, including empty strings, to ensure proper clearing of fields
      if (value !== null && value !== undefined) {
        metadataProperties[key] = value;
      }
    });

    return {
      metadataProperties
    };
  }

  /**
   * Create a new object instance
   */
  async createObject(
    objectName: string,
    formData: Record<string, any>,
    parentObjectValueId?: string
  ): Promise<UpsertObjectWithMetadataResponse> {
    try {
      // Get ObjectId from navigation data
      const objectId = this.getObjectIdByName(objectName);
      if (!objectId) {
        throw new Error(`ObjectId not found for object name: ${objectName}`);
      }

      // Transform form data to API payload
      const payload = this.transformFormDataToApiPayload(
        formData,
        objectId,
        undefined, // Let the API generate RefId
        parentObjectValueId
      );

      // Make API call
      const response = await fetch(`${this.API_BASE_URL}/api/objectvalues/upsert-single-with-metadata`, {
        method: 'POST',
        headers: this.API_HEADERS,
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(this.REQUEST_TIMEOUT)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.status} ${response.statusText}. ${errorText}`);
      }

      const result: UpsertObjectWithMetadataResponse = await response.json();

      return result;
    } catch (error) {
      console.error('Error creating object:', error);
      throw error instanceof Error ? error : new Error('Failed to create object');
    }
  }

  /**
   * Update an existing object instance
   */
  async updateObject(
    objectName: string,
    refId: string,
    formData: Record<string, any>,
    parentObjectValueId?: string
  ): Promise<UpsertObjectWithMetadataResponse> {
    try {
      // Get ObjectId from navigation data
      const objectId = this.getObjectIdByName(objectName);
      if (!objectId) {
        throw new Error(`ObjectId not found for object name: ${objectName}`);
      }

      // Transform form data to API payload
      const payload = this.transformFormDataToApiPayload(
        formData,
        objectId,
        refId,
        parentObjectValueId
      );

      // Make API call
      const response = await fetch(`${this.API_BASE_URL}/api/objectvalues/upsert-single-with-metadata`, {
        method: 'POST',
        headers: this.API_HEADERS,
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(this.REQUEST_TIMEOUT)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.status} ${response.statusText}. ${errorText}`);
      }

      const result: UpsertObjectWithMetadataResponse = await response.json();

      return result;
    } catch (error) {
      console.error('Error updating object:', error);
      throw error instanceof Error ? error : new Error('Failed to update object');
    }
  }

  /**
   * Get object instance data by RefId
   */
  async getObjectInstance(refId: string, objectName: string): Promise<ObjectInstanceResponse> {
    try {
      const response = await fetch(
        `${this.API_BASE_URL}/api/objectvalues/instance-view-by-refid/${refId}?viewName=${encodeURIComponent(objectName)}`,
        {
          method: 'GET',
          headers: this.API_HEADERS,
          signal: AbortSignal.timeout(this.REQUEST_TIMEOUT)
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.status} ${response.statusText}. ${errorText}`);
      }

      const result: ObjectInstanceResponse = await response.json();
      return result;
    } catch (error) {
      console.error('Error fetching object instance:', error);
      throw error instanceof Error ? error : new Error('Failed to fetch object instance');
    }
  }

  /**
   * Debug method to get available object names and IDs
   */
  public getAvailableObjects(): Array<{ name: string; id: string }> {
    try {
      const navigationService = NavigationService.getInstance();
      const navigationData = navigationService.getCurrentData();

      if (!navigationData || !navigationData.nodes) {
        return [];
      }

      const objects: Array<{ name: string; id: string }> = [];

      const extractObjects = (nodes: NavigationNode[]) => {
        nodes.forEach(node => {
          if (node.type === 'object') {
            if (node.name && node.id) {
              objects.push({ name: node.name, id: node.id });
            }
          }
          if (node.children) {
            extractObjects(node.children);
          }
        });
      };

      extractObjects(navigationData.nodes);
      return objects;
    } catch (error) {
      console.error('Error getting available objects:', error);
      return [];
    }
  }
}

// Create and export singleton instance
export const objectValuesService = ObjectValuesService.getInstance();
