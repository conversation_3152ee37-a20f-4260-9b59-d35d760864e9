using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetAllTenantContexts;

/// <summary>
/// Query to get all tenant contexts
/// </summary>
public class GetAllTenantContextsQuery : IRequest<Result<List<TenantContextDto>>>
{
    /// <summary>
    /// Whether to include inactive tenant contexts
    /// </summary>
    public bool IncludeInactive { get; set; } = false;

    /// <summary>
    /// Category filter
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Search term for name
    /// </summary>
    public string? SearchTerm { get; set; }
}
