/**
 * Subscription Creation API Service
 * Handles the three-step subscription creation process using the centralized HTTP client
 */

import { BaseApiService } from '../baseApiService';
import type { ApiResult } from '../types';
import { HttpClientFactory } from '../httpClient';
import { TENANT_ENDPOINTS, PRODUCT_ENDPOINTS, SUBSCRIPTION_ENDPOINTS } from '../../config/endpoints';
import { tenantApiService, CreateTenantRequest } from './tenantApiService';
import { subscriptionApiService, CreateSubscriptionRequest } from './subscriptionApiService';

// Product Structure interfaces
export interface ProductStructureDto {
  id?: string;
  name: string;
  type?: string;
  description?: string;
  version?: string;
  isActive?: boolean;
  metadata?: MetadataDto[];
  objects?: ObjectStructureDto[];
}

export interface MetadataDto {
  id?: string;
  name: string;
  type: string;
  description?: string;
  required?: boolean;
  isActive?: boolean;
  defaultValue?: string;
}

export interface ObjectStructureDto {
  id?: string;
  name: string;
  type?: string;
  description?: string;
  isActive?: boolean;
  metadata?: MetadataDto[];
  objects?: ObjectStructureDto[];
}

export interface ProductStructureCreationResult {
  success: boolean;
  message: string;
  totalProductsCreated: number;
  totalObjectsCreated: number;
  totalMetadataCreated: number;
  createdProducts: CreatedProductInfo[];
  errors: string[];
  warnings: string[];
  processingTimeMs: number;
}

export interface CreatedProductInfo {
  productId: string;
  name: string;
  wasCreated: boolean;
  objectsCreated: number;
  metadataFieldsCreated: number;
}

// Three-step creation interfaces
export interface SubscriptionCreationRequest {
  tenantData: {
    id: string;
    name: string;
    adminEmail: string;
  };
  templateData: {
    id: string;
    name: string;
    version: string;
    stage: string;
    templateJson: any;
  };
  subscriptionData: {
    subscriptionType: string;
    status: string;
    startDate: string;
    endDate?: string;
  };
}

export interface SubscriptionCreationResult {
  success: boolean;
  message: string;
  tenantId?: string;
  productId?: string;
  subscriptionId?: string;
  errors: string[];
  step: 'tenant' | 'product' | 'subscription' | 'complete';
  processingTimeMs: number;
}

/**
 * Subscription Creation API Service Class
 */
export class SubscriptionCreationApiService extends BaseApiService {
  constructor() {
    super(HttpClientFactory.subscriptionCreationClient, 'SubscriptionCreationAPI');
  }

  /**
   * Step 1: Create or validate tenant
   */
  async createTenant(tenantData: CreateTenantRequest): Promise<string> {
    try {
      this.log('createTenant', { tenantData });

      return await tenantApiService.createTenant(tenantData);
    } catch (error) {
      this.handleError('createTenant', error);
    }
  }

  /**
   * Step 2: Create product structure from template
   */
  async createProductStructure(templateJson: any, tenantId: string): Promise<string> {
    try {
      this.log('createProductStructure', { tenantId, templateJson });

      // Validate templateJson input
      if (!templateJson) {
        throw new Error('Template JSON is required but was not provided');
      }

      // Parse templateJson if it's a string, otherwise use as-is
      let request: any;
      try {
        request = typeof templateJson === 'string' ? JSON.parse(templateJson) : templateJson;
      } catch (parseError) {
        throw new Error(`Failed to parse template JSON: ${parseError instanceof Error ? parseError.message : 'Invalid JSON'}`);
      }

      // Validate parsed request
      if (!request || typeof request !== 'object') {
        throw new Error('Parsed template JSON is not a valid object');
      }

      const response = await this.post<ApiResult<ProductStructureCreationResult>>(
        PRODUCT_ENDPOINTS.PRODUCT_STRUCTURE,
        request,
        tenantId
      );

      const result = this.transformApiResult(response);

      if (!result.success) {
        const errorMessage = result.errors.join(', ') || 'Failed to create product structure';
        throw new Error(errorMessage);
      }

      const productId = result.createdProducts[0]?.productId;
      if (!productId) {
        throw new Error('No product ID returned from product structure creation');
      }

      this.log('createProductStructure_success', { productId });
      return productId;
    } catch (error) {
      this.handleError('createProductStructure', error);
    }
  }

  /**
   * Step 3: Create subscription
   */
  async createSubscription(subscriptionData: CreateSubscriptionRequest, tenantId: string): Promise<string> {
    try {
      this.log('createSubscription', { subscriptionData, tenantId });

      return await subscriptionApiService.createSubscription(subscriptionData, tenantId);
    } catch (error) {
      this.handleError('createSubscription', error);
    }
  }

  /**
   * Main method: Execute all three steps
   */
  async createCompleteSubscription(request: SubscriptionCreationRequest): Promise<SubscriptionCreationResult> {
    const startTime = Date.now();
    const result: SubscriptionCreationResult = {
      success: false,
      message: '',
      errors: [],
      step: 'tenant',
      processingTimeMs: 0
    };

    try {
      // Step 1: Use the selected tenant ID directly
      result.step = 'tenant';
      const selectedTenantId = request.tenantData.id;
      const tenantIdString = typeof selectedTenantId === 'string' ? selectedTenantId : String(selectedTenantId);
      result.tenantId = tenantIdString;

      this.log('step1_tenant', {
        selectedTenantId,
        tenantIdString,
        tenantName: request.tenantData.name
      });

      // Step 2: Create Product Structure
      result.step = 'product';
      this.log('step2_product_start', {
        templateData: request.templateData,
        templateJson: request.templateData?.templateJson
      });

      result.productId = await this.createProductStructure(
        request.templateData.templateJson,
        tenantIdString
      );

      // Step 3: Create Subscription
      result.step = 'subscription';

      // Convert dates to UTC format
      const startDateUTC = this.convertToUTCDate(request.subscriptionData.startDate);
      const endDateUTC = request.subscriptionData.endDate 
        ? this.convertToUTCDate(request.subscriptionData.endDate) 
        : undefined;

      this.log('step3_subscription_dates', {
        originalStartDate: request.subscriptionData.startDate,
        convertedStartDate: startDateUTC,
        originalEndDate: request.subscriptionData.endDate,
        convertedEndDate: endDateUTC
      });

      const subscriptionDto: CreateSubscriptionRequest = {
        productId: result.productId,
        subscriptionType: request.subscriptionData.subscriptionType,
        status: request.subscriptionData.status,
        startDate: startDateUTC,
        endDate: endDateUTC,
        autoRenew: false,
        pricingTier: this.derivePricingTier(request.subscriptionData.subscriptionType),
        version: request.templateData.version,
        templateJson: JSON.stringify(request.templateData.templateJson),
        templateDetails: {
          [request.templateData.id]: true
        },
        isActive: request.subscriptionData.status === 'active'
      };

      result.subscriptionId = await this.createSubscription(subscriptionDto, tenantIdString);

      // Success
      result.step = 'complete';
      result.success = true;
      result.message = 'Subscription created successfully';
      result.processingTimeMs = Date.now() - startTime;

      this.log('createCompleteSubscription_success', result);
      return result;

    } catch (error) {
      result.success = false;
      result.processingTimeMs = Date.now() - startTime;
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(errorMessage);
      
      // Set appropriate error message based on current step
      switch (result.step) {
        case 'tenant':
          result.message = `Failed to setup tenant: ${errorMessage}`;
          break;
        case 'product':
          result.message = `Failed to create product structure: ${errorMessage}`;
          break;
        case 'subscription':
          result.message = `Failed to create subscription: ${errorMessage}`;
          break;
        default:
          result.message = `Creation failed: ${errorMessage}`;
      }

      this.log('createCompleteSubscription_error', { step: result.step, error });
      return result;
    }
  }

  /**
   * Helper method to convert local date to UTC ISO format
   */
  private convertToUTCDate(localDateString: string): string {
    const localDate = new Date(localDateString + 'T00:00:00');
    return localDate.toISOString();
  }

  /**
   * Helper method to derive pricing tier from subscription type
   */
  private derivePricingTier(subscriptionType: string): string {
    switch (subscriptionType.toLowerCase()) {
      case 'basic':
        return 'starter';
      case 'premium':
        return 'professional';
      case 'enterprise':
        return 'enterprise';
      default:
        return 'standard';
    }
  }

  /**
   * Validate subscription creation request
   */
  validateCreationRequest(request: SubscriptionCreationRequest): string[] {
    const errors: string[] = [];

    // Validate tenant data
    if (!request.tenantData?.id) {
      errors.push('Tenant ID is required');
    }
    if (!request.tenantData?.name) {
      errors.push('Tenant name is required');
    }
    if (!request.tenantData?.adminEmail) {
      errors.push('Tenant admin email is required');
    }

    // Validate template data
    if (!request.templateData?.id) {
      errors.push('Template ID is required');
    }
    if (!request.templateData?.templateJson) {
      errors.push('Template JSON is required');
    }

    // Validate subscription data
    if (!request.subscriptionData?.subscriptionType) {
      errors.push('Subscription type is required');
    }
    if (!request.subscriptionData?.status) {
      errors.push('Subscription status is required');
    }
    if (!request.subscriptionData?.startDate) {
      errors.push('Start date is required');
    }

    return errors;
  }

  /**
   * Get creation progress steps
   */
  getCreationSteps(): Array<{ step: string; label: string; description: string }> {
    return [
      {
        step: 'tenant',
        label: 'Tenant Setup',
        description: 'Validate and setup tenant configuration'
      },
      {
        step: 'product',
        label: 'Product Structure',
        description: 'Create product structure from template'
      },
      {
        step: 'subscription',
        label: 'Subscription',
        description: 'Create subscription with configuration'
      },
      {
        step: 'complete',
        label: 'Complete',
        description: 'Subscription creation completed successfully'
      }
    ];
  }
}

// Export singleton instance
export const subscriptionCreationApiService = new SubscriptionCreationApiService();
