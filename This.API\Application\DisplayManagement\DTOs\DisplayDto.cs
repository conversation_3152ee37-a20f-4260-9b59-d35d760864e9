namespace Application.DisplayManagement.DTOs;

/// <summary>
/// Display response DTO
/// </summary>
public class DisplayDto
{
    /// <summary>
    /// Display ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Display name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the display
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Display name for UI
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default display for this type
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// URL route template for navigation
    /// </summary>
    public string? RouteTemplate { get; set; }

    /// <summary>
    /// Icon for the display
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Sort order for display ordering
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// Whether the display is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the display is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last modification timestamp
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified by user ID
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
