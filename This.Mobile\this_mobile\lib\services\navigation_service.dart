import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/nav_item.dart';
import '../models/navigation_response_model.dart';
import '../services/icon_resolver_service.dart';
import '../utils/constants.dart';

/// Service class for managing dynamic navigation data from API
class NavigationService {
  static const String _navigationEndpoint = '/navigation/menu';
  static const String _objectTypesEndpoint = '/metadata/object-types';
  static const String _categoriesEndpoint = '/comprehensive-entity/8c38b02f-4c1e-4416-a1bf-c705d1ac8761?onlyVisibleMetadata=true&onlyActiveMetadata=true&pageSize=100&pageNumber=1';

  /// Cache for navigation data
  static List<NavItem>? _cachedNavigationItems;
  static DateTime? _lastCacheTime;
  static const Duration _cacheTimeout = Duration(minutes: 30);

  /// Load navigation data dynamically from API
  static Future<List<NavItem>> loadNavigationData() async {
    // Check cache first
    // if (_isCacheValid()) {
    // return _cachedNavigationItems!;
    //}

    try {
      // Try to fetch from API
      final navigationData = await _fetchNavigationFromApi();
      if (navigationData.isNotEmpty) {
        _updateCache(navigationData);
        return navigationData;
      }
    } catch (e) {
      print('Failed to fetch navigation from API: $e');
      //return _cachedNavigationItems!;
      return [];
    }

    // Fallback to enhanced mock data with dynamic structure
    // final mockData = await _generateEnhancedMockData();
    // _updateCache(mockData);
    // return mockData;

    // Ensure a non-nullable list is always returned
    return [];
  }

  /// Check if cache is valid
  static bool _isCacheValid() {
    return _cachedNavigationItems != null && _lastCacheTime != null && DateTime.now().difference(_lastCacheTime!) < _cacheTimeout;
  }

  /// Update cache
  static void _updateCache(List<NavItem> items) {
    _cachedNavigationItems = items;
    _lastCacheTime = DateTime.now();
  }

  /// Clear cache
  static void clearCache() {
    _cachedNavigationItems = null;
    _lastCacheTime = null;
  }

  /// Fetch navigation data from API
  static Future<List<NavItem>> _fetchNavigationFromApi() async {
    final url = Uri.parse(
      'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net/api/comprehensive-entity/8c38b02f-4c1e-4416-a1bf-c705d1ac8761?onlyVisibleMetadata=true&onlyActiveMetadata=true&pageSize=100&pageNumber=1',
    );

    try {
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'tenant': 'kitchsync',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);

        if (jsonData['succeeded'] == true && jsonData['data'] != null && jsonData['data']['products'] != null) {
          final List<dynamic> productsJson = jsonData['data']['products'];

          final List<NavItem> allNavItems = [];

          for (final product in productsJson) {
            if (product['rootObjects'] != null) {
              final List<dynamic> rootObjects = product['rootObjects'];

              for (final root in rootObjects) {
                final List<NavItem> children = [];

                if (root['childObjects'] != null) {
                  for (final child in root['childObjects']) {
                    print('Processing child object: $child');
                    final childItem = NavItem(
                      id: child['id'] ?? '',
                      name: child['name'] ?? '',
                      objectType: child['objectType'] ?? child['name'],
                      iconData: IconResolverService.resolveIcon(iconName: child['iconName']),
                      parentId: root['id'],
                      hierarchyLevel: 2,
                      children: []
                    );
                    print('Created child NavItem: $childItem');
                    children.add(childItem);
                  }
                }

                final rootNavItem = NavItem(
                  id: root['id'] ?? '',
                  name: root['name'] ?? '',
                  objectType: root['objectType'] ?? root['name'],
                  iconData: IconResolverService.resolveIcon(iconName: root['iconName']),
                  hierarchyLevel: 1,
                  isExpanded: false,
                  children: children
                );

                allNavItems.add(rootNavItem);
                // Remove this line as it's an incomplete/invalid fragment
              }
            }
          }

          return allNavItems;
        } else {
          throw Exception('Navigation data not found in API response.');
        }
      } else {
        throw Exception('Failed to fetch navigation data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error during API call: $e');
    }
  }

  /// Convert DTO objects to NavItem objects
  static List<NavItem> _convertDtoToNavItems(List<NavigationItemDto> dtos) {
    return dtos.where((dto) => dto.isActive == true && dto.isVisible == true).map((dto) => _convertSingleDto(dto)).toList()..sort((a, b) => (a.sortOrder ?? 0).compareTo(b.sortOrder ?? 0));
  }

  /// Convert single DTO to NavItem
  static NavItem _convertSingleDto(NavigationItemDto dto) {
    final iconData = IconResolverService.resolveIcon(
      iconName: dto.iconName,
      iconType: dto.iconType,
      objectType: dto.objectType,
    );

    List<NavItem>? children;
    if (dto.children != null && dto.children!.isNotEmpty) {
      children = _convertDtoToNavItems(dto.children!);
    }

    return NavItem(
      id: dto.id,
      name: dto.name,
      displayName: dto.displayName,
      iconData: iconData,
      iconName: dto.iconName,
      iconType: dto.iconType,
      objectType: dto.objectType,
      parentId: dto.parentId,
      hierarchyLevel: dto.hierarchyLevel,
      hierarchyPath: dto.hierarchyPath,
      children: children,
      isActive: dto.isActive ?? true,
      isVisible: dto.isVisible ?? true,
      sortOrder: dto.sortOrder,
      permissions: dto.permissions,
      metadata: dto.metadata,
    );
  }

  /// Generate enhanced mock data that simulates API response structure
  static Future<List<NavItem>> _generateEnhancedMockData() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 1500));

    // This simulates what would come from an API
    final mockApiResponse = [
      {
        'id': 'inventory',
        'name': 'Inventory Management',
        'displayName': 'Inventory',
        'iconName': 'package',
        'iconType': 'lucide',
        'hierarchyLevel': 0,
        'hierarchyPath': '/inventory',
        'isActive': true,
        'isVisible': true,
        'sortOrder': 1,
        'children': [
          {
            'id': 'products',
            'name': 'Products',
            'displayName': 'Products',
            'iconName': 'box',
            'iconType': 'lucide',
            'objectType': 'product',
            'parentId': 'inventory',
            'hierarchyLevel': 1,
            'hierarchyPath': '/inventory/products',
            'isActive': true,
            'isVisible': true,
            'sortOrder': 1,
            'children': [
              {
                'id': 'electronics',
                'name': 'Electronics',
                'iconName': 'smartphone',
                'iconType': 'lucide',
                'objectType': 'electronics',
                'parentId': 'products',
                'hierarchyLevel': 2,
                'hierarchyPath': '/inventory/products/electronics',
                'isActive': true,
                'isVisible': true,
                'sortOrder': 1,
              },
              {
                'id': 'clothing',
                'name': 'Clothing',
                'iconName': 'shirt',
                'iconType': 'lucide',
                'objectType': 'clothing',
                'parentId': 'products',
                'hierarchyLevel': 2,
                'hierarchyPath': '/inventory/products/clothing',
                'isActive': true,
                'isVisible': true,
                'sortOrder': 2,
              },
              {
                'id': 'books',
                'name': 'Books',
                'iconName': 'book',
                'iconType': 'lucide',
                'objectType': 'books',
                'parentId': 'products',
                'hierarchyLevel': 2,
                'hierarchyPath': '/inventory/products/books',
                'isActive': true,
                'isVisible': true,
                'sortOrder': 3,
              },
            ],
          },
          {
            'id': 'categories',
            'name': 'Categories',
            'iconName': 'tags',
            'iconType': 'lucide',
            'objectType': 'category',
            'parentId': 'inventory',
            'hierarchyLevel': 1,
            'hierarchyPath': '/inventory/categories',
            'isActive': true,
            'isVisible': true,
            'sortOrder': 2,
          },
          {
            'id': 'suppliers',
            'name': 'Suppliers',
            'iconName': 'truck',
            'iconType': 'lucide',
            'objectType': 'supplier',
            'parentId': 'inventory',
            'hierarchyLevel': 1,
            'hierarchyPath': '/inventory/suppliers',
            'isActive': true,
            'isVisible': true,
            'sortOrder': 3,
          },
        ],
      },
      {
        'id': 'settings',
        'name': 'Settings',
        'iconName': 'settings',
        'iconType': 'lucide',
        'hierarchyLevel': 0,
        'hierarchyPath': '/settings',
        'isActive': true,
        'isVisible': true,
        'sortOrder': 5,
        'children': [
          {
            'id': 'users',
            'name': 'User Management',
            'iconName': 'users',
            'iconType': 'lucide',
            'objectType': 'user',
            'parentId': 'settings',
            'hierarchyLevel': 1,
            'hierarchyPath': '/settings/users',
            'isActive': true,
            'isVisible': true,
            'sortOrder': 1,
          },
          {
            'id': 'permissions',
            'name': 'Permissions',
            'iconName': 'shield',
            'iconType': 'lucide',
            'objectType': 'permission',
            'parentId': 'settings',
            'hierarchyLevel': 1,
            'hierarchyPath': '/settings/permissions',
            'isActive': true,
            'isVisible': true,
            'sortOrder': 2,
          },
          {
            'id': 'system-config',
            'name': 'System Configuration',
            'iconName': 'cog',
            'iconType': 'lucide',
            'objectType': 'config',
            'parentId': 'settings',
            'hierarchyLevel': 1,
            'hierarchyPath': '/settings/system-config',
            'isActive': true,
            'isVisible': true,
            'sortOrder': 3,
          },
        ],
      },
    ];

    // Convert mock API response to NavigationItemDto objects
    final dtos = mockApiResponse.map((json) => NavigationItemDto.fromJson(json)).toList();

    return _convertDtoToNavItems(dtos);
  }

  /// Fetch object types from API for dynamic navigation building
  static Future<List<ObjectTypeDto>> fetchObjectTypes() async {
    try {
      final url = Uri.parse('${AppConstants.apiBaseUrl}$_objectTypesEndpoint');

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(AppConstants.apiTimeout);

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final metadataResponse = ObjectMetadataResponse.fromJson(jsonData);

        return metadataResponse.objectTypes ?? [];
      }
    } catch (e) {
      print('Failed to fetch object types: $e');
    }

    return [];
  }

  /// Build navigation dynamically from object types
  static Future<List<NavItem>> buildNavigationFromObjectTypes(
    List<ObjectTypeDto> objectTypes,
  ) async {
    final Map<String, List<ObjectTypeDto>> categorizedTypes = {};

    // Group object types by category
    for (final objectType in objectTypes) {
      final category = objectType.category ?? 'Other';
      categorizedTypes.putIfAbsent(category, () => []).add(objectType);
    }

    final List<NavItem> navigationItems = [];
    int sortOrder = 1;

    // Create navigation items for each category
    for (final entry in categorizedTypes.entries) {
      final category = entry.key;
      final types = entry.value;

      final categoryIcon = IconResolverService.resolveIcon(
        category: category,
        iconName: category.toLowerCase(),
        iconType: 'lucide',
      );

      final children = types.map((type) {
        final icon = IconResolverService.resolveIcon(
          iconName: type.iconName,
          iconType: 'lucide',
          objectType: type.name,
        );

        return NavItem(
          id: type.id,
          name: type.displayName ?? type.name,
          displayName: type.displayName,
          iconData: icon,
          iconName: type.iconName,
          iconType: 'lucide',
          objectType: type.name,
          parentId: category.toLowerCase(),
          hierarchyLevel: 1,
          hierarchyPath: '/${category.toLowerCase()}/${type.name}',
          isActive: type.isActive ?? true,
          isVisible: true,
          sortOrder: sortOrder++,
          permissions: type.permissions,
        );
      }).toList();

      navigationItems.add(
        NavItem(
          id: category.toLowerCase(),
          name: category,
          displayName: category,
          iconData: categoryIcon,
          iconName: category.toLowerCase(),
          iconType: 'lucide',
          hierarchyLevel: 0,
          hierarchyPath: '/${category.toLowerCase()}',
          children: children,
          isActive: true,
          isVisible: true,
          sortOrder: sortOrder++,
        ),
      );
    }

    return navigationItems;
  }

  /// Find a navigation item by ID
  static NavItem? findNavItemById(List<NavItem> items, String id) {
    for (final item in items) {
      if (item.id == id) {
        return item;
      }
      if (item.hasChildren) {
        final found = findNavItemById(item.children!, id);
        if (found != null) return found;
      }
    }
    return null;
  }

  /// Get all navigation items with object types (for API calls)
  static List<NavItem> getItemsWithObjectTypes(List<NavItem> items) {
    final List<NavItem> result = [];

    for (final item in items) {
      if (item.objectType != null) {
        result.add(item);
      }
      if (item.hasChildren) {
        result.addAll(getItemsWithObjectTypes(item.children!));
      }
    }

    return result;
  }

  /// Get the path to a navigation item (for breadcrumbs)
  static List<NavItem> getPathToItem(List<NavItem> items, String targetId) {
    final List<NavItem> path = [];

    bool findPath(List<NavItem> currentItems, String id) {
      for (final item in currentItems) {
        path.add(item);

        if (item.id == id) {
          return true;
        }

        if (item.hasChildren && findPath(item.children!, id)) {
          return true;
        }

        path.removeLast();
      }
      return false;
    }

    findPath(items, targetId);
    return path;
  }

  /// Get icon for object type (legacy method - use IconResolverService instead)
  static IconData getIconForObjectType(String? objectType) {
    return IconResolverService.resolveIcon(objectType: objectType);
  }
}



