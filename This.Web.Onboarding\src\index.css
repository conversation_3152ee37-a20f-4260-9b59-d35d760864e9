@import url('https://fonts.googleapis.com/css2?family=Lexend+Deca:wght@300;400;500;600;700&family=DM+Sans:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Green Theme Colors - Based on provided SCSS variables */
    --light-green-100: #e5f5f2;
    --light-green-200: #c7e8e1;
    --light-green-300: #d3f3ec;
    --light-green-400: #76ccb9;
    --light-green-500: #5dc3ad;
    --light-green-600: #38dab8;
    --dark-green: #50bea7e5;
    --dark-green-100: #28544B;
    --accent-green: #50bea7;
    --accent-green-40: #b3dfd6;
    --accent-green-60: #50bea7b3;
    --accent-green-100: #50bfa8;
    --accent-green-200: #348d7a;
    --accent-green-300: #399b86;
  }

  html {
    font-family: 'Lexend Deca', 'DM Sans', sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Lexend Deca', sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-800;
  }

  /* Hide scrollbar for webkit browsers (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for Firefox */
  html {
    scrollbar-width: none;
  }

  /* Hide scrollbar for IE and Edge */
  body {
    -ms-overflow-style: none;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200 flex items-center justify-center gap-2;
  }

  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-700 hover:bg-gray-300;
  }

  .btn-danger {
    @apply bg-red-500 text-white hover:bg-red-600;
  }

  .input {
    @apply w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500;
  }

  .toggle {
    @apply relative inline-flex h-6 w-11 items-center rounded-full;
  }

  .toggle-bg {
    @apply bg-gray-200 absolute mx-auto w-11 h-6 rounded-full transition-colors ease-in-out duration-200;
  }

  .toggle-active {
    @apply bg-primary-500;
  }

  .toggle-circle {
    @apply absolute left-0.5 inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200;
  }

  .toggle-active .toggle-circle {
    @apply translate-x-5;
  }

  .table-container {
    @apply w-full overflow-x-auto shadow-sm rounded-lg;
  }

  .data-table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .data-table-header {
    @apply bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider;
  }

  .data-table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm;
  }

  .data-table-row {
    @apply hover:bg-gray-50;
  }

  .data-table-row:nth-child(even) {
    @apply bg-gray-50;
  }

  .pagination-btn {
    @apply px-3 py-1 border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 rounded-md;
  }

  .pagination-btn-active {
    @apply bg-primary-500 text-white border-primary-500 hover:bg-primary-600;
  }

  .card {
    @apply bg-white p-6 rounded-lg shadow-sm border border-gray-100;
  }

  /* Onboarding Step Enhancements */
  .step-completed {
    background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.primary.700'));
    box-shadow: 0 10px 15px -3px rgba(80, 190, 167, 0.2), 0 4px 6px -2px rgba(80, 190, 167, 0.1);
  }

  .step-current {
    background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.primary.600'));
    box-shadow: 0 8px 12px -2px rgba(80, 190, 167, 0.25), 0 4px 6px -1px rgba(80, 190, 167, 0.15);
    animation: pulse-green 2s infinite;
  }

  @keyframes pulse-green {
    0%, 100% {
      box-shadow: 0 8px 12px -2px rgba(80, 190, 167, 0.25), 0 4px 6px -1px rgba(80, 190, 167, 0.15), 0 0 0 0 rgba(80, 190, 167, 0.7);
    }
    50% {
      box-shadow: 0 8px 12px -2px rgba(80, 190, 167, 0.25), 0 4px 6px -1px rgba(80, 190, 167, 0.15), 0 0 0 8px rgba(80, 190, 167, 0);
    }
  }
}