import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { NotificationProvider } from './contexts/NotificationContext';
import AppLayout from './features/layout/AppLayout';
import { SettingsView } from './features/settings/SettingsView';
import { ObjectDetailPage } from './features/object/ObjectDetailPage';
import { UpsertObject } from './features/object/UpsertObject';
import { UsersPage } from './features/users/UsersPage';
import { RolesPage } from './features/roles/RolesPage';
import OnboardingWrapper from './features/onboarding/OnboardingWrapper';
import { defaultRoute } from './config/routes';

function App() {
  return (
    <ThemeProvider defaultTheme="professional">
      <NotificationProvider>
        <div className="min-h-screen bg-background text-foreground">
          {/* Enhanced Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 pointer-events-none" />

          {/* Main Application with Routing */}
          <div className="relative h-screen">
            <Router>
              <Routes>
                {/* Onboarding route - standalone without AppLayout */}
                <Route path="" element={<OnboardingWrapper />} />
                <Route path="/onboarding" element={<OnboardingWrapper />} />

                {/* Main application routes with AppLayout */}
                <Route path="/*" element={
                  <AppLayout>
                    <Routes>
                      <Route path="/settings" element={<SettingsView />} />
                      <Route path="/users" element={<UsersPage />} />
                      <Route path="/roles" element={<RolesPage />} />
                      <Route path="/object/:objectName" element={<ObjectDetailPage />} />
                      <Route path="/object/:objectName/upsert" element={<UpsertObject />} />
                      <Route path="/" element={<Navigate to={defaultRoute} replace />} />
                      <Route path="*" element={<Navigate to={defaultRoute} replace />} />
                    </Routes>
                  </AppLayout>
                } />
              </Routes>
            </Router>
          </div>
        </div>
      </NotificationProvider>
    </ThemeProvider>
  );
}

export default App
