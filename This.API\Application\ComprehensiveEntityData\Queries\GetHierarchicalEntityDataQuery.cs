using Application.ComprehensiveEntityData.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ComprehensiveEntityData.Queries;

/// <summary>
/// UNIFIED: Query for hierarchical entity data with proper parent-child nesting and consolidated metadata
/// </summary>
public class GetHierarchicalEntityDataQuery : IRequest<Result<UnifiedHierarchicalEntityDataResponseDto>>
{
    /// <summary>
    /// Product ID filter (optional)
    /// </summary>
    public Guid? ProductId { get; set; }

    /// <summary>
    /// Feature ID filter (optional)
    /// </summary>
    public Guid? FeatureId { get; set; }

    /// <summary>
    /// Search term for names and descriptions (optional)
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by active status (optional)
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Include only visible metadata (default: true)
    /// </summary>
    public bool OnlyVisibleMetadata { get; set; } = true;

    /// <summary>
    /// Include only active metadata (default: true)
    /// </summary>
    public bool OnlyActiveMetadata { get; set; } = true;

    /// <summary>
    /// Page number for pagination (default: 1)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size for pagination (default: 50)
    /// </summary>
    public int PageSize { get; set; } = 50;
}
