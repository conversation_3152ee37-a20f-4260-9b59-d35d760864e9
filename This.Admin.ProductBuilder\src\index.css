/* Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom styles */
:root {
  --primary: #0d6efd;
  --secondary: #6c757d;
  --success: #198754;
  --info: #0dcaf0;
  --warning: #ffc107;
  --danger: #dc3545;
  --light: #f8f9fa;
  --dark: #212529;

  /* Modern gradient colors */
  --gradient-start: #667eea;
  --gradient-middle: #764ba2;
  --gradient-end: #f093fb;

  /* Modern card colors */
  --card-bg: #ffffff;
  --card-border: rgba(0, 0, 0, 0.08);
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Base styles */
html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  min-height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background-color: #f8f9fa;
  color: #212529;
  -webkit-tap-highlight-color: transparent;
}

/* Layout */
.main-container {
  min-height: calc(100vh - 120px);
}

/* Fixed sidebar layout */
.fixed-sidebar-layout {
  height: 100vh;
  overflow: hidden;
}

.sidebar-fixed {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.main-content-scrollable {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* Table responsive scrolling */
.table-responsive {
  overflow-x: auto;
  overflow-y: visible;
}

/* Ensure tables don't break layout */
.modern-card .table-responsive {
  margin: 0;
  border: none;
}

/* Full width layout optimizations */
.full-width-layout {
  max-width: none !important;
  width: 100% !important;
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

/* Reduce sidebar gap */
.layout-main-content {
  margin-left: -8px;
}

/* Optimize content spacing */
.content-wrapper {
  padding: 0.25rem 1rem;
  width: 100%;
  max-width: none;
}

/* Minimize vertical gaps */
.minimize-gaps {
  margin-bottom: 0.25rem !important;
}

.minimize-gaps:last-child {
  margin-bottom: 0 !important;
}

/* Compact layout utilities */
.compact-spacing {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.compact-margin {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

/* Modern gradient header */
.gradient-header {
  background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-middle) 50%, var(--gradient-end) 100%);
  position: relative;
  overflow: hidden;
}

.gradient-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.gradient-header .container {
  position: relative;
  z-index: 1;
}

/* Modern card components */
.modern-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  overflow: hidden;
  width: 100%;
  margin: 0;
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--card-shadow-hover);
  border-color: rgba(102, 126, 234, 0.2);
}

.modern-card.no-hover:hover {
  transform: none;
  box-shadow: var(--card-shadow);
  border-color: var(--card-border);
}

.modern-card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid var(--card-border);
  padding: 1.25rem 1.5rem;
}

.modern-card-body {
  padding: 1.5rem;
}

/* Stats cards */
.stats-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-middle), var(--gradient-end));
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.stats-card .card-body {
  padding: 1.5rem;
  text-align: center;
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-middle));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Custom components */
.custom-card {
  transition: transform 0.2s ease-in-out;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.375rem;
}

.custom-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white;
    color: black;
    font-size: 12pt;
  }
  
  .print-container {
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }
}
