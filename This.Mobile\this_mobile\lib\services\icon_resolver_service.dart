import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

/// Service for resolving icons dynamically from API responses
class IconResolverService {
  /// Default fallback icon
  static const IconData defaultIcon = Icons.folder;

  /// Resolve icon from API response
  static IconData resolveIcon({
    String? iconName,
    String? iconType,
    String? objectType,
    String? category,
  }) {
    // Try to resolve by icon name and type first
    if (iconName != null && iconType != null) {
      final icon = _resolveByNameAndType(iconName, iconType);
      if (icon != null) return icon;
    }

    // Fallback to object type
    if (objectType != null) {
      final icon = _resolveByObjectType(objectType);
      if (icon != null) return icon;
    }

    // Fallback to category
    if (category != null) {
      final icon = _resolveByCategory(category);
      if (icon != null) return icon;
    }

    return defaultIcon;
  }

  /// Resolve icon by name and type
  static IconData? _resolveByNameAndType(String iconName, String iconType) {
    switch (iconType.toLowerCase()) {
      case 'lucide':
        return _resolveLucideIcon(iconName);
      case 'material':
        return _resolveMaterialIcon(iconName);
      case 'custom':
        return _resolveCustomIcon(iconName);
      default:
        return null;
    }
  }

  /// Resolve Lucide icon by name
  static IconData? _resolveLucideIcon(String iconName) {
    // Map of common icon names to Lucide icons
    final lucideIconMap = <String, IconData>{
      // Navigation & UI
      'menu': LucideIcons.menu,
      'home': Icons.home,
      'search': LucideIcons.search,
      'settings': LucideIcons.settings,
      'user': LucideIcons.user,
      'users': LucideIcons.users,
      'bell': LucideIcons.bell,
      'mail': LucideIcons.mail,

      // Business & Commerce
      'package': LucideIcons.package,
      'box': LucideIcons.box,
      'shopping-cart': LucideIcons.shoppingCart,
      'shopping-bag': LucideIcons.shoppingBag,
      'truck': LucideIcons.truck,
      'warehouse': LucideIcons.warehouse,
      'map-pin': LucideIcons.mapPin,
      'receipt': LucideIcons.receipt,
      'credit-card': LucideIcons.creditCard,
      'dollar-sign': LucideIcons.dollarSign,

      // Technology & Devices
      'smartphone': LucideIcons.smartphone,
      'laptop': LucideIcons.laptop,
      'monitor': LucideIcons.monitor,
      'tablet': LucideIcons.tablet,
      'headphones': LucideIcons.headphones,
      'camera': LucideIcons.camera,
      'wifi': LucideIcons.wifi,
      'bluetooth': LucideIcons.bluetooth,

      // Files & Documents
      'file': LucideIcons.file,
      'file-text': LucideIcons.fileText,
      'folder': LucideIcons.folder,
      'book': LucideIcons.book,
      'bookmark': LucideIcons.bookmark,
      'archive': LucideIcons.archive,
      'download': LucideIcons.download,
      'upload': LucideIcons.upload,

      // Actions & Controls
      'plus': LucideIcons.plus,
      'minus': LucideIcons.minus,
      'edit': Icons.edit,
      'trash': LucideIcons.trash,
      'save': LucideIcons.save,
      'copy': LucideIcons.copy,
      'share': LucideIcons.share,
      'refresh': LucideIcons.refreshCw,

      // Navigation & Movement
      'arrow-left': LucideIcons.arrowLeft,
      'arrow-right': LucideIcons.arrowRight,
      'arrow-up': LucideIcons.arrowUp,
      'arrow-down': LucideIcons.arrowDown,
      'chevron-left': LucideIcons.chevronLeft,
      'chevron-right': LucideIcons.chevronRight,
      'chevron-up': LucideIcons.chevronUp,
      'chevron-down': LucideIcons.chevronDown,

      // Status & Feedback
      'check': LucideIcons.check,
      'x': LucideIcons.x,
      'alert-circle': Icons.error_outline,
      'info': LucideIcons.info,
      'help-circle': Icons.help_outline,
      'star': LucideIcons.star,
      'heart': LucideIcons.heart,
      'thumbs-up': LucideIcons.thumbsUp,

      // Charts & Analytics
      'trending-up': LucideIcons.trendingUp,
      'trending-down': LucideIcons.trendingDown,
      'activity': LucideIcons.activity,
      'bar-chart': Icons.bar_chart,
      'line-chart': Icons.show_chart,

      // Security & Access
      'lock': LucideIcons.lock,
      'unlock': Icons.lock_open,
      'key': LucideIcons.key,
      'shield': LucideIcons.shield,
      'eye': LucideIcons.eye,
      'eye-off': LucideIcons.eyeOff,

      // Time & Calendar
      'clock': LucideIcons.clock,
      'calendar': LucideIcons.calendar,
      'calendar-days': LucideIcons.calendarDays,

      // Communication
      'phone': LucideIcons.phone,
      'message-circle': LucideIcons.messageCircle,
      'message-square': LucideIcons.messageSquare,
      'video': LucideIcons.video,

      // Categories & Organization
      'tag': LucideIcons.tag,
      'tags': LucideIcons.tags,
      'flag': LucideIcons.flag,

      // Clothing & Fashion
      'shirt': LucideIcons.shirt,

      // Tools & Utilities
      'tool': LucideIcons.wrench,
      'wrench': LucideIcons.wrench,
      'hammer': LucideIcons.hammer,
      'scissors': LucideIcons.scissors,
    };

    return lucideIconMap[iconName.toLowerCase()];
  }

  /// Resolve Material icon by name
  static IconData? _resolveMaterialIcon(String iconName) {
    // Map of common icon names to Material icons
    final materialIconMap = <String, IconData>{
      // Basic
      'home': Icons.home,
      'menu': Icons.menu,
      'search': Icons.search,
      'settings': Icons.settings,
      'person': Icons.person,
      'people': Icons.people,
      'notifications': Icons.notifications,
      'mail': Icons.mail,

      // Business
      'inventory': Icons.inventory,
      'shopping_cart': Icons.shopping_cart,
      'shopping_bag': Icons.shopping_bag,
      'local_shipping': Icons.local_shipping,
      'warehouse': Icons.warehouse,
      'location_on': Icons.location_on,
      'receipt': Icons.receipt,
      'payment': Icons.payment,
      'attach_money': Icons.attach_money,

      // Charts
      'bar_chart': Icons.bar_chart,
      'pie_chart': Icons.pie_chart,
      'line_chart': Icons.show_chart,
      'trending_up': Icons.trending_up,
      'trending_down': Icons.trending_down,
      'analytics': Icons.analytics,

      // Actions
      'add': Icons.add,
      'remove': Icons.remove,
      'edit': Icons.edit,
      'delete': Icons.delete,
      'save': Icons.save,
      'copy': Icons.copy,
      'share': Icons.share,
      'refresh': Icons.refresh,
      'rotate_left': Icons.rotate_left,
      'rotate_right': Icons.rotate_right,

      // Navigation
      'arrow_back': Icons.arrow_back,
      'arrow_forward': Icons.arrow_forward,
      'arrow_upward': Icons.arrow_upward,
      'arrow_downward': Icons.arrow_downward,
      'expand_more': Icons.expand_more,
      'expand_less': Icons.expand_less,
      'chevron_left': Icons.chevron_left,
      'chevron_right': Icons.chevron_right,

      // Status
      'check': Icons.check,
      'close': Icons.close,
      'error': Icons.error,
      'warning': Icons.warning,
      'info': Icons.info,
      'help': Icons.help,
      'star': Icons.star,
      'favorite': Icons.favorite,
      'thumb_up': Icons.thumb_up,

      // Files
      'folder': Icons.folder,
      'file_copy': Icons.file_copy,
      'description': Icons.description,
      'book': Icons.book,
      'library_books': Icons.library_books,
      'archive': Icons.archive,
      'download': Icons.download,
      'upload': Icons.upload,

      // Security
      'lock': Icons.lock,
      'lock_open': Icons.lock_open,
      'key': Icons.key,
      'security': Icons.security,
      'visibility': Icons.visibility,
      'visibility_off': Icons.visibility_off,

      // Time
      'access_time': Icons.access_time,
      'calendar_today': Icons.calendar_today,
      'event': Icons.event,
      'schedule': Icons.schedule,

      // Communication
      'phone': Icons.phone,
      'message': Icons.message,
      'chat': Icons.chat,
      'video_call': Icons.video_call,

      // Categories
      'label': Icons.label,
      'local_offer': Icons.local_offer,
      'bookmark': Icons.bookmark,
      'flag': Icons.flag,

      // Devices
      'phone_android': Icons.phone_android,
      'phone_iphone': Icons.phone_iphone,
      'laptop': Icons.laptop,
      'desktop_windows': Icons.desktop_windows,
      'tablet': Icons.tablet,
      'headset': Icons.headset,
      'camera': Icons.camera,
      'wifi': Icons.wifi,
      'bluetooth': Icons.bluetooth,

      // More specific
      'inbox': Icons.inbox,
      'outbox': Icons.outbox,
      'dashboard': Icons.dashboard,
      'view_list': Icons.view_list,
      'view_module': Icons.view_module,
      'grid_view': Icons.grid_view,
    };

    return materialIconMap[iconName.toLowerCase()];
  }

  /// Resolve custom icon by name (placeholder for custom icon implementation)
  static IconData? _resolveCustomIcon(String iconName) {
    // This could be extended to load custom icons from assets or fonts
    // For now, return null to fallback to other methods
    return null;
  }

  /// Resolve icon by object type
  static IconData? _resolveByObjectType(String objectType) {
    final objectTypeIconMap = <String, IconData>{
      // Products & Inventory
      'product': LucideIcons.box,
      'item': LucideIcons.package,
      'inventory': Icons.inventory,
      'stock': Icons.bar_chart,
      'category': LucideIcons.tags,
      'subcategory': LucideIcons.tag,

      // Electronics
      'electronics': LucideIcons.smartphone,
      'phone': LucideIcons.smartphone,
      'laptop': LucideIcons.laptop,
      'computer': LucideIcons.monitor,
      'tablet': LucideIcons.tablet,
      'headphones': LucideIcons.headphones,
      'camera': LucideIcons.camera,

      // Clothing & Fashion
      'clothing': LucideIcons.shirt,
      'apparel': LucideIcons.shirt,
      'fashion': LucideIcons.shirt,

      // Books & Media
      'book': LucideIcons.book,
      'books': LucideIcons.book,
      'media': LucideIcons.fileText,
      'document': LucideIcons.fileText,

      // Business Operations
      'supplier': LucideIcons.truck,
      'vendor': LucideIcons.truck,
      'customer': LucideIcons.user,
      'client': LucideIcons.users,
      'order': LucideIcons.shoppingCart,
      'purchase-order': LucideIcons.shoppingBag,
      'sales-order': LucideIcons.receipt,
      'return': Icons.rotate_left,
      'refund': Icons.rotate_left,

      // Locations & Logistics
      'location': LucideIcons.mapPin,
      'warehouse': LucideIcons.warehouse,
      'store': LucideIcons.warehouse,
      'branch': LucideIcons.mapPin,
      'movement': LucideIcons.arrowRightLeft,
      'transfer': LucideIcons.arrowRightLeft,
      'shipment': LucideIcons.truck,

      // Reports & Analytics
      'report': LucideIcons.fileText,
      'analytics': LucideIcons.activity,
      'dashboard': Icons.dashboard,
      'chart': Icons.bar_chart,
      'graph': Icons.show_chart,
      'metric': LucideIcons.activity,

      // System & Configuration
      'user': LucideIcons.user,
      'users': LucideIcons.users,
      'role': LucideIcons.shield,
      'permission': LucideIcons.shield,
      'config': LucideIcons.settings,
      'setting': LucideIcons.settings,
      'system': LucideIcons.settings,
      'admin': LucideIcons.shield,

      // Financial
      'payment': Icons.payment,
      'invoice': LucideIcons.receipt,
      'billing': LucideIcons.receipt,
      'finance': LucideIcons.dollarSign,
      'accounting': LucideIcons.dollarSign,
      'budget': Icons.pie_chart,

      // Quality & Compliance
      'quality': LucideIcons.shield,
      'compliance': LucideIcons.shield,
      'audit': LucideIcons.shield,
      'inspection': LucideIcons.eye,

      // Communication
      'notification': LucideIcons.bell,
      'message': LucideIcons.messageCircle,
      'email': LucideIcons.mail,
      'alert': Icons.error_outline,
    };

    return objectTypeIconMap[objectType.toLowerCase()];
  }

  /// Resolve icon by category
  static IconData? _resolveByCategory(String category) {
    final categoryIconMap = <String, IconData>{
      'inventory': LucideIcons.package,
      'products': LucideIcons.box,
      'sales': LucideIcons.trendingUp,
      'purchasing': LucideIcons.shoppingBag,
      'logistics': LucideIcons.truck,
      'warehouse': LucideIcons.warehouse,
      'reports': LucideIcons.fileText,
      'analytics': LucideIcons.activity,
      'finance': LucideIcons.dollarSign,
      'administration': LucideIcons.settings,
      'users': LucideIcons.users,
      'security': LucideIcons.shield,
      'system': LucideIcons.settings,
      'configuration': LucideIcons.settings,
      'communication': LucideIcons.messageCircle,
      'notifications': LucideIcons.bell,
    };

    return categoryIconMap[category.toLowerCase()];
  }

  /// Get all available icon names for a specific type
  static List<String> getAvailableIconNames(String iconType) {
    switch (iconType.toLowerCase()) {
      case 'lucide':
        return _getLucideIconNames();
      case 'material':
        return _getMaterialIconNames();
      default:
        return [];
    }
  }

  static List<String> _getLucideIconNames() {
    return [
      'menu', 'home', 'search', 'settings', 'user', 'users', 'bell', 'mail',
      'package', 'box', 'shopping-cart', 'shopping-bag', 'truck', 'warehouse',
      'map-pin', 'receipt', 'credit-card', 'dollar-sign', 'smartphone', 'laptop',
      'monitor', 'tablet', 'headphones', 'camera', 'wifi', 'bluetooth', 'file',
      'file-text', 'folder', 'book', 'bookmark', 'archive', 'download', 'upload',
      'plus', 'minus', 'edit', 'trash', 'save', 'copy', 'share', 'refresh',
      'arrow-left', 'arrow-right', 'arrow-up', 'arrow-down', 'chevron-left',
      'chevron-right', 'chevron-up', 'chevron-down', 'check', 'x', 'alert-circle',
      'info', 'help-circle', 'star', 'heart', 'thumbs-up', 'trending-up',
      'trending-down', 'activity', 'bar-chart', 'line-chart', 'lock', 'unlock',
      'key', 'shield', 'eye', 'eye-off', 'clock', 'calendar', 'calendar-days',
      'phone', 'message-circle', 'message-square', 'video', 'tag', 'tags',
      'flag', 'shirt', 'tool', 'wrench', 'hammer', 'scissors',
    ];
  }

  static List<String> _getMaterialIconNames() {
    return [
      'home', 'menu', 'search', 'settings', 'person', 'people', 'notifications',
      'mail', 'inventory', 'shopping_cart', 'shopping_bag', 'local_shipping',
      'warehouse', 'location_on', 'receipt', 'payment', 'attach_money',
      'bar_chart', 'pie_chart', 'line_chart', 'trending_up', 'trending_down',
      'analytics', 'add', 'remove', 'edit', 'delete', 'save', 'copy', 'share',
      'refresh', 'rotate_left', 'rotate_right', 'arrow_back', 'arrow_forward',
      'arrow_upward', 'arrow_downward', 'expand_more', 'expand_less',
      'chevron_left', 'chevron_right', 'check', 'close', 'error', 'warning',
      'info', 'help', 'star', 'favorite', 'thumb_up', 'folder', 'file_copy',
      'description', 'book', 'library_books', 'archive', 'download', 'upload',
      'lock', 'lock_open', 'key', 'security', 'visibility', 'visibility_off',
      'access_time', 'calendar_today', 'event', 'schedule', 'phone', 'message',
      'chat', 'video_call', 'label', 'local_offer', 'bookmark', 'flag',
      'phone_android', 'phone_iphone', 'laptop', 'desktop_windows', 'tablet',
      'headset', 'camera', 'wifi', 'bluetooth', 'inbox', 'outbox', 'dashboard',
      'view_list', 'view_module', 'grid_view',
    ];
  }
}
