import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Button, Form, Row, Col, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { SearchableDropdown } from '../atoms/SearchableDropdown';
import type { DropdownOption } from '../atoms/SearchableDropdown';
import { templateService, TemplateUtils } from '../../services/templateService';
import { subscriptionCreationService } from '../../services/api/subscriptionCreationService';
import { tenantCreationService } from '../../services/tenantCreationService';
import { subscriptionApiService } from '../../services/api/subscriptionApiService';
import type { UpdateSubscriptionRequest } from '../../services/api/subscriptionApiService';
import type { CreateProductStructureRequest } from '../../services/api/subscriptionCreationService';

// Utility function to convert date string to UTC ISO format
const convertToUtcIsoString = (dateString: string): string => {
  if (!dateString) return '';

  // Create date object from YYYY-MM-DD format and set to start of day in UTC
  const date = new Date(dateString + 'T00:00:00.000Z');
  return date.toISOString();
};

// Local interfaces to match the new API service
interface SubscriptionCreationResult {
  success: boolean;
  tenantId: string;
  productId: string;
  subscriptionId: string;
  subscription: any;
  steps: {
    tenantCreated: boolean;
    productStructureCreated: boolean;
    subscriptionCreated: boolean;
  };
  errors?: string[];
}



// Subscription interface (matching the existing one)
interface Subscription {
  id: string;
  name: string;
  type: 'basic' | 'premium' | 'enterprise';
  status: 'active' | 'inactive' | 'expired';
  startDate: string;
  endDate: string;
  userCount: number;
  price: number;
}

// Form data interface
interface SubscriptionFormData {
  tenantId: string;
  templateId: string;
  name: string;
  type: 'basic' | 'premium' | 'enterprise';
  status: 'active' | 'inactive';
  startDate: string;
  endDate: string;
}

// Local TenantResponse interface to match API data
interface TenantResponse {
  id: string;
  name: string;
  connectionString: string;
  readReplicaConnectionString: string | null;
  adminEmail: string;
  isActive: boolean;
  validUpto: string;
  issuer: string | null;
}

// Template interface for dropdown - now includes full template data
interface TemplateOption {
  id: string;
  name: string;
  version: string;
  stage: string;
  displayText: string;
  templateJson: any; // Include the template JSON data
}

interface AddSubscriptionModalProps {
  show: boolean;
  onHide: () => void;
  onSubscriptionAdded: (subscription: Subscription) => void;
  mode?: 'create' | 'edit';
  editSubscription?: any; // The subscription to edit
  onSubscriptionUpdated?: (subscription: Subscription) => void;
}

export const AddSubscriptionModal: React.FC<AddSubscriptionModalProps> = ({
  show,
  onHide,
  onSubscriptionAdded,
  mode = 'create',
  editSubscription,
  onSubscriptionUpdated
}) => {
  const [formData, setFormData] = useState<SubscriptionFormData>({
    tenantId: '',
    templateId: '',
    name: '',
    type: 'basic',
    status: 'active',
    startDate: '',
    endDate: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Tenant state
  const [tenants, setTenants] = useState<TenantResponse[]>([]);
  const [isLoadingTenants, setIsLoadingTenants] = useState(false);
  const [tenantError, setTenantError] = useState<string | null>(null);

  // Template state
  const [templates, setTemplates] = useState<TemplateOption[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const [templateError, setTemplateError] = useState<string | null>(null);

  // Three-step creation state
  const [creationStep, setCreationStep] = useState<'idle' | 'tenant' | 'product' | 'subscription' | 'complete'>('idle');
  const [creationProgress, setCreationProgress] = useState<string>('');
  const [creationResult, setCreationResult] = useState<SubscriptionCreationResult | null>(null);

  // Template change state for edit mode
  const [originalTemplateId, setOriginalTemplateId] = useState<string>('');
  const [isTemplateChanged, setIsTemplateChanged] = useState<boolean>(false);
  const [isUpdatingProductStructure, setIsUpdatingProductStructure] = useState<boolean>(false);

  // Load tenants when modal opens
  const loadTenants = async () => {
    setIsLoadingTenants(true);
    setTenantError(null);

    try {
      const availableTenants = await tenantCreationService.getAvailableTenants();
      setTenants(availableTenants);

      if (availableTenants.length === 0) {
        setTenantError('No tenants available. Please create a tenant first.');
      }
    } catch (error) {
      console.error('Error loading tenants:', error);
      setTenantError('Failed to load tenants. Please try again.');
      setTenants([]);
    } finally {
      setIsLoadingTenants(false);
    }
  };

  // Load templates when modal opens
  const loadTemplates = async () => {
    setIsLoadingTemplates(true);
    setTemplateError(null);

    try {
      const liveTemplates = await templateService.getLiveTemplates();

      // Sort templates by name (ascending) then version (descending)
      const sortedTemplates = TemplateUtils.sortTemplatesForDropdown(liveTemplates);

      // Convert to dropdown options - include templateJson
      const templateOptions: TemplateOption[] = sortedTemplates.map(template => ({
        id: template.id,
        name: template.name,
        version: template.version,
        stage: template.stage,
        displayText: TemplateUtils.formatTemplateForDropdown(template),
        templateJson: template.templateJson // Include the template JSON data
      }));

      setTemplates(templateOptions);

      if (templateOptions.length === 0) {
        setTemplateError('No live templates available. Please create and publish a template first.');
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      setTemplateError('Failed to load templates. Please try again.');
      setTemplates([]);
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  // Reset form when modal opens
  useEffect(() => {
    if (show) {
      const today = new Date().toISOString().split('T')[0];
      const nextYear = new Date();
      nextYear.setFullYear(nextYear.getFullYear() + 1);
      const endDate = nextYear.toISOString().split('T')[0];

      if (mode === 'edit' && editSubscription) {
        // Pre-populate form with existing subscription data
        const templateId = editSubscription.templateId || '';
        setFormData({
          tenantId: editSubscription.tenantId || '',
          templateId: templateId,
          name: editSubscription.productName || '',
          type: editSubscription.subscriptionType || 'basic',
          status: editSubscription.isActive ? 'active' : 'inactive',
          startDate: editSubscription.startDate ? editSubscription.startDate.split('T')[0] : today,
          endDate: editSubscription.endDate ? editSubscription.endDate.split('T')[0] : endDate
        });

        // Track original template for change detection
        setOriginalTemplateId(templateId);
        setIsTemplateChanged(false);
      } else {
        // Create mode - use default values
        setFormData({
          tenantId: '',
          templateId: '',
          name: '',
          type: 'basic',
          status: 'active',
          startDate: today,
          endDate: endDate
        });
      }

      setValidationErrors([]);
      setTemplateError(null);

      // Reset creation state
      setCreationStep('idle');
      setCreationProgress('');
      setCreationResult(null);
      setTenantError(null);

      // Load tenants and templates when modal opens
      loadTenants();
      loadTemplates();
    }
  }, [show, mode, editSubscription]);

  // Handle form field changes
  const handleInputChange = (field: keyof SubscriptionFormData, value: any) => {
    // Debug logging for tenant selection
    if (field === 'tenantId') {
      console.log('🔍 Debug: Tenant dropdown changed');
      console.log('🔍 Debug: New value type:', typeof value);
      console.log('🔍 Debug: New value:', value);
    }
    
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Auto-generate subscription name when tenant or type changes
      if (field === 'tenantId' || field === 'type') {
        const tenant = field === 'tenantId' ?
          tenants.find((t: TenantResponse) => t.id === value) :
          tenants.find((t: TenantResponse) => t.id === prev.tenantId);

        const type = field === 'type' ? value : prev.type;

        if (tenant) {
          newData.name = `${tenant.name} - ${type.charAt(0).toUpperCase() + type.slice(1)} Plan`;
        }
      }

      // Track template changes in edit mode
      if (field === 'templateId' && mode === 'edit') {
        const hasChanged = value !== originalTemplateId;
        setIsTemplateChanged(hasChanged);
        console.log('Template change detected:', {
          originalTemplateId,
          newTemplateId: value,
          hasChanged
        });
      }

      return newData;
    });

    // Clear validation errors when user starts typing
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  };

  // Create or update templateDetails dictionary
  const createTemplateDetails = (oldTemplateId?: string, newTemplateId?: string, existingTemplateDetails?: Record<string, boolean>): Record<string, boolean> => {
    // Start with existing templateDetails or empty object
    const templateDetails: Record<string, boolean> = existingTemplateDetails ? { ...existingTemplateDetails } : {};

    // If we have an old template ID, set it to false (disabled)
    if (oldTemplateId && oldTemplateId !== newTemplateId) {
      templateDetails[oldTemplateId] = false;
    }

    // If we have a new template ID, set it to true (enabled)
    if (newTemplateId) {
      templateDetails[newTemplateId] = true;
    }

    console.log('Template details updated:', {
      oldTemplateId,
      newTemplateId,
      existingTemplateDetails,
      newTemplateDetails: templateDetails
    });

    return templateDetails;
  };

  // Validate form
  const validateForm = (): string[] => {
    const errors: string[] = [];

    if (!formData.tenantId) errors.push('Tenant selection is required');
    if (!formData.templateId) errors.push('Template selection is required');
    if (!formData.name.trim()) errors.push('Subscription name is required');
    if (!formData.startDate) errors.push('Start date is required');
    if (!formData.endDate) errors.push('End date is required');

    // Check if end date is after start date
    if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate);
      const endDate = new Date(formData.endDate);
      if (endDate <= startDate) {
        errors.push('End date must be after start date');
      }
    }

    return errors;
  };

  // Handle update subscription with template change support
  const handleUpdateSubscription = async () => {
    if (!editSubscription) return;

    const errors = validateForm();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    setIsSubmitting(true);
    let newProductId = editSubscription.productId;

    try {
      // Step 1: Handle template change if needed
      if (isTemplateChanged) {
        setCreationProgress('Template changed - updating product structure...');
        setIsUpdatingProductStructure(true);

        const selectedTemplate = templates.find(t => t.id === formData.templateId);
        if (!selectedTemplate) {
          throw new Error('Selected template not found');
        }

        console.log('Creating new product structure for template change:', {
          oldTemplateId: originalTemplateId,
          newTemplateId: formData.templateId,
          templateJson: selectedTemplate.templateJson
        });

        // Parse templateJson to extract products array
        let parsedTemplate;
        try {
          parsedTemplate = typeof selectedTemplate.templateJson === 'string'
            ? JSON.parse(selectedTemplate.templateJson)
            : selectedTemplate.templateJson;
        } catch (parseError) {
          throw new Error(`Failed to parse template JSON: ${parseError instanceof Error ? parseError.message : 'Invalid JSON'}`);
        }

        if (!parsedTemplate.products || !Array.isArray(parsedTemplate.products)) {
          throw new Error('Template JSON must contain a products array');
        }

        // Create new product structure with the parsed products
        const productStructureRequest: CreateProductStructureRequest = {
          products: parsedTemplate.products
        };

        // Pass tenant ID from the dropdown selection
        console.log('🔍 Debug: formData.tenantId type:', typeof formData.tenantId);
        console.log('🔍 Debug: formData.tenantId value:', formData.tenantId);
        
        const selectedTenant = tenants.find(t => t.id === formData.tenantId);
        const tenantId = selectedTenant?.id;
        
        console.log('🔍 Debug: selectedTenant:', selectedTenant);
        console.log('🔍 Debug: tenantId type:', typeof tenantId);
        console.log('🔍 Debug: tenantId value:', tenantId);
        console.log('🏗️ Creating product structure with tenant header:', tenantId);
        
        const productStructureResult = await subscriptionCreationService.createProductStructure(productStructureRequest, tenantId);
        
        // Extract productId from the response structure
        if (!productStructureResult.createdProducts || productStructureResult.createdProducts.length === 0) {
          throw new Error('No products were created in the response');
        }
        
        newProductId = productStructureResult.createdProducts[0].productId;

        console.log('Product structure created successfully:', {
          newProductId,
          totalProductsCreated: productStructureResult.totalProductsCreated,
          totalObjectsCreated: productStructureResult.totalObjectsCreated,
          productName: productStructureResult.createdProducts[0].name
        });

        setIsUpdatingProductStructure(false);
      }

      // Step 2: Update subscription
      setCreationProgress('Updating subscription...');

      const selectedTemplate = templates.find(t => t.id === formData.templateId);
      if (!selectedTemplate) {
        throw new Error('Selected template not found');
      }

      // Create templateDetails dictionary
      const templateDetails = createTemplateDetails(
        originalTemplateId,
        formData.templateId,
        editSubscription.templateDetails
      );

      const updateData: UpdateSubscriptionRequest = {
        id: editSubscription.id,
        productId: newProductId, // Use new product ID if template changed
        subscriptionType: formData.type,
        status: formData.status,
        startDate: convertToUtcIsoString(formData.startDate), // Convert to UTC
        endDate: convertToUtcIsoString(formData.endDate), // Convert to UTC
        isActive: formData.status === 'active',
        autoRenew: editSubscription.autoRenew || false,
        pricingTier: formData.type,
        version: selectedTemplate.version,
        templateJson: typeof selectedTemplate.templateJson === 'string'
          ? selectedTemplate.templateJson
          : JSON.stringify(selectedTemplate.templateJson),
        templateDetails: templateDetails
      };

      // Get tenant ID from dropdown selection for subscription update
      console.log('🔍 Debug: formData.tenantId for update type:', typeof formData.tenantId);
      console.log('🔍 Debug: formData.tenantId for update value:', formData.tenantId);
      
      const selectedTenantForUpdate = tenants.find(t => t.id === formData.tenantId);
      const tenantIdForUpdate = selectedTenantForUpdate?.id;
      
      console.log('🔍 Debug: selectedTenantForUpdate:', selectedTenantForUpdate);
      console.log('🔍 Debug: tenantIdForUpdate type:', typeof tenantIdForUpdate);
      console.log('🔍 Debug: tenantIdForUpdate value:', tenantIdForUpdate);
      
      console.log('Updating subscription with data:', updateData);
      console.log('🎫 Updating subscription with tenant header:', tenantIdForUpdate);

      const updatedSubscription = await subscriptionApiService.updateSubscription(updateData, tenantIdForUpdate);

      // Create subscription object for parent component (use original dates for display)
      const subscription: Subscription = {
        id: updatedSubscription.id,
        name: formData.name,
        type: formData.type,
        status: formData.status,
        startDate: formData.startDate, // Keep original format for display
        endDate: formData.endDate, // Keep original format for display
        userCount: 1,
        price: 0
      };

      // Notify parent component
      if (onSubscriptionUpdated) {
        onSubscriptionUpdated(subscription);
      }

      const successMessage = isTemplateChanged
        ? 'Subscription and template updated successfully!'
        : 'Subscription updated successfully!';
      toast.success(successMessage);

      // Close modal after a short delay
      setTimeout(() => {
        onHide();
      }, 1500);

    } catch (error) {
      console.error('Error updating subscription:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      const failureMessage = isTemplateChanged
        ? `Failed to update subscription and template: ${errorMessage}`
        : `Failed to update subscription: ${errorMessage}`;

      toast.error(failureMessage);
      setValidationErrors([errorMessage]);
    } finally {
      setIsSubmitting(false);
      setCreationProgress('');
      setIsUpdatingProductStructure(false);
    }
  };

  // Handle form submission with three-step API integration
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If in edit mode, handle update instead
    if (mode === 'edit') {
      await handleUpdateSubscription();
      return;
    }

    const errors = validateForm();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    setIsSubmitting(true);
    setCreationStep('tenant');
    setCreationProgress('Preparing subscription creation...');

    try {
      // Get selected tenant and template data
      console.log('🔍 Debug: formData.tenantId:', formData.tenantId);
      console.log('🔍 Debug: formData.tenantId type:', typeof formData.tenantId);
      console.log('🔍 Debug: Available tenants:', tenants.map(t => ({ id: t.id, name: t.name })));
      
      const selectedTenant = tenants.find(t => t.id === formData.tenantId);
      const selectedTemplate = templates.find(t => t.id === formData.templateId);
      
      console.log('🔍 Debug: selectedTenant found:', selectedTenant);
      console.log('🔍 Debug: selectedTenant.id:', selectedTenant?.id);
      console.log('🔍 Debug: selectedTenant.id type:', typeof selectedTenant?.id);

      if (!selectedTenant || !selectedTemplate) {
        throw new Error('Selected tenant or template not found');
      }

      // Use the template data that's already loaded (no need for additional API call)
      console.log('🔍 Using template data from dropdown:', selectedTemplate);
      console.log('📋 Template JSON field:', selectedTemplate.templateJson);
      console.log('📋 Template JSON type:', typeof selectedTemplate.templateJson);

      // Validate template data
      if (!selectedTemplate.templateJson) {
        throw new Error('Template JSON is missing from selected template');
      }

      // Step 1: Call tenant upsert to ensure tenant exists
      setCreationProgress('Step 1: Creating/updating tenant...');
      setCreationStep('tenant');

      console.log('🔍 Debug: Calling tenant upsert for:', selectedTenant);

      // Prepare tenant data for upsert
      const tenantUpsertData = {
        id: selectedTenant.id,
        name: selectedTenant.name,
        adminEmail: selectedTenant.adminEmail,
        connectionString: selectedTenant.connectionString,
        isActive: selectedTenant.isActive,
        validUpto: selectedTenant.validUpto,
        issuer: selectedTenant.issuer || '' // Convert null to empty string
      };

      console.log('🔍 Debug: Tenant upsert data:', tenantUpsertData);

      // Call tenant upsert API
      let tenantId: string;
      try {
        const tenantUpsertResult = await subscriptionCreationService.createTenant(tenantUpsertData);
        console.log('✅ Tenant upsert completed:', tenantUpsertResult);

        // The createTenant method can return either a string or an object
        if (typeof tenantUpsertResult === 'string') {
          tenantId = tenantUpsertResult;
        } else if (tenantUpsertResult && typeof tenantUpsertResult === 'object' && 'id' in tenantUpsertResult) {
          // If it's a TenantDto object, extract the id
          tenantId = (tenantUpsertResult as any).id;
        } else {
          // Fallback to original tenant ID
          tenantId = selectedTenant.id;
        }
      } catch (tenantError) {
        console.warn('⚠️ Tenant upsert failed, using original tenant ID:', tenantError);
        // If tenant upsert fails, use the original tenant ID (it might already exist)
        tenantId = selectedTenant.id;
      }

      // Final verification that we have a string ID
      if (typeof tenantId !== 'string' || !tenantId) {
        throw new Error(`Invalid tenant ID: must be a non-empty string. Got: ${typeof tenantId} "${tenantId}"`);
      }

      console.log('✅ Using tenant ID:', tenantId);

      // Step 2: Create product structure with tenant ID
      setCreationProgress('Step 1: Creating product structure...');
      setCreationStep('product');
      
      // Parse template JSON to extract products
      let parsedTemplate;
      try {
        parsedTemplate = typeof selectedTemplate.templateJson === 'string'
          ? JSON.parse(selectedTemplate.templateJson)
          : selectedTemplate.templateJson;
      } catch (parseError) {
        throw new Error(`Failed to parse template JSON: ${parseError.message}`);
      }

      if (!parsedTemplate.products || !Array.isArray(parsedTemplate.products)) {
        throw new Error('Template JSON must contain a products array');
      }

      console.log('🔍 Debug: Using tenant ID for create-product-structure:', tenantId);
      console.log('🔍 Debug: Tenant ID type:', typeof tenantId);

      // Call create-product-structure with tenant header
      const productStructureResult = await subscriptionCreationService.createProductStructure({
        products: parsedTemplate.products
      }, tenantId);  // Use selected tenant ID

      // Extract productId from the response structure
      if (!productStructureResult.createdProducts || productStructureResult.createdProducts.length === 0) {
        throw new Error('No products were created in the response');
      }

      const newProductId = productStructureResult.createdProducts[0].productId;
      console.log('✅ Product structure created, productId:', newProductId);
      console.log('📊 Product structure response:', {
        totalProductsCreated: productStructureResult.totalProductsCreated,
        totalObjectsCreated: productStructureResult.totalObjectsCreated,
        productName: productStructureResult.createdProducts[0].name
      });

      // Step 2: Create subscription with tenant header and productId
      setCreationProgress('Step 2: Creating subscription...');
      setCreationStep('subscription');

      const subscriptionData = {
        productId: newProductId,  // Use productId from create-product-structure response
        subscriptionType: formData.type,
        status: formData.status,
        startDate: convertToUtcIsoString(formData.startDate), // Convert to UTC
        endDate: convertToUtcIsoString(formData.endDate), // Convert to UTC
        isActive: formData.status === 'active',
        autoRenew: false,
        pricingTier: formData.type,
        version: selectedTemplate.version,
        templateJson: JSON.stringify(selectedTemplate.templateJson),
        templateDetails: { [formData.templateId]: true }
      };

      console.log('🔍 Debug: Using tenant ID for subscriptions API:', tenantId);
      console.log('🔍 Debug: Subscription data with productId:', subscriptionData);

      // Call subscriptions API with tenant header and productId in payload
      const result = await subscriptionCreationService.createSubscription(
        subscriptionData, 
        tenantId  // Use selected tenant ID
      );

      // Create result object for compatibility
      const workflowResult = {
        success: true,
        tenantId: tenantId,
        productId: newProductId,
        subscriptionId: result.id,
        subscription: result,
        steps: {
          tenantCreated: true, // Used existing tenant
          productStructureCreated: true,
          subscriptionCreated: true
        }
      };
      setCreationResult(workflowResult);

      if (workflowResult.success) {
        setCreationStep('complete');
        setCreationProgress('Subscription created successfully!');

        // Create subscription object for parent component (use original dates for display)
        const newSubscription: Subscription = {
          id: workflowResult.subscriptionId || `sub-${Date.now()}`,
          name: `${selectedTenant.name} - ${formData.type.charAt(0).toUpperCase() + formData.type.slice(1)} Plan`,
          type: formData.type,
          status: formData.status,
          startDate: formData.startDate, // Keep original format for display
          endDate: formData.endDate, // Keep original format for display
          userCount: 1,
          price: 0
        };

        // Add to parent component
        onSubscriptionAdded(newSubscription);

        // Show success message
        toast.success(`Subscription created successfully!`);

        // Close modal after a short delay
        setTimeout(() => {
          onHide();
        }, 1500);

      } else {
        // Handle creation failure
        setCreationStep('idle');
        setCreationProgress('');

        const errorMessage = result.errors && result.errors.length > 0 ? result.errors.join(', ') : 'Unknown error occurred';
        toast.error(`Failed to create subscription: ${errorMessage}`);
        setValidationErrors(result.errors || ['Unknown error occurred']);
      }

    } catch (error) {
      console.error('Error creating subscription:', error);
      setCreationStep('idle');
      setCreationProgress('');

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error(`Failed to create subscription: ${errorMessage}`);
      setValidationErrors([errorMessage]);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedTenant = tenants.find((t: TenantResponse) => t.id === formData.tenantId);
  const selectedTemplate = templates.find(t => t.id === formData.templateId);

  return (
    <Modal show={show} onHide={onHide} size="lg" backdrop="static">
      <Modal.Header closeButton>
        <Modal.Title>{mode === 'edit' ? 'Edit Subscription' : 'Add New Subscription'}</Modal.Title>
      </Modal.Header>

      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          {/* Creation/Update Progress */}
          {(creationStep !== 'idle' || (mode === 'edit' && isSubmitting)) && (
            <Alert variant={creationStep === 'complete' ? 'success' : 'info'} className="mb-4">
              <div className="d-flex align-items-center">
                {creationStep !== 'complete' && isSubmitting && (
                  <Spinner animation="border" size="sm" className="me-3" />
                )}
                <div>
                  <strong>{mode === 'edit' ? 'Updating Subscription' : 'Creating Subscription'}</strong>
                  {mode === 'create' && (
                    <div className="mt-1">
                      <div className="small">
                        Step 1: {creationStep === 'tenant' ? '🔄 Creating/updating tenant...' : ['product', 'subscription', 'complete'].includes(creationStep) ? '✅ Tenant created/updated' : '⏳ Pending'}
                      </div>
                      <div className="small">
                        Step 2: {creationStep === 'product' ? '🔄 Creating product structure...' : ['subscription', 'complete'].includes(creationStep) ? '✅ Product structure created' : '⏳ Pending'}
                      </div>
                      <div className="small">
                        Step 3: {creationStep === 'subscription' ? '🔄 Creating subscription...' : creationStep === 'complete' ? '✅ Subscription created' : '⏳ Pending'}
                      </div>
                    </div>
                  )}
                  {mode === 'edit' && isTemplateChanged && (
                    <div className="mt-1">
                      <div className="small">
                        Step 1: {isUpdatingProductStructure ? '🔄 Updating product structure...' : !isSubmitting ? '⏳ Pending' : '✅ Product structure updated'}
                      </div>
                      <div className="small">
                        Step 2: {!isUpdatingProductStructure && isSubmitting ? '🔄 Updating subscription...' : isSubmitting ? '⏳ Pending' : '⏳ Pending'}
                      </div>
                    </div>
                  )}
                  {creationProgress && (
                    <div className="mt-2 text-muted small">{creationProgress}</div>
                  )}
                  {creationResult && creationResult.success && mode === 'create' && (
                    <div className="mt-2 small">
                      <strong>Success!</strong> Tenant: {creationResult.tenantId?.substring(0, 8)}...,
                      Product: {creationResult.productId?.substring(0, 8)}...,
                      Subscription: {creationResult.subscriptionId?.substring(0, 8)}...
                    </div>
                  )}
                </div>
              </div>
            </Alert>
          )}

          {/* Validation Errors */}
          {validationErrors.length > 0 && creationStep === 'idle' && (
            <Alert variant="danger" className="mb-4">
              <strong>Please fix the following errors:</strong>
              <ul className="mb-0 mt-2">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </Alert>
          )}

          {/* Row 1: Tenant and Template */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>Tenant <span className="text-danger">*</span></Form.Label>
                <SearchableDropdown
                  value={formData.tenantId}
                  onChange={(value) => handleInputChange('tenantId', value)}
                  options={tenants.map(tenant => ({
                    value: tenant.id,
                    label: tenant.name,
                    disabled: false
                  }))}
                  placeholder={
                    isLoadingTenants ? 'Loading tenants...' :
                    tenants.length === 0 ? 'No tenants available...' : 'Select tenant...'
                  }
                  disabled={isLoadingTenants || tenants.length === 0 || mode === 'edit'}
                  showSearch={true}
                  searchPlaceholder="Search tenants..."
                  className="shadow-sm"
                />
                {isLoadingTenants && (
                  <div className="mt-1">
                    <Spinner animation="border" size="sm" className="me-2" />
                    <small className="text-muted">Loading tenants...</small>
                  </div>
                )}
                {tenantError && (
                  <div className="mt-1">
                    <small className="text-danger">{tenantError}</small>
                  </div>
                )}
                {!isLoadingTenants && !tenantError && tenants.length === 0 && (
                  <div className="mt-1">
                    <small className="text-warning">No tenants available</small>
                  </div>
                )}
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Template <span className="text-danger">*</span></Form.Label>
                <SearchableDropdown
                  value={formData.templateId}
                  onChange={(value) => handleInputChange('templateId', value)}
                  options={templates.map(template => ({
                    value: template.id,
                    label: template.displayText,
                    disabled: false
                  }))}
                  placeholder={isLoadingTemplates ? 'Loading templates...' : 'Select template...'}
                  disabled={isLoadingTemplates}
                  showSearch={true}
                  searchPlaceholder="Search templates..."
                  className="shadow-sm"
                />
                {isLoadingTemplates && (
                  <div className="mt-1">
                    <Spinner animation="border" size="sm" className="me-2" />
                    <small className="text-muted">Loading live templates...</small>
                  </div>
                )}
                {templateError && (
                  <div className="mt-1">
                    <small className="text-danger">{templateError}</small>
                  </div>
                )}
                {!isLoadingTemplates && !templateError && templates.length === 0 && (
                  <div className="mt-1">
                    <small className="text-warning">No live templates available</small>
                  </div>
                )}
              </Form.Group>
            </Col>
          </Row>

          {/* Row 2: Subscription Name */}
          {/* <Row className="mb-3">
            <Col md={12}>
              <Form.Group>
                <Form.Label>Subscription Name <span className="text-danger">*</span></Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter subscription name..."
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                />
              </Form.Group>
            </Col>
          </Row> */}

          {/* Row 3: Subscription Type and Status */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>Subscription Type <span className="text-danger">*</span></Form.Label>
                <SearchableDropdown
                  value={formData.type}
                  onChange={(value) => handleInputChange('type', value as 'basic' | 'premium' | 'enterprise')}
                  options={[
                    { value: 'basic', label: 'Basic' },
                    { value: 'premium', label: 'Premium' },
                    { value: 'enterprise', label: 'Enterprise' }
                  ]}
                  placeholder="Select subscription type..."
                  showSearch={false}
                  className="shadow-sm"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Status</Form.Label>
                <Form.Check
                  type="switch"
                  id="status-switch"
                  label={formData.status === 'active' ? 'Active' : 'Inactive'}
                  checked={formData.status === 'active'}
                  onChange={(e) => handleInputChange('status', e.target.checked ? 'active' : 'inactive')}
                  className="mt-2"
                />
              </Form.Group>
            </Col>
          </Row>

          {/* Row 4: Start Date and End Date */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label>Start Date <span className="text-danger">*</span></Form.Label>
                <Form.Control
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  required
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label>End Date <span className="text-danger">*</span></Form.Label>
                <Form.Control
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                  required
                />
              </Form.Group>
            </Col>
          </Row>

          {/* Summary */}
          {selectedTenant && selectedTemplate && (
            <Alert variant="info" className="mt-3">
              <strong>Summary:</strong> {mode === 'edit' ? 'Updating' : 'Creating'} {formData.type} subscription for {selectedTenant.name}
              using {selectedTemplate.name} template (v{selectedTemplate.version}).
              {mode === 'edit' && isTemplateChanged && (
                <div className="mt-1">
                  <strong>⚠️ Template Change Detected:</strong> This will create a new product structure and update the subscription's template details.
                </div>
              )}
            </Alert>
          )}
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            variant="primary"
            type="submit"
            disabled={isSubmitting || validationErrors.length > 0 || creationStep !== 'idle'}
          >
            {isSubmitting ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                {mode === 'edit' ? (
                  isTemplateChanged ? (
                    isUpdatingProductStructure ? 'Updating Product Structure...' : 'Updating Subscription...'
                  ) : 'Updating...'
                ) : (
                  <>
                    {creationStep === 'tenant' && 'Creating/Updating Tenant...'}
                    {creationStep === 'product' && 'Creating Product...'}
                    {creationStep === 'subscription' && 'Creating Subscription...'}
                    {creationStep === 'complete' && 'Completed!'}
                    {creationStep === 'idle' && 'Creating...'}
                  </>
                )}
              </>
            ) : (
              mode === 'edit' ? (
                isTemplateChanged ? 'Update Subscription & Template' : 'Update Subscription'
              ) : 'Create Subscription'
            )}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
