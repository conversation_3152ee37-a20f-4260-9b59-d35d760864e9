// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResponseModel _$ResponseModelFromJson(Map<String, dynamic> json) =>
    ResponseModel(
      objectName: json['objectName'] as String?,
      tenantId: json['tenantId'] as String?,
      viewName: json['viewName'] as String?,
      viewCreationResult: json['viewCreationResult'] as String?,
      columnNames: (json['columnNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      viewData: (json['viewData'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      totalRows: (json['totalRows'] as num?)?.toInt(),
      pageNumber: (json['pageNumber'] as num?)?.toInt(),
      pageSize: (json['pageSize'] as num?)?.toInt(),
      totalPages: (json['totalPages'] as num?)?.toInt(),
      hasPreviousPage: json['hasPreviousPage'] as bool?,
      hasNextPage: json['hasNextPage'] as bool?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$ResponseModelToJson(ResponseModel instance) =>
    <String, dynamic>{
      'objectName': instance.objectName,
      'tenantId': instance.tenantId,
      'viewName': instance.viewName,
      'viewCreationResult': instance.viewCreationResult,
      'columnNames': instance.columnNames,
      'viewData': instance.viewData,
      'totalRows': instance.totalRows,
      'pageNumber': instance.pageNumber,
      'pageSize': instance.pageSize,
      'totalPages': instance.totalPages,
      'hasPreviousPage': instance.hasPreviousPage,
      'hasNextPage': instance.hasNextPage,
      'message': instance.message,
    };

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: json['id'] as String?,
      name: json['name'] as String?,
      description: json['description'] as String?,
      version: json['version'] as String?,
      isActive: json['isActive'] as bool?,
      metadata: (json['metadata'] as List<dynamic>?)
          ?.map((e) => MetadataWrapper.fromJson(e as Map<String, dynamic>))
          .toList(),
      rootObjects: (json['rootObjects'] as List<dynamic>?)
          ?.map((e) => RootObject.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'version': instance.version,
      'isActive': instance.isActive,
      'metadata': instance.metadata,
      'rootObjects': instance.rootObjects,
    };

MetadataWrapper _$MetadataWrapperFromJson(Map<String, dynamic> json) =>
    MetadataWrapper(
      metadata: json['metadata'] == null
          ? null
          : Metadata.fromJson(json['metadata'] as Map<String, dynamic>),
      values: (json['values'] as List<dynamic>?)
          ?.map((e) => Value.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MetadataWrapperToJson(MetadataWrapper instance) =>
    <String, dynamic>{
      'metadata': instance.metadata,
      'values': instance.values,
    };

Metadata _$MetadataFromJson(Map<String, dynamic> json) => Metadata(
      id: json['id'] as String?,
      name: json['name'] as String?,
      displayLabel: json['displayLabel'] as String?,
      helpText: json['helpText'] as String?,
      fieldOrder: (json['fieldOrder'] as num?)?.toInt(),
      isVisible: json['isVisible'] as bool?,
      isReadonly: json['isReadonly'] as bool?,
      validationPattern: json['validationPattern'] as String?,
      minLength: (json['minLength'] as num?)?.toInt(),
      maxLength: (json['maxLength'] as num?)?.toInt(),
      minValue: (json['minValue'] as num?)?.toInt(),
      maxValue: (json['maxValue'] as num?)?.toInt(),
      isRequired: json['isRequired'] as bool?,
      placeholder: json['placeholder'] as String?,
      defaultOptions: json['defaultOptions'] as String?,
      maxSelections: (json['maxSelections'] as num?)?.toInt(),
      allowedFileTypes: json['allowedFileTypes'] as String?,
      maxFileSize: (json['maxFileSize'] as num?)?.toInt(),
      errorMessage: json['errorMessage'] as String?,
      dataType: json['dataType'] == null
          ? null
          : DataType.fromJson(json['dataType'] as Map<String, dynamic>),
      metadataLink: json['metadataLink'] == null
          ? null
          : MetadataLink.fromJson(json['metadataLink'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MetadataToJson(Metadata instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayLabel': instance.displayLabel,
      'helpText': instance.helpText,
      'fieldOrder': instance.fieldOrder,
      'isVisible': instance.isVisible,
      'isReadonly': instance.isReadonly,
      'validationPattern': instance.validationPattern,
      'minLength': instance.minLength,
      'maxLength': instance.maxLength,
      'minValue': instance.minValue,
      'maxValue': instance.maxValue,
      'isRequired': instance.isRequired,
      'placeholder': instance.placeholder,
      'defaultOptions': instance.defaultOptions,
      'maxSelections': instance.maxSelections,
      'allowedFileTypes': instance.allowedFileTypes,
      'maxFileSize': instance.maxFileSize,
      'errorMessage': instance.errorMessage,
      'dataType': instance.dataType,
      'metadataLink': instance.metadataLink,
    };

DataType _$DataTypeFromJson(Map<String, dynamic> json) => DataType(
      id: json['id'] as String?,
      name: json['name'] as String?,
      displayName: json['displayName'] as String?,
      category: json['category'] as String?,
      uiComponent: json['uiComponent'] as String?,
      validationPattern: json['validationPattern'] as String?,
      minLength: (json['minLength'] as num?)?.toInt(),
      maxLength: (json['maxLength'] as num?)?.toInt(),
      minValue: (json['minValue'] as num?)?.toInt(),
      maxValue: (json['maxValue'] as num?)?.toInt(),
      decimalPlaces: (json['decimalPlaces'] as num?)?.toInt(),
      stepValue: (json['stepValue'] as num?)?.toInt(),
      isRequired: json['isRequired'] as bool?,
      inputType: json['inputType'] as String?,
      inputMask: json['inputMask'] as String?,
      placeholder: json['placeholder'] as String?,
      htmlAttributes: json['htmlAttributes'] as String?,
      defaultOptions: json['defaultOptions'] as String?,
      allowsMultiple: json['allowsMultiple'] as bool?,
      allowsCustomOptions: json['allowsCustomOptions'] as bool?,
      maxSelections: (json['maxSelections'] as num?)?.toInt(),
      allowedFileTypes: json['allowedFileTypes'] as String?,
      maxFileSizeBytes: (json['maxFileSizeBytes'] as num?)?.toInt(),
      requiredErrorMessage: json['requiredErrorMessage'] as String?,
      patternErrorMessage: json['patternErrorMessage'] as String?,
      minLengthErrorMessage: json['minLengthErrorMessage'] as String?,
      maxLengthErrorMessage: json['maxLengthErrorMessage'] as String?,
      minValueErrorMessage: json['minValueErrorMessage'] as String?,
      maxValueErrorMessage: json['maxValueErrorMessage'] as String?,
      fileTypeErrorMessage: json['fileTypeErrorMessage'] as String?,
      fileSizeErrorMessage: json['fileSizeErrorMessage'] as String?,
      isActive: json['isActive'] as bool?,
    );

Map<String, dynamic> _$DataTypeToJson(DataType instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'category': instance.category,
      'uiComponent': instance.uiComponent,
      'validationPattern': instance.validationPattern,
      'minLength': instance.minLength,
      'maxLength': instance.maxLength,
      'minValue': instance.minValue,
      'maxValue': instance.maxValue,
      'decimalPlaces': instance.decimalPlaces,
      'stepValue': instance.stepValue,
      'isRequired': instance.isRequired,
      'inputType': instance.inputType,
      'inputMask': instance.inputMask,
      'placeholder': instance.placeholder,
      'htmlAttributes': instance.htmlAttributes,
      'defaultOptions': instance.defaultOptions,
      'allowsMultiple': instance.allowsMultiple,
      'allowsCustomOptions': instance.allowsCustomOptions,
      'maxSelections': instance.maxSelections,
      'allowedFileTypes': instance.allowedFileTypes,
      'maxFileSizeBytes': instance.maxFileSizeBytes,
      'requiredErrorMessage': instance.requiredErrorMessage,
      'patternErrorMessage': instance.patternErrorMessage,
      'minLengthErrorMessage': instance.minLengthErrorMessage,
      'maxLengthErrorMessage': instance.maxLengthErrorMessage,
      'minValueErrorMessage': instance.minValueErrorMessage,
      'maxValueErrorMessage': instance.maxValueErrorMessage,
      'fileTypeErrorMessage': instance.fileTypeErrorMessage,
      'fileSizeErrorMessage': instance.fileSizeErrorMessage,
      'isActive': instance.isActive,
    };

MetadataLink _$MetadataLinkFromJson(Map<String, dynamic> json) => MetadataLink(
      objectMetaDataId: json['objectMetaDataId'] as String?,
      isUnique: json['isUnique'] as bool?,
      isActive: json['isActive'] as bool?,
      shouldVisibleInList: json['shouldVisibleInList'] as bool?,
      shouldVisibleInEdit: json['shouldVisibleInEdit'] as bool?,
      shouldVisibleInCreate: json['shouldVisibleInCreate'] as bool?,
      shouldVisibleInView: json['shouldVisibleInView'] as bool?,
      isCalculate: json['isCalculate'] as bool?,
    );

Map<String, dynamic> _$MetadataLinkToJson(MetadataLink instance) =>
    <String, dynamic>{
      'objectMetaDataId': instance.objectMetaDataId,
      'isUnique': instance.isUnique,
      'isActive': instance.isActive,
      'shouldVisibleInList': instance.shouldVisibleInList,
      'shouldVisibleInEdit': instance.shouldVisibleInEdit,
      'shouldVisibleInCreate': instance.shouldVisibleInCreate,
      'shouldVisibleInView': instance.shouldVisibleInView,
      'isCalculate': instance.isCalculate,
    };

Value _$ValueFromJson(Map<String, dynamic> json) => Value(
      id: json['id'] as String?,
      refId: json['refId'] as String?,
      value: json['value'] as String?,
      parentObjectValueId: json['parentObjectValueId'] as String?,
    );

Map<String, dynamic> _$ValueToJson(Value instance) => <String, dynamic>{
      'id': instance.id,
      'refId': instance.refId,
      'value': instance.value,
      'parentObjectValueId': instance.parentObjectValueId,
    };

RootObject _$RootObjectFromJson(Map<String, dynamic> json) => RootObject(
      id: json['id'] as String?,
      name: json['name'] as String?,
      description: json['description'] as String?,
      parentObjectId: json['parentObjectId'] as String?,
      isActive: json['isActive'] as bool?,
      hierarchyLevel: (json['hierarchyLevel'] as num?)?.toInt(),
      hierarchyPath: json['hierarchyPath'] as String?,
      metadata: (json['metadata'] as List<dynamic>?)
          ?.map((e) => MetadataWrapper.fromJson(e as Map<String, dynamic>))
          .toList(),
      childObjects: (json['childObjects'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$RootObjectToJson(RootObject instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'parentObjectId': instance.parentObjectId,
      'isActive': instance.isActive,
      'hierarchyLevel': instance.hierarchyLevel,
      'hierarchyPath': instance.hierarchyPath,
      'metadata': instance.metadata,
      'childObjects': instance.childObjects,
    };
