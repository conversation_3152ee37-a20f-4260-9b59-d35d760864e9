import React, { useState } from 'react';
import { DATA_TYPES, type MetadataItem } from '../../utils/index';

interface RenderEditCellProps {
  row: any;
  column: any;
  onRowChange: (row: any) => void;
  onClose: (commitChanges?: boolean) => void;
  updateTempMetadataRow: (rowId: string, updates: Partial<MetadataItem>) => void;
}

export const CustomTypeEditor: React.FC<RenderEditCellProps> = ({ 
  row, 
  column, 
  onRowChange, 
  onClose, 
  updateTempMetadataRow 
}) => {
  const [value, setValue] = useState(row[column.key] || 'Text');

  const handleSave = () => {
    updateTempMetadataRow(row._internalId, { [column.key]: value });
    onRowChange({ ...row, [column.key]: value });
    onClose(true);
  };

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newValue = event.target.value;
    setValue(newValue);
    updateTempMetadataRow(row._internalId, { [column.key]: newValue });
    onRowChange({ ...row, [column.key]: newValue });
    onClose(true);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSave();
    } else if (event.key === 'Escape') {
      onClose(false);
    }
  };

  return (
    <select
      value={value}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      autoFocus
      style={{
        width: '100%',
        height: '100%',
        border: 'none',
        outline: 'none',
        padding: '8px',
        fontSize: 'inherit',
        fontFamily: 'inherit',
        backgroundColor: 'white'
      }}
    >
      {DATA_TYPES.map(type => (
        <option key={type} value={type}>
          {type}
        </option>
      ))}
    </select>
  );
};
