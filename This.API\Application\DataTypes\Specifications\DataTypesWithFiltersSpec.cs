using Ardalis.Specification;
using Domain.Entities;

namespace Application.DataTypes.Specifications;

/// <summary>
/// Specification to get DataTypes with filters
/// </summary>
public class DataTypesWithFiltersSpec : Specification<DataType>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DataTypesWithFiltersSpec(string? searchTerm, string? category, bool? isActive, string? orderBy, int skip = 0, int take = 0)
    {
        Query.Where(dt => !dt.IsDeleted);

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(dt => dt.Name.Contains(searchTerm) ||
                             dt.DisplayName.Contains(searchTerm) ||
                             (dt.Category != null && dt.Category.Contains(searchTerm)));
        }

        if (!string.IsNullOrEmpty(category))
        {
            Query.Where(dt => dt.Category == category);
        }

        if (isActive.HasValue)
        {
            Query.Where(dt => dt.IsActive == isActive.Value);
        }

        // Apply ordering
        if (!string.IsNullOrEmpty(orderBy))
        {
            switch (orderBy.ToLower())
            {
                case "name":
                    Query.OrderBy(dt => dt.Name);
                    break;
                case "name_desc":
                    Query.OrderByDescending(dt => dt.Name);
                    break;
                case "displayname":
                    Query.OrderBy(dt => dt.DisplayName);
                    break;
                case "displayname_desc":
                    Query.OrderByDescending(dt => dt.DisplayName);
                    break;
                case "category":
                    Query.OrderBy(dt => dt.Category);
                    break;
                case "category_desc":
                    Query.OrderByDescending(dt => dt.Category);
                    break;
                case "created":
                    Query.OrderBy(dt => dt.CreatedAt);
                    break;
                case "created_desc":
                    Query.OrderByDescending(dt => dt.CreatedAt);
                    break;
                default:
                    Query.OrderBy(dt => dt.Name);
                    break;
            }
        }
        else
        {
            Query.OrderBy(dt => dt.Name);
        }

        // Apply pagination
        if (skip > 0)
        {
            Query.Skip(skip);
        }

        if (take > 0)
        {
            Query.Take(take);
        }
    }
}
