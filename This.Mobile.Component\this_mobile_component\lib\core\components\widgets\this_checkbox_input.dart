import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// Option model for checkbox items
class CheckboxOption {
  final String value;
  final String label;
  final bool disabled;
  final Widget? icon;

  const CheckboxOption({
    required this.value,
    required this.label,
    this.disabled = false,
    this.icon,
  });
}

class ThisCheckboxInput extends StatefulWidget {
  final String id;
  final String label;
  final List<CheckboxOption> options;
  final List<String> value;
  final ValueChanged<List<String>> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final int? minSelected;
  final int? maxSelected;
  final bool allowSelectAll;
  final String selectAllText;
  final Axis direction;
  final int? crossAxisCount;
  final double spacing;
  final double runSpacing;
  final String? Function(List<String>)? customValidation;

  const ThisCheckboxInput({
    super.key,
    required this.id,
    required this.label,
    required this.options,
    required this.value,
    required this.onChanged,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.minSelected,
    this.maxSelected,
    this.allowSelectAll = false,
    this.selectAllText = 'Select All',
    this.direction = Axis.vertical,
    this.crossAxisCount,
    this.spacing = 8.0,
    this.runSpacing = 8.0,
    this.customValidation,
  });

  @override
  State<ThisCheckboxInput> createState() => _ThisCheckboxInputState();
}

class _ThisCheckboxInputState extends State<ThisCheckboxInput> {
  List<String> _errors = [];

  @override
  void initState() {
    super.initState();
    _validateValue(widget.value);
  }

  List<String> _validateValue(List<String> value) {
    final errors = <String>[];

    // Required validation
    if (widget.required && value.isEmpty) {
      errors.add('${widget.label} is required');
      return errors;
    }

    // Minimum selection validation
    if (widget.minSelected != null && value.length < widget.minSelected!) {
      errors.add('Select at least ${widget.minSelected} option${widget.minSelected! > 1 ? 's' : ''}');
    }

    // Maximum selection validation
    if (widget.maxSelected != null && value.length > widget.maxSelected!) {
      errors.add('Select at most ${widget.maxSelected} option${widget.maxSelected! > 1 ? 's' : ''}');
    }

    // Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(value);
      if (customError != null) {
        errors.add(customError);
      }
    }

    return errors;
  }

  void _handleChange(List<String> newValue) {
    if (widget.disabled || widget.readOnly) return;

    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  void _handleOptionChange(String optionValue, bool isSelected) {
    final newValue = List<String>.from(widget.value);

    if (isSelected) {
      if (!newValue.contains(optionValue)) {
        newValue.add(optionValue);
      }
    } else {
      newValue.remove(optionValue);
    }

    _handleChange(newValue);
  }

  void _handleSelectAll(bool selectAll) {
    if (selectAll) {
      final allValues = widget.options.where((option) => !option.disabled).map((option) => option.value).toList();
      _handleChange(allValues);
    } else {
      _handleChange([]);
    }
  }

  bool get _isAllSelected {
    final enabledOptions = widget.options.where((option) => !option.disabled);
    return enabledOptions.every((option) => widget.value.contains(option.value));
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: ColorPalette.red,
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Select All Option
        if (widget.allowSelectAll && widget.options.isNotEmpty) ...[
          CheckboxListTile(
            value: _isAllSelected,
            tristate: true,
            onChanged: widget.disabled || widget.readOnly ? null : (value) => _handleSelectAll(value ?? false),
            title: Text(
              widget.selectAllText,
              style: LexendTextStyles.lexend14Regular.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
            dense: true,
            activeColor: ColorPalette.black,
            checkColor: ColorPalette.primaryDarkColor,
          ),
          const Divider(height: 16),
        ],

        // Options
        if (widget.crossAxisCount != null)
          // Grid layout
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: widget.crossAxisCount!,
              crossAxisSpacing: widget.spacing,
              mainAxisSpacing: widget.runSpacing,
              childAspectRatio: 4,
            ),
            itemCount: widget.options.length,
            itemBuilder: (context, index) => _buildCheckboxTile(widget.options[index]),
          )
        else
          // List layout
          Column(
            children: widget.options.map(_buildCheckboxTile).toList(),
          ),

        // Error Messages
        if (hasErrors)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _errors.first,
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.red,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCheckboxTile(CheckboxOption option) {
    final isSelected = widget.value.contains(option.value);
    final isDisabled = widget.disabled || widget.readOnly || option.disabled;

    return CheckboxListTile(
      value: isSelected,
      onChanged: isDisabled ? null : (value) => _handleOptionChange(option.value, value ?? false),
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (option.icon != null) ...[
            option.icon!,
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              option.label,
              style: LexendTextStyles.lexend14Regular.copyWith(
                color: isDisabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
          ),
        ],
      ),
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: EdgeInsets.zero,
      dense: true,
      activeColor: ColorPalette.black,
      checkColor: ColorPalette.primaryDarkColor,
    );
  }
}
