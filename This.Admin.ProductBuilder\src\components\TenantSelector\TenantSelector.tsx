/**
 * Tenant Selector Component
 * Global tenant selection component for the application header
 */

import React, { useState } from 'react';
import { ChevronDown, Building2, Check, RefreshCw } from 'lucide-react';
import { useTenantContext } from '../../hooks/useTenantContext';

interface TenantSelectorProps {
  isCollapsed?: boolean;
  className?: string;
}

export const TenantSelector: React.FC<TenantSelectorProps> = ({ 
  isCollapsed = false, 
  className = '' 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const {
    selectedTenantId,
    selectedTenant,
    availableTenants,
    isLoading,
    error,
    setSelectedTenant,
    refreshTenants
  } = useTenantContext();

  const handleTenantSelect = (tenantId: string | null) => {
    setSelectedTenant(tenantId);
    setIsOpen(false);
  };

  const displayText = selectedTenant 
    ? selectedTenant.name 
    : (selectedTenantId ? selectedTenantId : 'All Tenants');

  const shortDisplayText = selectedTenant 
    ? selectedTenant.name.substring(0, 8) + (selectedTenant.name.length > 8 ? '...' : '')
    : (selectedTenantId ? selectedTenantId.substring(0, 8) + '...' : 'All');

  if (isCollapsed) {
    return (
      <div className={`tenant-selector-collapsed ${className}`}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="tenant-selector-button-collapsed"
          title={`Current tenant: ${displayText}`}
          disabled={isLoading}
        >
          <Building2 size={16} />
          {isLoading && <RefreshCw size={12} className="spinning" />}
        </button>

        {isOpen && (
          <>
            <div 
              className="tenant-selector-overlay"
              onClick={() => setIsOpen(false)}
            />
            <div className="tenant-selector-dropdown collapsed">
              <div className="tenant-selector-header">
                <span>Select Tenant</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    refreshTenants();
                  }}
                  className="tenant-selector-refresh"
                  disabled={isLoading}
                >
                  <RefreshCw size={14} className={isLoading ? 'spinning' : ''} />
                </button>
              </div>
              
              <div className="tenant-selector-options">
                <button
                  onClick={() => handleTenantSelect(null)}
                  className={`tenant-selector-option ${!selectedTenantId ? 'selected' : ''}`}
                >
                  <span>All Tenants</span>
                  {!selectedTenantId && <Check size={14} />}
                </button>
                
                {availableTenants.map((tenant) => (
                  <button
                    key={tenant.id}
                    onClick={() => handleTenantSelect(tenant.name)}
                    className={`tenant-selector-option ${selectedTenantId === tenant.name ? 'selected' : ''}`}
                  >
                    <div>
                      <div className="tenant-name">{tenant.name}</div>
                      <div className="tenant-email">{tenant.adminEmail}</div>
                    </div>
                    {selectedTenantId === tenant.name && <Check size={14} />}
                  </button>
                ))}
              </div>
              
              {error && (
                <div className="tenant-selector-error">
                  {error}
                </div>
              )}
            </div>
          </>
        )}

        <style jsx>{`
          .tenant-selector-collapsed {
            position: relative;
          }

          .tenant-selector-button-collapsed {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;
            padding: 4px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
          }

          .tenant-selector-button-collapsed:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
          }

          .tenant-selector-button-collapsed:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }

          .tenant-selector-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
          }

          .tenant-selector-dropdown.collapsed {
            position: absolute;
            top: calc(100% + 8px);
            left: -200px;
            width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 1001;
            overflow: hidden;
          }

          .tenant-selector-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
            font-size: 13px;
          }

          .tenant-selector-refresh {
            padding: 4px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-radius: 4px;
            color: #6c757d;
            transition: all 0.2s ease;
          }

          .tenant-selector-refresh:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #495057;
          }

          .tenant-selector-options {
            max-height: 300px;
            overflow-y: auto;
          }

          .tenant-selector-option {
            width: 100%;
            padding: 12px 16px;
            border: none;
            background: none;
            text-align: left;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
            font-size: 13px;
          }

          .tenant-selector-option:hover {
            background: #f8f9fa;
          }

          .tenant-selector-option.selected {
            background: #e7f3ff;
            color: #0d6efd;
          }

          .tenant-name {
            font-weight: 500;
            color: #212529;
          }

          .tenant-selector-option.selected .tenant-name {
            color: #0d6efd;
          }

          .tenant-email {
            font-size: 11px;
            color: #6c757d;
            margin-top: 2px;
          }

          .tenant-selector-error {
            padding: 12px 16px;
            background: #fee;
            color: #dc3545;
            font-size: 12px;
            border-top: 1px solid #fcc;
          }

          .spinning {
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div className={`tenant-selector ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="tenant-selector-button"
        disabled={isLoading}
      >
        <Building2 size={16} />
        <span className="tenant-selector-text">{displayText}</span>
        <ChevronDown size={14} className={`tenant-selector-chevron ${isOpen ? 'open' : ''}`} />
        {isLoading && <RefreshCw size={14} className="spinning" />}
      </button>

      {isOpen && (
        <>
          <div 
            className="tenant-selector-overlay"
            onClick={() => setIsOpen(false)}
          />
          <div className="tenant-selector-dropdown">
            <div className="tenant-selector-header">
              <span>Select Tenant</span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  refreshTenants();
                }}
                className="tenant-selector-refresh"
                disabled={isLoading}
              >
                <RefreshCw size={14} className={isLoading ? 'spinning' : ''} />
              </button>
            </div>
            
            <div className="tenant-selector-options">
              <button
                onClick={() => handleTenantSelect(null)}
                className={`tenant-selector-option ${!selectedTenantId ? 'selected' : ''}`}
              >
                <span>All Tenants</span>
                {!selectedTenantId && <Check size={14} />}
              </button>
              
              {availableTenants.map((tenant) => (
                <button
                  key={tenant.id}
                  onClick={() => handleTenantSelect(tenant.name)}
                  className={`tenant-selector-option ${selectedTenantId === tenant.name ? 'selected' : ''}`}
                >
                  <div>
                    <div className="tenant-name">{tenant.name}</div>
                    <div className="tenant-email">{tenant.adminEmail}</div>
                  </div>
                  {selectedTenantId === tenant.name && <Check size={14} />}
                </button>
              ))}
            </div>
            
            {error && (
              <div className="tenant-selector-error">
                {error}
              </div>
            )}
          </div>
        </>
      )}

      <style jsx>{`
        .tenant-selector {
          position: relative;
          min-width: 200px;
        }

        .tenant-selector-button {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 4px 8px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          background: rgba(255, 255, 255, 0.1);
          color: white;
          cursor: pointer;
          transition: all 0.2s ease;
          width: 100%;
          backdrop-filter: blur(10px);
        }

        .tenant-selector-button:hover:not(:disabled) {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
        }

        .tenant-selector-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .tenant-selector-text {
          flex: 1;
          text-align: left;
          font-size: 12px;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .tenant-selector-chevron {
          transition: transform 0.2s ease;
          flex-shrink: 0;
        }

        .tenant-selector-chevron.open {
          transform: rotate(180deg);
        }

        .tenant-selector-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 1000;
        }

        .tenant-selector-dropdown {
          position: absolute;
          top: calc(100% + 8px);
          left: 0;
          right: 0;
          background: white;
          border-radius: 12px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
          border: 1px solid rgba(0, 0, 0, 0.1);
          z-index: 1001;
          overflow: hidden;
        }

        .tenant-selector-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
          font-weight: 600;
          color: #495057;
          font-size: 13px;
        }

        .tenant-selector-refresh {
          padding: 4px;
          border: none;
          background: transparent;
          cursor: pointer;
          border-radius: 4px;
          color: #6c757d;
          transition: all 0.2s ease;
        }

        .tenant-selector-refresh:hover:not(:disabled) {
          background: rgba(0, 0, 0, 0.05);
          color: #495057;
        }

        .tenant-selector-options {
          max-height: 300px;
          overflow-y: auto;
        }

        .tenant-selector-option {
          width: 100%;
          padding: 12px 16px;
          border: none;
          background: none;
          text-align: left;
          cursor: pointer;
          display: flex;
          justify-content: space-between;
          align-items: center;
          transition: background-color 0.2s ease;
          font-size: 13px;
        }

        .tenant-selector-option:hover {
          background: #f8f9fa;
        }

        .tenant-selector-option.selected {
          background: #e7f3ff;
          color: #0d6efd;
        }

        .tenant-name {
          font-weight: 500;
          color: #212529;
        }

        .tenant-selector-option.selected .tenant-name {
          color: #0d6efd;
        }

        .tenant-email {
          font-size: 11px;
          color: #6c757d;
          margin-top: 2px;
        }

        .tenant-selector-error {
          padding: 12px 16px;
          background: #fee;
          color: #dc3545;
          font-size: 12px;
          border-top: 1px solid #fcc;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};