using MediatR;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertBulkContext;

/// <summary>
/// Command to upsert multiple contexts in bulk
/// </summary>
public class UpsertBulkContextCommand : IRequest<Result<BulkUpsertContextResponse>>
{
    /// <summary>
    /// List of contexts to upsert
    /// </summary>
    public List<ContextUpsertItem> Contexts { get; set; } = new();

    /// <summary>
    /// Batch size for processing
    /// </summary>
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Individual context item for bulk upsert
/// </summary>
public class ContextUpsertItem
{
    /// <summary>
    /// Context ID (optional for insert)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Name of the context
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the context
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Category for grouping contexts
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Whether the context is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Response for bulk upsert operation
/// </summary>
public class BulkUpsertContextResponse
{
    /// <summary>
    /// Number of contexts created
    /// </summary>
    public int CreatedCount { get; set; }

    /// <summary>
    /// Number of contexts updated
    /// </summary>
    public int UpdatedCount { get; set; }

    /// <summary>
    /// Number of contexts failed
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// List of created context IDs
    /// </summary>
    public List<Guid> CreatedIds { get; set; } = new();

    /// <summary>
    /// List of updated context IDs
    /// </summary>
    public List<Guid> UpdatedIds { get; set; } = new();

    /// <summary>
    /// List of errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Total processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}
