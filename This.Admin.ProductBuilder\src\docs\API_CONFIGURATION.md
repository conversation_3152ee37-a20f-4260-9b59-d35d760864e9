# API Configuration Guide

This document outlines the clean dual API configuration system for both localhost and deployed environments.

## Environment Configuration

### .env File
```bash
# Primary API - Standard data operations
# Used for: Subscription data retrieval, Template operations, General CRUD
VITE_API_BaseUrl=https://localhost:7222/api

# Secondary API - Tenant management operations  
# Used for: Tenant loading, Tenant creation/management, Tenant-related calls
VITE_API_Leadrat_BaseUrl=https://localhost:7243/api
```

### For Deployed Environments
```bash
# Primary API - Standard data operations
VITE_API_BaseUrl=https://api.yourcompany.com/primary

# Secondary API - Tenant management operations
VITE_API_Leadrat_BaseUrl=https://api.yourcompany.com/secondary
```

## API Routing

### Primary API Operations
**Base URL**: `VITE_API_BaseUrl`

- `GET /subscriptions` - Retrieve subscription data
- `GET /subscriptions/{id}` - Get specific subscription
- `GET /subscriptions/stats` - Get subscription statistics
- `GET /templates` - Retrieve template data
- `GET /templates/live` - Get live templates for dropdowns
- `POST/PUT/DELETE /templates` - Template CRUD operations
- `GET /comprehensive-entity` - General entity operations
- `GET /objectvalues` - Object value operations
- `GET /fieldmappings` - Field mapping operations

### Secondary API Operations
**Base URL**: `VITE_API_Leadrat_BaseUrl`

- `GET /tenants` - **Load tenant dropdown options** (for Add Subscription modal)
- `POST /tenants/upsert` - Create/update tenants
- `GET /tenants/{id}` - Get specific tenant
- `POST /tenants/{id}/validate` - Validate tenant connection
- `POST /comprehensive-entity/create-product-structure` - Create product structure
- `POST /subscriptions` - Create new subscriptions

## Service Implementation

### DualApiService Usage
```typescript
import { dualApiService, apiOperations } from '../services/api/dualApiService';

// Primary API call (subscription data)
const subscriptions = await dualApiService.primary('/subscriptions', 'GET');

// Secondary API call (tenant data)
const tenants = await dualApiService.secondary('/tenants', 'GET');

// Using predefined operations
const tenantOptions = await apiOperations.tenants.getAll();
const subscriptionStats = await apiOperations.subscriptions.getStats();
```

### Service-Specific Routing

#### SubscriptionApiService
- **API Used**: Primary
- **Purpose**: Data retrieval and statistics
- **Key Methods**: `getSubscriptions()`, `getSubscriptionById()`, `getSubscriptionStats()`

#### TenantApiService
- **API Used**: Secondary
- **Purpose**: Tenant management and dropdown loading
- **Key Method**: `getTenantDropdownOptionsForSubscription()` - specifically for Add Subscription modal
- **Other Methods**: `getTenants()`, `createTenant()`, `updateTenant()`

#### TemplateApiService
- **API Used**: Primary
- **Purpose**: Template management and data operations
- **Key Methods**: `getTemplates()`, `getLiveTemplates()`, `createTemplate()`

#### SubscriptionCreationService
- **API Used**: Secondary
- **Purpose**: Complete subscription creation workflow
- **Key Methods**: `createSubscriptionWorkflow()`, `createTenant()`, `createProductStructure()`

## Component Usage Examples

### Add Subscription Modal
```typescript
// Uses Secondary API for tenant loading
const { loadDropdownOptions } = useTenants();

useEffect(() => {
  // Calls getTenantDropdownOptionsForSubscription() -> Secondary API
  loadDropdownOptions();
}, []);
```

### Subscription Table
```typescript
// Uses Primary API for data retrieval
const { loadSubscriptions } = useSubscriptions();

useEffect(() => {
  // Calls getSubscriptions() -> Primary API
  loadSubscriptions();
}, []);
```

## Configuration Files

### Key Files
- `src/config/environment.ts` - Environment variable mapping
- `src/config/apiConfig.ts` - API endpoint configuration
- `src/services/api/dualApiService.ts` - Dual API service implementation
- `.env` - Environment variables

### API Configuration Structure
```typescript
export const API_CONFIG = {
  // Primary API - Standard data operations
  PRIMARY: {
    BASE_URL: environment.apiBaseUrl,
    ENDPOINTS: {
      SUBSCRIPTIONS: '/subscriptions',
      TEMPLATES: '/templates',
      // ... other endpoints
    }
  },

  // Secondary API - Tenant management operations
  SECONDARY: {
    BASE_URL: environment.apiLeadratBaseUrl,
    ENDPOINTS: {
      TENANTS: '/tenants',
      TENANTS_UPSERT: '/tenants/upsert',
      // ... other endpoints
    }
  }
} as const;
```

## Environment Flexibility

The configuration is designed to work seamlessly in both localhost and deployed environments:

### Localhost Development
- Uses specific ports (7222, 7243)
- Full URLs with ports in .env file

### Deployed Production
- Uses standard HTTPS URLs without ports
- Environment-specific base URLs
- No port-specific logic in code

## Migration Notes

### Removed Legacy Code
- Removed port-specific configuration logic
- Cleaned up legacy environment variables
- Removed unused helper functions
- Simplified API routing logic

### Backward Compatibility
- Maintained existing service interfaces
- Preserved Redux store integration
- Kept component API contracts unchanged

## Testing API Configuration

### Verify Configuration
```typescript
// Check current API configuration
console.log('Primary API:', API_CONFIG.PRIMARY.BASE_URL);
console.log('Secondary API:', API_CONFIG.SECONDARY.BASE_URL);

// Test API connectivity
await dualApiService.primary('/health', 'GET');
await dualApiService.secondary('/health', 'GET');
```

### Debug Network Calls
1. Open browser Developer Tools
2. Go to Network tab
3. Look for API calls to verify correct endpoints
4. Primary API calls should go to `VITE_API_BaseUrl`
5. Secondary API calls should go to `VITE_API_Leadrat_BaseUrl`

## Troubleshooting

### Common Issues

1. **Tenant dropdown not loading**
   - Verify `VITE_API_Leadrat_BaseUrl` is correct
   - Check Secondary API is accessible
   - Ensure `getTenantDropdownOptionsForSubscription()` is being called

2. **Subscription data not loading**
   - Verify `VITE_API_BaseUrl` is correct
   - Check Primary API is accessible
   - Ensure subscription service is using Primary API

3. **Environment variable issues**
   - Restart development server after .env changes
   - Verify variable names match exactly (case-sensitive)
   - Check for typos in URLs

### Debug Commands
```bash
# Check environment variables are loaded
echo $VITE_API_BaseUrl
echo $VITE_API_Leadrat_BaseUrl

# Test API endpoints
curl https://localhost:7222/api/health
curl https://localhost:7243/api/health
```
