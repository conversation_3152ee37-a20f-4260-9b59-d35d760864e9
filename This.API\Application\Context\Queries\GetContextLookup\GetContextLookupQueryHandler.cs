using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetContextLookup;

/// <summary>
/// Handler for GetContextLookupQuery
/// </summary>
public class GetContextLookupQueryHandler : IRequestHandler<GetContextLookupQuery, Result<List<ContextLookupDto>>>
{
    private readonly IRepository<Domain.Entities.Context> _contextRepository;
    private readonly ILogger<GetContextLookupQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetContextLookupQueryHandler(
        IRepository<Domain.Entities.Context> contextRepository,
        ILogger<GetContextLookupQueryHandler> logger)
    {
        _contextRepository = contextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<List<ContextLookupDto>>> Handle(GetContextLookupQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting context lookup data with filters: Category={Category}, SearchTerm={SearchTerm}, IncludeInactive={IncludeInactive}",
                request.Category, request.SearchTerm, request.IncludeInactive);

            // Create specification
            var spec = new ContextLookupSpec(
                searchTerm: request.SearchTerm,
                category: request.Category,
                includeInactive: request.IncludeInactive);

            // Execute query using specification
            var contexts = await _contextRepository.ListAsync(spec, cancellationToken);

            // Map to lookup DTOs
            var lookupDtos = contexts.Adapt<List<ContextLookupDto>>();

            _logger.LogInformation("Successfully retrieved {Count} context lookup items", lookupDtos.Count);

            return Result<List<ContextLookupDto>>.Success(lookupDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting context lookup data");
            return Result<List<ContextLookupDto>>.Failure($"Error retrieving context lookup data: {ex.Message}");
        }
    }
}
