using Ardalis.Specification;
using Domain.Entities;

namespace Application.ActionManagement.Specifications;

/// <summary>
/// Specification to get Action by name
/// </summary>
public class ActionByNameSpec : Specification<Domain.Entities.Action>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ActionByNameSpec(string name)
    {
        Query.Where(a => a.Name == name && a.IsActive && !a.IsDeleted);
        
        // Take only one record
        Query.Take(1);
    }
}

/// <summary>
/// Specification to get Action by name excluding a specific ID
/// </summary>
public class ActionByNameExcludingIdSpec : Specification<Domain.Entities.Action>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ActionByNameExcludingIdSpec(string name, Guid excludeId)
    {
        Query.Where(a => a.Name == name && a.Id != excludeId && a.IsActive && !a.IsDeleted);
        
        // Take only one record
        Query.Take(1);
    }
}

/// <summary>
/// Specification to get Actions with filtering
/// </summary>
public class ActionsWithFilterSpec : Specification<Domain.Entities.Action>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ActionsWithFilterSpec(
        string? searchTerm = null,
        bool? isActive = null,
        string? orderBy = null)
    {
        Query.Where(a => !a.IsDeleted);

        if (isActive.HasValue)
        {
            Query.Where(a => a.IsActive == isActive.Value);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(a => a.Name.Contains(searchTerm) || 
                           (a.Description != null && a.Description.Contains(searchTerm)));
        }

        // Apply ordering
        switch (orderBy?.ToLower())
        {
            case "name":
                Query.OrderBy(a => a.Name);
                break;
            case "createdat":
                Query.OrderByDescending(a => a.CreatedAt);
                break;
            default:
                Query.OrderBy(a => a.Name);
                break;
        }
    }
}
