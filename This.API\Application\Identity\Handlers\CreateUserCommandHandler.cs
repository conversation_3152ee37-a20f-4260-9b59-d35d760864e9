using Application.Identity.Commands;
using Application.Identity.DTOs;
using Abstraction.Identity;
using Abstraction.Identity.Dtos;
using MediatR;
using Shared.Common.Response;
using Mapster;

namespace Application.Identity.Handlers;

/// <summary>
/// Handler for CreateUserCommand
/// </summary>
public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, ApiResponse<UserDto>>
{
    private readonly IIdentityService _identityService;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateUserCommandHandler(IIdentityService identityService)
    {
        _identityService = identityService;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<ApiResponse<UserDto>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate the request
            if (string.IsNullOrWhiteSpace(request.UserName))
            {
                return new ApiResponse<UserDto>(false, "Username is required.", default!);
            }

            if (string.IsNullOrWhiteSpace(request.Email))
            {
                return new ApiResponse<UserDto>(false, "Email is required.", default!);
            }

            if (string.IsNullOrWhiteSpace(request.Password))
            {
                return new ApiResponse<UserDto>(false, "Password is required.", default!);
            }

            if (request.Password != request.ConfirmPassword)
            {
                return new ApiResponse<UserDto>(false, "Passwords do not match.", default!);
            }

            // Check if user already exists
            if (await _identityService.ExistsWithEmailAsync(request.Email))
            {
                return new ApiResponse<UserDto>(false, "User with this email already exists.", default!);
            }

            if (await _identityService.ExistsWithNameAsync(request.UserName))
            {
                return new ApiResponse<UserDto>(false, "User with this username already exists.", default!);
            }

            // Set display name if not provided
            if (string.IsNullOrWhiteSpace(request.DisplayName))
            {
                request.DisplayName = $"{request.FirstName} {request.LastName}".Trim();
            }

            // Create the user
            var createRequest = new CreateUserRequestDto
            {
                UserName = request.UserName,
                Email = request.Email,
                Password = request.Password,
                ConfirmPassword = request.ConfirmPassword,
                FirstName = request.FirstName,
                LastName = request.LastName,
                DisplayName = request.DisplayName,
                PhoneNumber = request.PhoneNumber,
                ImageUrl = request.ImageUrl,
                IsActive = request.IsActive,
                IsMFAEnabled = request.IsMFAEnabled,
                IntegrationSourceId = request.IntegrationSourceId,
                ExternalUserId = request.ExternalUserId,
                ExternalUserData = request.ExternalUserData,
                MustChangePassword = request.MustChangePassword,
                ObjectId = request.ObjectId,
                Roles = request.Roles
            };

            var result = await _identityService.CreateUserAsync(createRequest);

            if (string.IsNullOrEmpty(result))
            {
                return new ApiResponse<UserDto>(false, "Failed to create user.", default!);
            }

            // Get the created user
            var userDetails = await _identityService.GetUserByEmailAsync(request.Email);
            if (userDetails == null)
            {
                return new ApiResponse<UserDto>(false, "User was created but could not be retrieved.", default!);
            }

            // Map UserDetailsDto to UserDto
            var user = userDetails.Adapt<UserDto>();

            return new ApiResponse<UserDto>(true, "User created successfully.", user);
        }
        catch (Exception ex)
        {
            return new ApiResponse<UserDto>(false, $"Error creating user: {ex.Message}", default!);
        }
    }
}
