class FieldMetadata {
  final String id;
  final String name;
  final String displayLabel;
  final String inputType;
  final bool isRequired;
  final String? placeholder;
  final String? validationPattern;
  final String? errorMessage;
  final int? minLength;
  final int? maxLength;
  final String? helpText;
  final bool isVisible;
  final bool isReadonly;
  final String dataTypeName;
  final String category;
  final String uiComponent;
  final String? metadataKey; // Key used for API payload
  final List<dynamic>? values; // For field values
  final List<FieldMetadata>? children; // For nested fields/sections

  FieldMetadata({
    required this.id,
    required this.name,
    required this.displayLabel,
    required this.inputType,
    required this.isRequired,
    required this.dataTypeName,
    required this.category,
    required this.uiComponent,
    this.placeholder,
    this.validationPattern,
    this.errorMessage,
    this.minLength,
    this.maxLength,
    this.helpText,
    this.isVisible = true,
    this.isReadonly = false,
    this.metadataKey,
    this.values,
    this.children,
  });

  factory FieldMetadata.fromJson(Map<String, dynamic> json) {
    // Handle the nested metadata structure from API
    final metadata = json['metadata'] ?? json;

    return FieldMetadata(
      id: metadata['id'] ?? '',
      name: metadata['name'] ?? '',
      displayLabel: metadata['displayLabel'] ?? metadata['name'] ?? '',
      inputType: metadata['inputType'] ?? 'text',
      isRequired: metadata['isRequired'] ?? false,
      dataTypeName: metadata['dataTypeName'] ?? 'text',
      category: metadata['category'] ?? 'Input',
      uiComponent: metadata['uiComponent'] ?? 'ThisText',
      placeholder: metadata['placeholder'],
      validationPattern: metadata['validationPattern'],
      errorMessage: metadata['errorMessage'],
      minLength: metadata['minLength'],
      maxLength: metadata['maxLength'],
      helpText: metadata['helpText'],
      isVisible: metadata['isVisible'] ?? true,
      isReadonly: metadata['isReadonly'] ?? false,
      metadataKey: metadata['metadataKey'] ?? metadata['name'] ?? metadata['id'],
      values: json['values'] as List<dynamic>?,
      children: (json['children'] as List?)?.map((e) => FieldMetadata.fromJson(e)).toList(),
    );
  }

  // Helper method to get validation rules
  Map<String, dynamic> get validationRules => {
    'required': isRequired,
    'minLength': minLength,
    'maxLength': maxLength,
    'pattern': validationPattern,
  };

  // Helper method to get error messages
  Map<String, String> get errorMessages => {
    'required': errorMessage ?? 'This field is required',
    'minLength': 'Minimum length is $minLength characters',
    'maxLength': 'Maximum length is $maxLength characters',
    'pattern': errorMessage ?? 'Invalid format', // Use custom error message if provided
  };
}
