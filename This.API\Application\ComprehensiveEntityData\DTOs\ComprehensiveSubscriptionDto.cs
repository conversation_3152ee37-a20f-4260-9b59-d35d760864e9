using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Application.ComprehensiveEntityData.DTOs;

/// <summary>
/// Comprehensive Subscription DTO for cross-tenant subscription data
/// </summary>
public class ComprehensiveSubscriptionDto
{
    /// <summary>
    /// Subscription ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Tenant ID
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Tenant name
    /// </summary>
    public string? TenantName { get; set; }

    /// <summary>
    /// Product ID this subscription is for
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string? ProductName { get; set; }

    /// <summary>
    /// Subscription type
    /// </summary>
    public string SubscriptionType { get; set; } = string.Empty;

    /// <summary>
    /// Subscription status
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Subscription start date
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Subscription end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Whether auto-renewal is enabled
    /// </summary>
    public bool AutoRenew { get; set; }

    /// <summary>
    /// Pricing tier
    /// </summary>
    public string? PricingTier { get; set; }

    /// <summary>
    /// Template version
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Template JSON content
    /// </summary>
    public string TemplateJson { get; set; } = "{}";
    
    /// <summary>
    /// Template Ids with their enabled/disabled status
    /// </summary>
    /// 
    public string? TemplateDetails { get; set; }

    /// <summary>
    /// Whether the subscription is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Number of metadata definitions
    /// </summary>
    public int MetadataCount { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Whether the subscription is expired
    /// </summary>
    public bool IsExpired => EndDate.HasValue && EndDate.Value < DateTime.UtcNow;

    /// <summary>
    /// Days until expiration (null if no end date)
    /// </summary>
    public int? DaysUntilExpiration => EndDate.HasValue ? 
        (int)(EndDate.Value - DateTime.UtcNow).TotalDays : null;
}

/// <summary>
/// Response DTO for comprehensive subscription data
/// </summary>
public class ComprehensiveSubscriptionResponseDto
{
    /// <summary>
    /// List of subscriptions across all tenants
    /// </summary>
    public List<ComprehensiveSubscriptionDto> Subscriptions { get; set; } = new();

    /// <summary>
    /// Total count of subscriptions
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// Whether there are more pages
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// Whether there are previous pages
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// Summary statistics
    /// </summary>
    public ComprehensiveSubscriptionSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// Summary statistics for comprehensive subscription data
/// </summary>
public class ComprehensiveSubscriptionSummaryDto
{
    /// <summary>
    /// Total number of active subscriptions
    /// </summary>
    public int ActiveSubscriptions { get; set; }

    /// <summary>
    /// Total number of expired subscriptions
    /// </summary>
    public int ExpiredSubscriptions { get; set; }

    /// <summary>
    /// Total number of subscriptions expiring in next 30 days
    /// </summary>
    public int ExpiringIn30Days { get; set; }

    /// <summary>
    /// Number of unique tenants with subscriptions
    /// </summary>
    public int UniqueTenants { get; set; }

    /// <summary>
    /// Number of unique products with subscriptions
    /// </summary>
    public int UniqueProducts { get; set; }

    /// <summary>
    /// Subscription count by status
    /// </summary>
    public Dictionary<string, int> SubscriptionsByStatus { get; set; } = new();

    /// <summary>
    /// Subscription count by type
    /// </summary>
    public Dictionary<string, int> SubscriptionsByType { get; set; } = new();
}
