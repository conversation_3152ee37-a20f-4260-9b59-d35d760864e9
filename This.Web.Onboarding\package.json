{"name": "product-builder-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --mode development", "dev:qa": "vite --mode qa", "dev:prd": "vite --mode production", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:qa": "vite build --mode qa", "build:prd": "vite build --mode production", "lint": "eslint .", "preview": "vite preview", "preview:dev": "vite preview --mode development", "preview:qa": "vite preview --mode qa", "preview:prd": "vite preview --mode production", "start": "serve -s dist -l 8080", "env:copy": "node scripts/copy-env.cjs", "env:validate": "node scripts/validate-env.cjs", "deploy:dev": "node scripts/deploy.cjs dev", "deploy:qa": "node scripts/deploy.cjs qa", "deploy:prd": "node scripts/deploy.cjs prd"}, "dependencies": {"@headlessui/react": "^1.7.18", "axios": "^1.9.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "serve": "^14.2.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}