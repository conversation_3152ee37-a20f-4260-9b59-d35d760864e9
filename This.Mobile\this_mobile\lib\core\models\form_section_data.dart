import 'field_metadata.dart';

class FormSectionData {
  final String sectionName;
  final List<FieldMetadata> fields;
  final List<FormSectionData>? children;

  FormSectionData({
    required this.sectionName,
    required this.fields,
    this.children,
  });

  factory FormSectionData.fromJson(Map<String, dynamic> json) {
    return FormSectionData(
      sectionName: json['sectionName'],
      fields: (json['fields'] as List).map((e) => FieldMetadata.fromJson(e)).toList(),
      children: (json['children'] as List?)?.map((e) => FormSectionData.fromJson(e)).toList(),
    );
  }
}
