import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/components/dynamic_form_generator.dart';
import '../core/services/metadata_service.dart';
import '../services/api_service.dart';
import '../providers/app_provider.dart';
import '../utils/constants.dart';

/// Mobile-optimized form screen for creating and editing records
class MobileFormScreen extends StatefulWidget {
  final String objectType;
  final Map<String, dynamic>? initialData;
  final bool isEditMode;
  final String? recordId;

  const MobileFormScreen({
    super.key,
    required this.objectType,
    this.initialData,
    this.isEditMode = false,
    this.recordId,
  });

  @override
  State<MobileFormScreen> createState() => _MobileFormScreenState();
}

class _MobileFormScreenState extends State<MobileFormScreen> {
  late MetadataService _metadataService;
  late ApiService _apiService;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  void _initializeServices() {
    // Initialize services with app configuration
    _metadataService = MetadataService(
      baseUrl: 'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net',
      tenant: 'kitchsync',
    );
    _apiService = ApiService();

    setState(() {
      _isInitialized = true;
    });
  }

  /// Handle form submission
  Future<void> _handleSubmit(Map<String, dynamic> formData) async {
    final provider = Provider.of<AppProvider>(context, listen: false);

    try {
      if (widget.isEditMode && widget.recordId != null) {
        // Update existing record
        await _updateRecord(formData);
      } else {
        // Create new record
        await _createRecord(formData);
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.isEditMode
                  ? '${widget.objectType} updated successfully'
                  : '${widget.objectType} created successfully',
            ),
            backgroundColor: AppColors.success,
          ),
        );

        // Refresh data and navigate back
        provider.loadInstanceData(widget.objectType);
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// Create new record
  Future<void> _createRecord(Map<String, dynamic> formData) async {
    final objectId = _getObjectIdByType(widget.objectType);
    if (objectId == null) {
      throw Exception('Unknown object type: ${widget.objectType}');
    }

    // Generate a new RefId for create operations
    final newRefId = _generateUuid();

    // The formData already contains the properly formatted fields from DynamicFormGenerator
    // Just add the required ObjectId and RefId
    final metadataProperties = Map<String, dynamic>.from(formData);
    metadataProperties['ObjectId'] = objectId;
    metadataProperties['RefId'] = newRefId;

    print('Create payload: $metadataProperties');

    // Call the API service
    await _apiService.upsertObjectWithMetadata(
      metadataProperties: metadataProperties,
    );
  }

  /// Update existing record
  Future<void> _updateRecord(Map<String, dynamic> formData) async {
    final objectId = _getObjectIdByType(widget.objectType);
    if (objectId == null) {
      throw Exception('Unknown object type: ${widget.objectType}');
    }

    if (widget.recordId == null) {
      throw Exception('Record ID is required for updates');
    }

    // Validate RefId format - should be a UUID, not 'item_X'
    String refId = widget.recordId!;
    if (refId.startsWith('item_')) {
      // If it's a generated ID like 'item_0', we need to get the actual UUID from the data
      // For now, let's extract it from initialData if available
      if (widget.initialData != null && widget.initialData!.containsKey('id')) {
        refId = widget.initialData!['id'].toString();
      } else {
        throw Exception('Cannot update record: Invalid RefId format. Expected UUID but got: $refId');
      }
    }

    // The formData already contains the properly formatted fields from DynamicFormGenerator
    // Just add the required ObjectId and RefId
    final metadataProperties = Map<String, dynamic>.from(formData);
    metadataProperties['ObjectId'] = objectId;
    metadataProperties['RefId'] = refId;

    print('Update payload: $metadataProperties');

    // Call the API service
    await _apiService.upsertObjectWithMetadata(
      metadataProperties: metadataProperties,
    );
  }

  /// Get object ID by object type name
  String? _getObjectIdByType(String objectType) {
    // Map object type names to their corresponding ObjectIds
    // You should update these IDs based on your actual API data
    final objectIdMap = {
      'mealkit': '50f97590-6969-400c-9523-9e698f982b98', // From your curl example
      'spicepacket': 'another-object-id-here', // Add other object types as needed
      // Add more mappings as needed
    };

    return objectIdMap[objectType.toLowerCase()];
  }

  /// Generate a UUID v4 string
  String _generateUuid() {
    // Simple UUID v4 generation using timestamp
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final timestampLength = timestamp.length;

    // Ensure we have enough characters
    if (timestampLength < 13) {
      // Pad with zeros if needed
      final paddedTimestamp = timestamp.padLeft(13, '0');
      return '${paddedTimestamp.substring(0, 8)}-${paddedTimestamp.substring(8, 12)}-4000-8000-${paddedTimestamp}000';
    }

    // Create a UUID-like format: xxxxxxxx-xxxx-4xxx-8xxx-xxxxxxxxxxxx
    return '${timestamp.substring(0, 8)}-${timestamp.substring(8, 12)}-4000-8000-${timestamp.substring(0, 12)}';
  }

  /// Handle form cancellation
  void _handleCancel() {
    Navigator.of(context).pop(false); // Return false to indicate cancellation
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        appBar: AppBar(
          title: Text('${widget.isEditMode ? 'Edit' : 'New'} ${widget.objectType}'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return DynamicFormGenerator(
      objectType: widget.objectType,
      initialData: widget.initialData,
      isEditMode: widget.isEditMode,
      metadataService: _metadataService,
      onSubmit: _handleSubmit,
      onCancel: _handleCancel,
      title: _getFormTitle(),
    );
  }

  /// Get appropriate form title
  String _getFormTitle() {
    if (widget.isEditMode) {
      return 'Edit ${_formatObjectTypeName(widget.objectType)}';
    } else {
      return 'New ${_formatObjectTypeName(widget.objectType)}';
    }
  }

  /// Format object type name for display
  String _formatObjectTypeName(String objectType) {
    // Convert camelCase to Title Case
    return objectType
        .replaceAllMapped(RegExp(r'([a-z])([A-Z])'), (match) => '${match.group(1)} ${match.group(2)}')
        .split(' ')
        .map((word) => word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}' : '')
        .join(' ');
  }
}

/// Static helper methods for navigation
class MobileFormScreenHelper {
  /// Navigate to create form
  static Future<bool?> navigateToCreate(
    BuildContext context,
    String objectType,
  ) {
    return Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => MobileFormScreen(
          objectType: objectType,
          isEditMode: false,
        ),
      ),
    );
  }

  /// Navigate to edit form
  static Future<bool?> navigateToEdit(
    BuildContext context,
    String objectType,
    String recordId,
    Map<String, dynamic> initialData,
  ) {
    return Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => MobileFormScreen(
          objectType: objectType,
          recordId: recordId,
          initialData: initialData,
          isEditMode: true,
        ),
      ),
    );
  }

  /// Show form in bottom sheet (for quick edits)
  static Future<bool?> showFormBottomSheet(
    BuildContext context,
    String objectType, {
    Map<String, dynamic>? initialData,
    bool isEditMode = false,
    String? recordId,
  }) {
    return showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: MobileFormScreen(
          objectType: objectType,
          initialData: initialData,
          isEditMode: isEditMode,
          recordId: recordId,
        ),
      ),
    );
  }
}
