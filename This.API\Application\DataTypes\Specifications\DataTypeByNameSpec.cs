using Ardalis.Specification;
using Domain.Entities;

namespace Application.DataTypes.Specifications;

/// <summary>
/// Specification to get DataType by name
/// </summary>
public class DataTypeByNameSpec : Specification<DataType>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DataTypeByNameSpec(string name)
    {
        Query.Where(dt => dt.Name == name && !dt.IsDeleted);
    }
}
