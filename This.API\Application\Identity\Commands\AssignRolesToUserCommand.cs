using MediatR;
using Shared.Common.Response;

namespace Application.Identity.Commands;

/// <summary>
/// Command for assigning roles to a user
/// </summary>
public class AssignRolesToUserCommand : IRequest<ApiResponse<string>>
{
    /// <summary>
    /// User ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// List of role names to assign
    /// </summary>
    public List<string> RoleNames { get; set; } = new();
}
