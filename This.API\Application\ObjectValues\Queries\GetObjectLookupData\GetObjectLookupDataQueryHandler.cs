using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Application.ObjectValues.Queries.GetMetadataKeyValues;

namespace Application.ObjectValues.Queries.GetObjectLookupData;

/// <summary>
/// Handler for GetObjectLookupDataQuery
/// </summary>
public class GetObjectLookupDataQueryHandler : IRequestHandler<GetObjectLookupDataQuery, Result<GetObjectLookupDataResponse>>
{
    private readonly IRepository<ObjectLookup> _objectLookupRepository;
    private readonly IMediator _mediator;
    private readonly ILogger<GetObjectLookupDataQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetObjectLookupDataQueryHandler(
        IRepository<ObjectLookup> objectLookupRepository,
        IMediator mediator,
        ILogger<GetObjectLookupDataQueryHandler> logger)
    {
        _objectLookupRepository = objectLookupRepository;
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<GetObjectLookupDataResponse>> Handle(GetObjectLookupDataQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting ObjectLookup data for ID: {ObjectLookupId}", request.ObjectLookupId);

            // Step 1: Get ObjectLookup configuration
            var objectLookup = await _objectLookupRepository.GetByIdAsync(request.ObjectLookupId, cancellationToken);
            if (objectLookup == null)
            {
                return Result<GetObjectLookupDataResponse>.Failure($"ObjectLookup with ID {request.ObjectLookupId} not found");
            }

            if (!objectLookup.IsActive)
            {
                return Result<GetObjectLookupDataResponse>.Failure($"ObjectLookup with ID {request.ObjectLookupId} is not active");
            }

            // Step 2: Map ObjectLookup to DTO
            var objectLookupDto = new ObjectLookupDto
            {
                Id = objectLookup.Id,
                Name = objectLookup.Name,
                SourceType = objectLookup.SourceType,
                DisplayField = objectLookup.DisplayField,
                ValueField = objectLookup.ValueField,
                ObjectId = objectLookup.ObjectId,
                MetadataFieldForDisplay = objectLookup.MetadataFieldForDisplay,
                MetadataFieldForValue = objectLookup.MetadataFieldForValue,
                SupportsTenantFiltering = objectLookup.SupportsTenantFiltering,
                SortBy = objectLookup.SortBy,
                SortOrder = objectLookup.SortOrder,
                IsActive = objectLookup.IsActive
            };

            // Step 3: Get metadata key values using the existing API
            var metadataKeyValuesQuery = new GetMetadataKeyValuesQuery(
                objectLookup.SourceType, // ObjectName
                objectLookup.DisplayField, // MetadataKey
                request.TenantId,
                request.CreateView,
                request.PageNumber,
                request.PageSize);

            var metadataKeyValuesResult = await _mediator.Send(metadataKeyValuesQuery, cancellationToken);

            if (!metadataKeyValuesResult.Succeeded)
            {
                return Result<GetObjectLookupDataResponse>.Failure($"Failed to get metadata key values: {metadataKeyValuesResult.Message}");
            }

            // Step 4: Create response
            var response = new GetObjectLookupDataResponse
            {
                ObjectLookup = objectLookupDto,
                ObjectLookUpValues = metadataKeyValuesResult.Data!,
                Message = $"Successfully retrieved ObjectLookup data and {metadataKeyValuesResult.Data!.Values.Count} metadata ke values"
            };

            return Result<GetObjectLookupDataResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ObjectLookup data for ID: {ObjectLookupId}", request.ObjectLookupId);
            return Result<GetObjectLookupDataResponse>.Failure($"An error occurred while getting ObjectLookup data: {ex.Message}");
        }
    }
}
