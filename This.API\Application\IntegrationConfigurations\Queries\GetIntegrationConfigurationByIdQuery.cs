using Application.IntegrationConfigurations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Queries;

/// <summary>
/// Get integration configuration by ID query
/// </summary>
public class GetIntegrationConfigurationByIdQuery : IRequest<Result<ViewIntegrationConfigurationDto>>
{
    /// <summary>
    /// Integration Configuration ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationConfigurationByIdQuery(Guid id)
    {
        Id = id;
    }
}
