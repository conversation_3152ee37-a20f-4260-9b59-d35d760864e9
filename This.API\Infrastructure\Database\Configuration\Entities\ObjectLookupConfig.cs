using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for ObjectLookup entity
/// </summary>
public class ObjectLookupConfig : IEntityTypeConfiguration<ObjectLookup>
{
    public void Configure(EntityTypeBuilder<ObjectLookup> builder)
    {
        builder.ToTable("ObjectLookups", "Genp");

        // Properties
        builder.Property(e => e.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.SourceType)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.DisplayField)
            .HasMaxLength(100)
            .HasDefaultValue("Name")
            .IsRequired();

        builder.Property(e => e.ValueField)
            .HasMaxLength(100)
            .HasDefaultValue("Id")
            .IsRequired();

        builder.Property(e => e.MetadataFieldForDisplay)
            .HasMaxLength(100);

        builder.Property(e => e.MetadataFieldForValue)
            .HasMaxLength(100);

        builder.Property(e => e.SupportsTenantFiltering)
            .HasDefaultValue(true);

        builder.Property(e => e.SortBy)
            .HasMaxLength(100)
            .HasDefaultValue("Name")
            .IsRequired();

        builder.Property(e => e.SortOrder)
            .HasMaxLength(10)
            .HasDefaultValue("ASC")
            .IsRequired();

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false);

        // Multi-tenancy
        builder.IsMultiTenant();

        // Indexes
        builder.HasIndex(e => e.SourceType)
            .HasDatabaseName("IX_ObjectLookups_SourceType");

        builder.HasIndex(e => e.ObjectId)
            .HasDatabaseName("IX_ObjectLookups_ObjectId");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_ObjectLookups_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        builder.HasOne(e => e.Object)
            .WithMany()
            .HasForeignKey(e => e.ObjectId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(e => e.Metadata)
            .WithOne(e => e.ObjectLookup)
            .HasForeignKey(e => e.ObjectLookupId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
