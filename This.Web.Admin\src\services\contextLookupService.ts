// Context and TenantContext lookup service for metadata-driven form fields

import { apiClient } from './apiClient';
import type {
  LookupOption,
  LookupConfig,
  ContextWithLookupsDto,
  TenantContextWithLookupsDto
} from '../types/context';

export class ContextLookupService {
  private static instance: ContextLookupService;
  private cache = new Map<string, LookupOption[]>();
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes

  public static getInstance(): ContextLookupService {
    if (!ContextLookupService.instance) {
      ContextLookupService.instance = new ContextLookupService();
    }
    return ContextLookupService.instance;
  }

  /**
   * Get lookup options for a field based on contextId and tenantContextId
   * Priority: TenantContext > Context (as per requirements)
   */
  async getLookupOptions(config: LookupConfig): Promise<LookupOption[]> {
    const { contextId, tenantContextId, includeInactiveLookups = false } = config;

    // If no context IDs provided, return empty array
    if (!contextId && !tenantContextId) {
      return [];
    }

    // Create cache key
    const cacheKey = `${tenantContextId || 'none'}_${contextId || 'none'}_${includeInactiveLookups}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      let lookupOptions: LookupOption[] = [];

      // Priority 1: TenantContext (tenant-specific data)
      if (tenantContextId) {
        const tenantOptions = await this.getTenantContextLookups(tenantContextId, includeInactiveLookups);
        lookupOptions = tenantOptions;
      }
      // Priority 2: Context (master/common data) - only if no tenant context or tenant context returned no results
      else if (contextId) {
        const contextOptions = await this.getContextLookups(contextId, includeInactiveLookups);
        lookupOptions = contextOptions;
      }

      // Cache the results
      this.cache.set(cacheKey, lookupOptions);
      
      // Clear cache after timeout
      setTimeout(() => {
        this.cache.delete(cacheKey);
      }, this.cacheTimeout);

      return lookupOptions;
    } catch (error) {
      console.error('Error fetching lookup options:', error);
      return [];
    }
  }

  /**
   * Get Context lookups (master/common data for all tenants)
   */
  private async getContextLookups(contextId: string, includeInactiveLookups: boolean): Promise<LookupOption[]> {
    try {
      const response = await apiClient.get<ContextWithLookupsDto>(
        `/api/context/${contextId}/with-lookups?includeInactiveLookups=${includeInactiveLookups}`
      );

      // The API returns the data directly, not wrapped in a Result<T> structure for this endpoint
      if (response.data && response.data.context) {
        return this.mapContextLookupsToOptions(response.data);
      } else {
        console.warn('Context API call returned no data');
        return [];
      }
    } catch (error) {
      console.error('Error fetching context lookups:', error);
      return [];
    }
  }

  /**
   * Get TenantContext lookups (tenant-specific data)
   */
  private async getTenantContextLookups(tenantContextId: string, includeInactiveLookups: boolean): Promise<LookupOption[]> {
    try {
      const response = await apiClient.get<TenantContextWithLookupsDto>(
        `/api/context/tenant/${tenantContextId}/with-lookups?includeInactiveLookups=${includeInactiveLookups}`
      );

      // The API returns the data directly, not wrapped in a Result<T> structure for this endpoint
      if (response.data && response.data.tenantContext) {
        return this.mapTenantContextLookupsToOptions(response.data);
      } else {
        console.warn('TenantContext API call returned no data');
        return [];
      }
    } catch (error) {
      console.error('Error fetching tenant context lookups:', error);
      return [];
    }
  }

  /**
   * Map Context lookups to unified LookupOption format
   */
  private mapContextLookupsToOptions(data: ContextWithLookupsDto): LookupOption[] {
    return data.lookups
      .filter(lookup => lookup.isActive && !lookup.isDeleted)
      .sort((a, b) => a.showSequence - b.showSequence)
      .map(lookup => ({
        id: lookup.id,
        value: lookup.value,
        label: lookup.value, // Use value as label, can be customized
        isDefault: lookup.isDefault,
        value1: lookup.value1,
        value2: lookup.value2,
        showSequence: lookup.showSequence,
        source: 'context' as const,
        contextId: lookup.contextId
      }));
  }

  /**
   * Map TenantContext lookups to unified LookupOption format
   */
  private mapTenantContextLookupsToOptions(data: TenantContextWithLookupsDto): LookupOption[] {
    return data.tenantLookups
      .filter(lookup => lookup.isActive && !lookup.isDeleted)
      .sort((a, b) => a.showSequence - b.showSequence)
      .map(lookup => ({
        id: lookup.id,
        value: lookup.value,
        label: lookup.value, // Use value as label, can be customized
        isDefault: lookup.isDefault,
        value1: lookup.value1,
        value2: lookup.value2,
        showSequence: lookup.showSequence,
        source: 'tenantContext' as const,
        tenantContextId: lookup.tenantContextId
      }));
  }

  /**
   * Clear all cached lookup data
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Clear cache for specific context
   */
  clearCacheForContext(contextId?: string, tenantContextId?: string): void {
    const keysToDelete: string[] = [];
    
    for (const key of this.cache.keys()) {
      if ((contextId && key.includes(contextId)) || 
          (tenantContextId && key.includes(tenantContextId))) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key));
  }
}

// Export singleton instance
export const contextLookupService = ContextLookupService.getInstance();
