// ApplicationDetails interface matching exact DTO from parent application
export interface ApplicationDetails {
  id: string;
  name: string;
  description: string;
  version: string;
  isActive: boolean;
  isUserImported: boolean;
  isRoleAssigned: boolean;
  apiKey: string | null;
  isOnboardCompleted: boolean;
  applicationUrl: string;
  icon: string;
  createdAt: string;
  createdBy: string;
  modifiedAt: string;
  modifiedBy: string;
  isAdmin: boolean;
  tenantId: string;
}

// User interface for onboarding
export interface User {
  id: string;
  userName: string;
  firstName: string;
  lastName: string;
  email: string;
  isActive: boolean;
  emailConfirmed: boolean;
  phoneNumber: string;
  phoneNumberConfirmed: boolean;
  imageUrl: string | null;
  isDeleted: boolean;
  lastModifiedOn: string;
  createdOn: string;
  createdBy: string;
  lastModifiedBy: string;
  isMFAEnabled: boolean;
  otp: string | null;
  otpUpdatedOn: string | null;
  timeZoneInfo: string | null;
  licenseNo: string | null;
  roles: string[];
}

// Role interface for onboarding
export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions?: string[];
}

// Simplified onboarding data structure
export interface OnboardingData {
  tenantId: string;
  users: User[];
  roleAssignments: Record<string, string[]>;
}

// Props for the main Onboarding component
export interface OnboardingProps {
  applicationDetails: ApplicationDetails;
}
