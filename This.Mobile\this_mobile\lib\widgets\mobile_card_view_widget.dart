import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/card_data_model.dart';
import '../utils/constants.dart';

/// Mobile-optimized card view widget for displaying dynamic API data
class MobileCardViewWidget extends StatefulWidget {
  final List<CardDataModel> cards;
  final String? title;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final Function(CardDataModel)? onCardTap;

  const MobileCardViewWidget({
    super.key,
    required this.cards,
    this.title,
    this.isLoading = false,
    this.onRefresh,
    this.onCardTap,
  });

  @override
  State<MobileCardViewWidget> createState() => _MobileCardViewWidgetState();
}

class _MobileCardViewWidgetState extends State<MobileCardViewWidget> {
  String _searchQuery = '';
  final Set<String> _expandedCards = <String>{};

  List<CardDataModel> get _filteredCards {
    if (_searchQuery.isEmpty) return widget.cards;

    return widget.cards.where((card) {
      // Search in title, subtitle, and field values
      final searchLower = _searchQuery.toLowerCase();

      if (card.title.toLowerCase().contains(searchLower) ||
          card.subtitle.toLowerCase().contains(searchLower)) {
        return true;
      }

      // Search in field values
      return card.fields.any((field) =>
          field.key.toLowerCase().contains(searchLower) ||
          field.value.toLowerCase().contains(searchLower));
    }).toList();
  }

  void _toggleCardExpansion(String cardId) {
    setState(() {
      if (_expandedCards.contains(cardId)) {
        _expandedCards.remove(cardId);
      } else {
        _expandedCards.add(cardId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with search
        _buildHeader(),

        // Cards list
        Expanded(
          child: widget.isLoading
              ? _buildLoadingState()
              : _filteredCards.isEmpty
                  ? _buildEmptyState()
                  : _buildCardsList(),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Card(
      margin: const EdgeInsets.all(AppConstants.spacingM),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and refresh button
            Row(
              children: [
                Expanded(
                  child: Text(
                    widget.title ?? 'Data Cards',
                    style: AppTextStyles.titleLarge,
                  ),
                ),
                if (widget.onRefresh != null)
                  IconButton(
                    onPressed: widget.onRefresh,
                    icon: const Icon(LucideIcons.refreshCw),
                    tooltip: 'Refresh data',
                  ),
              ],
            ),

            const SizedBox(height: AppConstants.spacingM),

            // Search bar
            TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search cards...',
                prefixIcon: const Icon(LucideIcons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                        icon: const Icon(LucideIcons.x),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.radiusM),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.spacingM,
                  vertical: AppConstants.spacingS,
                ),
              ),
            ),

            // Results count
            if (_searchQuery.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: AppConstants.spacingS),
                child: Text(
                  '${_filteredCards.length} result${_filteredCards.length != 1 ? 's' : ''} found',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: AppConstants.spacingM),
          Text('Loading data...'),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _searchQuery.isNotEmpty ? LucideIcons.searchX : LucideIcons.inbox,
              size: AppConstants.iconSizeXXL,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: AppConstants.spacingM),
            Text(
              _searchQuery.isNotEmpty
                  ? 'No results found'
                  : 'No data available',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppConstants.spacingS),
            Text(
              _searchQuery.isNotEmpty
                  ? 'Try adjusting your search terms'
                  : 'Data will appear here when available',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardsList() {
    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefresh?.call();
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: AppConstants.spacingM),
        itemCount: _filteredCards.length,
        itemBuilder: (context, index) {
          final card = _filteredCards[index];
          return _buildCard(card, index);
        },
      ),
    );
  }

  Widget _buildCard(CardDataModel card, int index) {
    final isExpanded = _expandedCards.contains(card.id);

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.spacingM),
      elevation: AppConstants.elevationM,
      child: InkWell(
        onTap: () => widget.onCardTap?.call(card),
        borderRadius: BorderRadius.circular(AppConstants.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Card header
              _buildCardHeader(card),

              const SizedBox(height: AppConstants.spacingM),

              // Primary fields (always visible)
              _buildPrimaryFields(card),

              // Expansion toggle for secondary fields
              if (card.hasSecondaryFields) ...[
                const SizedBox(height: AppConstants.spacingS),
                _buildExpansionToggle(card, isExpanded),
              ],

              // Secondary fields (expandable)
              if (isExpanded && card.hasSecondaryFields) ...[
                const SizedBox(height: AppConstants.spacingM),
                _buildSecondaryFields(card),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardHeader(CardDataModel card) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Object type icon
        Container(
          padding: const EdgeInsets.all(AppConstants.spacingS),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.radiusS),
          ),
          child: Icon(
            LucideIcons.package,
            size: AppConstants.iconSizeM,
            color: AppColors.primary,
          ),
        ),

        const SizedBox(width: AppConstants.spacingM),

        // Title and subtitle
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                card.title,
                style: AppTextStyles.titleMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppConstants.spacingXS),
              Text(
                card.subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),

        // Action button
        IconButton(
          onPressed: () => widget.onCardTap?.call(card),
          icon: const Icon(LucideIcons.externalLink),
          iconSize: AppConstants.iconSizeS,
          tooltip: 'View details',
        ),
      ],
    );
  }

  Widget _buildPrimaryFields(CardDataModel card) {
    return Column(
      children: card.primaryFields.map((field) => _buildFieldRow(field)).toList(),
    );
  }

  Widget _buildSecondaryFields(CardDataModel card) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: AppConstants.spacingS),
        ...card.secondaryFields.map((field) => _buildFieldRow(field)),
      ],
    );
  }

  Widget _buildFieldRow(CardFieldModel field) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.spacingS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Field key
          Expanded(
            flex: 2,
            child: Text(
              field.displayKey,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: field.isHighlighted
                    ? AppColors.primary
                    : AppColors.textSecondary,
              ),
            ),
          ),

          const SizedBox(width: AppConstants.spacingM),

          // Field value
          Expanded(
            flex: 3,
            child: _buildFieldValue(field),
          ),
        ],
      ),
    );
  }

  Widget _buildFieldValue(CardFieldModel field) {
    if (field.isEmpty) {
      return Text(
        'Not specified',
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textTertiary,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    // Handle long values with expansion
    if (field.isLong) {
      return _buildExpandableValue(field);
    }

    // Regular value with type-specific styling
    return Text(
      field.displayValue,
      style: AppTextStyles.bodyMedium.copyWith(
        color: _getFieldValueColor(field.type),
        fontWeight: field.isHighlighted ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  Widget _buildExpandableValue(CardFieldModel field) {
    final isValueExpanded = _expandedCards.contains('${field.key}_value');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isValueExpanded ? field.value : field.displayValue,
          style: AppTextStyles.bodyMedium.copyWith(
            color: _getFieldValueColor(field.type),
          ),
        ),
        if (field.value.length > field.displayValue.length)
          TextButton(
            onPressed: () {
              setState(() {
                if (isValueExpanded) {
                  _expandedCards.remove('${field.key}_value');
                } else {
                  _expandedCards.add('${field.key}_value');
                }
              });
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              isValueExpanded ? 'Show less' : 'Show more',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildExpansionToggle(CardDataModel card, bool isExpanded) {
    return InkWell(
      onTap: () => _toggleCardExpansion(card.id),
      borderRadius: BorderRadius.circular(AppConstants.radiusS),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppConstants.spacingXS),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              isExpanded
                  ? 'Show less'
                  : 'Show ${card.secondaryFields.length} more field${card.secondaryFields.length != 1 ? 's' : ''}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: AppConstants.spacingXS),
            Icon(
              isExpanded ? LucideIcons.chevronUp : LucideIcons.chevronDown,
              size: AppConstants.iconSizeS,
              color: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }

  Color _getFieldValueColor(CardFieldType type) {
    switch (type) {
      case CardFieldType.email:
        return AppColors.primary;
      case CardFieldType.phone:
        return AppColors.success;
      case CardFieldType.url:
        return AppColors.secondary;
      case CardFieldType.currency:
        return AppColors.error;
      case CardFieldType.boolean:
        return AppColors.success;
      default:
        return AppColors.textPrimary;
    }
  }
}
