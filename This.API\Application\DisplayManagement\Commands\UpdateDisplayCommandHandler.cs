using Abstraction.Database.Repositories;
using Application.DisplayManagement.DTOs;
using Application.DisplayManagement.Specifications;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.DisplayManagement.Commands;

/// <summary>
/// Update Display command handler
/// </summary>
public class UpdateDisplayCommandHandler : IRequestHandler<UpdateDisplayCommand, Result<DisplayDto>>
{
    private readonly IRepository<Display> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateDisplayCommandHandler(IRepository<Display> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<DisplayDto>> Handle(UpdateDisplayCommand request, CancellationToken cancellationToken)
    {
        // Get existing display
        var existingDisplay = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (existingDisplay == null)
        {
            return Result<DisplayDto>.Failure($"Display with ID '{request.Id}' not found.");
        }

        // Check if another Display with same name already exists for this tenant
        var duplicateDisplay = await _repository.GetBySpecAsync(
            new DisplayByNameExcludingIdSpec(request.Name, request.Id), 
            cancellationToken);
        
        if (duplicateDisplay != null)
        {
            return Result<DisplayDto>.Failure($"Display with name '{request.Name}' already exists.");
        }

        // Update the display
        request.Adapt(existingDisplay);

        await _repository.UpdateAsync(existingDisplay, cancellationToken);

        var dto = existingDisplay.Adapt<DisplayDto>();

        return Result<DisplayDto>.Success(dto);
    }
}
