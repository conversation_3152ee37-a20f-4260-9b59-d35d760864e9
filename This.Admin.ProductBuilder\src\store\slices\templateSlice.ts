/**
 * Template Slice
 * Redux Toolkit slice for template state management
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import { templateApiService } from '../../services/api';
import type { 
  TemplateDto, 
  TemplatesResponse, 
  GetTemplatesParams,
  CreateTemplateRequest,
  UpdateTemplateRequest,
  GroupedTemplate
} from '../../services/api';

// State interface
export interface TemplateState {
  // Data
  templates: TemplateDto[];
  groupedTemplates: GroupedTemplate[];
  currentTemplate: TemplateDto | null;
  liveTemplates: TemplateDto[];
  
  // Pagination
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  
  // Filters
  filters: {
    searchTerm: string;
    stage: string;
    status: string;
    isActive: boolean | null;
    productId: string;
  };
  
  // Loading states
  loading: {
    list: boolean;
    create: boolean;
    update: boolean;
    delete: boolean;
    live: boolean;
  };
  
  // Error states
  errors: {
    list: string | null;
    create: string | null;
    update: string | null;
    delete: string | null;
    live: string | null;
  };
  
  // UI state
  selectedIds: string[];
  viewMode: 'list' | 'grouped';
  lastUpdated: string | null;
}

// Initial state
const initialState: TemplateState = {
  templates: [],
  groupedTemplates: [],
  currentTemplate: null,
  liveTemplates: [],
  totalCount: 0,
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  hasNextPage: false,
  hasPreviousPage: false,
  filters: {
    searchTerm: '',
    stage: 'all',
    status: 'all',
    isActive: null,
    productId: '',
  },
  loading: {
    list: false,
    create: false,
    update: false,
    delete: false,
    live: false,
  },
  errors: {
    list: null,
    create: null,
    update: null,
    delete: null,
    live: null,
  },
  selectedIds: [],
  viewMode: 'grouped',
  lastUpdated: null,
};

// Async thunks
export const fetchTemplates = createAsyncThunk(
  'templates/fetchTemplates',
  async (params: GetTemplatesParams = {}, { rejectWithValue }) => {
    try {
      const response = await templateApiService.getTemplates(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch templates');
    }
  }
);

export const fetchAllTemplates = createAsyncThunk(
  'templates/fetchAllTemplates',
  async (_, { rejectWithValue }) => {
    try {
      const templates = await templateApiService.getAllTemplates();
      return templates;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch all templates');
    }
  }
);

export const fetchTemplateById = createAsyncThunk(
  'templates/fetchTemplateById',
  async (id: string, { rejectWithValue }) => {
    try {
      const template = await templateApiService.getTemplateById(id);
      return template;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch template');
    }
  }
);

export const createTemplate = createAsyncThunk(
  'templates/createTemplate',
  async (data: CreateTemplateRequest, { rejectWithValue }) => {
    try {
      const template = await templateApiService.createTemplate(data);
      return template;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create template');
    }
  }
);

export const updateTemplate = createAsyncThunk(
  'templates/updateTemplate',
  async (data: UpdateTemplateRequest, { rejectWithValue }) => {
    try {
      const template = await templateApiService.updateTemplate(data);
      return template;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update template');
    }
  }
);

export const deleteTemplate = createAsyncThunk(
  'templates/deleteTemplate',
  async (id: string, { rejectWithValue }) => {
    try {
      await templateApiService.deleteTemplate(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete template');
    }
  }
);

export const fetchLiveTemplates = createAsyncThunk(
  'templates/fetchLiveTemplates',
  async (_, { rejectWithValue }) => {
    try {
      const templates = await templateApiService.getLiveTemplates();
      return templates;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch live templates');
    }
  }
);

export const fetchTemplatesByStage = createAsyncThunk(
  'templates/fetchTemplatesByStage',
  async (stage: string, { rejectWithValue }) => {
    try {
      const templates = await templateApiService.getTemplatesByStage(stage);
      return { stage, templates };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch templates by stage');
    }
  }
);

// Slice
const templateSlice = createSlice({
  name: 'templates',
  initialState,
  reducers: {
    // Filter actions
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.filters.searchTerm = action.payload;
    },
    setStageFilter: (state, action: PayloadAction<string>) => {
      state.filters.stage = action.payload;
    },
    setStatusFilter: (state, action: PayloadAction<string>) => {
      state.filters.status = action.payload;
    },
    setActiveFilter: (state, action: PayloadAction<boolean | null>) => {
      state.filters.isActive = action.payload;
    },
    setProductIdFilter: (state, action: PayloadAction<string>) => {
      state.filters.productId = action.payload;
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Pagination actions
    setPageNumber: (state, action: PayloadAction<number>) => {
      state.pageNumber = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.pageNumber = 1;
    },
    
    // View mode
    setViewMode: (state, action: PayloadAction<'list' | 'grouped'>) => {
      state.viewMode = action.payload;
    },
    
    // Selection actions
    setSelectedIds: (state, action: PayloadAction<string[]>) => {
      state.selectedIds = action.payload;
    },
    toggleSelection: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      const index = state.selectedIds.indexOf(id);
      if (index > -1) {
        state.selectedIds.splice(index, 1);
      } else {
        state.selectedIds.push(id);
      }
    },
    selectAll: (state) => {
      state.selectedIds = state.templates.map(template => template.id);
    },
    clearSelection: (state) => {
      state.selectedIds = [];
    },
    
    // Current template
    setCurrentTemplate: (state, action: PayloadAction<TemplateDto | null>) => {
      state.currentTemplate = action.payload;
    },
    
    // Grouped templates
    setGroupedTemplates: (state, action: PayloadAction<GroupedTemplate[]>) => {
      state.groupedTemplates = action.payload;
    },
    updateGroupedTemplates: (state) => {
      // Group templates by name using the service utility
      state.groupedTemplates = templateApiService.groupTemplatesByName(state.templates);
    },
    
    // Error clearing
    clearError: (state, action: PayloadAction<keyof TemplateState['errors']>) => {
      state.errors[action.payload] = null;
    },
    clearAllErrors: (state) => {
      state.errors = initialState.errors;
    },
    
    // Reset state
    resetState: () => initialState,
  },
  extraReducers: (builder) => {
    // Fetch templates
    builder
      .addCase(fetchTemplates.pending, (state) => {
        state.loading.list = true;
        state.errors.list = null;
      })
      .addCase(fetchTemplates.fulfilled, (state, action) => {
        state.loading.list = false;
        state.templates = action.payload.data;
        state.totalCount = action.payload.totalCount;
        state.pageNumber = action.payload.pageNumber;
        state.pageSize = action.payload.pageSize;
        state.totalPages = action.payload.totalPages;
        state.hasNextPage = action.payload.hasNextPage;
        state.hasPreviousPage = action.payload.hasPreviousPage;
        state.lastUpdated = new Date().toISOString();
        // Update grouped templates
        state.groupedTemplates = templateApiService.groupTemplatesByName(action.payload.data);
      })
      .addCase(fetchTemplates.rejected, (state, action) => {
        state.loading.list = false;
        state.errors.list = action.payload as string;
      });

    // Fetch all templates
    builder
      .addCase(fetchAllTemplates.fulfilled, (state, action) => {
        state.templates = action.payload;
        state.groupedTemplates = templateApiService.groupTemplatesByName(action.payload);
        state.lastUpdated = new Date().toISOString();
      });

    // Fetch template by ID
    builder
      .addCase(fetchTemplateById.fulfilled, (state, action) => {
        state.currentTemplate = action.payload;
        // Update in list if exists
        const index = state.templates.findIndex(template => template.id === action.payload.id);
        if (index > -1) {
          state.templates[index] = action.payload;
          state.groupedTemplates = templateApiService.groupTemplatesByName(state.templates);
        }
      });

    // Create template
    builder
      .addCase(createTemplate.pending, (state) => {
        state.loading.create = true;
        state.errors.create = null;
      })
      .addCase(createTemplate.fulfilled, (state, action) => {
        state.loading.create = false;
        state.templates.unshift(action.payload);
        state.totalCount += 1;
        state.groupedTemplates = templateApiService.groupTemplatesByName(state.templates);
      })
      .addCase(createTemplate.rejected, (state, action) => {
        state.loading.create = false;
        state.errors.create = action.payload as string;
      });

    // Update template
    builder
      .addCase(updateTemplate.pending, (state) => {
        state.loading.update = true;
        state.errors.update = null;
      })
      .addCase(updateTemplate.fulfilled, (state, action) => {
        state.loading.update = false;
        const index = state.templates.findIndex(template => template.id === action.payload.id);
        if (index > -1) {
          state.templates[index] = action.payload;
          state.groupedTemplates = templateApiService.groupTemplatesByName(state.templates);
        }
        if (state.currentTemplate?.id === action.payload.id) {
          state.currentTemplate = action.payload;
        }
      })
      .addCase(updateTemplate.rejected, (state, action) => {
        state.loading.update = false;
        state.errors.update = action.payload as string;
      });

    // Delete template
    builder
      .addCase(deleteTemplate.pending, (state) => {
        state.loading.delete = true;
        state.errors.delete = null;
      })
      .addCase(deleteTemplate.fulfilled, (state, action) => {
        state.loading.delete = false;
        state.templates = state.templates.filter(template => template.id !== action.payload);
        state.selectedIds = state.selectedIds.filter(id => id !== action.payload);
        state.totalCount = Math.max(0, state.totalCount - 1);
        state.groupedTemplates = templateApiService.groupTemplatesByName(state.templates);
        if (state.currentTemplate?.id === action.payload) {
          state.currentTemplate = null;
        }
      })
      .addCase(deleteTemplate.rejected, (state, action) => {
        state.loading.delete = false;
        state.errors.delete = action.payload as string;
      });

    // Fetch live templates
    builder
      .addCase(fetchLiveTemplates.pending, (state) => {
        state.loading.live = true;
        state.errors.live = null;
      })
      .addCase(fetchLiveTemplates.fulfilled, (state, action) => {
        state.loading.live = false;
        state.liveTemplates = action.payload;
      })
      .addCase(fetchLiveTemplates.rejected, (state, action) => {
        state.loading.live = false;
        state.errors.live = action.payload as string;
      });

    // Fetch templates by stage
    builder
      .addCase(fetchTemplatesByStage.fulfilled, (state, action) => {
        // Update templates if this is the current stage filter
        if (state.filters.stage === action.payload.stage || state.filters.stage === 'all') {
          state.templates = action.payload.templates;
          state.groupedTemplates = templateApiService.groupTemplatesByName(action.payload.templates);
          state.lastUpdated = new Date().toISOString();
        }
      });
  },
});

// Export actions
export const {
  setSearchTerm,
  setStageFilter,
  setStatusFilter,
  setActiveFilter,
  setProductIdFilter,
  clearFilters,
  setPageNumber,
  setPageSize,
  setViewMode,
  setSelectedIds,
  toggleSelection,
  selectAll,
  clearSelection,
  setCurrentTemplate,
  setGroupedTemplates,
  updateGroupedTemplates,
  clearError,
  clearAllErrors,
  resetState,
} = templateSlice.actions;

// Selectors
export const selectTemplates = (state: { templates: TemplateState }) => state.templates.templates;
export const selectGroupedTemplates = (state: { templates: TemplateState }) => state.templates.groupedTemplates;
export const selectCurrentTemplate = (state: { templates: TemplateState }) => state.templates.currentTemplate;
export const selectLiveTemplates = (state: { templates: TemplateState }) => state.templates.liveTemplates;
export const selectTemplateFilters = (state: { templates: TemplateState }) => state.templates.filters;
export const selectTemplateLoading = (state: { templates: TemplateState }) => state.templates.loading;
export const selectTemplateErrors = (state: { templates: TemplateState }) => state.templates.errors;
export const selectSelectedTemplateIds = (state: { templates: TemplateState }) => state.templates.selectedIds;
export const selectTemplateViewMode = (state: { templates: TemplateState }) => state.templates.viewMode;
export const selectTemplatePagination = (state: { templates: TemplateState }) => ({
  pageNumber: state.templates.pageNumber,
  pageSize: state.templates.pageSize,
  totalCount: state.templates.totalCount,
  totalPages: state.templates.totalPages,
  hasNextPage: state.templates.hasNextPage,
  hasPreviousPage: state.templates.hasPreviousPage,
});

// Export reducer
export default templateSlice.reducer;
