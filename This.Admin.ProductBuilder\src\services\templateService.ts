// Template API Service
import { API_CONFIG } from '../config/apiConfig';

const API_BASE_URL = API_CONFIG.PRIMARY.BASE_URL;

export interface CreateTemplateRequest {
  name: string;
  version: string;
  stage: string;
  templateJson: string; // API expects string, not object
  isActive: boolean;
}

export interface UpdateTemplateRequest {
  name: string;
  version: string;
  stage: string;
  templateJson: string; // API expects string, not object
  isActive: boolean;
}

export interface TemplateResponse {
  id: string;
  name: string; // Template name for grouping
  version: string;
  stage: string;
  templateJson: any; // Now parsed object, not string
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  isActive: boolean;
  isDeleted: boolean;
}

export interface GetTemplatesParams {
  pageNumber?: number;
  pageSize?: number;
  isActive?: boolean;
  includeDeleted?: boolean;
  searchTerm?: string;
  productId?: string;
  stage?: string;
}

export interface PaginatedTemplateResponse {
  data: TemplateResponse[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

export interface GroupedTemplate {
  name: string; // Changed from productId to name
  templateName: string;
  displayTemplate: TemplateResponse;
  allTemplates: TemplateResponse[];
  latestVersion: string;
  totalVersions: number;
}

export class TemplateService {
  private async makeRequest<T>(
    url: string, 
    options: RequestInit
  ): Promise<T> {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'accept': 'text/plain',
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText || response.statusText}`);
      }

      // Handle empty responses
      const text = await response.text();
      if (!text) {
        return {} as T;
      }

      try {
        return JSON.parse(text) as T;
      } catch {
        // If response is not JSON, return as string
        return text as unknown as T;
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Network error: ${String(error)}`);
    }
  }

  async createTemplate(data: CreateTemplateRequest): Promise<TemplateResponse> {
    return this.makeRequest<TemplateResponse>(
      `${API_BASE_URL}/templates`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      }
    );
  }

  async updateTemplate(
    id: string,
    data: UpdateTemplateRequest
  ): Promise<TemplateResponse> {
    return this.makeRequest<TemplateResponse>(
      `${API_BASE_URL}/templates/${id}`,
      {
        method: 'PUT',
        body: JSON.stringify(data),
      }
    );
  }

  async getTemplate(productId: string): Promise<TemplateResponse> {
    return this.makeRequest<TemplateResponse>(
      `${API_BASE_URL}/templates/${productId}`,
      {
        method: 'GET',
      }
    );
  }

  async getTemplates(params: GetTemplatesParams = {}): Promise<PaginatedTemplateResponse> {
    const queryParams = new URLSearchParams();

    // Add pagination parameters
    if (params.pageNumber !== undefined) {
      queryParams.append('PageNumber', params.pageNumber.toString());
    } else {
      queryParams.append('PageNumber', '1');
    }

    if (params.pageSize !== undefined) {
      queryParams.append('PageSize', params.pageSize.toString());
    } else {
      queryParams.append('PageSize', '10');
    }

    // Add filtering parameters
    if (params.isActive !== undefined) {
      queryParams.append('IsActive', params.isActive.toString());
    } else {
      queryParams.append('IsActive', 'true');
    }

    if (params.includeDeleted !== undefined) {
      queryParams.append('IncludeDeleted', params.includeDeleted.toString());
    } else {
      queryParams.append('IncludeDeleted', 'false');
    }

    // Add optional search parameters
    if (params.searchTerm) {
      queryParams.append('SearchTerm', params.searchTerm);
    }

    if (params.productId) {
      queryParams.append('ProductId', params.productId);
    }

    if (params.stage) {
      queryParams.append('Stage', params.stage);
    }

    const url = `${API_BASE_URL}/templates?${queryParams.toString()}`;
    return this.makeRequest<PaginatedTemplateResponse>(url, {
      method: 'GET',
    });
  }

  async getAllTemplates(): Promise<TemplateResponse[]> {
    return this.makeRequest<TemplateResponse[]>(
      `${API_BASE_URL}/templates`,
      {
        method: 'GET',
      }
    );
  }

  async deleteTemplate(productId: string): Promise<void> {
    return this.makeRequest<void>(
      `${API_BASE_URL}/templates/${productId}`,
      {
        method: 'DELETE',
      }
    );
  }

  // Get live templates for subscription dropdown
  async getLiveTemplates(): Promise<TemplateResponse[]> {
    try {
      const response = await this.getTemplates({
        stage: 'live',
        isActive: true,
        pageSize: 1000, // Get all live templates
        pageNumber: 1
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching live templates:', error);
      throw error;
    }
  }

  // Get templates by stage with sorting
  async getTemplatesByStage(stage: string): Promise<TemplateResponse[]> {
    try {
      const response = await this.getTemplates({
        stage: stage,
        isActive: true,
        pageSize: 1000,
        pageNumber: 1
      });

      // Sort templates by name (ascending) and then by version (descending)
      const sortedTemplates = response.data.sort((a, b) => {
        // Primary sort: Template name (alphabetical ascending)
        const nameComparison = a.name.localeCompare(b.name);
        if (nameComparison !== 0) {
          return nameComparison;
        }

        // Secondary sort: Version (descending - newest first)
        return this.compareVersions(b.version, a.version);
      });

      return sortedTemplates;
    } catch (error) {
      console.error(`Error fetching templates for stage ${stage}:`, error);
      throw error;
    }
  }

  // Helper method to compare version strings
  private compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);

    const maxLength = Math.max(v1Parts.length, v2Parts.length);

    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }

    return 0;
  }
}

// Template grouping utilities
export class TemplateGrouper {
  static groupTemplatesByProductId(templates: TemplateResponse[]): GroupedTemplate[] {
    const groups = new Map<string, TemplateResponse[]>();

    // Group templates by name field
    templates.forEach(template => {
      const templateName = template.name || 'Unnamed Template';
      const existing = groups.get(templateName) || [];
      existing.push(template);
      groups.set(templateName, existing);
    });

    // Convert groups to GroupedTemplate objects
    return Array.from(groups.entries()).map(([name, templateGroup]) => {
      // Sort templates by version (latest first)
      const sortedTemplates = templateGroup.sort((a, b) =>
        VersionManager.compareVersions(b.version, a.version)
      );

      // Use the first template for display (latest version)
      const displayTemplate = sortedTemplates[0];

      // Use the template name directly from the API
      let templateName = name;

      // Fallback to a more descriptive name if still unnamed
      if (!templateName || templateName === 'Unnamed Template') {
        const shortId = displayTemplate.id.split('-')[0];
        const createdDate = new Date(displayTemplate.createdAt);
        const dateStr = createdDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        templateName = `Template ${shortId} (${dateStr})`;
      }

      return {
        name,
        templateName,
        displayTemplate,
        allTemplates: sortedTemplates,
        latestVersion: displayTemplate.version,
        totalVersions: templateGroup.length
      };
    });
  }

  static filterGroupedTemplates(
    groupedTemplates: GroupedTemplate[],
    searchTerm: string,
    stageFilter: string,
    statusFilter: string
  ): GroupedTemplate[] {
    return groupedTemplates.filter(group => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();

        // Use template name directly from API for search
        const productName = group.templateName || 'Unnamed Template';

        const matchesName = productName.toLowerCase().includes(searchLower);
        const matchesId = group.name.toLowerCase().includes(searchLower);
        const matchesVersion = group.latestVersion.toLowerCase().includes(searchLower);

        if (!matchesName && !matchesId && !matchesVersion) {
          return false;
        }
      }

      // Stage filter
      if (stageFilter && stageFilter !== 'all') {
        if (group.displayTemplate.stage.toLowerCase() !== stageFilter.toLowerCase()) {
          return false;
        }
      }

      // Status filter (active/inactive)
      if (statusFilter && statusFilter !== 'all') {
        const isActive = group.displayTemplate.isActive;
        if (statusFilter === 'active' && !isActive) {
          return false;
        }
        if (statusFilter === 'inactive' && isActive) {
          return false;
        }
      }

      return true;
    });
  }
}

// Template utilities for dropdown formatting
export class TemplateUtils {
  // Format template for dropdown display: "Template Name (Stage) - vVersion"
  static formatTemplateForDropdown(template: TemplateResponse): string {
    return `${template.name} (${template.stage}) - v${template.version}`;
  }

  // Sort templates by name (ascending) then version (descending)
  static sortTemplatesForDropdown(templates: TemplateResponse[]): TemplateResponse[] {
    return templates.sort((a, b) => {
      // Primary sort: Template name (alphabetical ascending)
      const nameComparison = a.name.localeCompare(b.name);
      if (nameComparison !== 0) {
        return nameComparison;
      }

      // Secondary sort: Version (descending - newest first)
      return TemplateUtils.compareVersions(b.version, a.version);
    });
  }

  // Compare version strings (e.g., "2.1.0" vs "1.5.0")
  static compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);

    const maxLength = Math.max(v1Parts.length, v2Parts.length);

    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }

    return 0;
  }

  // Get badge variant for template stage
  static getStageBadgeVariant(stage: string): string {
    switch (stage.toLowerCase()) {
      case 'live':
        return 'success';
      case 'beta':
        return 'warning';
      case 'draft':
        return 'secondary';
      case 'archived':
        return 'dark';
      default:
        return 'primary';
    }
  }
}

// Export singleton instance
export const templateService = new TemplateService();

// Version management utilities
export class VersionManager {
  static getVersionByStage(stage: string, currentVersion?: string): string {
    switch (stage.toLowerCase()) {
      case 'draft':
        return currentVersion && currentVersion.startsWith('0.0.0.') 
          ? VersionManager.incrementDraftVersion(currentVersion) 
          : '0.0.0.1';
      case 'beta':
        return '1.0.0.0';
      case 'live':
        return '2.0.0.0';
      case 'archived':
        return '3.0.0.0';
      default:
        return '0.0.0.1';
    }
  }

  static incrementDraftVersion(version: string): string {
    const parts = version.split('.');
    if (parts.length !== 4 || parts[0] !== '0' || parts[1] !== '0' || parts[2] !== '0') {
      return '0.0.0.1';
    }
    
    const lastPart = parseInt(parts[3] || '0');
    return `0.0.0.${lastPart + 1}`;
  }

  static isValidVersion(version: string): boolean {
    const versionRegex = /^\d+\.\d+\.\d+(\.\d+)?$/;
    return versionRegex.test(version);
  }

  static compareVersions(v1: string, v2: string): number {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);
    
    const maxLength = Math.max(parts1.length, parts2.length);
    
    for (let i = 0; i < maxLength; i++) {
      const part1 = parts1[i] || 0;
      const part2 = parts2[i] || 0;
      
      if (part1 < part2) return -1;
      if (part1 > part2) return 1;
    }
    
    return 0;
  }
}

// Error handling utilities
export class TemplateError extends Error {
  public statusCode?: number;
  public originalError?: Error;

  constructor(
    message: string,
    statusCode?: number,
    originalError?: Error
  ) {
    super(message);
    this.name = 'TemplateError';
    this.statusCode = statusCode;
    this.originalError = originalError;
  }
}

// Template validation utilities
export class TemplateValidator {
  static validateTemplateData(data: any): string[] {
    const errors: string[] = [];

    if (!data.name) {
      errors.push('Template name is required');
    }

    if (!data.version || !VersionManager.isValidVersion(data.version)) {
      errors.push('Valid version is required');
    }

    if (!data.stage || !['draft', 'beta', 'live', 'archived'].includes(data.stage.toLowerCase())) {
      errors.push('Valid stage is required (draft, beta, live, archived)');
    }

    if (!data.templateJson) {
      errors.push('Template JSON is required');
    } else {
      try {
        // templateJson should always be a string for API calls
        if (typeof data.templateJson === 'string') {
          JSON.parse(data.templateJson);
        } else {
          errors.push('Template JSON must be a string');
        }
      } catch {
        errors.push('Template JSON must be valid JSON');
      }
    }

    return errors;
  }
  
  static validateTemplateJson(templateJson: string | any): string[] {
    const errors: string[] = [];

    try {
      const parsed = typeof templateJson === 'string' ? JSON.parse(templateJson) : templateJson;

      if (!parsed.products || !Array.isArray(parsed.products)) {
        errors.push('Template must contain a products array');
      }

      if (parsed.products && parsed.products.length === 0) {
        errors.push('Template must contain at least one product');
      }

      // Validate each product (removed ID validation)
      parsed.products?.forEach((product: any, index: number) => {
        if (!product.name) {
          errors.push(`Product ${index + 1} must have a name`);
        }

        if (!product.type) {
          errors.push(`Product ${index + 1} must have a type`);
        }
      });

    } catch (error) {
      errors.push('Invalid JSON format');
    }

    return errors;
  }
}
