/**
 * Dual API Configuration System
 *
 * PRIMARY API: Standard data operations
 * - Subscription data retrieval (GET /subscriptions)
 * - Template data operations
 * - General CRUD operations
 *
 * SECONDARY API: Tenant management operations
 * - Tenant loading operations (for dropdowns)
 * - Tenant creation and management
 * - Tenant-related API calls
 */

import { environment } from './environment';

export const API_CONFIG = {
  // Primary API - Standard data operations
  PRIMARY: {
    BASE_URL: environment.apiBaseUrl,
    ENDPOINTS: {
      // Subscription operations
      SUBSCRIPTIONS: '/subscriptions',
      SUBSCRIPTION_BY_ID: (id: string) => `/subscriptions/${id}`,
      SUBSCRIPTION_STATS: '/subscriptions/stats',
      SUBSCRIPTION_SEARCH: '/subscriptions/search',

      // Template operations
      TEMPLATES: '/templates',
      TEMPLATE_BY_ID: (id: string) => `/templates/${id}`,
      TEMPLATES_LIVE: '/templates/live',
      TEMPLATES_BY_STAGE: (stage: string) => `/templates/stage/${stage}`,

      // General data operations
      COMPREHENSIVE_ENTITY: '/comprehensive-entity',
      OBJECT_VALUES: '/objectvalues',
      FIELD_MAPPINGS: '/fieldmappings'
    }
  },

  // Secondary API - Tenant management operations
  SECONDARY: {
    BASE_URL: environment.apiLeadratBaseUrl,
    ENDPOINTS: {
      // Tenant operations
      TENANTS: '/tenants',
      TENANTS_UPSERT: '/tenants/upsert',
      TENANT_BY_ID: (id: string) => `/tenants/${id}`,
      TENANT_VALIDATE: (id: string) => `/tenants/${id}/validate`,

      // Tenant creation workflow
      CREATE_TENANT: '/tenants',
      CREATE_PRODUCT_STRUCTURE: '/comprehensive-entity/create-product-structure',
      CREATE_SUBSCRIPTION: '/subscriptions'
    }
  }
} as const;

// Helper functions to get full URLs
export const getPrimaryApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.PRIMARY.BASE_URL}${endpoint}`;
};

export const getSecondaryApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.SECONDARY.BASE_URL}${endpoint}`;
};



// API service types
export type ApiServiceType = 'primary' | 'secondary' | 'data' | 'creation';

// Get base URL by service type
export const getApiBaseUrl = (serviceType: ApiServiceType): string => {
  switch (serviceType) {
    case 'primary':
    case 'data':
      return API_CONFIG.PRIMARY.BASE_URL;
    case 'secondary':
    case 'creation':
      return API_CONFIG.SECONDARY.BASE_URL;
    default:
      throw new Error(`Unknown API service type: ${serviceType}`);
  }
};

/**
 * API Endpoint Documentation
 *
 * PRIMARY API Operations (Port 7222):
 * - GET /subscriptions - Retrieve subscription data
 * - GET /subscriptions/{id} - Get specific subscription
 * - GET /subscriptions/stats - Get subscription statistics
 * - GET /templates - Retrieve template data
 * - GET /templates/live - Get live templates for dropdowns
 * - POST/PUT/DELETE /templates - Template CRUD operations
 * - GET /comprehensive-entity - General entity operations
 * - GET /objectvalues - Object value operations
 * - GET /fieldmappings - Field mapping operations
 * - POST /tenants/upsert - Create/update tenants (MOVED TO PRIMARY)
 * - POST /comprehensive-entity/create-product-structure - Create product structure (MOVED TO PRIMARY)
 * - POST /subscriptions - Create new subscriptions (MOVED TO PRIMARY)
 *
 * SECONDARY API Operations (Port 7243):
 * - GET /tenants - Load tenant dropdown options
 * - GET /tenants/{id} - Get specific tenant
 * - POST /tenants/{id}/validate - Validate tenant connection
 */

// Default export for easy access
export default API_CONFIG;
