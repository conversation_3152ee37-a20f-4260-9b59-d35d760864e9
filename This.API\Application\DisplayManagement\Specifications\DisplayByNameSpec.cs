using Ardalis.Specification;
using Domain.Entities;

namespace Application.DisplayManagement.Specifications;

/// <summary>
/// Specification to get Display by name
/// </summary>
public class DisplayByNameSpec : Specification<Display>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DisplayByNameSpec(string name)
    {
        Query.Where(d => d.Name == name && d.IsActive && !d.IsDeleted);
        
        // Take only one record
        Query.Take(1);
    }
}

/// <summary>
/// Specification to get Display by name excluding a specific ID
/// </summary>
public class DisplayByNameExcludingIdSpec : Specification<Display>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DisplayByNameExcludingIdSpec(string name, Guid excludeId)
    {
        Query.Where(d => d.Name == name && d.Id != excludeId && d.IsActive && !d.IsDeleted);
        
        // Take only one record
        Query.Take(1);
    }
}

/// <summary>
/// Specification to get Display by ID
/// </summary>
public class DisplayByIdSpec : Specification<Display>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DisplayByIdSpec(Guid displayId)
    {
        Query.Where(d => d.Id == displayId && d.IsActive && !d.IsDeleted);

        // Take only one record
        Query.Take(1);
    }
}

/// <summary>
/// Specification to get Displays with filtering
/// </summary>
public class DisplaysWithFilterSpec : Specification<Display>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DisplaysWithFilterSpec(
        string? searchTerm = null,
        bool? isActive = null,
        string? orderBy = null)
    {
        Query.Where(d => !d.IsDeleted);

        if (isActive.HasValue)
        {
            Query.Where(d => d.IsActive == isActive.Value);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(d => d.Name.Contains(searchTerm) ||
                           d.DisplayName.Contains(searchTerm) ||
                           (d.Description != null && d.Description.Contains(searchTerm)));
        }

        // Apply ordering
        switch (orderBy?.ToLower())
        {
            case "name":
                Query.OrderBy(d => d.Name);
                break;
            case "displayname":
                Query.OrderBy(d => d.DisplayName);
                break;
            case "createdat":
                Query.OrderByDescending(d => d.CreatedAt);
                break;
            default:
                Query.OrderBy(d => d.SortOrder).ThenBy(d => d.Name);
                break;
        }
    }
}
