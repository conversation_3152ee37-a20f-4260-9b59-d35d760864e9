using Application.ActionManagement.DTOs;
using Application.DisplayActionManagement.DTOs;
using System.ComponentModel.DataAnnotations;

namespace Application.DisplayManagement.DTOs;

/// <summary>
/// Display DTO with embedded list of Actions and their DisplayAction relationships
/// </summary>
public class DisplayWithActionsDto
{
    #region Display Properties

    /// <summary>
    /// Display name - 'List', 'View', 'Update', 'Create', 'Card', or custom names
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the display
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Display name for UI
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default display for this type
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// URL route template for navigation
    /// </summary>
    [MaxLength(500)]
    public string? RouteTemplate { get; set; }

    /// <summary>
    /// Icon for the display
    /// </summary>
    [MaxLength(100)]
    public string? Icon { get; set; }

    /// <summary>
    /// Sort order for display ordering
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// Whether the display is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    #endregion

    #region Actions Collection

    /// <summary>
    /// List of actions associated with this display, each containing DisplayAction relationship data
    /// </summary>
    [Required]
    public List<ActionWithDisplayActionDto> Actions { get; set; } = new List<ActionWithDisplayActionDto>();

    #endregion
}

/// <summary>
/// Response DTO for Display with Actions after upsert operation
/// </summary>
public class DisplayWithActionsResponseDto
{
    /// <summary>
    /// Display ID
    /// </summary>
    public Guid DisplayId { get; set; }

    /// <summary>
    /// Display information
    /// </summary>
    public DisplayDto Display { get; set; } = null!;

    /// <summary>
    /// List of action results with their IDs and operation status
    /// </summary>
    public List<ActionResultDto> ActionResults { get; set; } = new List<ActionResultDto>();

    /// <summary>
    /// Indicates whether the Display was created (true) or updated (false)
    /// </summary>
    public bool DisplayWasCreated { get; set; }

    /// <summary>
    /// Summary of the operation performed
    /// </summary>
    public string OperationSummary { get; set; } = string.Empty;

    /// <summary>
    /// Total processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}

/// <summary>
/// Result information for each action in the upsert operation
/// </summary>
public class ActionResultDto
{
    /// <summary>
    /// Action ID
    /// </summary>
    public Guid ActionId { get; set; }

    /// <summary>
    /// DisplayAction ID
    /// </summary>
    public Guid DisplayActionId { get; set; }

    /// <summary>
    /// Action information
    /// </summary>
    public ActionDto Action { get; set; } = null!;

    /// <summary>
    /// DisplayAction relationship information
    /// </summary>
    public DisplayActionDto DisplayAction { get; set; } = null!;

    /// <summary>
    /// Whether the Action was created (true) or updated (false)
    /// </summary>
    public bool ActionWasCreated { get; set; }

    /// <summary>
    /// Whether the DisplayAction was created (true) or updated (false)
    /// </summary>
    public bool DisplayActionWasCreated { get; set; }
}
