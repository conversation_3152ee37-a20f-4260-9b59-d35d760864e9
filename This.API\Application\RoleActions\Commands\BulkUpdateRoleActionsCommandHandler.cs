using Abstraction.Database.Repositories;
using Application.RoleActions.DTOs;
using Application.RoleActions.Specifications;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Common.Response;

namespace Application.RoleActions.Commands;

/// <summary>
/// Bulk update role actions command handler
/// </summary>
public class BulkUpdateRoleActionsCommandHandler : IRequestHandler<BulkUpdateRoleActionsCommand, Result<List<RoleActionsResponse>>>
{
    private readonly IRepository<Domain.Entities.RoleActions> _roleActionsRepository;
    private readonly IRepository<Role> _roleRepository;
    private readonly IRepository<Domain.Entities.Action> _actionRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public BulkUpdateRoleActionsCommandHandler(
        IRepository<Domain.Entities.RoleActions> roleActionsRepository,
        IRepository<Role> roleRepository,
        IRepository<Domain.Entities.Action> actionRepository)
    {
        _roleActionsRepository = roleActionsRepository;
        _roleRepository = roleRepository;
        _actionRepository = actionRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<List<RoleActionsResponse>>> Handle(BulkUpdateRoleActionsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var responses = new List<RoleActionsResponse>();

            // Validate all roles exist
            var roleIds = request.RoleActions.Select(ra => ra.RoleId).Distinct().ToList();
            var rolesSpec = new RolesByIdsSpec(roleIds);
            var roles = await _roleRepository.ListAsync(rolesSpec, cancellationToken);
            var foundRoleIds = roles.Select(r => r.Id).ToList();
            var missingRoleIds = roleIds.Except(foundRoleIds).ToList();

            if (missingRoleIds.Any())
            {
                return Result<List<RoleActionsResponse>>.Failure($"Roles with IDs {string.Join(", ", missingRoleIds)} not found.");
            }

            // Validate all actions exist
            var allActionIds = request.RoleActions.SelectMany(ra => ra.ActionIds).Distinct().ToList();
            var actionsSpec = new ActionsByIdsSpec(allActionIds);
            var actions = await _actionRepository.ListAsync(actionsSpec, cancellationToken);
            var foundActionIds = actions.Select(a => a.Id).ToList();
            var missingActionIds = allActionIds.Except(foundActionIds).ToList();

            if (missingActionIds.Any())
            {
                return Result<List<RoleActionsResponse>>.Failure($"Actions with IDs {string.Join(", ", missingActionIds)} not found.");
            }

            // Process each role-action update
            foreach (var roleActionUpdate in request.RoleActions)
            {
                // Get existing role actions for this role
                var existingRoleActionsSpec = new RoleActionsByRoleIdSpec(roleActionUpdate.RoleId, includeInactive: true);
                var existingRoleActions = await _roleActionsRepository.ListAsync(existingRoleActionsSpec, cancellationToken);

                // Remove existing role actions that are not in the new list
                var actionsToRemove = existingRoleActions
                    .Where(ra => !roleActionUpdate.ActionIds.Contains(ra.ActionId))
                    .ToList();

                foreach (var actionToRemove in actionsToRemove)
                {
                    await _roleActionsRepository.DeleteAsync(actionToRemove, cancellationToken);
                }

                // Add new role actions
                var existingActionIds = existingRoleActions.Select(ra => ra.ActionId).ToList();
                var newActionIds = roleActionUpdate.ActionIds.Except(existingActionIds).ToList();

                foreach (var actionId in newActionIds)
                {
                    var roleAction = new Domain.Entities.RoleActions
                    {
                        RoleId = roleActionUpdate.RoleId,
                        ActionId = actionId,
                        IsActive = true
                    };

                    await _roleActionsRepository.AddAsync(roleAction, cancellationToken);
                }

                // Get updated role actions for response
                var updatedRoleActionsSpec = new RoleActionsByRoleIdSpec(roleActionUpdate.RoleId);
                var updatedRoleActions = await _roleActionsRepository.ListAsync(updatedRoleActionsSpec, cancellationToken);

                var response = new RoleActionsResponse
                {
                    RoleId = roleActionUpdate.RoleId,
                    Actions = updatedRoleActions.Adapt<List<RoleActionsDto>>()
                };

                responses.Add(response);
            }

            await _roleActionsRepository.SaveChangesAsync(cancellationToken);

            return Result<List<RoleActionsResponse>>.Success(responses);
        }
        catch (Exception ex)
        {
            return Result<List<RoleActionsResponse>>.Failure($"Error bulk updating role actions: {ex.Message}");
        }
    }
}
