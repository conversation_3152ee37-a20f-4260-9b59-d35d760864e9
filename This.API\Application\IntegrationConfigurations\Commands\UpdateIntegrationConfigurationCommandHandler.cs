using Application.IntegrationConfigurations.DTOs;
using Application.IntegrationConfigurations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Commands;

/// <summary>
/// Update integration configuration command handler
/// </summary>
public class UpdateIntegrationConfigurationCommandHandler : IRequestHandler<UpdateIntegrationConfigurationCommand, Result<ViewIntegrationConfigurationDto>>
{
    private readonly IRepository<IntegrationConfiguration> _configurationRepository;
    private readonly IReadRepository<Integration> _integrationRepository;
    private readonly IReadRepository<IntegrationApi> _integrationApiRepository;
    private readonly IReadRepository<Domain.Entities.Object> _objectRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateIntegrationConfigurationCommandHandler(
        IRepository<IntegrationConfiguration> configurationRepository,
        IReadRepository<Integration> integrationRepository,
        IReadRepository<IntegrationApi> integrationApiRepository,
        IReadRepository<Domain.Entities.Object> objectRepository)
    {
        _configurationRepository = configurationRepository;
        _integrationRepository = integrationRepository;
        _integrationApiRepository = integrationApiRepository;
        _objectRepository = objectRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ViewIntegrationConfigurationDto>> Handle(UpdateIntegrationConfigurationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing configuration
            var configuration = await _configurationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (configuration == null)
            {
                return Result<ViewIntegrationConfigurationDto>.Failure("Integration configuration not found.");
            }

            // Verify integration API exists
            var integrationApi = await _integrationApiRepository.GetByIdAsync(request.IntegrationApiId, cancellationToken);
            if (integrationApi == null)
            {
                return Result<ViewIntegrationConfigurationDto>.Failure("Integration API not found.");
            }

            // Verify object exists
            var obj = await _objectRepository.GetByIdAsync(request.ObjectId, cancellationToken);
            if (obj == null)
            {
                return Result<ViewIntegrationConfigurationDto>.Failure("Object not found.");
            }

            // Check if another configuration exists for this combination
            var existingConfigSpec = new IntegrationConfigurationByIntegrationApiObjectSpec(
                configuration.IntegrationId, request.IntegrationApiId, request.ObjectId);
            var existingConfig = await _configurationRepository.GetBySpecAsync(existingConfigSpec, cancellationToken);

            if (existingConfig != null && existingConfig.Id != request.Id)
            {
                return Result<ViewIntegrationConfigurationDto>.Failure("Configuration already exists for this combination.");
            }

            // Update configuration properties
            configuration.IntegrationApiId = request.IntegrationApiId;
            configuration.ObjectId = request.ObjectId;
            configuration.Direction = request.Direction;
            configuration.IsActive = request.IsActive;

            await _configurationRepository.UpdateAsync(configuration, cancellationToken);

            // Get integration for view DTO
            var integration = await _integrationRepository.GetByIdAsync(configuration.IntegrationId, cancellationToken);

            // Create view DTO with related entity names
            var viewDto = new ViewIntegrationConfigurationDto
            {
                Id = configuration.Id,
                IntegrationId = configuration.IntegrationId,
                IntegrationName = integration?.Name ?? string.Empty,
                IntegrationApiId = configuration.IntegrationApiId,
                IntegrationApiName = integrationApi.Name,
                ObjectId = configuration.ObjectId,
                ObjectName = obj.Name,
                Direction = configuration.Direction,
                IsActive = configuration.IsActive,
                CreatedAt = configuration.CreatedAt,
                CreatedBy = configuration.CreatedBy,
                ModifiedAt = configuration.ModifiedAt,
                ModifiedBy = configuration.ModifiedBy
            };

            return Result<ViewIntegrationConfigurationDto>.Success(viewDto);
        }
        catch (Exception ex)
        {
            return Result<ViewIntegrationConfigurationDto>.Failure($"Failed to update integration configuration: {ex.Message}");
        }
    }
}
