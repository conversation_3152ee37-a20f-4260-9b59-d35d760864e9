using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for DisplayAction entity
/// </summary>
public class DisplayActionConfig : IEntityTypeConfiguration<DisplayAction>
{
    public void Configure(EntityTypeBuilder<DisplayAction> builder)
    {
        builder.ToTable("DisplayActions", "Genp");

        // Properties
        builder.Property(e => e.ObjectId)
            .IsRequired();

        builder.Property(e => e.DisplayId)
            .IsRequired();

        builder.Property(e => e.ActionId)
            .IsRequired();

        builder.Property(e => e.AccessLevel)
            .HasMaxLength(50)
            .HasDefaultValue("Public");

        builder.Property(e => e.IsDefault)
            .HasDefaultValue(false);

        builder.Property(e => e.SortOrder)
            .HasDefaultValue(0);

        builder.Property(e => e.IsVisibleInToolbar)
            .HasDefaultValue(true);

        builder.Property(e => e.IsVisibleInContextMenu)
            .HasDefaultValue(false);

        builder.Property(e => e.IsVisibleInRowActions)
            .HasDefaultValue(false);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.Id)
            .HasDatabaseName("IX_DisplayActions_Id");

        builder.HasIndex(e => e.ObjectId)
            .HasDatabaseName("IX_DisplayActions_ObjectId");

        builder.HasIndex(e => e.DisplayId)
            .HasDatabaseName("IX_DisplayActions_DisplayId");

        builder.HasIndex(e => e.ActionId)
            .HasDatabaseName("IX_DisplayActions_ActionId");

        builder.HasIndex(e => e.IsDefault)
            .HasDatabaseName("IX_DisplayActions_IsDefault")
            .HasFilter("\"IsDefault\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_DisplayActions_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.SortOrder)
            .HasDatabaseName("IX_DisplayActions_SortOrder");

        builder.HasIndex(e => e.AccessLevel)
            .HasDatabaseName("IX_DisplayActions_AccessLevel");

        // Unique constraint for Object-Display-Action combination
        builder.HasIndex(e => new { e.ObjectId, e.DisplayId, e.ActionId })
            .IsUnique()
            .HasDatabaseName("IX_DisplayActions_ObjectId_DisplayId_ActionId");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasOne(e => e.Object)
            .WithMany()
            .HasForeignKey(e => e.ObjectId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Display)
            .WithMany(e => e.DisplayActions)
            .HasForeignKey(e => e.DisplayId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Action)
            .WithMany(e => e.DisplayActions)
            .HasForeignKey(e => e.ActionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
