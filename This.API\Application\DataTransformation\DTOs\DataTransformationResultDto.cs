namespace Application.DataTransformation.DTOs;

/// <summary>
/// Simplified data transformation result DTO
/// </summary>
public class DataTransformationResultDto
{
    /// <summary>
    /// Whether the transformation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Message describing the result (error message if failed, success message if succeeded)
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Reference ID for grouping related ObjectValue records (only set on success)
    /// </summary>
    public Guid? RefId { get; set; }

    /// <summary>
    /// Number of ObjectValue records created (only set on success)
    /// </summary>
    public int ObjectValuesCreated { get; set; }

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long ProcessingTimeMs { get; set; }
}


/// <summary>
/// Internal DTO for processing field mappings
/// </summary>
internal class FieldProcessingResult
{
    public Guid ObjectMetadataId { get; set; }
    public string? TransformedValue { get; set; }
    public Guid? ParentObjectId { get; set; }
    public string SourceField { get; set; } = string.Empty;
    public Guid RefId { get; set; }
    public Guid? UserId { get; set; }
}


