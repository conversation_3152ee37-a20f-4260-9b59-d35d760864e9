using Abstraction.Database.Repositories;
using Application.ActionManagement.DTOs;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.ActionManagement.Queries;

/// <summary>
/// Get action by ID query handler
/// </summary>
public class GetActionByIdQueryHandler : IRequestHandler<GetActionByIdQuery, Result<ActionDto>>
{
    private readonly IRepository<Domain.Entities.Action> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetActionByIdQueryHandler(IRepository<Domain.Entities.Action> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ActionDto>> Handle(GetActionByIdQuery request, CancellationToken cancellationToken)
    {
        var action = await _repository.GetByIdAsync(request.Id, cancellationToken);

        if (action == null)
        {
            return Result<ActionDto>.Failure($"Action with ID '{request.Id}' not found.");
        }

        var dto = action.Adapt<ActionDto>();

        return Result<ActionDto>.Success(dto);
    }
}
