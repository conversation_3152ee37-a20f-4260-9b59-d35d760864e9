namespace Application.FieldMappings.DTOs;

/// <summary>
/// View Field Mapping DTO
/// </summary>
public class ViewFieldMappingDto
{
    /// <summary>
    /// Field Mapping ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Integration ID this field mapping belongs to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// API name for this field mapping
    /// </summary>
    public string? ApiName { get; set; }

    /// <summary>
    /// Name of the field in the incoming JSON
    /// </summary>
    public string SourceField { get; set; } = string.Empty;

    /// <summary>
    /// Data type in the source
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// Object metadata ID this mapping targets (optional)
    /// </summary>
    public Guid? ObjectMetadataId { get; set; }

    /// <summary>
    /// Object metadata key name
    /// </summary>
    public string? ObjectMetadataKey { get; set; }

    /// <summary>
    /// User ID this mapping is associated with (optional)
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// User name
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// Role ID this mapping is associated with (optional)
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Role name
    /// </summary>
    public string? RoleName { get; set; }

    /// <summary>
    /// Optional: which object this mapping is for
    /// </summary>
    public string? TargetObjectName { get; set; }

    /// <summary>
    /// Additional notes or mapping logic
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
