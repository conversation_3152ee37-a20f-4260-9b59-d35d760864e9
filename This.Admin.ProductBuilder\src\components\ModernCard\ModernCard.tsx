import React from 'react';

interface ModernCardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}

interface ModernCardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface ModernCardBodyProps {
  children: React.ReactNode;
  className?: string;
  noPadding?: boolean;
}

export const ModernCard: React.FC<ModernCardProps> = ({ 
  children, 
  className = '', 
  hover = true 
}) => {
  return (
    <div className={`modern-card ${hover ? '' : 'no-hover'} ${className}`}>
      {children}
    </div>
  );
};

export const ModernCardHeader: React.FC<ModernCardHeaderProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`modern-card-header ${className}`}>
      {children}
    </div>
  );
};

export const ModernCardBody: React.FC<ModernCardBodyProps> = ({ 
  children, 
  className = '',
  noPadding = false
}) => {
  return (
    <div className={`modern-card-body ${noPadding ? 'p-0' : ''} ${className}`}>
      {children}
    </div>
  );
};
