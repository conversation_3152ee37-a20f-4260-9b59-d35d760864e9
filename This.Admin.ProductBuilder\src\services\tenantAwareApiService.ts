/**
 * Tenant-Aware API Service
 * Automatically includes the selected tenant in API calls
 */

import { HttpClientFactory } from './httpClient';
import { API_CONFIG } from '../config/apiConfig';
import type { ApiResponse } from './types';

export interface TenantAwareRequestConfig {
  headers?: Record<string, string>;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  overrideTenant?: string;
  skipTenantHeader?: boolean;
}

/**
 * Enhanced API service that automatically includes tenant headers
 */
export class TenantAwareApiService {
  private httpClient = HttpClientFactory.primaryClient;
  private baseUrl = API_CONFIG.PRIMARY.BASE_URL;

  /**
   * Get the currently selected tenant from localStorage
   */
  private getCurrentTenant(): string | undefined {
    try {
      return localStorage.getItem('selectedTenantId') || undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * Make a GET request with automatic tenant header
   */
  async get<T = any>(
    endpoint: string, 
    params: Record<string, any> = {},
    config: TenantAwareRequestConfig = {}
  ): Promise<T> {
    const tenant = config.skipTenantHeader 
      ? undefined 
      : (config.overrideTenant || this.getCurrentTenant());

    const response = await this.httpClient.get<T>(endpoint, {
      ...config,
      tenant
    });

    return response.data;
  }

  /**
   * Make a POST request with automatic tenant header
   */
  async post<T = any>(
    endpoint: string, 
    data?: any,
    config: TenantAwareRequestConfig = {}
  ): Promise<T> {
    const tenant = config.skipTenantHeader 
      ? undefined 
      : (config.overrideTenant || this.getCurrentTenant());

    const response = await this.httpClient.post<T>(endpoint, data, {
      ...config,
      tenant
    });

    return response.data;
  }

  /**
   * Make a PUT request with automatic tenant header
   */
  async put<T = any>(
    endpoint: string, 
    data?: any,
    config: TenantAwareRequestConfig = {}
  ): Promise<T> {
    const tenant = config.skipTenantHeader 
      ? undefined 
      : (config.overrideTenant || this.getCurrentTenant());

    const response = await this.httpClient.put<T>(endpoint, data, {
      ...config,
      tenant
    });

    return response.data;
  }

  /**
   * Make a DELETE request with automatic tenant header
   */
  async delete<T = any>(
    endpoint: string,
    config: TenantAwareRequestConfig = {}
  ): Promise<T> {
    const tenant = config.skipTenantHeader 
      ? undefined 
      : (config.overrideTenant || this.getCurrentTenant());

    const response = await this.httpClient.delete<T>(endpoint, {
      ...config,
      tenant
    });

    return response.data;
  }

  /**
   * Make a PATCH request with automatic tenant header
   */
  async patch<T = any>(
    endpoint: string, 
    data?: any,
    config: TenantAwareRequestConfig = {}
  ): Promise<T> {
    const tenant = config.skipTenantHeader 
      ? undefined 
      : (config.overrideTenant || this.getCurrentTenant());

    const response = await this.httpClient.patch<T>(endpoint, data, {
      ...config,
      tenant
    });

    return response.data;
  }

  /**
   * Get current tenant info for debugging
   */
  getCurrentTenantInfo(): { tenantId: string | undefined; source: string } {
    const tenantId = this.getCurrentTenant();
    return {
      tenantId,
      source: tenantId ? 'localStorage' : 'none'
    };
  }
}

// Create a singleton instance
export const tenantAwareApiService = new TenantAwareApiService();

/**
 * Specialized services for different API endpoints
 */
export class ComprehensiveEntityService extends TenantAwareApiService {
  private baseEndpoint = '/api/comprehensive-entity';

  /**
   * Create product structure with selected tenant
   */
  async createProductStructure(data: any): Promise<any> {
    console.log('🏗️ Creating product structure with tenant:', this.getCurrentTenantInfo());
    
    return this.post(`${this.baseEndpoint}/create-product-structure`, data);
  }

  /**
   * Get subscriptions with selected tenant
   */
  async getSubscriptions(params: Record<string, any> = {}): Promise<any> {
    console.log('📋 Getting subscriptions with tenant:', this.getCurrentTenantInfo());
    
    return this.get(`${this.baseEndpoint}/subscriptions`, params);
  }

  /**
   * Get products with selected tenant
   */
  async getProducts(params: Record<string, any> = {}): Promise<any> {
    console.log('🛍️ Getting products with tenant:', this.getCurrentTenantInfo());
    
    return this.get(`${this.baseEndpoint}/products`, params);
  }

  /**
   * Create subscription with selected tenant
   */
  async createSubscription(data: any): Promise<any> {
    console.log('🎫 Creating subscription with tenant:', this.getCurrentTenantInfo());
    
    return this.post(`${this.baseEndpoint}/subscriptions`, data);
  }

  /**
   * Update subscription with selected tenant
   */
  async updateSubscription(id: string, data: any): Promise<any> {
    console.log('✏️ Updating subscription with tenant:', this.getCurrentTenantInfo());
    
    return this.put(`${this.baseEndpoint}/subscriptions/${id}`, data);
  }

  /**
   * Delete subscription with selected tenant
   */
  async deleteSubscription(id: string): Promise<any> {
    console.log('🗑️ Deleting subscription with tenant:', this.getCurrentTenantInfo());
    
    return this.delete(`${this.baseEndpoint}/subscriptions/${id}`);
  }
}

// Export specialized service instances
export const comprehensiveEntityService = new ComprehensiveEntityService();

/**
 * Utility function to make any API call with tenant awareness
 */
export const withTenant = {
  get: <T = any>(endpoint: string, params?: Record<string, any>, config?: TenantAwareRequestConfig): Promise<T> => 
    tenantAwareApiService.get<T>(endpoint, params, config),
    
  post: <T = any>(endpoint: string, data?: any, config?: TenantAwareRequestConfig): Promise<T> => 
    tenantAwareApiService.post<T>(endpoint, data, config),
    
  put: <T = any>(endpoint: string, data?: any, config?: TenantAwareRequestConfig): Promise<T> => 
    tenantAwareApiService.put<T>(endpoint, data, config),
    
  delete: <T = any>(endpoint: string, config?: TenantAwareRequestConfig): Promise<T> => 
    tenantAwareApiService.delete<T>(endpoint, config),
    
  patch: <T = any>(endpoint: string, data?: any, config?: TenantAwareRequestConfig): Promise<T> => 
    tenantAwareApiService.patch<T>(endpoint, data, config),
};

/**
 * Generate curl command for debugging
 */
export const generateCurlCommand = (
  method: string,
  endpoint: string,
  data?: any,
  tenantId?: string
): string => {
  const fullUrl = `${API_CONFIG.PRIMARY.BASE_URL}${endpoint}`;
  const currentTenant = tenantId || localStorage.getItem('selectedTenantId');
  
  let curl = `curl --location '${fullUrl}' \\\n`;
  curl += `--header 'accept: application/json' \\\n`;
  
  if (currentTenant) {
    curl += `--header 'tenant: ${currentTenant}' \\\n`;
  }
  
  if (method !== 'GET') {
    curl += `--header 'Content-Type: application/json' \\\n`;
    
    if (data) {
      curl += `--data '${JSON.stringify(data)}'`;
    }
  }
  
  return curl;
};