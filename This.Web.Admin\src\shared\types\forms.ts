// Shared form-related types

import type { Metadata } from '../../types/metadata';

// DataTypeConfig interface removed - now using UnifiedMetadata directly
// All form configuration now comes from unified metadata structure

// MetadataLink interface removed - now using unified metadata properties directly

// Updated FormFieldConfig that includes ALL fields from backend Metadata and DataType entities
export type FormFieldConfig = {
  // Core Identity
  id: string;
  name: string;

  // DataType Core Properties
  dataTypeId?: string | null; // Reference to DataType entity
  dataTypeName?: string | null; // DataType.Name
  displayName?: string | null; // DataType.DisplayName - UI display name for data type
  category?: string | null; // DataType.Category - primitive, formatted, choice, media, temporal, complex, interactive
  uiComponent: string; // DataType.UiComponent - UI component to render

  // UI Display Properties (with override hierarchy: Metadata > DataType)
  displayLabel: string; // Field display label
  helpText: string | null; // Field help text
  fieldOrder: number | null; // Field display order
  isVisible: boolean; // Field visibility
  isReadonly: boolean; // Field readonly state

  // Validation Properties (with override hierarchy: Metadata > DataType)
  validationPattern: string | null; // Regex validation pattern
  minLength: number | null; // Minimum text length
  maxLength: number | null; // Maximum text length
  minValue: number | null; // Minimum numeric value
  maxValue: number | null; // Maximum numeric value
  isRequired: boolean | null; // Required field flag
  decimalPlaces: number | null; // Number of decimal places for numeric fields
  stepValue: number | null; // Step value for numeric inputs

  // Input Properties (with override hierarchy: Metadata > DataType)
  placeholder: string | null; // Input placeholder text
  inputType: string | null; // HTML input type (text, number, email, etc.)
  inputMask: string | null; // Input mask pattern for formatting
  htmlAttributes: string | null; // Additional HTML attributes as JSON

  // Choice/Selection Properties (with override hierarchy: Metadata > DataType)
  defaultValue?: any; // Added for form field default values
  defaultOptions: any | null; // Default options for dropdowns/multi-select (JSON or comma-separated)
  allowsMultiple: boolean | null; // Allow multiple selections
  allowsCustomOptions: boolean | null; // Allow custom user-defined options
  maxSelections: number | null; // Maximum number of selections for multi-select

  // File/Media Properties (with override hierarchy: Metadata > DataType)
  allowedFileTypes: string[] | null; // Allowed file types (comma-separated)
  maxFileSize: number | null; // Legacy property
  maxFileSizeBytes: number | null; // Maximum file size in bytes

  // Error Messages (with override hierarchy: Metadata > DataType)
  errorMessage: string | null; // General error message
  requiredErrorMessage: string | null; // Required field error message
  patternErrorMessage: string | null; // Pattern validation error message
  minLengthErrorMessage: string | null; // Minimum length error message
  maxLengthErrorMessage: string | null; // Maximum length error message
  minValueErrorMessage: string | null; // Minimum value error message
  maxValueErrorMessage: string | null; // Maximum value error message
  fileTypeErrorMessage: string | null; // File type validation error message
  fileSizeErrorMessage?: string | null; // File size validation error message (from DataType.cs)

  // Link Information (from ObjectMetadata entity)
  metadataLinkId?: string; // Link to ObjectMetadata
  isUnique?: boolean; // Unique constraint flag
  isVisibleInList?: boolean; // Show in list views
  isVisibleInEdit?: boolean; // Show in edit forms
  isVisibleInCreate?: boolean; // Show in create forms
  isVisibleInView?: boolean; // Show in view/detail forms
  isCalculated?: boolean; // Calculated field flag

  // Context References (from Metadata.cs)
  contextId?: string; // Master Context ID for lookup
  tenantContextId?: string; // TenantContext ID for lookup (takes priority over contextId)
  objectLookupId?: string; // ObjectLookup ID for lookup

  // Lookup Overrides (from Metadata.cs)
  overrideLookupType?: string | null; // Override lookup type (master, tenant, object)
  overrideMasterContextId?: string | null; // Override master Context ID
  overrideTenantContextId?: string | null; // Override TenantContext ID
  overrideObjectLookupId?: string | null; // Override ObjectLookup ID
};

// Helper function to create FormFieldConfig from Metadata (includes ALL backend fields)
export function createFormFieldConfigFromMetadata(metadata: Metadata): FormFieldConfig {
  return {
    // Core Identity
    id: metadata.id || '',
    name: metadata.name || '',

    // DataType Core Properties
    dataTypeId: metadata.dataTypeId || null,
    dataTypeName: metadata.dataTypeName || null,
    displayName: metadata.displayName || null,
    category: metadata.category || null,
    uiComponent: metadata.uiComponent || 'ThisText',

    // UI Display Properties (with override hierarchy: Metadata > DataType)
    displayLabel: metadata.displayLabel || metadata.name || '',
    helpText: metadata.helpText || null,
    fieldOrder: metadata.fieldOrder || null,
    isVisible: metadata.isVisible ?? true,
    isReadonly: metadata.isReadonly ?? false,

    // Validation Properties (with override hierarchy: Metadata > DataType)
    validationPattern: metadata.validationPattern || null,
    minLength: metadata.minLength || null,
    maxLength: metadata.maxLength || null,
    minValue: metadata.minValue || null,
    maxValue: metadata.maxValue || null,
    isRequired: metadata.isRequired ?? false,
    decimalPlaces: metadata.decimalPlaces || null,
    stepValue: metadata.stepValue || null,

    // Input Properties (with override hierarchy: Metadata > DataType)
    placeholder: metadata.placeholder || null,
    inputType: metadata.inputType || null,
    inputMask: metadata.inputMask || null,
    htmlAttributes: metadata.htmlAttributes || null,

    // Choice/Selection Properties (with override hierarchy: Metadata > DataType)
    defaultOptions: metadata.defaultOptions || null,
    allowsMultiple: metadata.allowsMultiple || null,
    allowsCustomOptions: metadata.allowsCustomOptions || null,
    maxSelections: metadata.maxSelections || null,

    // File/Media Properties (with override hierarchy: Metadata > DataType)
    allowedFileTypes: metadata.allowedFileTypes ? metadata.allowedFileTypes.split(',') : null,
    maxFileSizeBytes: metadata.maxFileSizeBytes || null,
    maxFileSize: metadata.maxFileSizeBytes || null, // Legacy property

    // Error Messages (with override hierarchy: Metadata > DataType)
    errorMessage: metadata.errorMessage || null,
    requiredErrorMessage: metadata.requiredErrorMessage || null,
    patternErrorMessage: metadata.patternErrorMessage || null,
    minLengthErrorMessage: metadata.minLengthErrorMessage || null,
    maxLengthErrorMessage: metadata.maxLengthErrorMessage || null,
    minValueErrorMessage: metadata.minValueErrorMessage || null,
    maxValueErrorMessage: metadata.maxValueErrorMessage || null,
    fileTypeErrorMessage: metadata.fileTypeErrorMessage || null,
    fileSizeErrorMessage: metadata.fileSizeErrorMessage || null,

    // Link Information (from ObjectMetadata entity)
    metadataLinkId: metadata.metadataLinkId || undefined,
    isUnique: metadata.isUnique || undefined,
    isVisibleInList: metadata.isVisibleInList ?? true,
    isVisibleInEdit: metadata.isVisibleInEdit ?? true,
    isVisibleInCreate: metadata.isVisibleInCreate ?? true,
    isVisibleInView: metadata.isVisibleInView ?? true,
    isCalculated: metadata.isCalculated ?? false,

    // Context References (from Metadata.cs)
    contextId: metadata.contextId || undefined,
    tenantContextId: metadata.tenantContextId || undefined,
    objectLookupId: metadata.objectLookupId || undefined
  };
}
