using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertContext;

/// <summary>
/// Handler for UpsertContextCommand
/// </summary>
public class UpsertContextCommandHandler : IRequestHandler<UpsertContextCommand, Result<Guid>>
{
    private readonly IRepository<Domain.Entities.Context> _contextRepository;
    private readonly ILogger<UpsertContextCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertContextCommandHandler(
        IRepository<Domain.Entities.Context> contextRepository,
        ILogger<UpsertContextCommandHandler> logger)
    {
        _contextRepository = contextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<Guid>> Handle(UpsertContextCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Upserting context: {Name}", request.Name);

            Domain.Entities.Context? context;

            if (request.Id.HasValue && request.Id.Value != Guid.Empty)
            {
                // Update existing context
                context = await _contextRepository.GetByIdAsync(request.Id.Value, cancellationToken);
                if (context == null)
                {
                    return Result<Guid>.Failure($"Context with ID {request.Id.Value} not found");
                }

                context.Name = request.Name;
                context.Description = request.Description;
                context.Category = request.Category;
                context.IsActive = request.IsActive;

                await _contextRepository.UpdateAsync(context, cancellationToken);
                _logger.LogInformation("Updated context with ID: {Id}", context.Id);
            }
            else
            {
                // Create new context
                context = new Domain.Entities.Context
                {
                    Id = Guid.NewGuid(),
                    Name = request.Name,
                    Description = request.Description,
                    Category = request.Category,
                    IsActive = request.IsActive,
                    IsDeleted = false
                };

                await _contextRepository.AddAsync(context, cancellationToken);
                _logger.LogInformation("Created new context with ID: {Id}", context.Id);
            }

            return Result<Guid>.Success(context.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while upserting context: {Name}", request.Name);
            return Result<Guid>.Failure($"Error upserting context: {ex.Message}");
        }
    }
}
