import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../utils/constants.dart';

/// Compact data table widget optimized for mobile view
class CompactDataTableWidget extends StatefulWidget {
  final List<Map<String, dynamic>> data;
  final String? title;

  const CompactDataTableWidget({
    super.key,
    required this.data,
    this.title,
  });

  @override
  State<CompactDataTableWidget> createState() => _CompactDataTableWidgetState();
}

class _CompactDataTableWidgetState extends State<CompactDataTableWidget> {
  String _searchQuery = '';
  final ScrollController _scrollController = ScrollController();

  List<Map<String, dynamic>> get _filteredData {
    print('Filtering compact data with query: $_searchQuery');
    print('Original compact data: ${widget.data}');

    if (widget.data.isEmpty) {
      print('Compact data is empty');
      return [];
    }

    if (_searchQuery.isEmpty) {
      print('No search query, returning all compact data');
      return widget.data;
    }

    final filtered = widget.data.where((row) {
      return row.values.any((value) {
        if (value == null) return false;
        return value.toString().toLowerCase().contains(_searchQuery.toLowerCase());
      });
    }).toList();

    print('Filtered compact data: $filtered');
    return filtered;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _buildDataList(),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.spacingM),
      child: TextField(
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: 'Search data...',
          prefixIcon: const Icon(LucideIcons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                  icon: const Icon(LucideIcons.x),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusM),
          ),
        ),
      ),
    );
  }

  Widget _buildDataList() {
    final data = _filteredData;

    if (data.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              LucideIcons.inbox,
              size: AppConstants.iconSizeXL,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppConstants.spacingM),
            Text(
              _searchQuery.isEmpty
                  ? 'No data available'
                  : 'No results found for "$_searchQuery"',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.spacingM,
        vertical: AppConstants.spacingS,
      ),
      itemCount: data.length,
      itemBuilder: (context, index) {
        final item = data[index];
        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.spacingM),
          child: ExpansionTile(
            title: Text(
              item.values.first.toString(),
              style: AppTextStyles.titleMedium,
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(AppConstants.spacingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: item.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: AppConstants.spacingS),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Text(
                              entry.key,
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: AppColors.textSecondary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          const SizedBox(width: AppConstants.spacingM),
                          Expanded(
                            flex: 3,
                            child: Text(
                              entry.value?.toString() ?? '',
                              style: AppTextStyles.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}