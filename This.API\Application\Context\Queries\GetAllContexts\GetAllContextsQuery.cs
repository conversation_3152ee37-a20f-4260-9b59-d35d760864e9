using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetAllContexts;

/// <summary>
/// Query to get all contexts
/// </summary>
public class GetAllContextsQuery : IRequest<Result<List<ContextDto>>>
{
    /// <summary>
    /// Whether to include inactive contexts
    /// </summary>
    public bool IncludeInactive { get; set; } = false;

    /// <summary>
    /// Category filter
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Search term for name
    /// </summary>
    public string? SearchTerm { get; set; }
}
