import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/field_metadata.dart';

/// Service for fetching and managing object metadata for dynamic form generation
class MetadataService {
  final String baseUrl;
  final String tenant;

  // Cache for metadata to avoid repeated API calls
  final Map<String, List<FieldMetadata>> _metadataCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 10);

  MetadataService({required this.baseUrl, required this.tenant});

  /// Fetch metadata for a specific object type with caching
  Future<List<FieldMetadata>> fetchMetadata(String objectType) async {
    // Check cache first
    if (_isMetadataCached(objectType)) {
      return _metadataCache[objectType]!;
    }

    try {
      // Try to fetch from comprehensive-entity API first
      final metadata = await _fetchFromComprehensiveEntity(objectType);
      if (metadata.isNotEmpty) {
        print('Metadata fetched from comprehensive-entity API: ${metadata.length} fields');
        _cacheMetadata(objectType, metadata);
        return metadata;
      }
    } catch (e) {
      print('Failed to fetch from comprehensive-entity API: $e');
    }

    try {
      // Fallback to objects API
      final metadata = await _fetchFromObjectsAPI(objectType);
      if (metadata.isNotEmpty) {
        _cacheMetadata(objectType, metadata);
        return metadata;
      }
    } catch (e) {
      print('Failed to fetch from objects API: $e');
    }

    return [];
  }

  /// Fetch metadata from comprehensive-entity API for a specific object type
  Future<List<FieldMetadata>> _fetchFromComprehensiveEntity(String objectType) async {
    final objectId = await _getObjectIdByName(objectType);
    if (objectId == null) {
      throw Exception('Object not found: $objectType');
    }

    final url =
        'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net/api/comprehensive-entity/8c38b02f-4c1e-4416-a1bf-c705d1ac8761?onlyVisibleMetadata=true&onlyActiveMetadata=true&pageSize=100&pageNumber=1';

    final response = await http.get(
      Uri.parse(url),
      headers: {
        'accept': 'application/json',
        'tenant': tenant,
      },
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> responseData = json.decode(response.body);

      print('API response: ${response.body}');

      if (responseData['succeeded'] != true) {
        throw Exception('API returned error: ${responseData['message']}');
      }

      final Map<String, dynamic>? data = responseData['data'];
      if (data == null) {
        throw Exception('No data found in API response');
      }

      final List<dynamic> products = data['products'] ?? [];
      if (products.isEmpty) {
        print('No products found in response');
        return [];
      }

      List<FieldMetadata> objectMetadata = [];
      final Set<String> seenFields = {};

      for (final product in products) {
        final List<dynamic> rootObjects = product['rootObjects'] ?? [];
        print('Root object count: ${rootObjects.length}');

        for (final object in rootObjects) {
          // Debug: Print the entire object structure to understand the API response
          print('Complete root object structure: ${jsonEncode(object)}');
          
          // Try different possible field names for the object type
          final objType = _getObjectType(object);
          print('Extracted object type: $objType');

          if (objType == null || objType.toLowerCase() != objectType.toLowerCase()) {
            print('Skipping object - type mismatch: $objType vs $objectType');
            continue;
          }

          final List<dynamic> metadata = object['metadata'] ?? [];
          print('Metadata count for $objType: ${metadata.length}');
          print('Metadata for $objType: ${jsonEncode(metadata)}');

          for (final metadataWrapper in metadata) {
            // Handle double-nested metadata structure
            final field = metadataWrapper['metadata'] ?? metadataWrapper;
            
            final fieldName = field['name']?.toString().toLowerCase();
            final category = field['category']?.toString() ?? '';

            if (fieldName == null ||
                seenFields.contains('${category}_$fieldName') ||
                !(field['isVisible'] ?? true)) {
              continue;
            }

            seenFields.add('${category}_$fieldName');
            try {
              objectMetadata.add(FieldMetadata.fromJson(field));
              print('Added field: $fieldName');
            } catch (e) {
              print('Error parsing field $fieldName: $e');
              print('Field data: ${jsonEncode(field)}');
            }
          }

          // Recursively process child objects
          final childMetadata = _extractMetadataFromSpecificObject(
            object['childObjects'] ?? [],
            objectType,
            seenFields,
          );
          objectMetadata.addAll(childMetadata);
        }
      }

      if (objectMetadata.isEmpty) {
        print('No metadata found for object type: $objectType');
        print('Available object types in response:');
        _debugAvailableObjectTypes(products);
        return [];
      }

      print('Metadata fields extracted for $objectType: ${objectMetadata.length}');
      return _deduplicateFields(objectMetadata);
    } else {
      throw Exception('Failed to load metadata: ${response.statusCode} - ${response.body}');
    }
  }

  /// Extract object type from various possible field names in the API response
  String? _getObjectType(Map<String, dynamic> object) {
    // Try common field names that might contain the object type
    final possibleFields = ['name', 'objectType', 'type', 'entityName', 'tableName'];
    
    for (final field in possibleFields) {
      final value = object[field]?.toString();
      if (value != null && value.isNotEmpty) {
        print('Found object type in field "$field": $value');
        return value;
      }
    }
    
    // If no standard field found, check if there's an ID or other identifier
    if (object.containsKey('id')) {
      print('Object has ID but no clear type field: ${object['id']}');
    }
    
    return null;
  }

  /// Debug helper to show available object types in the response
  void _debugAvailableObjectTypes(List<dynamic> products) {
    for (final product in products) {
      final List<dynamic> rootObjects = product['rootObjects'] ?? [];
      for (final object in rootObjects) {
        final objType = _getObjectType(object);
        print('Available root object type: $objType');
        
        // Also check child objects
        final List<dynamic> childObjects = object['childObjects'] ?? [];
        for (final child in childObjects) {
          final childType = _getObjectType(child);
          print('Available child object type: $childType');
        }
      }
    }
  }

  List<FieldMetadata> _extractMetadataFromSpecificObject(
    List<dynamic> childObjects,
    String objectType,
    Set<String> seenFields,
  ) {
    List<FieldMetadata> extracted = [];

    for (final child in childObjects) {
      // Debug: Print the entire child object structure
      print('Complete child object structure: ${jsonEncode(child)}');
      
      final childType = _getObjectType(child);
      print('Extracted child object type: $childType');

      if (childType?.toLowerCase() != objectType.toLowerCase()) {
        // Still recursively check children
        final deeperMetadata = _extractMetadataFromSpecificObject(
          child['childObjects'] ?? [],
          objectType,
          seenFields,
        );
        extracted.addAll(deeperMetadata);
        continue;
      }

      final List<dynamic> metadata = child['metadata'] ?? [];
      print('Child metadata count for $childType: ${metadata.length}');

      for (final metadataWrapper in metadata) {
        // Handle double-nested metadata structure
        final field = metadataWrapper['metadata'] ?? metadataWrapper;
        
        final fieldName = field['name']?.toString().toLowerCase();
        final category = field['category']?.toString() ?? '';

        if (fieldName == null ||
            seenFields.contains('${category}_$fieldName') ||
            !(field['isVisible'] ?? true)) {
          continue;
        }

        seenFields.add('${category}_$fieldName');
        try {
          extracted.add(FieldMetadata.fromJson(field));
          print('Added child field: $fieldName');
        } catch (e) {
          print('Error parsing child field $fieldName: $e');
          print('Field data: ${jsonEncode(field)}');
        }
      }

      // Process nested children as well
      final deeperMetadata = _extractMetadataFromSpecificObject(
        child['childObjects'] ?? [],
        objectType,
        seenFields,
      );
      extracted.addAll(deeperMetadata);
    }

    return extracted;
  }

  /// Deduplicate fields to prevent duplicate form inputs
  /// This handles cases like 'ServingSize' and 'servingSize' appearing as separate fields
  /// Deduplicate fields based on name and category
  List<FieldMetadata> _deduplicateFields(List<FieldMetadata> metadata) {
    final Map<String, FieldMetadata> uniqueFields = {};
    final Set<String> seenNormalizedNames = {};

    for (final field in metadata) {
      // Create normalized versions of the field name for comparison
      final normalizedName = field.name.toLowerCase().trim();
      final normalizedDisplayLabel = field.displayLabel.toLowerCase().trim();

      // Check if we've already seen this field (by name or display label)
      final isDuplicate = seenNormalizedNames.contains(normalizedName) ||
                         seenNormalizedNames.contains(normalizedDisplayLabel);

      if (!isDuplicate) {
        // This is a unique field, add it
        uniqueFields[field.name] = field;
        seenNormalizedNames.add(normalizedName);
        seenNormalizedNames.add(normalizedDisplayLabel);
      } else {
        // This is a duplicate, decide which one to keep
        final existingField = uniqueFields.values.firstWhere(
          (existing) =>
            existing.name.toLowerCase() == normalizedName ||
            existing.displayLabel.toLowerCase() == normalizedDisplayLabel,
          orElse: () => field,
        );

        // Prefer the field with better naming convention (camelCase over PascalCase)
        if (_isBetterFieldName(field.name, existingField.name)) {
          // Replace the existing field with this better one
          uniqueFields.remove(existingField.name);
          uniqueFields[field.name] = field;
        }

        print('Duplicate field detected: "${field.name}" (${field.displayLabel}) - keeping "${uniqueFields[field.name]?.name ?? existingField.name}"');
      }
    }

    return uniqueFields.values.toList();
  }

  /// Determine if one field name is better than another
  /// Prefers camelCase over PascalCase, and more descriptive names
  bool _isBetterFieldName(String newName, String existingName) {
    // Prefer camelCase over PascalCase
    final newIsCamelCase = newName.isNotEmpty && newName[0].toLowerCase() == newName[0];
    final existingIsCamelCase = existingName.isNotEmpty && existingName[0].toLowerCase() == existingName[0];

    if (newIsCamelCase && !existingIsCamelCase) {
      return true; // New name is camelCase, existing is PascalCase
    }

    if (!newIsCamelCase && existingIsCamelCase) {
      return false; // Existing name is camelCase, new is PascalCase
    }

    // If both are same case style, prefer the shorter or more standard name
    return newName.length <= existingName.length;
  }

  /// Check if metadata is cached and not expired
  bool _isMetadataCached(String objectType) {
    if (!_metadataCache.containsKey(objectType)) return false;

    final timestamp = _cacheTimestamps[objectType];
    if (timestamp == null) return false;

    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  /// Cache metadata with timestamp
  void _cacheMetadata(String objectType, List<FieldMetadata> metadata) {
    _metadataCache[objectType] = metadata;
    _cacheTimestamps[objectType] = DateTime.now();
  }

  /// Clear cache for a specific object type
  void clearCache(String objectType) {
    _metadataCache.remove(objectType);
    _cacheTimestamps.remove(objectType);
  }

  /// Clear all cached metadata
  void clearAllCache() {
    _metadataCache.clear();
    _cacheTimestamps.clear();
  }

  /// Get object ID by name (mock implementation)
  Future<String?> _getObjectIdByName(String objectType) async {
    // Mock object IDs for testing
    final mockObjectIds = {
      'mealkit': '29d658dc-cdc7-4859-b950-f5f69e445ac2',
      'supplier': '12345678-1234-1234-1234-123456789012',
      'product': '87654321-4321-4321-4321-210987654321',
    };

    return mockObjectIds[objectType.toLowerCase()];
  }

  /// Fetch metadata from objects API (fallback)
  Future<List<FieldMetadata>> _fetchFromObjectsAPI(String objectType) async {
    // This would be implemented to fetch from /api/objects/{id}/metadata
    // For now, return empty list to trigger mock data
    return [];
  }  
}