import React, { useState } from "react";
import SearchBar from "./SearchBar";
import Filters from "./Filters";
import DataTable from "./DataTable";
import Pagination from "./Pagination";
import PageHeader from "./PageHeader";
import { Column } from "../../types";

interface EntityListProps<T> {
  title: string;
  subtitle?: string;
  data: T[];
  columns: Column<T>[];
  filterOptions: {
    field: string;
    label: string;
    type: "text" | "select" | "boolean" | "date";
    options?: { value: string; label: string }[];
  }[];
  onAdd?: () => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  addLabel?: string;
}

function EntityList<T extends { id: string }>({
  title,
  subtitle,
  data,
  columns,
  filterOptions,
  onAdd,
  onEdit,
  onDelete,
  addLabel,
}: EntityListProps<T>) {
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredData, setFilteredData] = useState<T[]>(data);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilters, setActiveFilters] = useState<Record<string, unknown>>(
    {}
  );

  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredData.slice(startIndex, endIndex);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterData(query, activeFilters);
  };

  const handleFilter = (filters: Record<string, unknown>) => {
    setActiveFilters(filters);
    filterData(searchQuery, filters);
  };

  const filterData = (query: string, filters: Record<string, unknown>) => {
    let result = [...data];

    // Apply search
    if (query) {
      const lowercasedQuery = query.toLowerCase();
      result = result.filter((item) => {
        return Object.values(item).some((value) => {
          if (typeof value === "string") {
            return value.toLowerCase().includes(lowercasedQuery);
          }
          return false;
        });
      });
    }

    // Apply filters
    if (Object.keys(filters).length > 0) {
      result = result.filter((item) => {
        return Object.entries(filters).every(([key, value]) => {
          if (!value) return true; // Skip empty filters

          const itemValue = item[key as keyof T];

          if (typeof value === "boolean") {
            return itemValue === value;
          }

          if (typeof itemValue === "string" && typeof value === "string") {
            return itemValue.toLowerCase().includes(value.toLowerCase());
          }

          return itemValue === value;
        });
      });
    }

    setFilteredData(result);
    setCurrentPage(1); // Reset to first page when filtering
  };

  return (
    <div>
      <PageHeader
        title={title}
        subtitle={subtitle}
        onAdd={onAdd}
        addLabel={addLabel}
      />

      <div className="bg-white shadow-sm rounded-lg p-6 mb-8">
        <div className="flex flex-wrap gap-4 justify-between mb-6">
          <SearchBar onSearch={handleSearch} placeholder="Type to search" />
          <div className="flex space-x-2">
            <Filters onFilter={handleFilter} filterOptions={filterOptions} />
            <select className="btn btn-secondary" defaultValue="10">
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>

        <DataTable
          data={currentItems}
          columns={columns}
          onEdit={onEdit}
          onDelete={onDelete}
        />

        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>
    </div>
  );
}

export default EntityList;
