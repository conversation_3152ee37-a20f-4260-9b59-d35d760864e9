/**
 * Subscriptions Page (Refactored)
 * Example of how the Subscriptions page would look using the new decomposed components
 */

import React, { useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { 
  SubscriptionTable, 
  StatsGrid, 
  createSubscriptionStats 
} from '../components';
import { useSubscriptions, useTenants, useUI } from '../store/hooks';
import type { SubscriptionDto } from '../services/api';

export const SubscriptionsRefactored: React.FC = () => {
  const {
    subscriptions,
    summary,
    loading,
    loadSubscriptions,
    loadStats
  } = useSubscriptions();

  const { loadActiveTenants, loadDropdownOptions } = useTenants();
  const { showModal, showNotification } = useUI();

  // Load data on component mount
  useEffect(() => {
    loadSubscriptions();
    loadStats();
    loadActiveTenants();
    loadDropdownOptions();
  }, [loadSubscriptions, loadStats, loadActiveTenants, loadDropdownOptions]);

  // Handle subscription actions
  const handleViewSubscription = (subscription: SubscriptionDto) => {
    showModal('viewSubscription', { subscription });
  };

  const handleEditSubscription = (subscription: SubscriptionDto) => {
    showModal('editSubscription', { subscription });
  };

  const handleDeleteSubscription = async (subscription: SubscriptionDto) => {
    try {
      // This would be handled by the Redux action
      showNotification({
        type: 'success',
        title: 'Success',
        message: `Subscription ${subscription.id} deleted successfully`
      });
      
      // Refresh data
      loadSubscriptions();
      loadStats();
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to delete subscription'
      });
    }
  };

  const handleAddSubscription = () => {
    showModal('addSubscription');
  };

  // Handle stats card clicks for filtering
  const handleStatsClick = (type: string) => {
    // This would update the filters in Redux
    switch (type) {
      case 'active':
        // setStatusFilter('active');
        break;
      case 'expired':
        // setStatusFilter('expired');
        break;
      case 'expiring':
        // setStatusFilter('expiring');
        break;
      default:
        // setStatusFilter('all');
    }
  };

  // Prepare stats data
  const statsData = summary ? {
    total: summary.activeSubscriptions + summary.expiredSubscriptions,
    active: summary.activeSubscriptions,
    expired: summary.expiredSubscriptions,
    expiring: summary.expiringIn30Days
  } : {
    total: subscriptions.length,
    active: subscriptions.filter(s => s.status === 'active').length,
    expired: subscriptions.filter(s => s.isExpired).length,
    expiring: subscriptions.filter(s => s.daysUntilExpiration !== null && s.daysUntilExpiration <= 30).length
  };

  const statsItems = createSubscriptionStats(statsData, handleStatsClick);

  return (
    <div className="d-flex flex-column min-vh-100">
      {/* Main Content */}
      <main className="flex-grow-1" style={{ padding: '0.25rem 1rem 0.25rem 1rem' }}>
        <div style={{ maxWidth: 'none', width: '100%' }}>
          
          {/* Stats Cards */}
          <StatsGrid
            items={statsItems}
            loading={loading.stats}
            className="mb-3"
            responsive={{ xs: 12, sm: 6, md: 3 }}
          />

          {/* Subscriptions Table */}
          <SubscriptionTable
            onViewSubscription={handleViewSubscription}
            onEditSubscription={handleEditSubscription}
            onDeleteSubscription={handleDeleteSubscription}
            onAddSubscription={handleAddSubscription}
            maxHeight="calc(100vh - 280px)"
          />

        </div>
      </main>
    </div>
  );
};

export default SubscriptionsRefactored;
