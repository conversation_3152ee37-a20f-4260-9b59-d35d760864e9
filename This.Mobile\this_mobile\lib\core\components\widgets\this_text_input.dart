import 'package:flutter/material.dart';
import 'package:this_mobile/core/theme/color_palette.dart';
import 'package:this_mobile/core/theme/text_styles.dart';

/// A customizable text input widget following the 'this_componentName_relatedTo' naming convention
/// This widget handles text input with validation, theming, and various configuration options
class ThisTextInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final String value;
  final ValueChanged<String> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final int? maxLength;
  final int? minLength;
  final String? pattern;
  final TextInputType keyboardType;
  final bool showCharacterCount;
  final bool allowClear;
  final bool autoFocus;
  final String? Function(String)? customValidation;

  const ThisTextInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.maxLength,
    this.minLength,
    this.pattern,
    this.keyboardType = TextInputType.text,
    this.showCharacterCount = false,
    this.allowClear = false,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisTextInput> createState() => _ThisTextInputState();
}

class _ThisTextInputState extends State<ThisTextInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
    _focusNode = FocusNode();

    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void didUpdateWidget(ThisTextInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  List<String> _validateValue(String value) {
    final errors = <String>[];

    // Required validation
    if (widget.required && value.trim().isEmpty) {
      errors.add('${widget.label} is required');
      return errors; // Return early for required field
    }

    // Only validate format if field has content or is required
    if (widget.required || value.trim().isNotEmpty) {
      // Minimum length validation
      if (widget.minLength != null && value.length < widget.minLength!) {
        errors.add('Minimum ${widget.minLength} characters required');
      }

      // Pattern validation
      if (widget.pattern != null && value.isNotEmpty) {
        final regex = RegExp(widget.pattern!);
        if (!regex.hasMatch(value)) {
          errors.add(_getPatternErrorMessage());
        }
      }

      // Custom validation
      if (widget.customValidation != null) {
        final customError = widget.customValidation!(value);
        if (customError != null) {
          errors.add(customError);
        }
      }

      // Maximum length validation
      if (widget.maxLength != null && value.length > widget.maxLength!) {
        errors.add('Maximum ${widget.maxLength} characters allowed');
      }
    }

    return errors;
  }

  String _getPatternErrorMessage() {
    if (widget.id.toLowerCase().contains('name')) {
      return 'Only letters and spaces allowed';
    }
    if (widget.id.toLowerCase().contains('phone')) {
      return 'Enter a valid phone number';
    }
    if (widget.id.toLowerCase().contains('email')) {
      return 'Enter a valid email address';
    }
    return 'Invalid format';
  }

  void _handleChange(String value) {
    widget.onChanged(value);

    // Real-time validation
    final errors = _validateValue(value);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state after build completes
    if (widget.onValidation != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onValidation?.call(errors);
      });
    }
  }

  void _handleBlur() {
    // Validate on blur for final check
    final errors = _validateValue(widget.value);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state after build completes
    if (widget.onValidation != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onValidation?.call(errors);
      });
    }
  }

  void _clearInput() {
    _controller.clear();
    widget.onChanged('');
    setState(() {
      _errors = [];
    });

    // Notify parent of validation state after build completes
    if (widget.onValidation != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onValidation?.call([]);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Input Field
        Stack(
          children: [
            TextFormField(
              controller: _controller,
              focusNode: _focusNode,
              enabled: !widget.disabled,
              readOnly: widget.readOnly,
              keyboardType: widget.keyboardType,
              maxLength: widget.maxLength,
              onChanged: _handleChange,
              onFieldSubmitted: (_) => _handleBlur(),
              onTapOutside: (_) => _handleBlur(),
              decoration: InputDecoration(
                hintText: widget.placeholder,
                hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                  color: ColorPalette.placeHolderTextColor,
                ),
                counterText: widget.showCharacterCount ? null : '',
                suffixIcon: widget.allowClear && widget.value.isNotEmpty && !widget.disabled && !widget.readOnly
                    ? IconButton(
                        icon: const Icon(Icons.clear, size: 20),
                        onPressed: _clearInput,
                        color: ColorPalette.placeHolderTextColor,
                      )
                    : null,
                errorText: hasErrors ? _errors.first : null,
                errorStyle: LexendTextStyles.lexend12Regular.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
              style: LexendTextStyles.lexend14Regular.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
          ],
        ),

        // Character count (if enabled and not shown by TextField)
        if (widget.showCharacterCount && widget.maxLength != null && !widget.disabled && !widget.readOnly)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                '${widget.value.length} / ${widget.maxLength}',
                style: LexendTextStyles.lexend12Regular.copyWith(
                  color: widget.value.length > (widget.maxLength! * 0.8) ? (widget.value.length >= widget.maxLength! ? const Color(0xFFC73E1D) : Colors.orange) : ColorPalette.placeHolderTextColor,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
