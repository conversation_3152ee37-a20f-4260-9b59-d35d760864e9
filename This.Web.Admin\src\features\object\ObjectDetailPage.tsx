import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { AgGridReact } from 'ag-grid-react';
import type {
  ColDef,
  GridApi
} from 'ag-grid-community';
import { ArrowLeft, RefreshCw, Search, Download, Plus, Edit, Filter, ChevronDown } from 'lucide-react';
import { Button } from '@/shared/components/atoms/Button/Button';
import { Breadcrumb } from '@/shared/components/molecules/Breadcrumb/Breadcrumb';
import { PaginationControls } from '@/shared/components/molecules/PaginationControls/PaginationControls';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/molecules/Card/Card';
import { objectDataService } from '../../services/objectDataService';
import { useObjectBreadcrumb } from '../../hooks/useObjectBreadcrumb';
import { contextLookupCacheService } from '../../services/contextLookupCacheService';
import type { ObjectDataFilter, DynamicColumnDefinition } from '../../services/objectDataService';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import './ObjectDetailPage.css';

interface ObjectDetailPageProps { }

// Interface for row data structure
interface RowData {
  RefId?: string;
  id?: string;
  [key: string]: any;
}

// Interface for form field configuration
interface FormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'email' | 'number' | 'select' | 'date' | 'tel';
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
  value?: any;
}

// Interface for responsive form layout
interface ResponsiveFormProps {
  fields: FormField[];
  onFieldChange: (fieldName: string, value: any) => void;
  className?: string;
}

// Utility functions for auto-sizing columns based on header text

const autoSizeColumnsToHeader = (
  api: any, // GridApi type
  options: {
    skipColumns?: string[];
    minWidth?: number;
    maxWidth?: number;
  } = {}
): void => {
  if (!api) return;

  const {
    skipColumns = [],
    minWidth = 120,
    maxWidth = 300
  } = options;

  try {
    // Get all column definitions from the grid
    const columnState = api.getColumnState();
    if (!columnState || columnState.length === 0) return;

    // Filter columns to auto-size (exclude actions and other specified columns)
    const columnsToAutoSize = columnState
      .filter((col: any) => {
        return col.colId && !skipColumns.includes(col.colId);
      })
      .map((col: any) => col.colId);

    if (columnsToAutoSize.length > 0) {
      // First, auto-size all columns to fit content
      api.autoSizeColumns(columnsToAutoSize, false);

      // Then ensure minimum width for headers by getting column definitions
      columnsToAutoSize.forEach((colId: string) => {
        const column = api.getColumn(colId);
        if (column) {
          const colDef = column.getColDef();
          const currentWidth = column.getActualWidth();

          if (colDef.headerName) {
            // Calculate minimum width needed for header text
            const headerTextLength = colDef.headerName.length;
            const estimatedHeaderWidth = Math.max(
              headerTextLength * 8 + 60, // 8px per character + padding for sort icon
              minWidth
            );

            // Use the larger of current width or estimated header width
            const finalWidth = Math.min(
              Math.max(currentWidth, estimatedHeaderWidth),
              maxWidth
            );

            if (finalWidth !== currentWidth) {
              api.setColumnWidth(colId, finalWidth);
            }
          }
        }
      });
    }
  } catch (error) {
    console.warn('Failed to auto-size columns:', error);
  }
};

const createAutoSizedDefaultColDef = (
  enableSorting: boolean = true,
  enableFiltering: boolean = true,
  additionalOptions: any = {}
): any => {
  return {
    sortable: enableSorting,
    filter: enableFiltering,
    resizable: true,
    minWidth: 120,
    maxWidth: 300,
    // Enable auto-sizing
    suppressSizeToFit: false,
    suppressAutoSize: false,
    ...additionalOptions
  };
};

// Responsive Form Field Component
const ResponsiveFormField: React.FC<{
  field: FormField;
  value: any;
  onChange: (value: any) => void;
}> = ({ field, value, onChange }) => {
  const inputId = `field-${field.id}`;

  const renderInput = () => {
    const baseClasses = "w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 bg-white";

    switch (field.type) {
      case 'select':
        return (
          <div className="relative">
            <select
              id={inputId}
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              className={`${baseClasses} appearance-none pr-8 cursor-pointer`}
              required={field.required}
            >
              <option value="">{field.placeholder || `Select ${field.label}`}</option>
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>
        );

      case 'number':
        return (
          <input
            id={inputId}
            type="number"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            className={baseClasses}
            required={field.required}
          />
        );

      case 'email':
        return (
          <input
            id={inputId}
            type="email"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            className={baseClasses}
            required={field.required}
          />
        );

      case 'tel':
        return (
          <input
            id={inputId}
            type="tel"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            className={baseClasses}
            required={field.required}
          />
        );

      case 'date':
        return (
          <input
            id={inputId}
            type="date"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
            required={field.required}
          />
        );

      default:
        return (
          <input
            id={inputId}
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            className={baseClasses}
            required={field.required}
          />
        );
    }
  };

  return (
    <div className="form-field-container">
      <label
        htmlFor={inputId}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {renderInput()}
    </div>
  );
};

// Responsive Form Layout Component
const ResponsiveFormLayout: React.FC<ResponsiveFormProps> = ({
  fields,
  onFieldChange,
  className = ""
}) => {
  return (
    <div className={`responsive-form-grid ${className}`}>
      {fields.map((field) => (
        <ResponsiveFormField
          key={field.id}
          field={field}
          value={field.value}
          onChange={(value) => onFieldChange(field.name, value)}
        />
      ))}
    </div>
  );
};

export const ObjectDetailPage: React.FC<ObjectDetailPageProps> = () => {
  const { objectName } = useParams<{ objectName: string }>();
  const navigate = useNavigate();

  const [rowData, setRowData] = useState<RowData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [dynamicColumnDefs, setDynamicColumnDefs] = useState<ColDef[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  // Refs to track changes and prevent unnecessary updates
  const lastColumnDefsRef = useRef<string>('');
  const lastDataKeyRef = useRef<string>('');

  // Form state for responsive layout
  const [showFilters, setShowFilters] = useState(false);
  const [_formData, setFormData] = useState<Record<string, any>>({});

  // Get breadcrumb navigation data
  const { breadcrumbItems, isLoading: breadcrumbLoading } = useObjectBreadcrumb();

  // Form fields will be populated from metadata
  const [formFields, _setFormFields] = useState<FormField[]>([]);

  // Convert dynamic column definitions to AG-Grid column definitions with auto-sizing
  const convertToAgGridColumns = (dynamicColumns: DynamicColumnDefinition[]): ColDef[] => {
    // If no dynamic columns exist, return empty array (no Actions column either)
    if (!dynamicColumns || dynamicColumns.length === 0) {
      return [];
    }

    // Create a map to ensure unique columns by field name
    const uniqueColumnsMap = new Map<string, DynamicColumnDefinition>();
    dynamicColumns.forEach(col => {
      if (col.field && !uniqueColumnsMap.has(col.field)) {
        uniqueColumnsMap.set(col.field, col);
      }
    });

    const uniqueColumns = Array.from(uniqueColumnsMap.values());
    const dataColumns = uniqueColumns.map(col => {
      const agGridCol: ColDef = {
        field: col.field,
        headerName: col.headerName,
        sortable: col.sortable,
        filter: col.filter,
        resizable: true,
        minWidth: 120, // Increased minimum width to accommodate longer headers
        maxWidth: 300,
        // Let AG-Grid handle initial sizing, we'll adjust after render
        suppressSizeToFit: false,
        cellRenderer: (params: any) => {
          if (params.value == null) return '';

          // Special rendering based on data type
          switch (col.type) {
            case 'number':
              return typeof params.value === 'number'
                ? params.value.toLocaleString()
                : params.value;

            case 'boolean':
              return params.value ? '✓' : '✗';

            case 'date':
              return params.value;

            default:
              return params.value;
          }
        }
      };

      // Add special styling for certain column types
      if (col.field.toLowerCase().includes('id')) {
        agGridCol.cellClass = 'font-mono text-xs';
      }

      return agGridCol;
    });

    // Only add Actions column if there are dynamic columns to act upon
    const actionsColumn: ColDef = {
      field: 'actions',
      headerName: 'Actions',
      sortable: false,
      filter: false,
      resizable: false,
      width: 100,
      pinned: 'right',
      cellRenderer: (params: any) => {
        return React.createElement('button', {
          className: 'inline-flex items-center justify-center h-8 w-8 text-sm cursor-pointer font-normal transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
          title: 'Edit',
          onClick: (e: React.MouseEvent) => {
            e.stopPropagation();
            handleEdit(params.data);
          }
        }, React.createElement(Edit, { size: 16 }));
      }
    };

    return [...dataColumns, actionsColumn];
  };

  // Fetch and cache bulk context and tenant context lookups - OPTIMIZED
  const fetchAndCacheBulkLookups = useCallback(async (columnDefinitions: DynamicColumnDefinition[]) => {
    try {
      // Extract context and tenant context IDs from column definitions
      const { contextIds, tenantContextIds } = contextLookupCacheService.extractContextIds(columnDefinitions);

      // OPTIMIZATION: Skip API calls if no context IDs found
      if (contextIds.length === 0 && tenantContextIds.length === 0) {
        return;
      }

      // Check if cache is expired or if we have new IDs to fetch
      const isExpired = contextLookupCacheService.isCacheExpired();
      const existingContextCache = contextLookupCacheService.getContextCache();
      const existingTenantContextCache = contextLookupCacheService.getTenantContextCache();

      // Find IDs that are not already cached
      const existingContextIds = new Set(existingContextCache.contextsWithLookups.map(ctx => ctx.context.id));
      const existingTenantContextIds = new Set(existingTenantContextCache.tenantContextsWithLookups.map(tc => tc.tenantContext.id));

      const newContextIds = contextIds.filter(id => !existingContextIds.has(id));
      const newTenantContextIds = tenantContextIds.filter(id => !existingTenantContextIds.has(id));

      // OPTIMIZATION: Skip API calls if all data is already cached and not expired
      if (newContextIds.length === 0 && newTenantContextIds.length === 0 && !isExpired) {
        return;
      }

      // Fetch bulk contexts only if we have new context IDs or cache is expired
      if ((newContextIds.length > 0 || isExpired) && contextIds.length > 0) {
        const contextResult = await contextLookupCacheService.fetchAndCacheBulkContexts(
          isExpired ? contextIds : newContextIds
        );
      }

      // Fetch bulk tenant contexts only if we have new tenant context IDs or cache is expired
      if ((newTenantContextIds.length > 0 || isExpired) && tenantContextIds.length > 0) {
        const tenantContextResult = await contextLookupCacheService.fetchAndCacheBulkTenantContexts(
          isExpired ? tenantContextIds : newTenantContextIds
        );
      }

      // Log cache statistics
      const cacheStats = contextLookupCacheService.getCacheStats();
    } catch (error) {
      console.error('Error fetching and caching bulk lookups:', error);
      // Don't throw error to avoid breaking the main data loading flow
    }
  }, []);

  // Load data with pagination support
  const loadData = useCallback(async (page?: number, size?: number, search?: string) => {
    if (!objectName) {
      return;
    }

    const targetPage = page || currentPage;
    const targetPageSize = size || pageSize;
    const searchFilter = search !== undefined ? search : searchTerm;

    setLoading(true);
    setError(null);

    try {
      const filters: ObjectDataFilter = {
        page: targetPage,
        pageSize: targetPageSize,
        search: searchFilter || undefined
      };

      const response = await objectDataService.fetchObjectData(objectName, filters);

      if (response.success) {
        // OPTIMIZATION: Only update state if data has actually changed
        // This prevents unnecessary re-renders when the same data is loaded
        const newDataKey = JSON.stringify({
          dataIds: response.data.map(item => item.RefId || item.id).slice(0, 10), // Sample first 10 IDs
          totalCount: response.totalCount,
          page: targetPage,
          pageSize: targetPageSize
        });

        if (newDataKey !== lastDataKeyRef.current) {
          lastDataKeyRef.current = newDataKey;
          setRowData(response.data);
          setTotalCount(response.totalCount);
          setCurrentPage(targetPage);
          setPageSize(targetPageSize);
        } else {
          // Still update pagination state in case page/pageSize changed
          setCurrentPage(targetPage);
          setPageSize(targetPageSize);
        }

        // Update column definitions if provided - OPTIMIZED
        if (response.columnDefinitions && response.columnDefinitions.length > 0) {
          // Create a stable key for the current column definitions
          const columnKey = JSON.stringify({
            fields: response.columnDefinitions.map(c => ({
              field: c.field,
              headerName: c.headerName
            })),
            objectName
          });

          // OPTIMIZATION: Only update if the columns have actually changed
          // This prevents unnecessary re-rendering and API calls
          if (columnKey !== lastColumnDefsRef.current) {
            lastColumnDefsRef.current = columnKey;

            const agGridColumns = convertToAgGridColumns(response.columnDefinitions);
            setDynamicColumnDefs(agGridColumns);

            // Store metadata in localStorage for UpsertObject page
            // This allows the upsert form to use existing metadata instead of making separate API calls
            const metadataForUpsert = {
              objectName: objectName,
              columnDefinitions: response.columnDefinitions,
              timestamp: new Date().toISOString()
            };
            localStorage.setItem(`object-metadata-${objectName}`, JSON.stringify(metadataForUpsert));

            // Extract context and tenant context IDs and fetch bulk lookups
            // This is optimized to skip API calls when data is already cached
            await fetchAndCacheBulkLookups(response.columnDefinitions);
          }
        }
      } else {
        setError(response.error || 'Failed to load data');
      }
    } catch (err) {
      console.error('Exception in loadData:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [objectName, currentPage, pageSize, searchTerm]);

  // Reset pagination and load data when object changes
  useEffect(() => {
    if (objectName) {
      // Reset pagination state when switching objects
      setCurrentPage(1);
      setPageSize(10);
      setSearchTerm('');
      loadData(1, 10, '');
    }
  }, [objectName]); // Only depend on objectName to avoid infinite loops

  // Handle window resize to maintain responsive column sizing
  useEffect(() => {
    const handleResize = () => {
      if (gridApi) {
        // Re-apply auto-sizing when window is resized
        setTimeout(() => {
          autoSizeColumnsToHeader(gridApi, {
            skipColumns: ['actions'],
            minWidth: 120,
            maxWidth: 300
          });
        }, 100);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [gridApi]);

  // Handle form field changes
  const handleFormFieldChange = useCallback((fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  }, []);

  // Handle search
  const handleSearch = () => {
    // Reset to first page when searching
    loadData(1, pageSize, searchTerm);
  };

  // Handle refresh
  const handleRefresh = () => {
    loadData(currentPage, pageSize);
  };



  // Handle custom pagination navigation
  const handlePageChange = (newPage: number) => {
    if (newPage !== currentPage && newPage >= 1 && newPage <= Math.ceil(totalCount / pageSize)) {
      loadData(newPage, pageSize);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    if (newPageSize !== pageSize) {
      // Reset to page 1 when changing page size
      loadData(1, newPageSize);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    navigate(-1);
  };

  // Handle add new object
  const handleAddNew = () => {
    navigate(`/object/${objectName}/upsert`);
  };

  // Handle edit object
  const handleEdit = (rowData: RowData) => {
    const refId = rowData.RefId || rowData.id;
    if (refId) {
      // Navigate to upsert form with RefId as query parameter and pass row data through state
      // This allows the form to use existing ag-grid data instead of making a separate API call
      navigate(`/object/${objectName}/upsert?refId=${encodeURIComponent(refId)}`, {
        state: {
          rowData: rowData,
          mode: 'edit'
        }
      });
    } else {
      console.warn('No RefId or id found in row data for editing:', rowData);
    }
  };

  // Handle export - optimized to avoid unnecessary API calls for small datasets
  const handleExport = async () => {
    if (!objectName || !gridApi) return;

    try {
      setLoading(true);

      // OPTIMIZATION: For small datasets (current page contains all data),
      // export current data instead of making a separate API call
      const isSmallDataset = totalCount <= pageSize;
      const hasAllDataLoaded = rowData.length === totalCount;

      if (isSmallDataset || hasAllDataLoaded) {
        // Use existing grid data - no API call needed
        // This improves performance by avoiding redundant data fetching

        gridApi.exportDataAsCsv({
          fileName: `object-${objectName}-data.csv`
        });
      } else {
        // For large datasets, fetch all data for complete export

        const filters: ObjectDataFilter = {
          page: 1,
          pageSize: totalCount || 10000, // Use total count or large number
          search: searchTerm || undefined
        };

        const response = await objectDataService.fetchObjectData(objectName, filters);

        if (response.success) {
          // Temporarily set all data for export
          const originalData = rowData;
          gridApi.setGridOption('rowData', response.data);

          // Export all data
          gridApi.exportDataAsCsv({
            fileName: `object-${objectName}-data.csv`
          });

          // Restore original paginated data
          setTimeout(() => {
            gridApi.setGridOption('rowData', originalData);
          }, 100);
        }
      }
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!objectName) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-xl font-medium text-gray-900 mb-2">Invalid Object</h2>
          <p className="text-gray-600 mb-4">No object name provided</p>
          <Button onClick={handleBack} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Breadcrumb Navigation */}
      {(breadcrumbItems.length > 0 || breadcrumbLoading) && (
        <div className="flex-shrink-0 bg-muted/30 border-b border-border">
          <div className="px-4 py-2 flex items-center justify-between gap-4 min-w-0">
            {/* Left side: Back Button + Breadcrumb */}
            <div className="flex items-center gap-3 min-w-0 flex-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="flex items-center gap-2 flex-shrink-0"
              >
                <ArrowLeft className="w-4 h-4" />
                <span className="hidden sm:inline">Back</span>
              </Button>

              <div className="min-w-0 flex-1">
                <Breadcrumb
                  items={breadcrumbItems}
                  isLoading={breadcrumbLoading}
                  className="text-sm"
                />
              </div>
            </div>

            {/* Right side: Refresh Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center flex-shrink-0"
              title="Refresh"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      )}

      {/* Controls Bar */}
      <div className="flex-shrink-0 border-b border-border bg-card">
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="w-4 h-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-muted rounded-lg p-1">
              <Search className="w-4 h-4 text-muted-foreground ml-2" />
              <input
                type="text"
                placeholder="Search data..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="bg-transparent border-none outline-none px-2 py-1 text-sm w-48"
              />
              <Button size="sm" onClick={handleSearch} variant="ghost">
                Search
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              disabled={!gridApi || rowData.length === 0}
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Export
            </Button>

            <Button
              variant="default"
              size="sm"
              onClick={handleAddNew}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add
            </Button>
          </div>
        </div>
      </div>

      {/* Responsive Form Filters */}
      {showFilters && (
        <div className="flex-shrink-0 border-b border-border bg-muted/30">
          <Card className="m-4 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-base font-medium">Filter Options</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveFormLayout
                fields={formFields}
                onFieldChange={handleFormFieldChange}
                className="mb-4"
              />
              <div className="flex items-center justify-end gap-2 pt-3 border-t border-border">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setFormData({});
                  }}
                >
                  Clear All
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => {
                    // Apply filters logic here
                    handleSearch();
                  }}
                >
                  Apply Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Content */}
      <div className="flex-1 p-4">
        <div className="h-full">
          {error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <h3 className="text-lg font-medium text-red-600 mb-2">Error Loading Data</h3>
                <p className="text-gray-600 mb-4">{error}</p>
                <Button onClick={handleRefresh} variant="outline">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </div>
          ) : loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
                <p className="text-muted-foreground">Loading data...</p>
              </div>
            </div>
          ) : (
            <div className="flex flex-col h-full">
              <div className="ag-theme-alpine ag-grid-container flex-1">
                <AgGridReact
                  rowData={rowData}
                  columnDefs={dynamicColumnDefs}
                  onGridReady={(params) => {
                    setGridApi(params.api);

                    // Auto-size columns when grid is ready
                    setTimeout(() => {
                      autoSizeColumnsToHeader(params.api, {
                        skipColumns: ['actions'],
                        minWidth: 120,
                        maxWidth: 300
                      });
                    }, 200);
                  }}

                  onFirstDataRendered={(params) => {
                    // Auto-size columns to header content when data is first rendered
                    setTimeout(() => {
                      autoSizeColumnsToHeader(params.api, {
                        skipColumns: ['actions'], // Don't auto-size the Actions column
                        minWidth: 120,
                        maxWidth: 300
                      });
                    }, 100); // Small delay to ensure DOM is ready
                  }}

                  onRowDataUpdated={(params) => {
                    // Auto-size columns when data is updated
                    if (params.api) {
                      setTimeout(() => {
                        autoSizeColumnsToHeader(params.api, {
                          skipColumns: ['actions'],
                          minWidth: 120,
                          maxWidth: 300
                        });
                      }, 50);
                    }
                  }}

                  // Disable AG-Grid's built-in pagination for server-side control
                  pagination={false}
                  suppressPaginationPanel={true}

                  // Loading overlay
                  loading={loading}
                  loadingOverlayComponent="agLoadingOverlay"

                  // Default column configuration with auto-sizing
                  defaultColDef={createAutoSizedDefaultColDef(true, true, {
                    minWidth: 120,
                    maxWidth: 300
                  })}

                  // Row ID for AG-Grid
                  getRowId={(params) => {
                    const id = params.data.RefId || params.data.id || `row-${Math.random().toString(36).substring(2, 11)}`;
                    return id;
                  }}
                />
              </div>

              {/* Pagination Controls */}
              <PaginationControls
                totalCount={totalCount}
                currentPage={currentPage}
                pageSize={pageSize}
                loading={loading}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
