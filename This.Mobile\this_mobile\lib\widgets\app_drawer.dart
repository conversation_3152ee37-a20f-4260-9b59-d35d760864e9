import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:provider/provider.dart';
import '../models/nav_item.dart';
import '../providers/app_provider.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import 'loading_widget.dart';
import 'error_widget.dart';

/// Main application drawer with hierarchical navigation
class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });

    // Debounce the search to avoid excessive filtering
    AppHelpers.debounce(
      'drawer_search',
      const Duration(milliseconds: 300),
      () {
        context.read<AppProvider>().updateSearchQuery(query);
      },
    );
  }

  void _clearSearch() {
    _searchController.clear();
    _onSearchChanged('');
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: AppConstants.drawerWidth,
      child: Column(
        children: [
          _buildHeader(),
          _buildSearchBar(),
          Expanded(
            child: _buildNavigationContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primaryDark,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(AppConstants.radiusM),
                    ),
                    child: const Icon(
                      LucideIcons.package,
                      color: Colors.white,
                      size: AppConstants.iconSizeM,
                    ),
                  ),
                  const SizedBox(width: AppConstants.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppConstants.appName,
                          style: AppTextStyles.titleMedium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'v${AppConstants.appVersion}',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacingM),
      child: TextField(
        controller: _searchController,
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Search navigation...',
          prefixIcon: const Icon(
            LucideIcons.search,
            size: AppConstants.iconSizeS,
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: _clearSearch,
                  icon: const Icon(
                    LucideIcons.x,
                    size: AppConstants.iconSizeS,
                  ),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusM),
            borderSide: const BorderSide(color: AppColors.border),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusM),
            borderSide: const BorderSide(color: AppColors.border),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusM),
            borderSide: const BorderSide(color: AppColors.primary, width: 2),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppConstants.spacingM,
            vertical: AppConstants.spacingS,
          ),
          isDense: true,
        ),
      ),
    );
  }

  Widget _buildNavigationContent() {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        if (provider.isNavigationLoading) {
          return const NavigationShimmerLoading();
        }

        if (provider.navigationError != null) {
          return NavigationErrorWidget(
            message: provider.navigationError!,
            onRetry: provider.retryNavigationLoad,
          );
        }

        final items = _searchQuery.isEmpty
            ? provider.navigationItems
            : provider.filteredNavigationItems;

        if (items.isEmpty) {
          return _searchQuery.isNotEmpty
              ? SearchNoResultsWidget(
                  searchQuery: _searchQuery,
                  onClearSearch: _clearSearch,
                )
              : const EmptyStateWidget(
                  title: 'No Navigation Items',
                  message: 'Navigation items will appear here',
                  icon: LucideIcons.navigation,
                );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: AppConstants.spacingS),
          itemCount: items.length,
          itemBuilder: (context, index) {
            return _buildNavigationItem(
              context,
              items[index],
              provider,
              0, // Root level
            );
          },
        );
      },
    );
  }

  Widget _buildNavigationItem(
    BuildContext context,
    NavItem item,
    AppProvider provider,
    int level,
  ) {
    final isSelected = provider.selectedNavItem?.id == item.id;
    final hasChildren = item.hasChildren;
    final isExpanded = item.isExpanded;

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(
            left: AppConstants.spacingM + (level * AppConstants.spacingL),
            right: AppConstants.spacingM,
            bottom: AppConstants.spacingXS,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.primary.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(AppConstants.radiusM),
          ),
          child: ListTile(
            dense: true,
            leading: Icon(
              item.iconData,
              size: AppConstants.iconSizeS,
              color: isSelected
                  ? AppColors.primary
                  : AppColors.textSecondary,
            ),
            title: Text(
              item.name,
              style: AppTextStyles.bodyMedium.copyWith(
                color: isSelected
                    ? AppColors.primary
                    : AppColors.textPrimary,
                fontWeight: isSelected
                    ? FontWeight.w600
                    : FontWeight.w400,
              ),
            ),
            trailing: hasChildren
                ? Icon(
                    isExpanded
                        ? LucideIcons.chevronDown
                        : LucideIcons.chevronRight,
                    size: AppConstants.iconSizeS,
                    color: AppColors.textSecondary,
                  )
                : null,
            onTap: () {
              if (hasChildren) {
                provider.toggleNavItemExpanded(item);
                provider.selectNavigationItem(item);
                Navigator.of(context).pop(); // Close drawer when parent item is selected
              } else {
                provider.selectNavigationItem(item);
                Navigator.of(context).pop(); // Close drawer when any item is selected
              }
            },
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.spacingM,
              vertical: AppConstants.spacingXS,
            ),
          ),
        ),

        // Render children if expanded
        if (hasChildren && isExpanded)
          ...item.children!.map((child) => _buildNavigationItem(
                context,
                child,
                provider,
                level + 1,
              )),
      ],
    );
  }
}

/// Compact drawer for smaller screens
class CompactAppDrawer extends StatelessWidget {
  const CompactAppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: 280,
      child: Consumer<AppProvider>(
        builder: (context, provider, child) {
          return ListView(
            padding: EdgeInsets.zero,
            children: [
              DrawerHeader(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary,
                      AppColors.primaryDark,
                    ],
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    const Icon(
                      LucideIcons.package,
                      color: Colors.white,
                      size: AppConstants.iconSizeL,
                    ),
                    const SizedBox(height: AppConstants.spacingS),
                    Text(
                      AppConstants.appName,
                      style: AppTextStyles.titleLarge.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),

              // Flatten navigation items for compact view
              ...provider.navigationItems.expand((item) => [
                if (item.objectType != null)
                  ListTile(
                    leading: Icon(item.iconData),
                    title: Text(item.name),
                    onTap: () {
                      provider.selectNavigationItem(item);
                      Navigator.of(context).pop();
                    },
                  ),
                if (item.hasChildren)
                  ...item.children!.where((child) => child.objectType != null).map(
                    (child) => ListTile(
                      leading: Icon(child.iconData),
                      title: Text(child.name),
                      contentPadding: const EdgeInsets.only(
                        left: AppConstants.spacingXL,
                        right: AppConstants.spacingM,
                      ),
                      onTap: () {
                        provider.selectNavigationItem(child);
                        Navigator.of(context).pop();
                      },
                    ),
                  ),
              ]),
            ],
          );
        },
      ),
    );
  }
}
