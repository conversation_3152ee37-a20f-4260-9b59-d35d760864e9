using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetContextWithLookups;

/// <summary>
/// Handler for GetContextWithLookupsQuery
/// </summary>
public class GetContextWithLookupsQueryHandler : IRequestHandler<GetContextWithLookupsQuery, Result<ContextWithLookupsDto>>
{
    private readonly IRepository<Domain.Entities.Context> _contextRepository;
    private readonly IRepository<Lookup> _lookupRepository;
    private readonly ILogger<GetContextWithLookupsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetContextWithLookupsQueryHandler(
        IRepository<Domain.Entities.Context> contextRepository,
        IRepository<Lookup> lookupRepository,
        ILogger<GetContextWithLookupsQueryHandler> logger)
    {
        _contextRepository = contextRepository;
        _lookupRepository = lookupRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ContextWithLookupsDto>> Handle(GetContextWithLookupsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting context with lookups for ContextId: {ContextId}, IncludeInactiveLookups: {IncludeInactiveLookups}",
                request.ContextId, request.IncludeInactiveLookups);

            // Get the context
            var context = await _contextRepository.GetByIdAsync(request.ContextId, cancellationToken);
            if (context == null)
            {
                return Result<ContextWithLookupsDto>.Failure($"Context with ID {request.ContextId} not found");
            }

            // Check if context is deleted
            if (context.IsDeleted)
            {
                return Result<ContextWithLookupsDto>.Failure($"Context with ID {request.ContextId} has been deleted");
            }

            // Create specification for lookups
            var lookupSpec = new LookupsByContextIdSpec(
                contextId: request.ContextId,
                includeInactive: request.IncludeInactiveLookups);

            // Get associated lookups
            var lookups = await _lookupRepository.ListAsync(lookupSpec, cancellationToken);

            // Map to DTOs
            var contextDto = context.Adapt<ContextDto>();
            var lookupDtos = lookups.Adapt<List<LookupDto>>();

            var result = new ContextWithLookupsDto
            {
                Context = contextDto,
                Lookups = lookupDtos
            };

            _logger.LogInformation("Successfully retrieved context with {LookupCount} lookups for ContextId: {ContextId}",
                lookupDtos.Count, request.ContextId);

            return Result<ContextWithLookupsDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting context with lookups for ContextId: {ContextId}", request.ContextId);
            return Result<ContextWithLookupsDto>.Failure($"Error retrieving context with lookups: {ex.Message}");
        }
    }
}
