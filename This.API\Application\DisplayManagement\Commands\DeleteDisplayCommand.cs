using MediatR;
using Shared.Common.Response;

namespace Application.DisplayManagement.Commands;

/// <summary>
/// Delete Display command
/// </summary>
public class DeleteDisplayCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Display ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteDisplayCommand(Guid id)
    {
        Id = id;
    }
}
