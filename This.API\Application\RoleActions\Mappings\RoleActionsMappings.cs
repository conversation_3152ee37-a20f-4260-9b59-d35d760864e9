using Application.RoleActions.Commands;
using Application.RoleActions.DTOs;
using Mapster;

namespace Application.RoleActions.Mappings;

/// <summary>
/// Mapping configurations for RoleActions entity and related DTOs
/// </summary>
public class RoleActionsMappings : IRegister
{
    /// <summary>
    /// Register mappings
    /// </summary>
    public void Register(TypeAdapterConfig config)
    {
        // Map RoleActions entity to RoleActionsDto
        config.NewConfig<Domain.Entities.RoleActions, RoleActionsDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.RoleId, src => src.RoleId)
            .Map(dest => dest.ActionId, src => src.ActionId)
            .Map(dest => dest.IsActive, src => src.IsActive)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.CreatedBy, src => src.CreatedBy)
            .Map(dest => dest.ModifiedAt, src => src.ModifiedAt)
            .Map(dest => dest.ModifiedBy, src => src.ModifiedBy);

        // Map UpdateRoleActionsRequest to UpdateRoleActionsCommand
        config.NewConfig<UpdateRoleActionsRequest, UpdateRoleActionsCommand>()
            .Map(dest => dest.RoleId, src => src.RoleId)
            .Map(dest => dest.ActionIds, src => src.ActionIds);

        // Map BulkUpdateRoleActionsRequest to BulkUpdateRoleActionsCommand
        config.NewConfig<BulkUpdateRoleActionsRequest, BulkUpdateRoleActionsCommand>()
            .Map(dest => dest.RoleActions, src => src.RoleActions);
    }
}
