/**
 * DataTable Molecule
 * Reusable data table component with sorting, pagination, and actions
 */

import React from 'react';
import { Table, Badge } from 'react-bootstrap';
import { LoadingSpinner } from '../atoms/LoadingSpinner';
import { ActionButton } from '../atoms/ActionButton';

export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  sortable?: boolean;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

export interface TableAction<T = any> {
  key: string;
  label?: string;
  icon?: string | React.ReactNode;
  variant?: string;
  onClick: (record: T, index: number) => void;
  disabled?: (record: T) => boolean;
  hidden?: (record: T) => boolean;
  tooltip?: string;
}

export interface DataTableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  actions?: TableAction<T>[];
  loading?: boolean;
  empty?: {
    icon?: string;
    title?: string;
    description?: string;
  };
  rowKey?: keyof T | ((record: T) => string);
  onRowClick?: (record: T, index: number) => void;
  striped?: boolean;
  hover?: boolean;
  bordered?: boolean;
  size?: 'sm' | 'lg';
  className?: string;
  maxHeight?: string | number;
}

export const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  actions = [],
  loading = false,
  empty = {
    icon: '📋',
    title: 'No data found',
    description: 'No items match your current criteria'
  },
  rowKey = 'id',
  onRowClick,
  striped = false,
  hover = true,
  bordered = false,
  size,
  className = '',
  maxHeight
}: DataTableProps<T>) => {
  // Get row key
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return String(record[rowKey] || index);
  };

  // Render cell content
  const renderCell = (column: TableColumn<T>, record: T, index: number) => {
    if (column.render) {
      return column.render(record[column.dataIndex!], record, index);
    }
    
    if (column.dataIndex) {
      const value = record[column.dataIndex];
      
      // Handle different data types
      if (value === null || value === undefined) {
        return <span className="text-muted">—</span>;
      }
      
      if (typeof value === 'boolean') {
        return (
          <Badge bg={value ? 'success' : 'secondary'}>
            {value ? 'Yes' : 'No'}
          </Badge>
        );
      }
      
      if (Array.isArray(value)) {
        return <Badge bg="info">{value.length} items</Badge>;
      }
      
      return String(value);
    }
    
    return null;
  };

  // Render actions
  const renderActions = (record: T, index: number) => {
    if (!actions.length) return null;

    const visibleActions = actions.filter(action => 
      !action.hidden || !action.hidden(record)
    );

    if (!visibleActions.length) return null;

    return (
      <div className="d-flex gap-1">
        {visibleActions.map(action => (
          <ActionButton
            key={action.key}
            variant={action.variant as any}
            size="sm"
            icon={action.icon}
            onClick={() => action.onClick(record, index)}
            disabled={action.disabled ? action.disabled(record) : false}
            tooltip={action.tooltip}
          >
            {action.label}
          </ActionButton>
        ))}
      </div>
    );
  };

  // Loading state
  if (loading && data.length === 0) {
    return (
      <div className="text-center py-5">
        <LoadingSpinner text="Loading data..." />
      </div>
    );
  }

  // Empty state
  if (!loading && data.length === 0) {
    return (
      <div className="text-center py-5">
        <div className="text-muted">
          <div className="h4 mb-3">{empty.icon}</div>
          <h6>{empty.title}</h6>
          <p className="small">{empty.description}</p>
        </div>
      </div>
    );
  }

  const tableStyle = maxHeight ? {
    maxHeight,
    overflowY: 'auto' as const
  } : undefined;

  return (
    <div style={tableStyle}>
      <Table 
        striped={striped}
        hover={hover}
        bordered={bordered}
        size={size}
        responsive
        className={`mb-0 ${className}`}
      >
        <thead style={{ 
          background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
          position: maxHeight ? 'sticky' : undefined,
          top: 0,
          zIndex: 10
        }}>
          <tr>
            {columns.map(column => (
              <th
                key={column.key}
                className={`fw-semibold text-dark py-2 ps-3 ${column.className || ''}`}
                style={{
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                  borderBottom: '2px solid #e9ecef',
                  width: column.width,
                  textAlign: column.align || 'left'
                }}
              >
                {column.title}
              </th>
            ))}
            {actions.length > 0 && (
              <th
                className="fw-semibold text-dark py-2 ps-3"
                style={{
                  borderTop: 'none',
                  borderLeft: 'none',
                  borderRight: 'none',
                  borderBottom: '2px solid #e9ecef',
                  width: '120px'
                }}
              >
                Actions
              </th>
            )}
          </tr>
        </thead>
        <tbody>
          {data.map((record, index) => (
            <tr
              key={getRowKey(record, index)}
              onClick={onRowClick ? () => onRowClick(record, index) : undefined}
              style={{
                cursor: onRowClick ? 'pointer' : undefined
              }}
            >
              {columns.map(column => (
                <td
                  key={column.key}
                  className={`ps-3 ${column.className || ''}`}
                  style={{ textAlign: column.align || 'left' }}
                >
                  {renderCell(column, record, index)}
                </td>
              ))}
              {actions.length > 0 && (
                <td className="ps-3">
                  {renderActions(record, index)}
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </Table>
    </div>
  );
};
