/**
 * PaginationBar Molecule
 * Reusable pagination component with page size controls
 */

import React from 'react';
import { Row, Col, Pagination, Form } from 'react-bootstrap';

export interface PaginationBarProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  pageSizeOptions?: number[];
  showPageSizeSelector?: boolean;
  showItemsInfo?: boolean;
  disabled?: boolean;
  className?: string;
  maxVisiblePages?: number;
}

export const PaginationBar: React.FC<PaginationBarProps> = ({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [5, 10, 25, 50, 100],
  showPageSizeSelector = true,
  showItemsInfo = true,
  disabled = false,
  className = '',
  maxVisiblePages = 5
}) => {
  // Calculate visible page range
  const getVisiblePages = (): number[] => {
    const pages: number[] = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, currentPage + halfVisible);
    
    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  };

  // Calculate items range
  const getItemsRange = (): { start: number; end: number } => {
    const start = (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, totalItems);
    return { start, end };
  };

  const visiblePages = getVisiblePages();
  const { start, end } = getItemsRange();

  // Don't render if there's only one page and no items
  if (totalPages <= 1 && totalItems === 0) {
    return null;
  }

  return (
    <div className={`pagination-bar ${className}`}>
      <Row className="align-items-center">
        {/* Items Info */}
        {showItemsInfo && (
          <Col xs="auto">
            <span className="text-muted small">
              {totalItems > 0 ? (
                <>Showing {start.toLocaleString()} to {end.toLocaleString()} of {totalItems.toLocaleString()} entries</>
              ) : (
                'No entries found'
              )}
            </span>
          </Col>
        )}

        {/* Page Size Selector */}
        {showPageSizeSelector && (
          <Col xs="auto">
            <div className="d-flex align-items-center gap-2">
              <span className="text-muted small">Show:</span>
              <Form.Select
                size="sm"
                value={pageSize}
                onChange={(e) => onPageSizeChange(Number(e.target.value))}
                disabled={disabled}
                style={{ width: 'auto' }}
              >
                {pageSizeOptions.map(size => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </Form.Select>
            </div>
          </Col>
        )}

        {/* Spacer */}
        <Col />

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <Col xs="auto">
            <Pagination className="mb-0" size="sm">
              {/* First Page */}
              <Pagination.First
                disabled={disabled || currentPage === 1}
                onClick={() => onPageChange(1)}
              />

              {/* Previous Page */}
              <Pagination.Prev
                disabled={disabled || currentPage === 1}
                onClick={() => onPageChange(currentPage - 1)}
              />

              {/* Show ellipsis if there are pages before visible range */}
              {visiblePages[0] > 1 && (
                <>
                  <Pagination.Item
                    disabled={disabled}
                    onClick={() => onPageChange(1)}
                  >
                    1
                  </Pagination.Item>
                  {visiblePages[0] > 2 && <Pagination.Ellipsis disabled />}
                </>
              )}

              {/* Visible Pages */}
              {visiblePages.map(page => (
                <Pagination.Item
                  key={page}
                  active={page === currentPage}
                  disabled={disabled}
                  onClick={() => onPageChange(page)}
                >
                  {page}
                </Pagination.Item>
              ))}

              {/* Show ellipsis if there are pages after visible range */}
              {visiblePages[visiblePages.length - 1] < totalPages && (
                <>
                  {visiblePages[visiblePages.length - 1] < totalPages - 1 && (
                    <Pagination.Ellipsis disabled />
                  )}
                  <Pagination.Item
                    disabled={disabled}
                    onClick={() => onPageChange(totalPages)}
                  >
                    {totalPages}
                  </Pagination.Item>
                </>
              )}

              {/* Next Page */}
              <Pagination.Next
                disabled={disabled || currentPage === totalPages}
                onClick={() => onPageChange(currentPage + 1)}
              />

              {/* Last Page */}
              <Pagination.Last
                disabled={disabled || currentPage === totalPages}
                onClick={() => onPageChange(totalPages)}
              />
            </Pagination>
          </Col>
        )}
      </Row>
    </div>
  );
};
