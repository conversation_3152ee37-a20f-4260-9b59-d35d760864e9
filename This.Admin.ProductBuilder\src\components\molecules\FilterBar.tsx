/**
 * FilterBar Molecule
 * Reusable filter bar component with search and multiple filter options
 */

import React from 'react';
import { Row, Col, Form } from 'react-bootstrap';
import { SearchInput } from '../atoms/SearchInput';
import { SearchableDropdown, DropdownOption } from '../atoms/SearchableDropdown';

export interface FilterOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface FilterConfig {
  key: string;
  label: string;
  value: string;
  options: FilterOption[];
  placeholder?: string;
  disabled?: boolean;
  size?: 'sm' | 'lg';
}

export interface FilterBarProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  searchPlaceholder?: string;
  filters: FilterConfig[];
  onFilterChange: (filterKey: string, value: string) => void;
  className?: string;
  searchColSize?: number;
  filterColSize?: number;
  disabled?: boolean;
}

export const FilterBar: React.FC<FilterBarProps> = ({
  searchValue,
  onSearchChange,
  searchPlaceholder = 'Search...',
  filters,
  onFilterChange,
  className = '',
  searchColSize = 3,
  filterColSize,
  disabled = false
}) => {
  // Calculate filter column size if not provided
  const calculatedFilterColSize = filterColSize || Math.floor((12 - searchColSize) / filters.length);

  return (
    <div className={`filter-bar ${className}`}>
      <Row className="g-3 align-items-center">
        {/* Search Input */}
        <Col md={searchColSize}>
          <SearchInput
            value={searchValue}
            onChange={onSearchChange}
            placeholder={searchPlaceholder}
            disabled={disabled}
          />
        </Col>

        {/* Filter Dropdowns */}
        {filters.map((filter) => (
          <Col
            key={filter.key}
            md={calculatedFilterColSize}
          >
            <SearchableDropdown
              value={filter.value}
              options={filter.options}
              onChange={(value) => onFilterChange(filter.key, value)}
              placeholder={filter.placeholder}
              disabled={disabled || filter.disabled}
              size={filter.size}
              aria-label={filter.label}
              className="shadow-sm"
              showSearch={true}
              allowClear={false}
              searchPlaceholder={`Search ${filter.label.toLowerCase()}...`}
            />
          </Col>
        ))}
      </Row>
    </div>
  );
};

// Predefined filter configurations for common use cases
export const createSubscriptionFilters = (
  statusFilter: string,
  tenantFilter: string,
  tenantOptions: FilterOption[]
): FilterConfig[] => [
  {
    key: 'tenant',
    label: 'Tenant',
    value: tenantFilter,
    placeholder: 'All Tenants',
    options: tenantOptions
  },
  {
    key: 'status',
    label: 'Status',
    value: statusFilter,
    placeholder: 'All Status',
    options: [
      { value: 'all', label: 'All Status' },
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
      { value: 'expired', label: 'Expired' },
      { value: 'expiring', label: 'Expiring Soon' }
    ]
  }
];

export const createTemplateFilters = (
  stageFilter: string,
  statusFilter: string
): FilterConfig[] => [
  {
    key: 'stage',
    label: 'Stage',
    value: stageFilter,
    placeholder: 'All Stages',
    options: [
      { value: 'all', label: 'All Stages' },
      { value: 'draft', label: 'Draft' },
      { value: 'beta', label: 'Beta' },
      { value: 'live', label: 'Live' },
      { value: 'archived', label: 'Archived' }
    ]
  },
  {
    key: 'status',
    label: 'Status',
    value: statusFilter,
    placeholder: 'All Status',
    options: [
      { value: 'all', label: 'All Status' },
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' }
    ]
  }
];

export const createTenantFilters = (
  statusFilter: string
): FilterConfig[] => [
  {
    key: 'status',
    label: 'Status',
    value: statusFilter,
    placeholder: 'All Status',
    options: [
      { value: 'all', label: 'All Status' },
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' }
    ]
  }
];
