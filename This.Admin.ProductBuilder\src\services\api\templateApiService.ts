/**
 * Template API Service
 * Handles all template-related API operations using the centralized HTTP client
 */

import { BaseApiService } from '../baseApiService';
import type { PaginationParams, PaginatedResponse, ApiResult } from '../types';
import { HttpClientFactory } from '../httpClient';
import { TEMPLATE_ENDPOINTS, EndpointBuilder } from '../../config/endpoints';

// Template interfaces
export interface TemplateDto {
  id: string;
  name: string;
  version: string;
  stage: string;
  templateJson: any;
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  isActive: boolean;
  isDeleted: boolean;
}

export interface GetTemplatesParams extends PaginationParams {
  isActive?: boolean;
  includeDeleted?: boolean;
  searchTerm?: string;
  productId?: string;
  stage?: string;
  name?: string;
  version?: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface TemplatesResponse extends PaginatedResponse<TemplateDto> {}

export interface CreateTemplateRequest {
  name: string;
  version: string;
  stage: string;
  templateJson: string; // API expects string, not object
  isActive: boolean;
}

export interface UpdateTemplateRequest extends Partial<CreateTemplateRequest> {
  id: string;
}

export interface GroupedTemplate {
  name: string;
  templateName: string;
  displayTemplate: TemplateDto;
  allTemplates: TemplateDto[];
  latestVersion: string;
  totalVersions: number;
}

/**
 * Template API Service Class
 */
export class TemplateApiService extends BaseApiService {
  constructor() {
    super(HttpClientFactory.subscriptionCreationClient, 'TemplateAPI');
  }

  /**
   * Get templates with filtering and pagination
   */
  async getTemplates(params: GetTemplatesParams = {}): Promise<TemplatesResponse> {
    try {
      this.log('getTemplates', { params });

      // Build query parameters
      const queryParams = {
        ...this.buildPaginationParams(params),
        ...this.buildSearchParams(params),
        ...this.buildSortParams(params),
      };

      // Add template-specific parameters
      if (params.productId) queryParams.ProductId = params.productId;
      if (params.stage) queryParams.Stage = params.stage;
      if (params.name) queryParams.Name = params.name;
      if (params.version) queryParams.Version = params.version;

      // Use uppercase parameter names to match existing API
      if (params.pageNumber) queryParams.PageNumber = params.pageNumber;
      if (params.pageSize) queryParams.PageSize = params.pageSize;
      if (params.isActive !== undefined) queryParams.IsActive = params.isActive;
      if (params.includeDeleted !== undefined) queryParams.IncludeDeleted = params.includeDeleted;
      if (params.searchTerm) queryParams.SearchTerm = params.searchTerm;

      const endpoint = EndpointBuilder.buildTemplateEndpoint(queryParams);
      const response = await this.get<TemplatesResponse>(endpoint);

      return response;
    } catch (error) {
      this.handleError('getTemplates', error);
    }
  }

  /**
   * Get template by ID
   */
  async getTemplateById(id: string): Promise<TemplateDto> {
    try {
      this.log('getTemplateById', { id });

      const endpoint = TEMPLATE_ENDPOINTS.TEMPLATE_BY_ID(id);
      const response = await this.get<TemplateDto>(endpoint);

      return response;
    } catch (error) {
      this.handleError('getTemplateById', error);
    }
  }

  /**
   * Create new template
   */
  async createTemplate(data: CreateTemplateRequest): Promise<TemplateDto> {
    try {
      this.log('createTemplate', { data });

      const response = await this.post<TemplateDto>(
        TEMPLATE_ENDPOINTS.TEMPLATES,
        data
      );

      return response;
    } catch (error) {
      this.handleError('createTemplate', error);
    }
  }

  /**
   * Update existing template
   */
  async updateTemplate(data: UpdateTemplateRequest): Promise<TemplateDto> {
    try {
      this.log('updateTemplate', { data });

      const endpoint = TEMPLATE_ENDPOINTS.TEMPLATE_BY_ID(data.id);
      const response = await this.put<TemplateDto>(endpoint, data);

      return response;
    } catch (error) {
      this.handleError('updateTemplate', error);
    }
  }

  /**
   * Delete template
   */
  async deleteTemplate(id: string): Promise<void> {
    try {
      this.log('deleteTemplate', { id });

      const endpoint = TEMPLATE_ENDPOINTS.TEMPLATE_BY_ID(id);
      await this.delete<void>(endpoint);
    } catch (error) {
      this.handleError('deleteTemplate', error);
    }
  }

  /**
   * Get all templates (without pagination)
   */
  async getAllTemplates(): Promise<TemplateDto[]> {
    try {
      this.log('getAllTemplates', {});

      const response = await this.get<TemplateDto[]>(TEMPLATE_ENDPOINTS.TEMPLATES);
      return response;
    } catch (error) {
      this.handleError('getAllTemplates', error);
    }
  }

  /**
   * Get live templates for dropdown selection
   */
  async getLiveTemplates(): Promise<TemplateDto[]> {
    try {
      this.log('getLiveTemplates', {});

      const response = await this.getTemplates({
        stage: 'live',
        isActive: true,
        pageSize: 1000,
        pageNumber: 1
      });

      return response.data;
    } catch (error) {
      this.handleError('getLiveTemplates', error);
    }
  }

  /**
   * Get templates by stage
   */
  async getTemplatesByStage(stage: string): Promise<TemplateDto[]> {
    try {
      this.log('getTemplatesByStage', { stage });

      const response = await this.getTemplates({
        stage,
        isActive: true,
        pageSize: 1000,
        pageNumber: 1
      });

      // Sort templates by name (ascending) and then by version (descending)
      const sortedTemplates = response.data.sort((a, b) => {
        const nameComparison = a.name.localeCompare(b.name);
        if (nameComparison !== 0) {
          return nameComparison;
        }
        return this.compareVersions(b.version, a.version);
      });

      return sortedTemplates;
    } catch (error) {
      this.handleError('getTemplatesByStage', error);
    }
  }

  /**
   * Search templates
   */
  async searchTemplates(searchTerm: string, params: GetTemplatesParams = {}): Promise<TemplatesResponse> {
    try {
      this.log('searchTemplates', { searchTerm, params });

      return this.getTemplates({
        ...params,
        searchTerm,
      });
    } catch (error) {
      this.handleError('searchTemplates', error);
    }
  }

  /**
   * Group templates by name for display
   */
  groupTemplatesByName(templates: TemplateDto[]): GroupedTemplate[] {
    const groups = new Map<string, TemplateDto[]>();

    // Group templates by name field
    templates.forEach(template => {
      const templateName = template.name || 'Unnamed Template';
      const existing = groups.get(templateName) || [];
      existing.push(template);
      groups.set(templateName, existing);
    });

    // Convert groups to GroupedTemplate objects
    return Array.from(groups.entries()).map(([name, templateGroup]) => {
      // Sort templates by version (latest first)
      const sortedTemplates = templateGroup.sort((a, b) =>
        this.compareVersions(b.version, a.version)
      );

      const displayTemplate = sortedTemplates[0];
      let templateName = name;

      // Fallback to a more descriptive name if still unnamed
      if (!templateName || templateName === 'Unnamed Template') {
        const shortId = displayTemplate.id.split('-')[0];
        const createdDate = new Date(displayTemplate.createdAt);
        const dateStr = createdDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        templateName = `Template ${shortId} (${dateStr})`;
      }

      return {
        name,
        templateName,
        displayTemplate,
        allTemplates: sortedTemplates,
        latestVersion: displayTemplate.version,
        totalVersions: templateGroup.length
      };
    });
  }

  /**
   * Filter grouped templates
   */
  filterGroupedTemplates(
    groupedTemplates: GroupedTemplate[],
    searchTerm: string,
    stageFilter: string,
    statusFilter: string
  ): GroupedTemplate[] {
    return groupedTemplates.filter(group => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const productName = group.templateName || 'Unnamed Template';
        const matchesName = productName.toLowerCase().includes(searchLower);
        const matchesId = group.name.toLowerCase().includes(searchLower);
        const matchesVersion = group.latestVersion.toLowerCase().includes(searchLower);

        if (!matchesName && !matchesId && !matchesVersion) {
          return false;
        }
      }

      // Stage filter
      if (stageFilter && stageFilter !== 'all') {
        if (group.displayTemplate.stage.toLowerCase() !== stageFilter.toLowerCase()) {
          return false;
        }
      }

      // Status filter
      if (statusFilter && statusFilter !== 'all') {
        const isActive = group.displayTemplate.isActive;
        if (statusFilter === 'active' && !isActive) return false;
        if (statusFilter === 'inactive' && isActive) return false;
      }

      return true;
    });
  }

  /**
   * Compare version strings
   */
  private compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    const maxLength = Math.max(v1Parts.length, v2Parts.length);

    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }

    return 0;
  }
}

// Export singleton instance
export const templateApiService = new TemplateApiService();
