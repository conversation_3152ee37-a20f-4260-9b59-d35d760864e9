using Ardalis.Specification;
using Domain.Entities;

namespace Application.IntegrationConfigurations.Specifications;

/// <summary>
/// Specification to count integration configurations with filters
/// </summary>
public class IntegrationConfigurationsCountSpec : Specification<IntegrationConfiguration>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public IntegrationConfigurationsCountSpec(
        string? searchTerm,
        bool? isActive,
        Guid? integrationId,
        Guid? integrationApiId,
        Guid? objectId,
        string? direction)
    {
        Query.Where(ic => true); // Base query

        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(ic => (ic.Integration != null && ic.Integration.Name.Contains(searchTerm)) ||
                             (ic.IntegrationApi != null && ic.IntegrationApi.Name.Contains(searchTerm)) ||
                             (ic.Object != null && ic.Object.Name.Contains(searchTerm)));
        }

        if (isActive.HasValue)
        {
            Query.Where(ic => ic.IsActive == isActive.Value);
        }

        if (integrationId.HasValue)
        {
            Query.Where(ic => ic.IntegrationId == integrationId.Value);
        }

        if (integrationApiId.HasValue)
        {
            Query.Where(ic => ic.IntegrationApiId == integrationApiId.Value);
        }

        if (objectId.HasValue)
        {
            Query.Where(ic => ic.ObjectId == objectId.Value);
        }

        if (!string.IsNullOrEmpty(direction))
        {
            Query.Where(ic => ic.Direction == direction);
        }

        Query.Include(ic => ic.Integration)
             .Include(ic => ic.IntegrationApi)
             .Include(ic => ic.Object);
    }
}
