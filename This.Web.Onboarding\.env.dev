# Development Environment Variables
# This file is loaded when NODE_ENV=development or when running 'npm run dev'

# API Configuration (Development)
VITE_API_BASE_URL=https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net/
VITE_LRB_BASE_URL=https://qa-lrb-webapi.leadrat.info/

# Application Configuration
VITE_APP_NAME=Product Builder App (Dev)
VITE_APP_VERSION=0.1.0
VITE_APP_ENVIRONMENT=development

# Development-specific settings
VITE_ENABLE_DEBUG=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_HOT_RELOAD=true
VITE_LOG_LEVEL=debug

# Development API endpoints
VITE_TENANT_API_ENDPOINT=/api/tenants/verify
VITE_USERS_API_ENDPOINT=/api/users/forproducts
VITE_ROLES_API_ENDPOINT=/api/roles

# Development feature flags
VITE_ENABLE_MOCK_DATA=true
VITE_ENABLE_ERROR_BOUNDARY=true
VITE_ENABLE_PERFORMANCE_MONITORING=false
