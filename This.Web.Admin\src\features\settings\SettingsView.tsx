import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>lette,
  <PERSON>,
  Moon,
  Briefcase,
  Check,
  Waves,
  Trees,
  Sunset,
  Monitor,
  Sparkles,
  Info,
  Paintbrush,
  Eye,
  Settings as _SettingsIcon
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { cn } from '@/shared/utils/utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/molecules/Card/Card';
import { Button } from '@/shared/components/atoms/Button/Button';
import type { ThemeName } from '../../types/theme';
// import { NavigationDebug } from '../debug/NavigationDebug';
import './SettingsView.css';

// Enhanced theme configuration with better icons and descriptions
type ExtendedThemeName = ThemeName | 'system';

const themeConfiguration: Record<ExtendedThemeName, {
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
  description: string;
  preview: string[];
  category: 'light' | 'dark' | 'colored' | 'system';
}> = {
  light: {
    icon: Sun,
    gradient: 'from-blue-50 to-indigo-100',
    description: 'Clean and bright interface perfect for daytime use',
    preview: ['#3b82f6', '#64748b', '#10b981'],
    category: 'light'
  },
  professional: {
    icon: Briefcase,
    gradient: 'from-gray-50 to-slate-100',
    description: 'Professional dark theme optimized for productivity',
    preview: ['#212121', '#50BEA7', '#dddddd'],
    category: 'dark'
  },
  dark: {
    icon: Moon,
    gradient: 'from-gray-800 to-gray-900',
    description: 'Easy on the eyes for low-light environments',
    preview: ['#60a5fa', '#94a3b8', '#34d399'],
    category: 'dark'
  },
  ocean: {
    icon: Waves,
    gradient: 'from-blue-50 to-cyan-100',
    description: 'Inspired by deep blue waters and ocean waves',
    preview: ['#0ea5e9', '#06b6d4', '#22d3ee'],
    category: 'colored'
  },
  forest: {
    icon: Trees,
    gradient: 'from-green-50 to-lime-100',
    description: 'Natural greens inspired by forest landscapes',
    preview: ['#22c55e', '#65a30d', '#eab308'],
    category: 'colored'
  },
  sunset: {
    icon: Sunset,
    gradient: 'from-orange-50 to-red-100',
    description: 'Warm oranges and reds like a beautiful sunset',
    preview: ['#f97316', '#ef4444', '#f59e0b'],
    category: 'colored'
  },
  system: {
    icon: Monitor,
    gradient: 'from-purple-50 to-pink-100',
    description: 'Automatically follows your operating system preference',
    preview: ['#8b5cf6', '#ec4899', '#06b6d4'],
    category: 'system'
  }
};

// System theme detection
const getSystemTheme = (): ThemeName => {
  if (typeof window === 'undefined') return 'professional';

  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  return mediaQuery.matches ? 'dark' : 'light';
};

interface SettingsViewProps {
  // Props can be added here if needed for external configuration
}

export const SettingsView: React.FC<SettingsViewProps> = () => {
  const { theme, setTheme, themes } = useTheme();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [systemTheme, setSystemTheme] = useState<ThemeName>(getSystemTheme());
  const [preferredTheme, setPreferredTheme] = useState<ThemeName | 'system'>(() => {
    if (typeof window !== 'undefined') {
      return (localStorage.getItem('app-theme-preference') as ThemeName | 'system') || 'professional';
    }
    return 'professional';
  });
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      const newSystemTheme = e.matches ? 'dark' : 'light';
      setSystemTheme(newSystemTheme);

      // If user has system preference, update the actual theme
      if (preferredTheme === 'system') {
        handleThemeChange(newSystemTheme);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [preferredTheme]);

  // Enhanced theme change handler with transitions
  const handleThemeChange = useCallback((newTheme: ThemeName | 'system') => {
    setIsTransitioning(true);

    // Store user preference
    setPreferredTheme(newTheme);
    localStorage.setItem('app-theme-preference', newTheme);

    // Determine actual theme to apply
    const actualTheme = newTheme === 'system' ? systemTheme : newTheme;

    // Add transition class to document
    document.documentElement.classList.add('theme-transitioning');

    // Apply theme with slight delay for smooth transition
    setTimeout(() => {
      setTheme(actualTheme);

      // Remove transition class after animation
      setTimeout(() => {
        document.documentElement.classList.remove('theme-transitioning');
        setIsTransitioning(false);
      }, 300);
    }, 50);
  }, [setTheme, systemTheme]);

  // Enhanced theme list including system option
  const enhancedThemes = [
    ...themes,
    {
      name: 'system' as ExtendedThemeName,
      displayName: 'System',
      description: 'Follow system preference',
      colors: {
        primary: systemTheme === 'dark' ? '#60a5fa' : '#3b82f6',
        secondary: systemTheme === 'dark' ? '#94a3b8' : '#64748b',
        accent: systemTheme === 'dark' ? '#34d399' : '#10b981'
      }
    }
  ];

  // Filter themes by category
  const filteredThemes = selectedCategory === 'all'
    ? enhancedThemes
    : enhancedThemes.filter(t => {
        const config = themeConfiguration[t.name as ExtendedThemeName];
        return config?.category === selectedCategory;
      });

  // Get current theme configuration
  const currentThemeConfig = themeConfiguration[preferredTheme === 'system' ? 'system' : theme];
  const CurrentThemeIcon = currentThemeConfig?.icon || Palette;

  return (
    <div className="space-y-8 settings-container p-6">
      {/* Current Theme Status */}
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5 text-primary" />
            Current Theme
            {isTransitioning && (
              <Sparkles className="h-4 w-4 text-primary animate-spin" />
            )}
          </CardTitle>
          <CardDescription>
            Your currently active theme configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 p-4 bg-muted/30 rounded-lg">
            <div className="p-3 bg-primary/10 rounded-lg">
              <CurrentThemeIcon className="h-6 w-6 text-primary" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-foreground">
                {preferredTheme === 'system' ? `System (${systemTheme})` : themes.find(t => t.name === theme)?.displayName}
              </h3>
              <p className="text-xs text-muted-foreground">
                {currentThemeConfig?.description}
              </p>
            </div>
            <div className="flex gap-2">
              {currentThemeConfig?.preview.map((color, index) => (
                <div
                  key={index}
                  className="w-4 h-4 rounded-full border border-border/50 theme-color-preview"
                  style={{ '--color': color } as React.CSSProperties}
                  title={`Color ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Paintbrush className="h-5 w-5 text-primary" />
            Theme Selection
          </CardTitle>
          <CardDescription>
            Choose from our collection of carefully crafted themes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'All Themes', icon: Eye },
              { key: 'light', label: 'Light', icon: Sun },
              { key: 'dark', label: 'Dark', icon: Moon },
              { key: 'colored', label: 'Colored', icon: Palette },
              { key: 'system', label: 'System', icon: Monitor }
            ].map(({ key, label, icon: Icon }) => (
              <Button
                key={key}
                variant={selectedCategory === key ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(key)}
                className="flex items-center gap-2"
              >
                <Icon className="h-4 w-4" />
                {label}
              </Button>
            ))}
          </div>

          {/* Theme Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredThemes.map((themeItem) => {
              const config = themeConfiguration[themeItem.name as ExtendedThemeName];
              const IconComponent = config?.icon || Palette;
              const isActive = (themeItem.name === 'system' && preferredTheme === 'system') ||
                             (themeItem.name !== 'system' && preferredTheme === themeItem.name);
              const isSystemTheme = themeItem.name === 'system';

              return (
                <button
                  key={themeItem.name}
                  type="button"
                  onClick={() => handleThemeChange(themeItem.name as ExtendedThemeName)}
                  className={cn(
                    'p-4 rounded-lg border-2 transition-all duration-200 text-left theme-option-card',
                    'hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50',
                    isActive
                      ? 'border-primary bg-primary/5 shadow-md'
                      : 'border-border hover:border-primary/50'
                  )}
                  disabled={isTransitioning}
                  aria-label={`Select ${themeItem.displayName} theme`}
                >
                  <div className="space-y-3">
                    {/* Theme Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={cn(
                          'p-2 rounded-md transition-colors',
                          isActive ? 'bg-primary/20' : 'bg-muted'
                        )}>
                          <IconComponent className={cn(
                            'h-5 w-5',
                            isActive ? 'text-primary' : 'text-muted-foreground'
                          )} />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-foreground">
                            {themeItem.displayName}
                            {isSystemTheme && (
                              <span className="ml-1 text-xxs text-muted-foreground">
                                ({systemTheme})
                              </span>
                            )}
                          </h4>
                        </div>
                      </div>
                      {isActive && (
                        <Check className="h-5 w-5 text-primary" />
                      )}
                    </div>

                    {/* Theme Description */}
                    <p className="text-xs text-muted-foreground">
                      {config?.description}
                    </p>

                    {/* Color Preview */}
                    <div className="flex gap-2">
                      {themeItem.colors && Object.values(themeItem.colors).slice(0, 3).map((color, index) => (
                        <div
                          key={index}
                          className="w-6 h-6 rounded-full border border-border/50 theme-color-preview"
                          style={{ '--color': color } as React.CSSProperties}
                          title={`${Object.keys(themeItem.colors)[index]} color`}
                        />
                      ))}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Theme Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5 text-primary" />
            Theme Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
            <div>
              <h4 className="text-sm font-medium text-foreground mb-2">Features</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Automatic system theme detection</li>
                <li>• Smooth theme transitions</li>
                <li>• Persistent theme preferences</li>
                <li>• Accessibility optimized</li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-medium text-foreground mb-2">Categories</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• <strong>Light:</strong> Bright, clean interfaces</li>
                <li>• <strong>Dark:</strong> Easy on the eyes</li>
                <li>• <strong>Colored:</strong> Vibrant, themed experiences</li>
                <li>• <strong>System:</strong> Follows OS preference</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
