using Ardalis.Specification;
using Domain.Entities;

namespace Application.DisplayActionManagement.Specifications;

/// <summary>
/// Specification to get DisplayAction by Object, Display, and Action IDs
/// </summary>
public class DisplayActionByObjectDisplayActionSpec : Specification<DisplayAction>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DisplayActionByObjectDisplayActionSpec(Guid objectId, Guid displayId, Guid actionId)
    {
        Query.Where(da => da.ObjectId == objectId && 
                          da.DisplayId == displayId && 
                          da.ActionId == actionId && 
                          da.IsActive && 
                          !da.IsDeleted);
        
        // Include related entities for complete information
        Query.Include(da => da.Display);
        Query.Include(da => da.Action);
        Query.Include(da => da.Object);
        
        // Take only one record
        Query.Take(1);
    }
}

/// <summary>
/// Specification to get DisplayActions by Object ID
/// </summary>
public class DisplayActionsByObjectSpec : Specification<DisplayAction>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DisplayActionsByObjectSpec(Guid objectId, bool? isActive = null)
    {
        Query.Where(da => da.ObjectId == objectId && !da.IsDeleted);

        if (isActive.HasValue)
        {
            Query.Where(da => da.IsActive == isActive.Value);
        }

        // Include related entities
        Query.Include(da => da.Display);
        Query.Include(da => da.Action);

        // Order by sort order and then by display name
        Query.OrderBy(da => da.SortOrder)
             .ThenBy(da => da.Display.Name);
    }
}

/// <summary>
/// Specification to get DisplayActions by Display ID
/// </summary>
public class DisplayActionsByDisplaySpec : Specification<DisplayAction>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DisplayActionsByDisplaySpec(Guid displayId, bool? isActive = null)
    {
        Query.Where(da => da.DisplayId == displayId && !da.IsDeleted);

        if (isActive.HasValue)
        {
            Query.Where(da => da.IsActive == isActive.Value);
        }

        // Include related entities
        Query.Include(da => da.Action);
        Query.Include(da => da.Object);

        // Order by sort order and then by action name
        Query.OrderBy(da => da.SortOrder)
             .ThenBy(da => da.Action.Name);
    }
}

/// <summary>
/// Specification to get DisplayActions by Action ID
/// </summary>
public class DisplayActionsByActionSpec : Specification<DisplayAction>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public DisplayActionsByActionSpec(Guid actionId, bool? isActive = null)
    {
        Query.Where(da => da.ActionId == actionId && !da.IsDeleted);

        if (isActive.HasValue)
        {
            Query.Where(da => da.IsActive == isActive.Value);
        }

        // Include related entities
        Query.Include(da => da.Display);
        Query.Include(da => da.Object);

        // Order by sort order and then by display name
        Query.OrderBy(da => da.SortOrder)
             .ThenBy(da => da.Display.Name);
    }
}
