/**
 * API Endpoints Configuration
 * Centralized endpoint definitions for all API services
 */

// Subscription Data API Endpoints
export const SUBSCRIPTION_ENDPOINTS = {
  // Basic subscription operations
  SUBSCRIPTIONS: '/subscriptions',
  SUBSCRIPTION_BY_ID: (id: string) => `/subscriptions/${id}`,
  SUBSCRIPTION_STATS: '/subscriptions/stats',
  SUBSCRIPTION_SEARCH: '/subscriptions/search',
  
  // Comprehensive subscription operations
  COMPREHENSIVE_SUBSCRIPTIONS: '/comprehensive-entity/subscriptions',
  COMPREHENSIVE_SUBSCRIPTION_BY_ID: (id: string) => `/comprehensive-entity/subscriptions/${id}`,
} as const;

// Tenant Data API Endpoints
export const TENANT_ENDPOINTS = {
  TENANTS: '/tenants',
  TENANT_BY_ID: (id: string) => `/tenants/${id}`,
  TENANT_UPSERT: '/tenants/upsert',
} as const;

// Template API Endpoints
export const TEMPLATE_ENDPOINTS = {
  TEMPLATES: '/templates',
  TEMPLATE_BY_ID: (id: string) => `/templates/${id}`,
  TEMPLATES_BY_STAGE: (stage: string) => `/templates?stage=${stage}`,
  TEMPLATES_LIVE: '/templates?stage=live&isActive=true',
} as const;

// Product Structure API Endpoints
export const PRODUCT_ENDPOINTS = {
  PRODUCT_STRUCTURE: '/comprehensive-entity/create-product-structure',
  PRODUCTS: '/products',
  PRODUCT_BY_ID: (id: string) => `/products/${id}`,
} as const;

// Authentication API Endpoints (for future use)
export const AUTH_ENDPOINTS = {
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REFRESH: '/auth/refresh',
  PROFILE: '/auth/profile',
} as const;

// Health Check Endpoints
export const HEALTH_ENDPOINTS = {
  HEALTH: '/health',
  READY: '/ready',
  LIVE: '/live',
} as const;

// All endpoints grouped by service
export const API_ENDPOINTS = {
  SUBSCRIPTION: SUBSCRIPTION_ENDPOINTS,
  TENANT: TENANT_ENDPOINTS,
  TEMPLATE: TEMPLATE_ENDPOINTS,
  PRODUCT: PRODUCT_ENDPOINTS,
  AUTH: AUTH_ENDPOINTS,
  HEALTH: HEALTH_ENDPOINTS,
} as const;

// Endpoint builder utilities
export class EndpointBuilder {
  /**
   * Build subscription endpoint with query parameters
   */
  static buildSubscriptionEndpoint(params: Record<string, any> = {}): string {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value));
      }
    });
    
    const queryString = queryParams.toString();
    return queryString 
      ? `${SUBSCRIPTION_ENDPOINTS.SUBSCRIPTIONS}?${queryString}`
      : SUBSCRIPTION_ENDPOINTS.SUBSCRIPTIONS;
  }

  /**
   * Build comprehensive subscription endpoint with query parameters
   */
  static buildComprehensiveSubscriptionEndpoint(params: Record<string, any> = {}): string {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value));
      }
    });
    
    const queryString = queryParams.toString();
    return queryString 
      ? `${SUBSCRIPTION_ENDPOINTS.COMPREHENSIVE_SUBSCRIPTIONS}?${queryString}`
      : SUBSCRIPTION_ENDPOINTS.COMPREHENSIVE_SUBSCRIPTIONS;
  }

  /**
   * Build template endpoint with query parameters
   */
  static buildTemplateEndpoint(params: Record<string, any> = {}): string {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value));
      }
    });
    
    const queryString = queryParams.toString();
    return queryString 
      ? `${TEMPLATE_ENDPOINTS.TEMPLATES}?${queryString}`
      : TEMPLATE_ENDPOINTS.TEMPLATES;
  }

  /**
   * Build tenant endpoint with query parameters
   */
  static buildTenantEndpoint(params: Record<string, any> = {}): string {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value));
      }
    });
    
    const queryString = queryParams.toString();
    return queryString 
      ? `${TENANT_ENDPOINTS.TENANTS}?${queryString}`
      : TENANT_ENDPOINTS.TENANTS;
  }
}

// Individual endpoint groups are already exported above with 'export const'
