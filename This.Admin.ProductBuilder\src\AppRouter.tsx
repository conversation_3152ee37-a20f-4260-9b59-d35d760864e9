import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Templates, Subscriptions, Settings, ProductBuilder } from './pages';
import { TenantApiTest } from './pages/TenantApiTest';
import { DisplayActionConfig } from './pages/DisplayActionConfig';
import { ContextManagement } from './pages/ContextManagement';
import { Layout } from './components/Layout';
import 'bootstrap/dist/css/bootstrap.min.css';

export const AppRouter: React.FC = () => {
  return (
    <Router>
      <Routes>
        {/* Default route redirects to templates */}
        <Route path="/" element={<Navigate to="/templates" replace />} />
        
        {/* Templates list page */}
        <Route path="/templates" element={
          <Layout>
            <Templates />
          </Layout>
        } />

        {/* Subscriptions page */}
        <Route path="/subscriptions" element={
          <Layout>
            <Subscriptions />
          </Layout>
        } />

        {/* Settings page */}
        <Route path="/settings" element={
          <Layout>
            <Settings />
          </Layout>
        } />

        {/* Tenant API Test page */}
        <Route path="/tenant-api-test" element={
          <Layout>
            <TenantApiTest />
          </Layout>
        } />

        {/* Product builder page - without layout to maintain existing functionality */}
        <Route path="/product-builder" element={<ProductBuilder />} />

        {/* Display & Action Configuration page */}
        <Route path="/display-action-config" element={<DisplayActionConfig />} />

        {/* Context Management page */}
        <Route path="/context-management" element={
          <Layout>
            <ContextManagement />
          </Layout>
        } />

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/templates" replace />} />
      </Routes>
    </Router>
  );
};
