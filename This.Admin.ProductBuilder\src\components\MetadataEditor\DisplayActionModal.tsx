import React, { useState, useCallback, useEffect } from 'react';
import { Mo<PERSON>, Row, Col, Card, Button, Form, Badge, Alert } from 'react-bootstrap';
import { Plus, Edit, Trash2, Settings, Eye, Save, X, Star, StarOff } from 'lucide-react';
import { generateGuid } from '../../utils';

// Types
interface ActionConfig {
  id: string;
  name: string;
  type: 'API' | 'Navigation';
  endpoint?: string;
  navigationTarget?: string;
  buttonStyle: 'Primary' | 'Secondary' | 'Success' | 'Danger' | 'Warning';
  icon: string;
  isSelected: boolean; // Whether this action is selected for the object
  confirmationMessage?: string;
  successMessage?: string;
  errorMessage?: string;
}

interface DisplayConfig {
  id: string;
  name: string;
  displayName: string;
  isDefault: boolean;
  isSelected: boolean; // Whether this display is selected for the object
  sortOrder: number;
  actions: ActionConfig[];
}

interface DisplayActionModalProps {
  show: boolean;
  onHide: () => void;
  selectedNode: any;
  onSave: (selectedDisplays: DisplayConfig[]) => void;
}

// Default configurations with all available displays and actions
const DEFAULT_DISPLAYS: DisplayConfig[] = [
  {
    id: generateGuid(),
    name: 'List',
    displayName: 'List View',
    isDefault: true,
    isSelected: true, // Selected by default
    sortOrder: 1,
    actions: [
      { id: generateGuid(), name: 'Create', type: 'Navigation', navigationTarget: '/create', buttonStyle: 'Primary', icon: 'plus', isSelected: true },
      { id: generateGuid(), name: 'View', type: 'Navigation', navigationTarget: '/view/{id}', buttonStyle: 'Secondary', icon: 'eye', isSelected: true },
      { id: generateGuid(), name: 'Edit', type: 'Navigation', navigationTarget: '/edit/{id}', buttonStyle: 'Warning', icon: 'edit', isSelected: true },
      { id: generateGuid(), name: 'Delete', type: 'Navigation', navigationTarget: '/delete/{id}', buttonStyle: 'Danger', icon: 'trash2', isSelected: true }
    ]
  },
  {
    id: generateGuid(),
    name: 'Create',
    displayName: 'Create New',
    isDefault: false,
    isSelected: true, // Selected by default
    sortOrder: 2,
    actions: [
      { id: generateGuid(), name: 'Save', type: 'API', endpoint: '/api/objects', buttonStyle: 'Success', icon: 'save', isSelected: true },
      { id: generateGuid(), name: 'Cancel', type: 'Navigation', navigationTarget: '/list', buttonStyle: 'Secondary', icon: 'x', isSelected: true }
    ]
  },
  {
    id: generateGuid(),
    name: 'Update',
    displayName: 'Edit',
    isDefault: false,
    isSelected: true, // Selected by default
    sortOrder: 3,
    actions: [
      { id: generateGuid(), name: 'Save', type: 'API', endpoint: '/api/objects/{id}', buttonStyle: 'Success', icon: 'save', isSelected: true },
      { id: generateGuid(), name: 'Cancel', type: 'Navigation', navigationTarget: '/list', buttonStyle: 'Secondary', icon: 'x', isSelected: true }
    ]
  },
  {
    id: generateGuid(),
    name: 'View',
    displayName: 'View Details',
    isDefault: false,
    isSelected: true, // Selected by default
    sortOrder: 4,
    actions: [
      { id: generateGuid(), name: 'Edit', type: 'Navigation', navigationTarget: '/edit/{id}', buttonStyle: 'Primary', icon: 'edit', isSelected: true }
    ]
  }
];

const AVAILABLE_ICONS = [
  { name: 'plus', url: 'https://cdn.jsdelivr.net/npm/lucide@latest/icons/plus.svg' },
  { name: 'eye', url: 'https://cdn.jsdelivr.net/npm/lucide@latest/icons/eye.svg' },
  { name: 'edit', url: 'https://cdn.jsdelivr.net/npm/lucide@latest/icons/edit.svg' },
  { name: 'trash2', url: 'https://cdn.jsdelivr.net/npm/lucide@latest/icons/trash-2.svg' },
  { name: 'save', url: 'https://cdn.jsdelivr.net/npm/lucide@latest/icons/save.svg' },
  { name: 'x', url: 'https://cdn.jsdelivr.net/npm/lucide@latest/icons/x.svg' },
  { name: 'settings', url: 'https://cdn.jsdelivr.net/npm/lucide@latest/icons/settings.svg' },
  { name: 'home', url: 'https://cdn.jsdelivr.net/npm/lucide@latest/icons/home.svg' }
];

const BUTTON_STYLES = ['Primary', 'Secondary', 'Success', 'Danger', 'Warning'];

export const DisplayActionModal: React.FC<DisplayActionModalProps> = ({
  show,
  onHide,
  selectedNode,
  onSave
}) => {
  const [displays, setDisplays] = useState<DisplayConfig[]>(DEFAULT_DISPLAYS);
  const [selectedDisplay, setSelectedDisplay] = useState<DisplayConfig | null>(null);
  const [showActionModal, setShowActionModal] = useState(false);
  const [showDisplayModal, setShowDisplayModal] = useState(false);
  const [editingAction, setEditingAction] = useState<ActionConfig | null>(null);
  const [editingDisplay, setEditingDisplay] = useState<DisplayConfig | null>(null);

  // Initialize displays from selectedNode when modal opens
  useEffect(() => {
    if (show && selectedNode) {
      console.log('🔍 DisplayActionModal opened for node:', selectedNode);
      if (selectedNode.displays && selectedNode.displays.length > 0) {
        console.log('🔍 Loading existing displays:', selectedNode.displays);
        setDisplays(selectedNode.displays);
        setSelectedDisplay(selectedNode.displays[0]);
      } else {
        console.log('🔍 No existing displays, using defaults');
        setDisplays(DEFAULT_DISPLAYS);
        setSelectedDisplay(DEFAULT_DISPLAYS[0]);
      }
    }
  }, [show, selectedNode]);

  // Action form state
  const [actionForm, setActionForm] = useState({
    name: '',
    type: 'Navigation' as 'API' | 'Navigation',
    endpoint: '',
    navigationTarget: '',
    buttonStyle: 'Primary' as any,
    icon: 'plus',
    confirmationMessage: '',
    successMessage: '',
    errorMessage: ''
  });

  // Display form state
  const [displayForm, setDisplayForm] = useState({
    name: '',
    displayName: '',
    isDefault: false,
    sortOrder: 1
  });

  const handleDisplaySelect = (display: DisplayConfig) => {
    setSelectedDisplay(display);
  };

  const handleDisplayToggle = (displayId: string) => {
    setDisplays(prev => prev.map(display => 
      display.id === displayId 
        ? { ...display, isSelected: !display.isSelected }
        : display
    ));
  };

  const handleActionToggle = (displayId: string, actionId: string) => {
    setDisplays(prev => {
      const updated = prev.map(display =>
        display.id === displayId
          ? {
              ...display,
              actions: display.actions.map(action =>
                action.id === actionId
                  ? { ...action, isSelected: !action.isSelected }
                  : action
              )
            }
          : display
      );

      // Update selectedDisplay if it matches the current display
      const updatedDisplay = updated.find(d => d.id === displayId);
      if (selectedDisplay && selectedDisplay.id === displayId && updatedDisplay) {
        setSelectedDisplay(updatedDisplay);
      }

      return updated;
    });
  };

  const handleSetDefaultDisplay = (displayId: string) => {
    setDisplays(prev => prev.map(display => {
      if (display.id === displayId) {
        // Toggle default status - if it's already default, remove it; if not, set it
        return { ...display, isDefault: !display.isDefault };
      } else {
        // If setting a new default, remove default from all others
        const clickedDisplay = prev.find(d => d.id === displayId);
        return { ...display, isDefault: clickedDisplay?.isDefault ? display.isDefault : false };
      }
    }));
  };

  const handleSave = () => {
    // Filter only selected displays and their selected actions
    const selectedDisplays = displays
      .filter(display => display.isSelected)
      .map((display) => ({
        id: display.id,
        name: display.name,
        displayName: display.displayName,
        isDefault: display.isDefault,
        routeTemplate: `/objects/${display.name.toLowerCase()}`, // Generate route template
        icon: display.name.toLowerCase(), // Use display name as icon
        sortOrder: display.sortOrder, // Use actual sortOrder from display
        actions: display.actions
          .filter(action => action.isSelected)
          .map(action => ({
            id: action.id,
            name: action.name,
            description: `${action.name} action`, // Add description
            type: action.type,
            endpointTemplate: action.type === 'API' ? action.endpoint : undefined,
            navigationTarget: action.type === 'Navigation' ? action.navigationTarget : undefined,
            buttonStyle: action.buttonStyle,
            icon: action.icon,
            isActive: true,
            confirmationMessage: action.confirmationMessage,
            successMessage: action.successMessage,
            errorMessage: action.errorMessage
          }))
      }));

    console.log('🔍 DisplayActionModal saving:', selectedDisplays);
    onSave(selectedDisplays);
    onHide();
  };

  const getSelectedActionsCount = (display: DisplayConfig) => {
    return display.actions.filter(action => action.isSelected).length;
  };

  const getTotalSelectedDisplays = () => {
    return displays.filter(display => display.isSelected).length;
  };

  const getTotalSelectedActions = () => {
    return displays.reduce((total, display) => {
      if (display.isSelected) {
        return total + display.actions.filter(action => action.isSelected).length;
      }
      return total;
    }, 0);
  };

  return (
    <Modal show={show} onHide={onHide} size="xl" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          <Settings className="me-2" size={20} />
          Configure Display & Actions for: {selectedNode?.name}
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* Summary */}
        <Alert variant="info" className="mb-4">
          <strong>Selection Summary:</strong> {getTotalSelectedDisplays()} displays selected, {getTotalSelectedActions()} total actions selected
        </Alert>

        <Row>
          {/* Display List */}
          <Col md={4}>
            <Card className="h-100">
              <Card.Header className="bg-primary text-white d-flex justify-content-between align-items-center">
                <h6 className="mb-0">
                  <Settings size={16} className="me-2" />
                  Display Types
                </h6>
                <Button variant="light" size="sm" onClick={() => setShowDisplayModal(true)}>
                  <Plus size={12} className="me-1" />
                  Add
                </Button>
              </Card.Header>
              <Card.Body className="p-0">
                {displays.map((display) => (
                  <div
                    key={display.id}
                    className={`p-3 border-bottom cursor-pointer ${
                      selectedDisplay?.id === display.id ? 'bg-light border-primary border-start border-3' : ''
                    }`}
                    onClick={() => handleDisplaySelect(display)}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="d-flex align-items-center mb-2">
                      <Form.Check
                        type="checkbox"
                        checked={display.isSelected}
                        onChange={() => handleDisplayToggle(display.id)}
                        onClick={(e) => e.stopPropagation()}
                        className="me-2"
                      />
                      <div className="flex-grow-1">
                        <h6 className="mb-0">{display.displayName}</h6>
                        <small className="text-muted">{display.name}</small>
                      </div>
                      <div className="d-flex">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingDisplay(display);
                            setDisplayForm({
                              name: display.name,
                              displayName: display.displayName,
                              isDefault: display.isDefault,
                              sortOrder: display.sortOrder
                            });
                            setShowDisplayModal(true);
                          }}
                          className="me-1"
                          title="Edit display"
                        >
                          <Edit size={10} />
                        </Button>
                        <Button
                          variant="link"
                          size="sm"
                          className="p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSetDefaultDisplay(display.id);
                          }}
                          title={display.isDefault ? "Remove as default" : "Set as default"}
                        >
                          {display.isDefault ? (
                            <Star size={14} className="text-warning" fill="currentColor" />
                          ) : (
                            <StarOff size={14} className="text-muted" />
                          )}
                        </Button>
                      </div>
                    </div>
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        {display.isDefault && <Badge bg="warning" text="dark" className="me-1">Default</Badge>}
                        {display.isSelected && <Badge bg="success">Selected</Badge>}
                      </div>
                      <Badge bg="secondary">
                        {getSelectedActionsCount(display)}/{display.actions.length} actions
                      </Badge>
                    </div>
                  </div>
                ))}
              </Card.Body>
            </Card>
          </Col>

          {/* Action Configuration */}
          <Col md={8}>
            {selectedDisplay ? (
              <Card className="h-100">
                <Card.Header className="d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-1">{selectedDisplay.displayName} Actions</h6>
                    <small className="text-muted">Select actions for this display type</small>
                  </div>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => {
                      setEditingAction(null);
                      setActionForm({
                        name: '',
                        type: 'Navigation',
                        endpoint: '',
                        navigationTarget: '',
                        buttonStyle: 'Primary',
                        icon: 'plus',
                        confirmationMessage: '',
                        successMessage: '',
                        errorMessage: ''
                      });
                      setShowActionModal(true);
                    }}
                  >
                    <Plus size={14} className="me-1" />
                    Add Action
                  </Button>
                </Card.Header>
                <Card.Body>
                  {selectedDisplay.actions.length === 0 ? (
                    <div className="text-center py-4">
                      <Settings size={48} className="mb-3 opacity-50" />
                      <h6>No Actions Available</h6>
                      <p className="text-muted">Click "Add Action" to create actions for this display.</p>
                    </div>
                  ) : (
                    <Row>
                      {selectedDisplay.actions.map((action) => (
                        <Col md={6} key={action.id} className="mb-3">
                          <Card className={`border ${action.isSelected ? 'border-primary' : 'border-light'}`}>
                            <Card.Body className="p-3">
                              <div className="d-flex align-items-start">
                                <Form.Check
                                  type="checkbox"
                                  checked={action.isSelected}
                                  onChange={() => handleActionToggle(selectedDisplay.id, action.id)}
                                  className="me-3 mt-1"
                                />
                                <div className="flex-grow-1">
                                  <h6 className="mb-1">{action.name}</h6>
                                  <div className="mb-2">
                                    <Badge bg={action.type === 'API' ? 'success' : 'info'} className="me-1">
                                      {action.type}
                                    </Badge>
                                    <Badge bg="secondary">{action.buttonStyle}</Badge>
                                  </div>
                                  <small className="text-muted">
                                    {action.type === 'API' && action.endpoint && (
                                      <div><strong>Endpoint:</strong> {action.endpoint}</div>
                                    )}
                                    {action.type === 'Navigation' && action.navigationTarget && (
                                      <div><strong>Target:</strong> {action.navigationTarget}</div>
                                    )}
                                    <div><strong>Icon:</strong> {action.icon}</div>
                                  </small>
                                </div>
                                <div className="d-flex">
                                  <Button
                                    variant="outline-primary"
                                    size="sm"
                                    onClick={() => {
                                      setEditingAction(action);
                                      setActionForm({
                                        name: action.name,
                                        type: action.type,
                                        endpoint: action.endpoint || '',
                                        navigationTarget: action.navigationTarget || '',
                                        buttonStyle: action.buttonStyle,
                                        icon: action.icon,
                                        confirmationMessage: action.confirmationMessage || '',
                                        successMessage: action.successMessage || '',
                                        errorMessage: action.errorMessage || ''
                                      });
                                      setShowActionModal(true);
                                    }}
                                    className="me-1"
                                    title="Edit action"
                                  >
                                    <Edit size={10} />
                                  </Button>
                                  <Button
                                    variant="outline-danger"
                                    size="sm"
                                    onClick={() => {
                                      setDisplays(prev => {
                                        const updated = prev.map(display =>
                                          display.id === selectedDisplay.id
                                            ? { ...display, actions: display.actions.filter(a => a.id !== action.id) }
                                            : display
                                        );

                                        // Update selectedDisplay to reflect the deletion
                                        const updatedDisplay = updated.find(d => d.id === selectedDisplay.id);
                                        if (updatedDisplay) {
                                          setSelectedDisplay(updatedDisplay);
                                        }

                                        return updated;
                                      });
                                    }}
                                    title="Delete action"
                                  >
                                    <Trash2 size={10} />
                                  </Button>
                                </div>
                              </div>
                            </Card.Body>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  )}
                </Card.Body>
              </Card>
            ) : (
              <Card className="h-100">
                <Card.Body className="d-flex align-items-center justify-content-center">
                  <div className="text-center text-muted">
                    <Eye size={48} className="mb-3 opacity-50" />
                    <h6>Select a Display Type</h6>
                    <p>Choose a display type from the left panel to configure its actions.</p>
                  </div>
                </Card.Body>
              </Card>
            )}
          </Col>
        </Row>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSave}>
          <Save className="me-1" size={16} />
          Save Configuration ({getTotalSelectedDisplays()} displays, {getTotalSelectedActions()} actions)
        </Button>
      </Modal.Footer>

      {/* Action Modal */}
      <Modal show={showActionModal} onHide={() => setShowActionModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{editingAction ? 'Edit Action' : 'Add New Action'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Action Name</Form.Label>
                  <Form.Control
                    type="text"
                    value={actionForm.name}
                    onChange={(e) => setActionForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Save, Delete, Create"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Action Type</Form.Label>
                  <Form.Select
                    value={actionForm.type}
                    onChange={(e) => setActionForm(prev => ({ ...prev, type: e.target.value as any }))}
                  >
                    <option value="Navigation">Navigation</option>
                    <option value="API">API Call</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            {actionForm.type === 'API' && (
              <Form.Group className="mb-3">
                <Form.Label>API Endpoint</Form.Label>
                <Form.Control
                  type="text"
                  value={actionForm.endpoint}
                  onChange={(e) => setActionForm(prev => ({ ...prev, endpoint: e.target.value }))}
                  placeholder="/api/objects/{id}"
                />
              </Form.Group>
            )}

            {actionForm.type === 'Navigation' && (
              <Form.Group className="mb-3">
                <Form.Label>Navigation Target</Form.Label>
                <Form.Control
                  type="text"
                  value={actionForm.navigationTarget}
                  onChange={(e) => setActionForm(prev => ({ ...prev, navigationTarget: e.target.value }))}
                  placeholder="/objects/list"
                />
              </Form.Group>
            )}

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Button Style</Form.Label>
                  <Form.Select
                    value={actionForm.buttonStyle}
                    onChange={(e) => setActionForm(prev => ({ ...prev, buttonStyle: e.target.value as any }))}
                  >
                    {BUTTON_STYLES.map(style => (
                      <option key={style} value={style}>{style}</option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Icon</Form.Label>
                  <Form.Control
                    type="text"
                    value={actionForm.icon}
                    onChange={(e) => setActionForm(prev => ({ ...prev, icon: e.target.value }))}
                    placeholder="Icon name (e.g., plus, edit) or full URL"
                  />
                  <Form.Text className="text-muted">
                    Enter icon name (plus, edit, trash, save, etc.) or full icon URL
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>

            {/* Optional Message Fields */}
            <h6 className="mb-3 text-muted">Optional Messages</h6>

            <Form.Group className="mb-3">
              <Form.Label>Confirmation Message</Form.Label>
              <Form.Control
                type="text"
                value={actionForm.confirmationMessage}
                onChange={(e) => setActionForm(prev => ({ ...prev, confirmationMessage: e.target.value }))}
                placeholder="e.g., Are you sure you want to delete this item?"
              />
              <Form.Text className="text-muted">
                Message shown in confirmation dialog (optional)
              </Form.Text>
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Success Message</Form.Label>
                  <Form.Control
                    type="text"
                    value={actionForm.successMessage}
                    onChange={(e) => setActionForm(prev => ({ ...prev, successMessage: e.target.value }))}
                    placeholder="e.g., Item saved successfully"
                  />
                  <Form.Text className="text-muted">
                    Message shown on successful action (optional)
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Error Message</Form.Label>
                  <Form.Control
                    type="text"
                    value={actionForm.errorMessage}
                    onChange={(e) => setActionForm(prev => ({ ...prev, errorMessage: e.target.value }))}
                    placeholder="e.g., Failed to save item"
                  />
                  <Form.Text className="text-muted">
                    Message shown on action failure (optional)
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowActionModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              if (!selectedDisplay || !actionForm.name) return;

              const newAction: ActionConfig = {
                id: editingAction?.id || generateGuid(),
                name: actionForm.name,
                type: actionForm.type,
                endpoint: actionForm.type === 'API' ? actionForm.endpoint : undefined,
                navigationTarget: actionForm.type === 'Navigation' ? actionForm.navigationTarget : undefined,
                buttonStyle: actionForm.buttonStyle,
                icon: actionForm.icon,
                isSelected: true,
                confirmationMessage: actionForm.confirmationMessage || undefined,
                successMessage: actionForm.successMessage || undefined,
                errorMessage: actionForm.errorMessage || undefined
              };

              setDisplays(prev => {
                const updated = prev.map(display => {
                  if (display.id === selectedDisplay.id) {
                    const updatedActions = editingAction
                      ? display.actions.map(action => action.id === editingAction.id ? newAction : action)
                      : [...display.actions, newAction];

                    return { ...display, actions: updatedActions };
                  }
                  return display;
                });

                // Update selectedDisplay to reflect the new action
                const updatedDisplay = updated.find(d => d.id === selectedDisplay.id);
                if (updatedDisplay) {
                  setSelectedDisplay(updatedDisplay);
                }

                return updated;
              });

              setShowActionModal(false);
              setEditingAction(null);
            }}
            disabled={!actionForm.name}
          >
            {editingAction ? 'Update Action' : 'Add Action'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Display Modal */}
      <Modal show={showDisplayModal} onHide={() => setShowDisplayModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>{editingDisplay ? 'Edit Display' : 'Add New Display'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Display Name</Form.Label>
              <Form.Control
                type="text"
                value={displayForm.displayName}
                onChange={(e) => setDisplayForm(prev => ({ ...prev, displayName: e.target.value }))}
                placeholder="e.g., List View, Create Form"
              />
              <Form.Text className="text-muted">
                This is the user-friendly name shown in the interface
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Internal Name</Form.Label>
              <Form.Control
                type="text"
                value={displayForm.name}
                onChange={(e) => setDisplayForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., List, Create, Update, View"
              />
              <Form.Text className="text-muted">
                Internal identifier for this display type
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Sort Order</Form.Label>
              <Form.Control
                type="number"
                min="1"
                value={displayForm.sortOrder}
                onChange={(e) => setDisplayForm(prev => ({ ...prev, sortOrder: parseInt(e.target.value) || 1 }))}
              />
              <Form.Text className="text-muted">
                Display order in the interface (lower numbers appear first)
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Check
                type="switch"
                id="default-display-switch"
                label="Set as Default Display"
                checked={displayForm.isDefault}
                onChange={(e) => setDisplayForm(prev => ({ ...prev, isDefault: e.target.checked }))}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDisplayModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              if (!displayForm.name || !displayForm.displayName) return;

              const newDisplay: DisplayConfig = {
                id: editingDisplay?.id || generateGuid(),
                name: displayForm.name,
                displayName: displayForm.displayName,
                isDefault: displayForm.isDefault,
                isSelected: true,
                sortOrder: displayForm.sortOrder,
                actions: editingDisplay?.actions || []
              };

              setDisplays(prev => {
                let updated = editingDisplay
                  ? prev.map(display => display.id === editingDisplay.id ? newDisplay : display)
                  : [...prev, newDisplay];

                if (newDisplay.isDefault) {
                  updated = updated.map(display => ({
                    ...display,
                    isDefault: display.id === newDisplay.id
                  }));
                }

                return updated;
              });

              setShowDisplayModal(false);
              setEditingDisplay(null);
              if (!editingDisplay) {
                setSelectedDisplay(newDisplay);
              }
            }}
            disabled={!displayForm.name || !displayForm.displayName}
          >
            {editingDisplay ? 'Update Display' : 'Create Display'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Modal>
  );
};
