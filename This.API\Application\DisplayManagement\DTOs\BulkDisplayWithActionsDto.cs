using System.ComponentModel.DataAnnotations;

namespace Application.DisplayManagement.DTOs;

/// <summary>
/// Bulk upsert request DTO for multiple Displays with their Actions
/// </summary>
public class BulkDisplayWithActionsDto
{
    /// <summary>
    /// List of displays with their associated actions to upsert
    /// </summary>
    [Required]
    [MinLength(1, ErrorMessage = "At least one display must be provided")]
    public List<DisplayWithActionsDto> Displays { get; set; } = new List<DisplayWithActionsDto>();
}

/// <summary>
/// Bulk upsert response DTO containing results for all processed displays
/// </summary>
public class BulkDisplayWithActionsResponseDto
{
    /// <summary>
    /// List of display results with their action results
    /// </summary>
    public List<DisplayWithActionsResponseDto> DisplayResults { get; set; } = new List<DisplayWithActionsResponseDto>();

    /// <summary>
    /// Total number of displays processed
    /// </summary>
    public int TotalDisplaysProcessed { get; set; }

    /// <summary>
    /// Total number of displays created
    /// </summary>
    public int TotalDisplaysCreated { get; set; }

    /// <summary>
    /// Total number of displays updated
    /// </summary>
    public int TotalDisplaysUpdated { get; set; }

    /// <summary>
    /// Total number of actions processed across all displays
    /// </summary>
    public int TotalActionsProcessed { get; set; }

    /// <summary>
    /// Total number of actions created across all displays
    /// </summary>
    public int TotalActionsCreated { get; set; }

    /// <summary>
    /// Total number of actions updated across all displays
    /// </summary>
    public int TotalActionsUpdated { get; set; }

    /// <summary>
    /// Total number of DisplayAction relationships processed
    /// </summary>
    public int TotalDisplayActionsProcessed { get; set; }

    /// <summary>
    /// Total number of DisplayAction relationships created
    /// </summary>
    public int TotalDisplayActionsCreated { get; set; }

    /// <summary>
    /// Total number of DisplayAction relationships updated
    /// </summary>
    public int TotalDisplayActionsUpdated { get; set; }

    /// <summary>
    /// Summary of the bulk operation performed
    /// </summary>
    public string OperationSummary { get; set; } = string.Empty;

    /// <summary>
    /// Total processing time in milliseconds for the entire bulk operation
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// List of any errors that occurred during processing
    /// </summary>
    public List<string> Errors { get; set; } = new List<string>();

    /// <summary>
    /// Whether the entire bulk operation was successful
    /// </summary>
    public bool IsSuccessful { get; set; } = true;
}
