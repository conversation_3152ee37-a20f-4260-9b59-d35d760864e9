/**
 * SubscriptionTable Organism
 * Complete subscription table with filtering, actions, and data management
 */

import React from 'react';
import { Card, Badge } from 'react-bootstrap';
import { DataTable, TableColumn, TableAction } from '../molecules/DataTable';
import { FilterBar, createSubscriptionFilters } from '../molecules/FilterBar';
import { ActionBar, createSubscriptionActions } from '../molecules/ActionBar';
import { StatusBadge } from '../atoms/StatusBadge';
import { useSubscriptions, useTenants } from '../../store/hooks';
import type { SubscriptionDto } from '../../services/api';
import { SubscriptionUtils } from '../../services/utils/subscriptionUtils';

export interface SubscriptionTableProps {
  onViewSubscription?: (subscription: SubscriptionDto) => void;
  onEditSubscription?: (subscription: SubscriptionDto) => void;
  onDeleteSubscription?: (subscription: SubscriptionDto) => void;
  onAddSubscription?: () => void;
  className?: string;
  maxHeight?: string | number;
}

export const SubscriptionTable: React.FC<SubscriptionTableProps> = ({
  onViewSubscription,
  onEditSubscription,
  onDeleteSubscription,
  onAddSubscription,
  className = '',
  maxHeight = '600px'
}) => {
  const {
    subscriptions,
    filters,
    loading,
    loadSubscriptions,
    setSearch,
    setStatus,
    setTenant
  } = useSubscriptions();

  const { dropdownOptions: tenantOptions } = useTenants();

  // Handle filter changes
  const handleFilterChange = (filterKey: string, value: string) => {
    switch (filterKey) {
      case 'tenant':
        setTenant(value);
        break;
      case 'status':
        setStatus(value);
        break;
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    loadSubscriptions();
  };

  // Table columns configuration
  const columns: TableColumn<SubscriptionDto>[] = [
    {
      key: 'productName',
      title: 'Product Name',
      dataIndex: 'productName',
      render: (value, record) => (
        <div>
          <div className="fw-medium">
            {value || `Product ${record.productId.substring(0, 8)}`}
          </div>
          <small className="text-muted">
            ID: {record.id.substring(0, 8)}...
          </small>
        </div>
      ),
      width: '200px'
    },
    {
      key: 'tenant',
      title: 'Tenant',
      dataIndex: 'tenantName',
      render: (value, record) => (
        <div>
          <div>{value || record.tenantId}</div>
          {value && (
            <small className="text-muted">
              {record.tenantId}
            </small>
          )}
        </div>
      ),
      width: '180px'
    },
    {
      key: 'type',
      title: 'Type',
      dataIndex: 'subscriptionType',
      render: (value) => (
        <StatusBadge 
          status={value} 
          variant="custom"
          customVariant={SubscriptionUtils.getTypeBadgeVariant(value)}
        />
      ),
      width: '120px'
    },
    {
      key: 'status',
      title: 'Status',
      dataIndex: 'status',
      render: (value, record) => {
        const statusText = SubscriptionUtils.getStatusText(record);
        const variant = SubscriptionUtils.getStatusBadgeVariant(value);
        
        return (
          <StatusBadge 
            status={statusText} 
            variant="custom"
            customVariant={variant}
          />
        );
      },
      width: '140px'
    },
    {
      key: 'period',
      title: 'Period',
      render: (_, record) => (
        <div>
          <div className="small">
            {SubscriptionUtils.formatPeriod(record)}
          </div>
          {record.daysUntilExpiration !== null && (
            <small className={`text-${
              SubscriptionUtils.isExpired(record) 
                ? 'danger' 
                : SubscriptionUtils.isExpiringSoon(record) 
                  ? 'warning' 
                  : 'muted'
            }`}>
              {record.daysUntilExpiration <= 0 
                ? 'Expired' 
                : `${record.daysUntilExpiration} days left`
              }
            </small>
          )}
        </div>
      ),
      width: '160px'
    },
    {
      key: 'pricing',
      title: 'Pricing',
      render: (_, record) => (
        <div>
          <div className="fw-medium">
            {SubscriptionUtils.formatPrice(record.pricingTier || 'standard')}
          </div>
          <small className="text-muted">
            {SubscriptionUtils.calculateUserCount(record.metadataCount)} users
          </small>
        </div>
      ),
      width: '120px'
    },
    {
      key: 'version',
      title: 'Version',
      dataIndex: 'version',
      render: (value) => (
        <Badge bg="secondary" className="font-monospace">
          v{value}
        </Badge>
      ),
      width: '80px'
    }
  ];

  // Table actions configuration
  const actions: TableAction<SubscriptionDto>[] = [
    {
      key: 'view',
      icon: '👁️',
      variant: 'outline-primary',
      onClick: (record) => onViewSubscription?.(record),
      tooltip: 'View Details'
    },
    {
      key: 'edit',
      icon: '✏️',
      variant: 'outline-secondary',
      onClick: (record) => onEditSubscription?.(record),
      tooltip: 'Edit Subscription'
    },
    {
      key: 'delete',
      icon: '🗑️',
      variant: 'outline-danger',
      onClick: (record) => onDeleteSubscription?.(record),
      tooltip: 'Delete Subscription'
    }
  ].filter(action => {
    // Only include actions that have handlers
    switch (action.key) {
      case 'view':
        return !!onViewSubscription;
      case 'edit':
        return !!onEditSubscription;
      case 'delete':
        return !!onDeleteSubscription;
      default:
        return true;
    }
  });

  // Filter configuration
  const filterConfigs = createSubscriptionFilters(
    filters.status,
    filters.tenantId,
    [
      { value: '', label: 'All Tenants' },
      ...tenantOptions.map(option => ({
        value: option.value,
        label: option.label
      }))
    ]
  );

  // Action configuration
  const actionConfigs = createSubscriptionActions(
    handleRefresh,
    onAddSubscription || (() => {}),
    loading.list
  );

  return (
    <Card className={`modern-card ${className}`}>
      <Card.Header className="bg-transparent border-0 pb-0">
        <ActionBar
          title="Subscriptions"
          subtitle={`${subscriptions.length} subscription${subscriptions.length !== 1 ? 's' : ''} found`}
          actions={onAddSubscription ? actionConfigs : actionConfigs.filter(a => a.key !== 'add')}
          loading={loading.list}
        />
      </Card.Header>

      <Card.Body className="pt-3">
        <FilterBar
          searchValue={filters.searchTerm}
          onSearchChange={setSearch}
          searchPlaceholder="Search subscriptions..."
          filters={filterConfigs}
          onFilterChange={handleFilterChange}
          className="mb-3"
          disabled={loading.list}
        />

        <DataTable
          data={subscriptions}
          columns={columns}
          actions={actions}
          loading={loading.list}
          hover
          maxHeight={maxHeight}
          empty={{
            icon: '📋',
            title: 'No subscriptions found',
            description: 'No subscriptions match your current search and filter criteria'
          }}
        />
      </Card.Body>
    </Card>
  );
};
