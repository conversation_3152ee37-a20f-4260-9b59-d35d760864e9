import React, { useState, useCallback } from 'react';
import { Con<PERSON><PERSON>, Row, Col, Card, Button, Form, Badge, Modal, Alert } from 'react-bootstrap';
import { ArrowLeft, Plus, Edit, Trash2, Settings, Eye, Save, X, Star, StarOff } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { generateGuid } from '../utils';

// Types
interface ActionConfig {
  id: string;
  name: string;
  type: 'API' | 'Navigation';
  endpoint?: string;
  navigationTarget?: string;
  buttonStyle: 'Primary' | 'Secondary' | 'Success' | 'Danger' | 'Warning';
  icon: string;
}

interface DisplayConfig {
  id: string;
  name: string;
  displayName: string;
  isDefault: boolean;
  actions: ActionConfig[];
}

// Default configurations
const DEFAULT_DISPLAYS: DisplayConfig[] = [
  {
    id: generateGuid(),
    name: 'List',
    displayName: 'List View',
    isDefault: true,
    actions: [
      { id: generateGuid(), name: 'Create', type: 'Navigation', navigationTarget: '/create', buttonStyle: 'Primary', icon: 'plus' },
      { id: generateGuid(), name: 'View', type: 'Navigation', navigationTarget: '/view/{id}', buttonStyle: 'Secondary', icon: 'eye' },
      { id: generateGuid(), name: 'Edit', type: 'Navigation', navigationTarget: '/edit/{id}', buttonStyle: 'Warning', icon: 'edit' },
      { id: generateGuid(), name: 'Delete', type: 'Navigation', navigationTarget: '/delete/{id}', buttonStyle: 'Danger', icon: 'trash' }
    ]
  },
  {
    id: generateGuid(),
    name: 'Create',
    displayName: 'Create New',
    isDefault: false,
    actions: [
      { id: generateGuid(), name: 'Save', type: 'API', endpoint: '/api/objects', buttonStyle: 'Success', icon: 'save' },
      { id: generateGuid(), name: 'Cancel', type: 'Navigation', navigationTarget: '/list', buttonStyle: 'Secondary', icon: 'x' }
    ]
  },
  {
    id: generateGuid(),
    name: 'Update',
    displayName: 'Edit',
    isDefault: false,
    actions: [
      { id: generateGuid(), name: 'Save', type: 'API', endpoint: '/api/objects/{id}', buttonStyle: 'Success', icon: 'save' },
      { id: generateGuid(), name: 'Cancel', type: 'Navigation', navigationTarget: '/list', buttonStyle: 'Secondary', icon: 'x' }
    ]
  },
  {
    id: generateGuid(),
    name: 'View',
    displayName: 'View Details',
    isDefault: false,
    actions: [
      { id: generateGuid(), name: 'Edit', type: 'Navigation', navigationTarget: '/edit/{id}', buttonStyle: 'Primary', icon: 'edit' }
    ]
  }
];

const AVAILABLE_ICONS = ['plus', 'eye', 'edit', 'trash', 'save', 'x', 'settings', 'home'];
const BUTTON_STYLES = ['Primary', 'Secondary', 'Success', 'Danger', 'Warning'];

export const DisplayActionConfig: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const selectedNode = location.state?.selectedNode;

  const [displays, setDisplays] = useState<DisplayConfig[]>(DEFAULT_DISPLAYS);
  const [selectedDisplay, setSelectedDisplay] = useState<DisplayConfig | null>(null);
  const [showActionModal, setShowActionModal] = useState(false);
  const [showDisplayModal, setShowDisplayModal] = useState(false);
  const [editingAction, setEditingAction] = useState<ActionConfig | null>(null);
  const [editingDisplay, setEditingDisplay] = useState<DisplayConfig | null>(null);
  const [showSaveAlert, setShowSaveAlert] = useState(false);

  // Action form state
  const [actionForm, setActionForm] = useState({
    name: '',
    type: 'Navigation' as 'API' | 'Navigation',
    endpoint: '',
    navigationTarget: '',
    buttonStyle: 'Primary' as any,
    icon: 'plus'
  });

  // Display form state
  const [displayForm, setDisplayForm] = useState({
    name: '',
    displayName: '',
    isDefault: false
  });

  const handleBack = () => {
    navigate('/product-builder');
  };

  const handleDisplaySelect = (display: DisplayConfig) => {
    setSelectedDisplay(display);
  };

  const handleSetDefaultDisplay = (displayId: string) => {
    setDisplays(prev => prev.map(display => ({
      ...display,
      isDefault: display.id === displayId
    })));
  };

  const handleAddDisplay = () => {
    setEditingDisplay(null);
    setDisplayForm({
      name: '',
      displayName: '',
      isDefault: false
    });
    setShowDisplayModal(true);
  };

  const handleEditDisplay = (display: DisplayConfig) => {
    setEditingDisplay(display);
    setDisplayForm({
      name: display.name,
      displayName: display.displayName,
      isDefault: display.isDefault
    });
    setShowDisplayModal(true);
  };

  const handleSaveDisplay = () => {
    if (!displayForm.name || !displayForm.displayName) return;

    const newDisplay: DisplayConfig = {
      id: editingDisplay?.id || generateGuid(),
      name: displayForm.name,
      displayName: displayForm.displayName,
      isDefault: displayForm.isDefault,
      actions: editingDisplay?.actions || []
    };

    setDisplays(prev => {
      let updated = editingDisplay
        ? prev.map(display => display.id === editingDisplay.id ? newDisplay : display)
        : [...prev, newDisplay];

      // If this display is set as default, remove default from others
      if (newDisplay.isDefault) {
        updated = updated.map(display => ({
          ...display,
          isDefault: display.id === newDisplay.id
        }));
      }

      return updated;
    });

    setShowDisplayModal(false);
    if (!editingDisplay) {
      setSelectedDisplay(newDisplay);
    }
  };

  const handleDeleteDisplay = (displayId: string) => {
    setDisplays(prev => prev.filter(display => display.id !== displayId));
    if (selectedDisplay?.id === displayId) {
      setSelectedDisplay(null);
    }
  };

  const handleAddAction = () => {
    setEditingAction(null);
    setActionForm({
      name: '',
      type: 'Navigation',
      endpoint: '',
      navigationTarget: '',
      buttonStyle: 'Primary',
      icon: 'plus'
    });
    setShowActionModal(true);
  };

  const handleEditAction = (action: ActionConfig) => {
    setEditingAction(action);
    setActionForm({
      name: action.name,
      type: action.type,
      endpoint: action.endpoint || '',
      navigationTarget: action.navigationTarget || '',
      buttonStyle: action.buttonStyle,
      icon: action.icon
    });
    setShowActionModal(true);
  };

  const handleSaveAction = () => {
    if (!selectedDisplay || !actionForm.name) return;

    const newAction: ActionConfig = {
      id: editingAction?.id || generateGuid(),
      name: actionForm.name,
      type: actionForm.type,
      endpoint: actionForm.type === 'API' ? actionForm.endpoint : undefined,
      navigationTarget: actionForm.type === 'Navigation' ? actionForm.navigationTarget : undefined,
      buttonStyle: actionForm.buttonStyle,
      icon: actionForm.icon
    };

    setDisplays(prev => prev.map(display => {
      if (display.id === selectedDisplay.id) {
        const updatedActions = editingAction
          ? display.actions.map(action => action.id === editingAction.id ? newAction : action)
          : [...display.actions, newAction];
        
        const updatedDisplay = { ...display, actions: updatedActions };
        setSelectedDisplay(updatedDisplay);
        return updatedDisplay;
      }
      return display;
    }));

    setShowActionModal(false);
  };

  const handleDeleteAction = (actionId: string) => {
    if (!selectedDisplay) return;

    setDisplays(prev => prev.map(display => {
      if (display.id === selectedDisplay.id) {
        const updatedDisplay = {
          ...display,
          actions: display.actions.filter(action => action.id !== actionId)
        };
        setSelectedDisplay(updatedDisplay);
        return updatedDisplay;
      }
      return display;
    }));
  };

  const handleSaveConfiguration = () => {
    // Here you would save the configuration back to the selected node
    console.log('Saving configuration:', displays);
    setShowSaveAlert(true);
    setTimeout(() => setShowSaveAlert(false), 3000);
  };

  return (
    <Container fluid className="py-4">
      {showSaveAlert && (
        <Alert variant="success" className="mb-4">
          Display & Action configuration saved successfully!
        </Alert>
      )}

      {/* Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <Button variant="outline-secondary" onClick={handleBack} className="me-3">
                <ArrowLeft size={16} className="me-1" />
                Back to Builder
              </Button>
              <div>
                <h2 className="mb-1">Display & Action Configuration</h2>
                <p className="text-muted mb-0">
                  Configure displays and their associated actions for: <strong>{selectedNode?.name || 'Object'}</strong>
                </p>
              </div>
            </div>
            <Button variant="success" onClick={handleSaveConfiguration}>
              <Save size={16} className="me-1" />
              Save Configuration
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Display List */}
        <Col md={4}>
          <Card className="h-100">
            <Card.Header className="bg-primary text-white d-flex justify-content-between align-items-center">
              <h5 className="mb-0">
                <Settings size={18} className="me-2" />
                Display Types
              </h5>
              <Button variant="light" size="sm" onClick={handleAddDisplay}>
                <Plus size={14} className="me-1" />
                Add Display
              </Button>
            </Card.Header>
            <Card.Body className="p-0">
              {displays.map((display) => (
                <div
                  key={display.id}
                  className={`p-3 border-bottom cursor-pointer ${
                    selectedDisplay?.id === display.id ? 'bg-light border-primary border-start border-3' : ''
                  }`}
                  onClick={() => handleDisplaySelect(display)}
                  style={{ cursor: 'pointer' }}
                >
                  <div className="d-flex justify-content-between align-items-center">
                    <div className="flex-grow-1">
                      <div className="d-flex align-items-center">
                        <h6 className="mb-1 me-2">{display.displayName}</h6>
                        <Button
                          variant="link"
                          size="sm"
                          className="p-0 me-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSetDefaultDisplay(display.id);
                          }}
                          title={display.isDefault ? "Remove as default" : "Set as default"}
                        >
                          {display.isDefault ? (
                            <Star size={16} className="text-warning" fill="currentColor" />
                          ) : (
                            <StarOff size={16} className="text-muted" />
                          )}
                        </Button>
                      </div>
                      <small className="text-muted">{display.name}</small>
                      {display.isDefault && <div><Badge bg="warning" text="dark" className="mt-1">Default Display</Badge></div>}
                    </div>
                    <div className="text-end">
                      <div className="mb-1">
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditDisplay(display);
                          }}
                          className="me-1"
                        >
                          <Edit size={12} />
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteDisplay(display.id);
                          }}
                          disabled={displays.length <= 1}
                        >
                          <Trash2 size={12} />
                        </Button>
                      </div>
                      <div>
                        <Badge bg="secondary">{display.actions.length} actions</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </Card.Body>
          </Card>
        </Col>

        {/* Action Configuration */}
        <Col md={8}>
          {selectedDisplay ? (
            <Card className="h-100">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <div>
                  <h5 className="mb-1">{selectedDisplay.displayName} Actions</h5>
                  <small className="text-muted">Configure actions for this display type</small>
                </div>
                <Button variant="primary" onClick={handleAddAction}>
                  <Plus size={16} className="me-1" />
                  Add Action
                </Button>
              </Card.Header>
              <Card.Body>
                {selectedDisplay.actions.length === 0 ? (
                  <div className="text-center py-5">
                    <div className="text-muted">
                      <Settings size={48} className="mb-3 opacity-50" />
                      <h5>No Actions Configured</h5>
                      <p>Click "Add Action" to create your first action for this display.</p>
                    </div>
                  </div>
                ) : (
                  <Row>
                    {selectedDisplay.actions.map((action) => (
                      <Col md={6} key={action.id} className="mb-3">
                        <Card className="border">
                          <Card.Body>
                            <div className="d-flex justify-content-between align-items-start mb-2">
                              <h6 className="mb-0">{action.name}</h6>
                              <div>
                                <Button
                                  variant="outline-primary"
                                  size="sm"
                                  onClick={() => handleEditAction(action)}
                                  className="me-1"
                                >
                                  <Edit size={12} />
                                </Button>
                                <Button
                                  variant="outline-danger"
                                  size="sm"
                                  onClick={() => handleDeleteAction(action.id)}
                                >
                                  <Trash2 size={12} />
                                </Button>
                              </div>
                            </div>
                            <div className="mb-2">
                              <Badge bg={action.type === 'API' ? 'success' : 'info'} className="me-2">
                                {action.type}
                              </Badge>
                              <Badge bg="secondary">{action.buttonStyle}</Badge>
                            </div>
                            <small className="text-muted">
                              {action.type === 'API' && action.endpoint && (
                                <div><strong>Endpoint:</strong> {action.endpoint}</div>
                              )}
                              {action.type === 'Navigation' && action.navigationTarget && (
                                <div><strong>Target:</strong> {action.navigationTarget}</div>
                              )}
                              <div><strong>Icon:</strong> {action.icon}</div>
                            </small>
                          </Card.Body>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                )}
              </Card.Body>
            </Card>
          ) : (
            <Card className="h-100">
              <Card.Body className="d-flex align-items-center justify-content-center">
                <div className="text-center text-muted">
                  <Eye size={48} className="mb-3 opacity-50" />
                  <h5>Select a Display Type</h5>
                  <p>Choose a display type from the left panel to configure its actions.</p>
                </div>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>

      {/* Action Modal */}
      <Modal show={showActionModal} onHide={() => setShowActionModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{editingAction ? 'Edit Action' : 'Add New Action'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Action Name</Form.Label>
                  <Form.Control
                    type="text"
                    value={actionForm.name}
                    onChange={(e) => setActionForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Save, Delete, Create"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Action Type</Form.Label>
                  <Form.Select
                    value={actionForm.type}
                    onChange={(e) => setActionForm(prev => ({ ...prev, type: e.target.value as any }))}
                  >
                    <option value="Navigation">Navigation</option>
                    <option value="API">API Call</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            {actionForm.type === 'API' && (
              <Form.Group className="mb-3">
                <Form.Label>API Endpoint</Form.Label>
                <Form.Control
                  type="text"
                  value={actionForm.endpoint}
                  onChange={(e) => setActionForm(prev => ({ ...prev, endpoint: e.target.value }))}
                  placeholder="/api/objects/{id}"
                />
              </Form.Group>
            )}

            {actionForm.type === 'Navigation' && (
              <Form.Group className="mb-3">
                <Form.Label>Navigation Target</Form.Label>
                <Form.Control
                  type="text"
                  value={actionForm.navigationTarget}
                  onChange={(e) => setActionForm(prev => ({ ...prev, navigationTarget: e.target.value }))}
                  placeholder="/objects/list"
                />
              </Form.Group>
            )}

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Button Style</Form.Label>
                  <Form.Select
                    value={actionForm.buttonStyle}
                    onChange={(e) => setActionForm(prev => ({ ...prev, buttonStyle: e.target.value as any }))}
                  >
                    {BUTTON_STYLES.map(style => (
                      <option key={style} value={style}>{style}</option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Icon</Form.Label>
                  <Form.Select
                    value={actionForm.icon}
                    onChange={(e) => setActionForm(prev => ({ ...prev, icon: e.target.value }))}
                  >
                    {AVAILABLE_ICONS.map(icon => (
                      <option key={icon} value={icon}>{icon}</option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowActionModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSaveAction} disabled={!actionForm.name}>
            {editingAction ? 'Update Action' : 'Add Action'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Display Modal */}
      <Modal show={showDisplayModal} onHide={() => setShowDisplayModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>{editingDisplay ? 'Edit Display' : 'Add New Display'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Display Name</Form.Label>
              <Form.Control
                type="text"
                value={displayForm.displayName}
                onChange={(e) => setDisplayForm(prev => ({ ...prev, displayName: e.target.value }))}
                placeholder="e.g., List View, Create Form"
              />
              <Form.Text className="text-muted">
                This is the user-friendly name shown in the interface
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Internal Name</Form.Label>
              <Form.Control
                type="text"
                value={displayForm.name}
                onChange={(e) => setDisplayForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., List, Create, Update, View"
              />
              <Form.Text className="text-muted">
                Internal identifier for this display type (use simple names like List, Create, etc.)
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Check
                type="switch"
                id="default-display-switch"
                label="Set as Default Display"
                checked={displayForm.isDefault}
                onChange={(e) => setDisplayForm(prev => ({ ...prev, isDefault: e.target.checked }))}
              />
              <Form.Text className="text-muted">
                The default display will be used as the primary view for this object type
              </Form.Text>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDisplayModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSaveDisplay}
            disabled={!displayForm.name || !displayForm.displayName}
          >
            {editingDisplay ? 'Update Display' : 'Create Display'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};
