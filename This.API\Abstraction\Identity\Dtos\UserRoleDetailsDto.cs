namespace Abstraction.Identity.Dtos;

/// <summary>
/// Data transfer object for user role details
/// </summary>
public class UserRoleDetailsDto
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Role name
    /// </summary>
    public string? RoleName { get; set; }

    /// <summary>
    /// Role description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether the role is enabled for the user
    /// </summary>
    public bool Enabled { get; set; }
}
