export const TEST_DATA = {
  applicationDetails: {
    id: "8c38b02f-4c1e-4416-a1bf-c705d1ac8761",
    name: "KitchenSync",
    description: "KitchenSync",
    version: "1.0.0",
    isActive: true,
    isUserImported: true,
    isRoleAssigned: true,
    apiKey: null,
    isOnboardCompleted: true,
    applicationUrl: "http://localhost:5174/",
    icon: "ic-cube",
    createdAt: "2025-06-20T18:53:36.079154Z",
    createdBy: "00000000-0000-0000-0000-000000000000",
    modifiedAt: "2025-06-20T19:11:13.783007Z",
    modifiedBy: "00000000-0000-0000-0000-000000000000",
    tenantId: "kitchsync",
    isAdmin: true,
  },
} as const;

// Runtime application details (can be set from postMessage)
let currentApplicationDetails: typeof TEST_DATA.applicationDetails | null = null;

// Helper function to get test data values
export const getTestData = () => TEST_DATA;

// Helper function to check if we should use test data
export const shouldUseTestData = () => {
  return window.self !== window.top ? false : true; // Not in iframe = use test data
};

// Helper function to set current application details (for iframe mode)
export const setCurrentApplicationDetails = (details: typeof TEST_DATA.applicationDetails) => {
  currentApplicationDetails = details;
};

// Helper function to get current application details
export const getCurrentApplicationDetails = () => {
  if (shouldUseTestData()) {
    return TEST_DATA.applicationDetails;
  }
  return currentApplicationDetails;
};
