/**
 * Centralized Types for API Services
 * This file contains all shared types to avoid circular dependencies
 */

// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

export interface ApiError {
  message: string;
  status?: number;
  statusText?: string;
  data?: any;
  originalError?: Error;
}

export interface LoadingState {
  isLoading: boolean;
  error: ApiError | null;
}

export interface ApiResult<T> {
  succeeded: boolean;
  data: T;
  message: string;
  errors?: string[];
}

// Pagination Types
export interface PaginationParams {
  pageNumber?: number;
  pageSize?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Search and Sort Types
export interface SearchParams {
  searchTerm?: string;
  isActive?: boolean;
  includeDeleted?: boolean;
}

export interface SortParams {
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

// HTTP Client Types
export interface RequestConfig extends RequestInit {
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  tenant?: string;
  skipAuth?: boolean;
  skipLogging?: boolean;
}
