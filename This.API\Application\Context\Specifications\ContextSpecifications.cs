using Ardalis.Specification;
using Domain.Entities;

namespace Application.Context.Specifications;

/// <summary>
/// Specification for getting contexts with filters and pagination
/// </summary>
public class ContextsWithFiltersSpec : Specification<Domain.Entities.Context>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ContextsWithFiltersSpec(
        string? searchTerm = null, 
        string? category = null, 
        bool includeInactive = false, 
        int skip = 0, 
        int take = 10)
    {
        // Base filter - always exclude deleted
        Query.Where(c => !c.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(c => c.IsActive);
        }

        // Filter by category
        if (!string.IsNullOrWhiteSpace(category))
        {
            Query.Where(c => c.Category != null && c.Category.ToLower().Contains(category.ToLower()));
        }

        // Filter by search term
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            Query.Where(c => c.Name.ToLower().Contains(searchTermLower) ||
                           (c.Description != null && c.Description.ToLower().Contains(searchTermLower)));
        }

        // Order and paginate
        Query.OrderBy(c => c.Name)
             .Skip(skip)
             .Take(take);
    }
}

/// <summary>
/// Specification for counting contexts with filters
/// </summary>
public class ContextsCountSpec : Specification<Domain.Entities.Context>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ContextsCountSpec(
        string? searchTerm = null, 
        string? category = null, 
        bool includeInactive = false)
    {
        // Base filter - always exclude deleted
        Query.Where(c => !c.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(c => c.IsActive);
        }

        // Filter by category
        if (!string.IsNullOrWhiteSpace(category))
        {
            Query.Where(c => c.Category != null && c.Category.ToLower().Contains(category.ToLower()));
        }

        // Filter by search term
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            Query.Where(c => c.Name.ToLower().Contains(searchTermLower) ||
                           (c.Description != null && c.Description.ToLower().Contains(searchTermLower)));
        }
    }
}

/// <summary>
/// Specification for getting context lookup data
/// </summary>
public class ContextLookupSpec : Specification<Domain.Entities.Context>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ContextLookupSpec(
        string? searchTerm = null, 
        string? category = null, 
        bool includeInactive = false)
    {
        // Base filter - always exclude deleted
        Query.Where(c => !c.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(c => c.IsActive);
        }

        // Filter by category
        if (!string.IsNullOrWhiteSpace(category))
        {
            Query.Where(c => c.Category != null && c.Category.ToLower().Contains(category.ToLower()));
        }

        // Filter by search term
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            Query.Where(c => c.Name.ToLower().Contains(searchTermLower));
        }

        // Order by name
        Query.OrderBy(c => c.Name);
    }
}

/// <summary>
/// Specification for getting tenant contexts with filters and pagination
/// </summary>
public class TenantContextsWithFiltersSpec : Specification<TenantContext>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public TenantContextsWithFiltersSpec(
        string? searchTerm = null, 
        string? category = null, 
        bool includeInactive = false, 
        int skip = 0, 
        int take = 10)
    {
        // Base filter - always exclude deleted
        Query.Where(tc => !tc.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(tc => tc.IsActive);
        }

        // Filter by category
        if (!string.IsNullOrWhiteSpace(category))
        {
            Query.Where(tc => tc.Category != null && tc.Category.ToLower().Contains(category.ToLower()));
        }

        // Filter by search term
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            Query.Where(tc => tc.Name.ToLower().Contains(searchTermLower) ||
                            (tc.Description != null && tc.Description.ToLower().Contains(searchTermLower)));
        }

        // Order and paginate
        Query.OrderBy(tc => tc.Name)
             .Skip(skip)
             .Take(take);
    }
}

/// <summary>
/// Specification for counting tenant contexts with filters
/// </summary>
public class TenantContextsCountSpec : Specification<TenantContext>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public TenantContextsCountSpec(
        string? searchTerm = null, 
        string? category = null, 
        bool includeInactive = false)
    {
        // Base filter - always exclude deleted
        Query.Where(tc => !tc.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(tc => tc.IsActive);
        }

        // Filter by category
        if (!string.IsNullOrWhiteSpace(category))
        {
            Query.Where(tc => tc.Category != null && tc.Category.ToLower().Contains(category.ToLower()));
        }

        // Filter by search term
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            Query.Where(tc => tc.Name.ToLower().Contains(searchTermLower) ||
                            (tc.Description != null && tc.Description.ToLower().Contains(searchTermLower)));
        }
    }
}

/// <summary>
/// Specification for getting tenant context lookup data
/// </summary>
public class TenantContextLookupSpec : Specification<TenantContext>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public TenantContextLookupSpec(
        string? searchTerm = null, 
        string? category = null, 
        bool includeInactive = false)
    {
        // Base filter - always exclude deleted
        Query.Where(tc => !tc.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(tc => tc.IsActive);
        }

        // Filter by category
        if (!string.IsNullOrWhiteSpace(category))
        {
            Query.Where(tc => tc.Category != null && tc.Category.ToLower().Contains(category.ToLower()));
        }

        // Filter by search term
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            Query.Where(tc => tc.Name.ToLower().Contains(searchTermLower));
        }

        // Order by name
        Query.OrderBy(tc => tc.Name);
    }
}

/// <summary>
/// Specification for getting lookups by context ID
/// </summary>
public class LookupsByContextIdSpec : Specification<Lookup>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public LookupsByContextIdSpec(Guid contextId, bool includeInactive = false)
    {
        // Filter by context ID and exclude deleted
        Query.Where(l => l.ContextId == contextId && !l.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(l => l.IsActive);
        }

        // Order by show sequence, then by value
        Query.OrderBy(l => l.ShowSequence)
             .ThenBy(l => l.Value);
    }
}

/// <summary>
/// Specification for getting tenant lookups by tenant context ID
/// </summary>
public class TenantLookupsByTenantContextIdSpec : Specification<TenantLookup>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public TenantLookupsByTenantContextIdSpec(Guid tenantContextId, bool includeInactive = false)
    {
        // Filter by tenant context ID and exclude deleted
        Query.Where(tl => tl.TenantContextId == tenantContextId && !tl.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(tl => tl.IsActive);
        }

        // Order by show sequence, then by value
        Query.OrderBy(tl => tl.ShowSequence)
             .ThenBy(tl => tl.Value);
    }
}

/// <summary>
/// Specification for getting contexts by multiple IDs
/// </summary>
public class ContextsByIdsSpec : Specification<Domain.Entities.Context>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public ContextsByIdsSpec(List<Guid> contextIds)
    {
        // Filter by context IDs
        Query.Where(c => contextIds.Contains(c.Id));

        // Order by name for consistent results
        Query.OrderBy(c => c.Name);
    }
}

/// <summary>
/// Specification for getting tenant contexts by multiple IDs
/// </summary>
public class TenantContextsByIdsSpec : Specification<TenantContext>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public TenantContextsByIdsSpec(List<Guid> tenantContextIds)
    {
        // Filter by tenant context IDs
        Query.Where(tc => tenantContextIds.Contains(tc.Id));

        // Order by name for consistent results
        Query.OrderBy(tc => tc.Name);
    }
}

/// <summary>
/// Specification for getting lookups by multiple context IDs
/// </summary>
public class LookupsByContextIdsSpec : Specification<Lookup>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public LookupsByContextIdsSpec(List<Guid> contextIds, bool includeInactive = false)
    {
        // Filter by context IDs and exclude deleted
        Query.Where(l => contextIds.Contains(l.ContextId) && !l.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(l => l.IsActive);
        }

        // Order by context ID, then by show sequence, then by value
        Query.OrderBy(l => l.ContextId)
             .ThenBy(l => l.ShowSequence)
             .ThenBy(l => l.Value);
    }
}

/// <summary>
/// Specification for getting tenant lookups by multiple tenant context IDs
/// </summary>
public class TenantLookupsByTenantContextIdsSpec : Specification<TenantLookup>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public TenantLookupsByTenantContextIdsSpec(List<Guid> tenantContextIds, bool includeInactive = false)
    {
        // Filter by tenant context IDs and exclude deleted
        Query.Where(tl => tenantContextIds.Contains(tl.TenantContextId) && !tl.IsDeleted);

        // Filter by active status
        if (!includeInactive)
        {
            Query.Where(tl => tl.IsActive);
        }

        // Order by tenant context ID, then by show sequence, then by value
        Query.OrderBy(tl => tl.TenantContextId)
             .ThenBy(tl => tl.ShowSequence)
             .ThenBy(tl => tl.Value);
    }
}
