using Application.ActionManagement.DTOs;
using Domain.Entities;
using Mapster;

namespace Application.ActionManagement.Mappings;

/// <summary>
/// Mapping configurations for Action entity and related DTOs
/// </summary>
public class ActionMappings : IRegister
{
    /// <summary>
    /// Register mappings
    /// </summary>
    public void Register(TypeAdapterConfig config)
    {
        // Map Action entity to ActionDto
        config.NewConfig<Domain.Entities.Action, ActionDto>()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.Description, src => src.Description)
            .Map(dest => dest.EndpointTemplate, src => src.EndpointTemplate)
            .Map(dest => dest.NavigationTarget, src => src.NavigationTarget)
            .Map(dest => dest.Icon, src => src.Icon)
            .Map(dest => dest.ButtonStyle, src => src.ButtonStyle)
            .Map(dest => dest.ConfirmationMessage, src => src.ConfirmationMessage)
            .Map(dest => dest.SuccessMessage, src => src.SuccessMessage)
            .Map(dest => dest.ErrorMessage, src => src.ErrorMessage)
            .Map(dest => dest.IsActive, src => src.IsActive)
            .Map(dest => dest.IsDeleted, src => src.IsDeleted)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.ModifiedAt, src => src.ModifiedAt)
            .Map(dest => dest.CreatedBy, src => src.CreatedBy)
            .Map(dest => dest.ModifiedBy, src => src.ModifiedBy);

        // Map CreateActionDto to Action entity
        config.NewConfig<CreateActionDto, Domain.Entities.Action>()
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.Description, src => src.Description)
            .Map(dest => dest.EndpointTemplate, src => src.EndpointTemplate)
            .Map(dest => dest.NavigationTarget, src => src.NavigationTarget)
            .Map(dest => dest.Icon, src => src.Icon)
            .Map(dest => dest.ButtonStyle, src => src.ButtonStyle)
            .Map(dest => dest.ConfirmationMessage, src => src.ConfirmationMessage)
            .Map(dest => dest.SuccessMessage, src => src.SuccessMessage)
            .Map(dest => dest.ErrorMessage, src => src.ErrorMessage)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.IsActive)
            .Ignore(dest => dest.IsDeleted)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.ModifiedAt)
            .Ignore(dest => dest.CreatedBy)
            .Ignore(dest => dest.ModifiedBy)
            .Ignore(dest => dest.DomainEvents)
            .Ignore(dest => dest.DisplayActions);

        // Map UpdateActionDto to Action entity
        config.NewConfig<UpdateActionDto, Domain.Entities.Action>()
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.Description, src => src.Description)
            .Map(dest => dest.EndpointTemplate, src => src.EndpointTemplate)
            .Map(dest => dest.NavigationTarget, src => src.NavigationTarget)
            .Map(dest => dest.Icon, src => src.Icon)
            .Map(dest => dest.ButtonStyle, src => src.ButtonStyle)
            .Map(dest => dest.ConfirmationMessage, src => src.ConfirmationMessage)
            .Map(dest => dest.SuccessMessage, src => src.SuccessMessage)
            .Map(dest => dest.ErrorMessage, src => src.ErrorMessage)
            .Map(dest => dest.IsActive, src => src.IsActive)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.IsDeleted)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.ModifiedAt)
            .Ignore(dest => dest.CreatedBy)
            .Ignore(dest => dest.ModifiedBy)
            .Ignore(dest => dest.DomainEvents)
            .Ignore(dest => dest.DisplayActions);
    }
}
