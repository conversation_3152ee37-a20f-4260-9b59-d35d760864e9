using Application.IntegrationApis.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationApis.Commands;

/// <summary>
/// Command to create IntegrationApi and automatically create IntegrationConfiguration
/// </summary>
public class CreateIntegrationApiWithConfigurationCommand : IRequest<Result<CreateIntegrationApiWithConfigurationResponseDto>>
{
    /// <summary>
    /// Integration ID to link this API to
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Object ID this configuration applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Product ID this integration API belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// API name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API endpoint URL
    /// </summary>
    public string EndpointUrl { get; set; } = string.Empty;

    /// <summary>
    /// API schema definition stored as JSON
    /// </summary>
    public string? Schema { get; set; }

    /// <summary>
    /// Whether the API is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Data flow direction (e.g., "In", "Out", "Both")
    /// </summary>
    public string? Direction { get; set; }

    /// <summary>
    /// Whether the configuration is active
    /// </summary>
    public bool IsConfigActive { get; set; } = true;
}

/// <summary>
/// Response DTO for CreateIntegrationApiWithConfiguration operation
/// </summary>
public class CreateIntegrationApiWithConfigurationResponseDto
{
    /// <summary>
    /// Created IntegrationApi details
    /// </summary>
    public IntegrationApiDetailsDto IntegrationApi { get; set; } = new();

    /// <summary>
    /// Created IntegrationConfiguration details
    /// </summary>
    public IntegrationConfigurationDetailsDto Configuration { get; set; } = new();

    /// <summary>
    /// Operation summary
    /// </summary>
    public OperationSummaryDto Summary { get; set; } = new();
}

/// <summary>
/// IntegrationApi details in response
/// </summary>
public class IntegrationApiDetailsDto
{
    /// <summary>
    /// IntegrationApi ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// API name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API endpoint URL
    /// </summary>
    public string EndpointUrl { get; set; } = string.Empty;

    /// <summary>
    /// API schema
    /// </summary>
    public string? Schema { get; set; }

    /// <summary>
    /// Whether the API is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid? CreatedBy { get; set; }
}

/// <summary>
/// IntegrationConfiguration details in response
/// </summary>
public class IntegrationConfigurationDetailsDto
{
    /// <summary>
    /// Configuration ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Integration ID
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    public string IntegrationName { get; set; } = string.Empty;

    /// <summary>
    /// IntegrationApi ID
    /// </summary>
    public Guid IntegrationApiId { get; set; }

    /// <summary>
    /// Object ID
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;

    /// <summary>
    /// Data flow direction
    /// </summary>
    public string? Direction { get; set; }

    /// <summary>
    /// Whether the configuration is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid? CreatedBy { get; set; }
}

/// <summary>
/// Operation summary
/// </summary>
public class OperationSummaryDto
{
    /// <summary>
    /// Total records created
    /// </summary>
    public int TotalRecordsCreated { get; set; }

    /// <summary>
    /// List of created entity IDs
    /// </summary>
    public List<Guid> CreatedEntityIds { get; set; } = new();

    /// <summary>
    /// Operation timestamp
    /// </summary>
    public DateTime OperationTimestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Success message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}
