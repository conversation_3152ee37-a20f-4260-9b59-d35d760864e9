import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/response_model.dart';
import '../models/card_data_model.dart';

/// Service class for handling API calls
class ApiService {
  static const String baseUrl =
      'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net/api';

  static const Duration timeoutDuration = Duration(seconds: 30);

  /// Fetch instance data for a specific object type
  Future<ResponseModel?> fetchInstanceData({
    required String objectType,
    int pageNumber = 1,
    int pageSize = 10,
    bool createView = true,
  }) async {
    try {
      print('Fetching instance data for objectType: $objectType');
      final uri = Uri.parse(
        '$baseUrl/objectvalues/instances-view/$objectType'
        '?createView=$createView&pageNumber=$pageNumber&pageSize=$pageSize',
      );
      print('Request URI: $uri');

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'tenant': 'kitchsync',
        },
      ).timeout(timeoutDuration);

      print('Response status code: ${response.statusCode}');
      print('Response headers: ${response.headers}');

      print('API Response Status: ${response.statusCode}');
      print('API Response Headers: ${response.headers}');
      print('API Response Body: ${response.body}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        print('Parsed JSON Data: $jsonData');
        final responseModel = ResponseModel.fromJson(jsonData);
        print('Response Model: $responseModel');
        return responseModel;
      } else {
        throw ApiException(
          'Failed to fetch data: ${response.statusCode}',
          response.statusCode,
        );
      }
    } on SocketException {
      throw ApiException(
        'No internet connection. Please check your network.',
        0,
      );
    } on http.ClientException {
      throw ApiException(
        'Network error occurred. Please try again.',
        0,
      );
    } on FormatException {
      throw ApiException(
        'Invalid response format received from server.',
        0,
      );
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException(
        'An unexpected error occurred: $e',
        0,
      );
    }
  }

  /// Convert API response to table data format
  List<Map<String, dynamic>> convertToTableData(ResponseModel response) {
    return response.viewData ?? [];
  }

  /// Convert API response to card data format with enhanced metadata
  List<CardDataModel> convertToCardData(ResponseModel response) {
    final viewData = response.viewData ?? [];
    return viewData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;

      // Try to get a proper ID from various possible fields
      String itemId = 'item_$index'; // fallback
      if (data['id'] != null) {
        itemId = data['id'].toString();
      } else if (data['refId'] != null) {
        itemId = data['refId'].toString();
      } else if (data['RefId'] != null) {
        itemId = data['RefId'].toString();
      } else if (data['objectId'] != null) {
        itemId = data['objectId'].toString();
      }

      return CardDataModel(
        id: itemId,
        title: _extractTitle(data),
        subtitle: _extractSubtitle(data, response.objectName),
        fields: _processFields(data),
        objectType: response.objectName ?? 'Unknown',
        metadata: {
          'index': index,
          'totalFields': data.length,
          'hasLongValues': _hasLongValues(data),
          'originalData': data, // Store original data for RefId extraction
        },
      );
    }).toList();
  }

  /// Extract a meaningful title from the data
  String _extractTitle(Map<String, dynamic> data) {
    // Priority order for title fields
    const titleFields = ['name', 'title', 'displayName', 'label', 'id'];

    for (final field in titleFields) {
      final value = data[field];
      if (value != null && value.toString().trim().isNotEmpty) {
        return value.toString();
      }
    }

    // Fallback to first non-null value
    final firstValue = data.values.firstWhere(
      (value) => value != null && value.toString().trim().isNotEmpty,
      orElse: () => 'Unnamed Item',
    );

    return firstValue.toString();
  }

  /// Extract a meaningful subtitle
  String _extractSubtitle(Map<String, dynamic> data, String? objectType) {
    final fieldCount = data.length;
    final objectTypeText = objectType != null ? 'Object Type: $objectType' : '';
    return '$fieldCount fields${objectTypeText.isNotEmpty ? ' • $objectTypeText' : ''}';
  }

  /// Process fields for card display
  List<CardFieldModel> _processFields(Map<String, dynamic> data) {
    return data.entries.map((entry) {
      final value = entry.value;
      final stringValue = value?.toString() ?? '';

      return CardFieldModel(
        key: entry.key,
        value: stringValue,
        displayValue: _formatDisplayValue(stringValue),
        type: _inferFieldType(entry.key, value),
        isLong: stringValue.length > 50,
        isEmpty: stringValue.trim().isEmpty,
      );
    }).toList();
  }

  /// Format value for display
  String _formatDisplayValue(String value) {
    if (value.trim().isEmpty) return 'Not specified';
    if (value.length > 100) {
      return '${value.substring(0, 97)}...';
    }
    return value;
  }

  /// Infer field type from key and value
  CardFieldType _inferFieldType(String key, dynamic value) {
    final lowerKey = key.toLowerCase();

    if (lowerKey.contains('email')) return CardFieldType.email;
    if (lowerKey.contains('phone') || lowerKey.contains('tel')) return CardFieldType.phone;
    if (lowerKey.contains('url') || lowerKey.contains('link')) return CardFieldType.url;
    if (lowerKey.contains('date') || lowerKey.contains('time')) return CardFieldType.date;
    if (lowerKey.contains('price') || lowerKey.contains('cost') || lowerKey.contains('amount')) return CardFieldType.currency;
    if (value is bool || (value is String && (value.toLowerCase() == 'true' || value.toLowerCase() == 'false'))) return CardFieldType.boolean;
    if (value is num || (value is String && double.tryParse(value) != null)) return CardFieldType.number;

    return CardFieldType.text;
  }

  /// Check if data has long values that might cause overflow
  bool _hasLongValues(Map<String, dynamic> data) {
    return data.values.any((value) =>
      value != null && value.toString().length > 50
    );
  }

  /// Upsert object data with metadata
  Future<Map<String, dynamic>> upsertObjectWithMetadata({
    required Map<String, dynamic> metadataProperties,
  }) async {
    try {
      print('Upserting object with metadata: $metadataProperties');

      final uri = Uri.parse('$baseUrl/objectvalues/upsert-single-with-metadata');

      final payload = {
        'metadataProperties': metadataProperties,
      };

      print('Upsert payload: ${json.encode(payload)}');

      final response = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'tenant': 'kitchsync',
        },
        body: json.encode(payload),
      ).timeout(timeoutDuration);

      print('Upsert response status: ${response.statusCode}');
      print('Upsert response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData;
      } else {
        throw ApiException(
          'Failed to upsert object: ${response.statusCode} - ${response.body}',
          response.statusCode,
        );
      }
    } on SocketException {
      throw ApiException(
        'No internet connection. Please check your network.',
        0,
      );
    } on http.ClientException {
      throw ApiException(
        'Network error occurred. Please try again.',
        0,
      );
    } on FormatException {
      throw ApiException(
        'Invalid response format received from server.',
        0,
      );
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException(
        'An unexpected error occurred: $e',
        0,
      );
    }
  }

  /// Test API connectivity
  Future<bool> testConnection() async {
    try {
      final uri = Uri.parse('$baseUrl/health'); // Assuming there's a health endpoint
      final response = await http.get(uri).timeout(
        const Duration(seconds: 10),
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}

/// Custom exception class for API errors
class ApiException implements Exception {
  final String message;
  final int statusCode;

  const ApiException(this.message, this.statusCode);

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}
