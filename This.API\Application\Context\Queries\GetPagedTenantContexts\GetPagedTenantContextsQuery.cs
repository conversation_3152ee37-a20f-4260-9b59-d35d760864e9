using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetPagedTenantContexts;

/// <summary>
/// Query to get tenant contexts with pagination
/// </summary>
public class GetPagedTenantContextsQuery : IRequest<PagedResponse<TenantContextDto, object>>
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Whether to include inactive tenant contexts
    /// </summary>
    public bool IncludeInactive { get; set; } = false;

    /// <summary>
    /// Category filter
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Search term for name or description
    /// </summary>
    public string? SearchTerm { get; set; }
}
