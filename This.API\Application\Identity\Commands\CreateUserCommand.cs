using MediatR;
using Shared.Common.Response;
using Application.Identity.DTOs;

namespace Application.Identity.Commands;

/// <summary>
/// Command for creating a new user
/// </summary>
public class CreateUserCommand : IRequest<ApiResponse<UserDto>>
{
    /// <summary>
    /// First name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Display name (computed from first and last name)
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// Email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Username
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Password
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Confirm password
    /// </summary>
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Profile image URL
    /// </summary>
    public string? ImageUrl { get; set; }

    /// <summary>
    /// Whether the user is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Whether MFA is enabled for the user
    /// </summary>
    public bool IsMFAEnabled { get; set; } = false;

    /// <summary>
    /// Integration source ID for external authentication
    /// </summary>
    public Guid? IntegrationSourceId { get; set; }

    /// <summary>
    /// External user ID from integration source
    /// </summary>
    public string? ExternalUserId { get; set; }

    /// <summary>
    /// External user data as JSON
    /// </summary>
    public string? ExternalUserData { get; set; }

    /// <summary>
    /// Whether the user must change password on next login
    /// </summary>
    public bool MustChangePassword { get; set; } = false;

    /// <summary>
    /// External object ID (for external authentication)
    /// </summary>
    public string? ObjectId { get; set; }

    /// <summary>
    /// List of role names to assign to the user
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// List of role IDs to assign to the user (alternative to role names)
    /// </summary>
    public List<Guid> RoleIds { get; set; } = new();
}
