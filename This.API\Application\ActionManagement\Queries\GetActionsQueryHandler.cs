using Abstraction.Database.Repositories;
using Application.ActionManagement.DTOs;
using Application.ActionManagement.Specifications;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.ActionManagement.Queries;

/// <summary>
/// Get actions query handler
/// </summary>
public class GetActionsQueryHandler : IRequestHandler<GetActionsQuery, Result<List<ActionDto>>>
{
    private readonly IRepository<Domain.Entities.Action> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetActionsQueryHandler(IRepository<Domain.Entities.Action> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<List<ActionDto>>> Handle(GetActionsQuery request, CancellationToken cancellationToken)
    {
        var spec = new ActionsWithFilterSpec(
            searchTerm: request.SearchTerm,
            isActive: request.IsActive,
            orderBy: request.OrderBy);

        var actions = await _repository.ListAsync(spec, cancellationToken);

        var dtos = actions.Adapt<List<ActionDto>>();

        return Result<List<ActionDto>>.Success(dtos);
    }
}
