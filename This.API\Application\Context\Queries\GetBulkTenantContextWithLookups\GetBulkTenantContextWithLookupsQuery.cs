using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetBulkTenantContextWithLookups;

/// <summary>
/// Query to get multiple tenant contexts with their associated tenant lookups by tenant context IDs
/// </summary>
public class GetBulkTenantContextWithLookupsQuery : IRequest<Result<BulkTenantContextWithLookupsDto>>
{
    /// <summary>
    /// List of TenantContext IDs to retrieve
    /// </summary>
    public List<Guid> TenantContextIds { get; set; } = new();

    /// <summary>
    /// Whether to include inactive tenant lookups
    /// </summary>
    public bool IncludeInactiveLookups { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetBulkTenantContextWithLookupsQuery(List<Guid> tenantContextIds, bool includeInactiveLookups = false)
    {
        TenantContextIds = tenantContextIds ?? new List<Guid>();
        IncludeInactiveLookups = includeInactiveLookups;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetBulkTenantContextWithLookupsQuery()
    {
    }
}
