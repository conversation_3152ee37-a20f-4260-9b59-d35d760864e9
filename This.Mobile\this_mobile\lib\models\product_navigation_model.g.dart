// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_navigation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductResponse _$ProductResponseFromJson(Map<String, dynamic> json) =>
    ProductResponse(
      succeeded: json['succeeded'] as bool,
      data: ProductData.fromJson(json['data'] as Map<String, dynamic>),
      statusCode: (json['statusCode'] as num).toInt(),
    );

Map<String, dynamic> _$ProductResponseToJson(ProductResponse instance) =>
    <String, dynamic>{
      'succeeded': instance.succeeded,
      'data': instance.data,
      'statusCode': instance.statusCode,
    };

ProductData _$ProductDataFromJson(Map<String, dynamic> json) => ProductData(
      products: (json['products'] as List<dynamic>)
          .map((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalObjectsCount: (json['totalObjectsCount'] as num).toInt(),
      totalMetadataCount: (json['totalMetadataCount'] as num).toInt(),
      maxHierarchyDepth: (json['maxHierarchyDepth'] as num).toInt(),
    );

Map<String, dynamic> _$ProductDataToJson(ProductData instance) =>
    <String, dynamic>{
      'products': instance.products,
      'totalObjectsCount': instance.totalObjectsCount,
      'totalMetadataCount': instance.totalMetadataCount,
      'maxHierarchyDepth': instance.maxHierarchyDepth,
    };

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: json['id'] as String,
      name: json['name'] as String,
      version: json['version'] as String,
      isActive: json['isActive'] as bool,
      applicationUrl: json['applicationUrl'] as String,
      createdAt: json['createdAt'] as String,
      rootObjects: (json['rootObjects'] as List<dynamic>)
          .map((e) => RootObject.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'version': instance.version,
      'isActive': instance.isActive,
      'applicationUrl': instance.applicationUrl,
      'createdAt': instance.createdAt,
      'rootObjects': instance.rootObjects,
    };

RootObject _$RootObjectFromJson(Map<String, dynamic> json) => RootObject(
      id: json['id'] as String,
      name: json['name'] as String,
      isActive: json['isActive'] as bool,
      hierarchyLevel: (json['hierarchyLevel'] as num).toInt(),
      metadata: (json['metadata'] as List<dynamic>)
          .map((e) => MetadataWrapper.fromJson(e as Map<String, dynamic>))
          .toList(),
      childObjects: (json['childObjects'] as List<dynamic>?)
          ?.map((e) => ChildObject.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$RootObjectToJson(RootObject instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'isActive': instance.isActive,
      'hierarchyLevel': instance.hierarchyLevel,
      'metadata': instance.metadata,
      'childObjects': instance.childObjects,
    };

ChildObject _$ChildObjectFromJson(Map<String, dynamic> json) => ChildObject(
      id: json['id'] as String,
      name: json['name'] as String,
      parentObjectId: json['parentObjectId'] as String,
      hierarchyLevel: (json['hierarchyLevel'] as num).toInt(),
      metadata: (json['metadata'] as List<dynamic>)
          .map((e) => MetadataWrapper.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ChildObjectToJson(ChildObject instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'parentObjectId': instance.parentObjectId,
      'hierarchyLevel': instance.hierarchyLevel,
      'metadata': instance.metadata,
    };

MetadataWrapper _$MetadataWrapperFromJson(Map<String, dynamic> json) =>
    MetadataWrapper(
      metadata: Metadata.fromJson(json['metadata'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MetadataWrapperToJson(MetadataWrapper instance) =>
    <String, dynamic>{
      'metadata': instance.metadata,
    };

Metadata _$MetadataFromJson(Map<String, dynamic> json) => Metadata(
      id: json['id'] as String,
      name: json['name'] as String,
      inputType: json['inputType'] as String,
      isRequired: json['isRequired'] as bool,
    );

Map<String, dynamic> _$MetadataToJson(Metadata instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'inputType': instance.inputType,
      'isRequired': instance.isRequired,
    };
