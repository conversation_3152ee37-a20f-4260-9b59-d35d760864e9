import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Table, Al<PERSON>, Spinner, Badge } from 'react-bootstrap';
import { Plus, Edit, Trash2, Save, X, Search, Star, StarOff } from 'lucide-react';
import { contextService } from '../../services/contextService';
import type { TenantContextWithLookups, TenantLookup } from '../../types/context';

interface TenantLookupManagementModalProps {
  show: boolean;
  onHide: () => void;
  tenantContextWithLookups: TenantContextWithLookups | null;
  onSaved: () => void;
  tenantId: string;
}

interface LookupFormData {
  id?: string;
  value: string;
  value1: string;
  value2: string;
  showSequence: number;
  isDefault: boolean;
  isActive: boolean;
}

export const TenantLookupManagementModal: React.FC<TenantLookupManagementModalProps> = ({
  show,
  onHide,
  tenantContextWithLookups,
  onSaved,
  tenantId
}) => {
  const [tenantLookups, setTenantLookups] = useState<TenantLookup[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingLookup, setEditingLookup] = useState<TenantLookup | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<LookupFormData>({
    value: '',
    value1: '',
    value2: '',
    showSequence: 0,
    isDefault: false,
    isActive: true
  });
  const [validated, setValidated] = useState(false);

  useEffect(() => {
    if (show && tenantContextWithLookups) {
      setTenantLookups(tenantContextWithLookups.tenantLookups || []);
      setError(null);
      setSearchTerm('');
      setShowForm(false);
      setEditingLookup(null);
    }
  }, [show, tenantContextWithLookups]);

  const filteredTenantLookups = tenantLookups.filter(lookup => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      lookup.value.toLowerCase().includes(searchLower) ||
      (lookup.value1 && lookup.value1.toLowerCase().includes(searchLower)) ||
      (lookup.value2 && lookup.value2.toLowerCase().includes(searchLower))
    );
  });

  const handleAddNew = () => {
    setEditingLookup(null);
    setFormData({
      value: '',
      value1: '',
      value2: '',
      showSequence: Math.max(...tenantLookups.map(l => l.showSequence), 0) + 1,
      isDefault: false,
      isActive: true
    });
    setShowForm(true);
    setValidated(false);
  };

  const handleEdit = (lookup: TenantLookup) => {
    setEditingLookup(lookup);
    setFormData({
      id: lookup.id,
      value: lookup.value,
      value1: lookup.value1 || '',
      value2: lookup.value2 || '',
      showSequence: lookup.showSequence,
      isDefault: lookup.isDefault,
      isActive: lookup.isActive
    });
    setShowForm(true);
    setValidated(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : 
               name === 'showSequence' ? parseInt(value) || 0 : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const form = e.currentTarget;
    if (form.checkValidity() === false) {
      setValidated(true);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = {
        id: formData.id,
        tenantContextId: tenantContextWithLookups!.tenantContext.id,
        value: formData.value.trim(),
        value1: formData.value1.trim() || null,
        value2: formData.value2.trim() || null,
        showSequence: formData.showSequence,
        isDefault: formData.isDefault,
        isActive: formData.isActive
      };

      if (editingLookup) {
        await contextService.updateTenantLookup(data, tenantId);
      } else {
        await contextService.createTenantLookup(data, tenantId);
      }

      // Refresh the tenant lookups
      const updated = await contextService.getTenantContextWithLookups(
        tenantContextWithLookups!.tenantContext.id, 
        tenantId
      );
      setTenantLookups(updated.tenantLookups || []);
      
      setShowForm(false);
      setEditingLookup(null);
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving the tenant lookup');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (lookup: TenantLookup) => {
    if (!confirm(`Are you sure you want to delete the tenant lookup "${lookup.value}"?`)) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await contextService.deleteTenantLookup(lookup.id, tenantId);
      
      // Refresh the tenant lookups
      const updated = await contextService.getTenantContextWithLookups(
        tenantContextWithLookups!.tenantContext.id, 
        tenantId
      );
      setTenantLookups(updated.tenantLookups || []);
    } catch (err: any) {
      setError(err.message || 'An error occurred while deleting the tenant lookup');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onSaved();
      onHide();
    }
  };

  if (!tenantContextWithLookups) {
    return null;
  }

  return (
    <Modal show={show} onHide={handleClose} size="xl" backdrop="static">
      <Modal.Header closeButton>
        <Modal.Title>
          Manage TenantLookups for "{tenantContextWithLookups.tenantContext.name}"
        </Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {error && (
          <Alert variant="danger" dismissible onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {!showForm ? (
          <>
            {/* Search and Add Controls */}
            <div className="d-flex justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-3">
                <div className="position-relative" style={{ minWidth: '300px' }}>
                  <Form.Control
                    type="text"
                    placeholder="Search tenant lookups..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Search size={16} className="position-absolute top-50 end-0 translate-middle-y me-3 text-muted" />
                </div>
                <span className="text-muted">
                  Showing {filteredTenantLookups.length} of {tenantLookups.length} tenant lookups
                </span>
              </div>
              <Button variant="primary" onClick={handleAddNew} disabled={loading}>
                <Plus size={16} className="me-2" />
                Add TenantLookup
              </Button>
            </div>

            {/* TenantLookups Table */}
            <Table responsive hover>
              <thead className="table-light">
                <tr>
                  <th>Value</th>
                  <th>Value1</th>
                  <th>Value2</th>
                  <th>Sequence</th>
                  <th>Default</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredTenantLookups.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="text-center text-muted p-4">
                      {searchTerm ? 'No tenant lookups match your search' : 'No tenant lookups found'}
                    </td>
                  </tr>
                ) : (
                  filteredTenantLookups.map(lookup => (
                    <tr key={lookup.id}>
                      <td>
                        <strong>{lookup.value}</strong>
                        {lookup.isDefault && (
                          <Star size={14} className="ms-2 text-warning" fill="currentColor" />
                        )}
                      </td>
                      <td>{lookup.value1 || '-'}</td>
                      <td>{lookup.value2 || '-'}</td>
                      <td>{lookup.showSequence}</td>
                      <td>
                        {lookup.isDefault ? (
                          <Badge bg="warning">Default</Badge>
                        ) : (
                          <Badge bg="secondary">No</Badge>
                        )}
                      </td>
                      <td>
                        <Badge bg={lookup.isActive ? 'success' : 'warning'}>
                          {lookup.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td>
                        <div className="d-flex gap-2">
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => handleEdit(lookup)}
                            disabled={loading}
                            title="Edit TenantLookup"
                          >
                            <Edit size={14} />
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => handleDelete(lookup)}
                            disabled={loading}
                            title="Delete TenantLookup"
                          >
                            <Trash2 size={14} />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </Table>
          </>
        ) : (
          /* TenantLookup Form */
          <Form noValidate validated={validated} onSubmit={handleSubmit}>
            <div className="mb-3">
              <h5>{editingLookup ? 'Edit TenantLookup' : 'Add New TenantLookup'}</h5>
            </div>

            <Form.Group className="mb-3">
              <Form.Label>Value <span className="text-danger">*</span></Form.Label>
              <Form.Control
                type="text"
                name="value"
                value={formData.value}
                onChange={handleInputChange}
                required
                disabled={loading}
                placeholder="Enter tenant lookup value"
              />
              <Form.Control.Feedback type="invalid">
                Please provide a valid value.
              </Form.Control.Feedback>
            </Form.Group>

            <div className="row">
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Value1</Form.Label>
                  <Form.Control
                    type="text"
                    name="value1"
                    value={formData.value1}
                    onChange={handleInputChange}
                    disabled={loading}
                    placeholder="Additional value 1 (optional)"
                  />
                </Form.Group>
              </div>
              <div className="col-md-6">
                <Form.Group className="mb-3">
                  <Form.Label>Value2</Form.Label>
                  <Form.Control
                    type="text"
                    name="value2"
                    value={formData.value2}
                    onChange={handleInputChange}
                    disabled={loading}
                    placeholder="Additional value 2 (optional)"
                  />
                </Form.Group>
              </div>
            </div>

            <div className="row">
              <div className="col-md-4">
                <Form.Group className="mb-3">
                  <Form.Label>Show Sequence</Form.Label>
                  <Form.Control
                    type="number"
                    name="showSequence"
                    value={formData.showSequence}
                    onChange={handleInputChange}
                    disabled={loading}
                    min="0"
                  />
                </Form.Group>
              </div>
              <div className="col-md-4">
                <Form.Group className="mb-3">
                  <Form.Check
                    type="switch"
                    id="tenant-lookup-default"
                    name="isDefault"
                    label="Default Value"
                    checked={formData.isDefault}
                    onChange={handleInputChange}
                    disabled={loading}
                  />
                </Form.Group>
              </div>
              <div className="col-md-4">
                <Form.Group className="mb-3">
                  <Form.Check
                    type="switch"
                    id="tenant-lookup-active"
                    name="isActive"
                    label="Active"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    disabled={loading}
                  />
                </Form.Group>
              </div>
            </div>

            <div className="d-flex gap-2">
              <Button variant="secondary" onClick={() => setShowForm(false)} disabled={loading}>
                <X size={16} className="me-2" />
                Cancel
              </Button>
              <Button variant="primary" type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <Spinner animation="border" size="sm" className="me-2" />
                    {editingLookup ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <Save size={16} className="me-2" />
                    {editingLookup ? 'Update' : 'Create'}
                  </>
                )}
              </Button>
            </div>
          </Form>
        )}
      </Modal.Body>

      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose} disabled={loading}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
