using Application.ActionManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ActionManagement.Queries;

/// <summary>
/// Get action by ID query
/// </summary>
public class GetActionByIdQuery : IRequest<Result<ActionDto>>
{
    /// <summary>
    /// Action ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetActionByIdQuery(Guid id)
    {
        Id = id;
    }
}
