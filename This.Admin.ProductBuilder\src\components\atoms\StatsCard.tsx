/**
 * StatsCard Atom
 * Reusable statistics card component
 */

import React from 'react';
import { Card } from 'react-bootstrap';

export interface StatsCardProps {
  title: string;
  value: number | string;
  icon?: string | React.ReactNode;
  variant?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'secondary';
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
    period?: string;
  };
  subtitle?: string;
  loading?: boolean;
  className?: string;
  onClick?: () => void;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  variant = 'primary',
  trend,
  subtitle,
  loading = false,
  className = '',
  onClick
}) => {
  const formatValue = (val: number | string): string => {
    if (typeof val === 'number') {
      // Format large numbers with commas
      return val.toLocaleString();
    }
    return val;
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'neutral'): string => {
    switch (direction) {
      case 'up':
        return '📈';
      case 'down':
        return '📉';
      default:
        return '➡️';
    }
  };

  const getTrendColor = (direction: 'up' | 'down' | 'neutral'): string => {
    switch (direction) {
      case 'up':
        return 'text-success';
      case 'down':
        return 'text-danger';
      default:
        return 'text-muted';
    }
  };

  const cardClasses = [
    'stats-card',
    'h-100',
    onClick ? 'cursor-pointer' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <Card 
      className={cardClasses}
      onClick={onClick}
      style={{ 
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        ...(onClick && {
          cursor: 'pointer'
        })
      }}
      onMouseEnter={(e) => {
        if (onClick) {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        }
      }}
      onMouseLeave={(e) => {
        if (onClick) {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '';
        }
      }}
    >
      <Card.Body className="text-center p-3">
        {/* Icon */}
        {icon && (
          <div className="mb-2">
            {typeof icon === 'string' ? (
              <span className="fs-4">{icon}</span>
            ) : (
              icon
            )}
          </div>
        )}

        {/* Value */}
        <div className={`stats-number text-${variant} mb-1`}>
          {loading ? (
            <div className="placeholder-glow">
              <span className="placeholder col-6"></span>
            </div>
          ) : (
            formatValue(value)
          )}
        </div>

        {/* Title */}
        <div className="stats-label text-muted small">
          {loading ? (
            <div className="placeholder-glow">
              <span className="placeholder col-8"></span>
            </div>
          ) : (
            title
          )}
        </div>

        {/* Subtitle */}
        {subtitle && !loading && (
          <div className="text-muted small mt-1">
            {subtitle}
          </div>
        )}

        {/* Trend */}
        {trend && !loading && (
          <div className={`small mt-2 ${getTrendColor(trend.direction)}`}>
            <span className="me-1">{getTrendIcon(trend.direction)}</span>
            <span className="fw-medium">
              {trend.direction === 'up' ? '+' : trend.direction === 'down' ? '-' : ''}
              {Math.abs(trend.value)}%
            </span>
            {trend.period && (
              <span className="text-muted ms-1">
                {trend.period}
              </span>
            )}
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

// Predefined stats card variants
export const TotalStatsCard: React.FC<Omit<StatsCardProps, 'variant' | 'icon'>> = (props) => (
  <StatsCard
    variant="primary"
    icon="📊"
    {...props}
  />
);

export const ActiveStatsCard: React.FC<Omit<StatsCardProps, 'variant' | 'icon'>> = (props) => (
  <StatsCard
    variant="success"
    icon="✅"
    {...props}
  />
);

export const ExpiredStatsCard: React.FC<Omit<StatsCardProps, 'variant' | 'icon'>> = (props) => (
  <StatsCard
    variant="danger"
    icon="❌"
    {...props}
  />
);

export const ExpiringStatsCard: React.FC<Omit<StatsCardProps, 'variant' | 'icon'>> = (props) => (
  <StatsCard
    variant="warning"
    icon="⚠️"
    {...props}
  />
);
