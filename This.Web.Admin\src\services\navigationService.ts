import type { MetadataResponse, NavigationNode, NavigationData, ObjectHierarchical } from '../types/metadata';
import { contextLookupCacheService } from './contextLookupCacheService';
import { env } from '../config/environment';
import { getCurrentApplicationDetails } from '../shared/testData';

class NavigationService {
  private static instance: NavigationService;
  private navigationData: NavigationData = {
    nodes: [],
    lastUpdated: new Date(),
    isLoading: false,
    error: null
  };
  private rawMetadataResponse: MetadataResponse | null = null;
  private listeners: ((data: NavigationData) => void)[] = [];
  private isInitialized: boolean = false;
  private lastFetchTime: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1 second

  // Fallback configuration
  private readonly FALLBACK_JSON_PATH = '/assets/JsonFormateForCreateMetaData%202.json';
  private readonly ENABLE_FALLBACK = true;

  private constructor() {
    // Initialize data on first access, not automatically
  }

  private getApiConfiguration(): { url: string; headers: Record<string, string> } {
    const applicationDetails = getCurrentApplicationDetails();

    if (!applicationDetails) {
      throw new Error('Application details not available. Cannot build API configuration.');
    }

    return {
      url: `${env.API_BASE_URL}api/comprehensive-entity/${applicationDetails.id}`,
      headers: {
        'accept': 'text/plain',
        'tenant': applicationDetails.tenantId
      }
    };
  }

  public static getInstance(): NavigationService {
    if (!NavigationService.instance) {
      NavigationService.instance = new NavigationService();
    }
    return NavigationService.instance;
  }

  // Subscribe to navigation data changes
  public subscribe(listener: (data: NavigationData) => void): () => void {
    // Add the listener
    this.listeners.push(listener);

    // If data is already loaded or loading, send current state
    if (this.isInitialized || this.navigationData.isLoading) {
      listener(this.navigationData);
    } else {
      // Initialize data on first subscription
      this.isInitialized = true;
      this.loadNavigationData();
    }

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Notify all listeners of data changes
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.navigationData));
  }

  /**
   * Update navigation data and notify listeners
   */
  private updateNavigationData(data: NavigationData): void {
    this.navigationData = data;
    this.notifyListeners();
  }

  /**
   * Check if the cache is still valid
   */
  public isCacheValid(): boolean {
    return this.lastFetchTime > 0 && (Date.now() - this.lastFetchTime) < this.CACHE_TTL;
  }

  /**
   * Clear the navigation cache
   */
  public clearCache(): void {
    this.rawMetadataResponse = null;
    this.lastFetchTime = 0;
    this.navigationData = {
      nodes: [],
      lastUpdated: new Date(),
      isLoading: false,
      error: null
    };
    this.notifyListeners();
  }

  // Load navigation data from API endpoint
  public async loadNavigationData(force: boolean = false): Promise<void> {
    // If already loading, don't start another load
    if (this.navigationData.isLoading) {
      return;
    }

    // If we have valid cached data and it's not a forced refresh, use it
    if (this.rawMetadataResponse && this.isCacheValid() && !force) {
      this.updateNavigationData({
        ...this.navigationData,
        isLoading: false,
        error: null
      });
      return;
    }

    // If we have valid data that's less than 5 minutes old, don't refresh
    const FIVE_MINUTES = 5 * 60 * 1000;
    if (this.navigationData.nodes.length > 0 && 
        (Date.now() - this.navigationData.lastUpdated.getTime() < FIVE_MINUTES)) {
      return;
    }

    this.navigationData.isLoading = true;
    this.navigationData.error = null;
    this.notifyListeners();

    let lastError: Error | null = null;

    // Retry logic for API calls
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        const apiConfig = this.getApiConfiguration();

        const response = await fetch(apiConfig.url, {
          method: 'GET',
          headers: apiConfig.headers,
          // Add timeout and other fetch options
          signal: AbortSignal.timeout(30000) // 30 second timeout
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const jsonData: MetadataResponse = await response.json();


        if (!jsonData.succeeded) {
          throw new Error(jsonData.message || 'API returned unsuccessful response');
        }

        const navigationNodes = this.transformToNavigationNodes(jsonData);

        // Extract context IDs and fetch bulk lookups after successful API response
        await this.fetchAndCacheBulkLookups(jsonData);

        // Store both the raw metadata response and transformed navigation data
        this.rawMetadataResponse = jsonData;
        this.navigationData = {
          nodes: navigationNodes,
          lastUpdated: new Date(),
          isLoading: false,
          error: null
        };

        this.lastFetchTime = Date.now(); // Update cache timestamp

        this.notifyListeners();
        return; // Success - exit retry loop

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error occurred');
        console.error(`NavigationService: Attempt ${attempt} failed:`, lastError.message);

        // If this is not the last attempt, wait before retrying
        if (attempt < this.MAX_RETRIES) {

          await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY * attempt)); // Exponential backoff
        }
      }
    }

    // All retries failed - try fallback if enabled
    console.error('NavigationService: All API retry attempts failed');

    if (this.ENABLE_FALLBACK) {

      try {
        await this.loadFallbackData();
        return; // Fallback succeeded
      } catch (fallbackError) {
        console.error('NavigationService: Fallback also failed:', fallbackError);
        lastError = fallbackError instanceof Error ? fallbackError : new Error('Fallback failed');
      }
    }

    // Both API and fallback failed
    this.navigationData = {
      ...this.navigationData,
      isLoading: false,
      error: `API failed: ${lastError?.message}. ${this.ENABLE_FALLBACK ? 'Fallback also failed.' : 'Fallback disabled.'}`
    };

    this.notifyListeners();
  }

  // Fallback method to load from static JSON file
  private async loadFallbackData(): Promise<void> {


    const response = await fetch(this.FALLBACK_JSON_PATH);
    if (!response.ok) {
      throw new Error(`Fallback failed: ${response.status} ${response.statusText}`);
    }

    const jsonData: MetadataResponse = await response.json();


    if (!jsonData.succeeded) {
      throw new Error(jsonData.message || 'Fallback data indicates failure');
    }

    const navigationNodes = this.transformToNavigationNodes(jsonData);

    // Store both the raw metadata response and transformed navigation data
    this.rawMetadataResponse = jsonData;
    this.navigationData = {
      nodes: navigationNodes,
      lastUpdated: new Date(),
      isLoading: false,
      error: 'Using fallback data - API unavailable'
    };

    this.notifyListeners();
  }

  /**
   * Extract context IDs from comprehensive-entity API response and fetch bulk lookups
   */
  private async fetchAndCacheBulkLookups(metadataResponse: MetadataResponse): Promise<void> {
    try {
      // Extract all metadata from all products and objects
      const allMetadata: any[] = [];

      metadataResponse.data.products.forEach(product => {
        if (product.rootObjects) {
          this.extractMetadataFromObjects(product.rootObjects, allMetadata);
        }
      });

      if (allMetadata.length === 0) {
        return;
      }

      // Extract context and tenant context IDs from all metadata
      const { contextIds, tenantContextIds } = contextLookupCacheService.extractContextIds(allMetadata);

      // ALWAYS call bulk context APIs when comprehensive-entity API is called (regardless of cache status)
      // This ensures fresh data is always fetched when the main API is called

      // Fetch bulk contexts if we have context IDs (ALWAYS)
      if (contextIds.length > 0) {
        const contextResult = await contextLookupCacheService.fetchAndCacheBulkContexts(contextIds);
      }

      // Fetch bulk tenant contexts if we have tenant context IDs (ALWAYS)
      if (tenantContextIds.length > 0) {
        const tenantContextResult = await contextLookupCacheService.fetchAndCacheBulkTenantContexts(tenantContextIds);
      }

      // Log cache statistics
      const cacheStats = contextLookupCacheService.getCacheStats();
    } catch (error) {
      console.error('NavigationService: Error fetching and caching bulk lookups from comprehensive-entity API:', error);
      // Don't throw error to avoid breaking the main navigation loading flow
    }
  }

  /**
   * Recursively extract metadata from objects hierarchy
   */
  private extractMetadataFromObjects(objects: ObjectHierarchical[], allMetadata: any[]): void {
    objects.forEach(obj => {
      if (obj.metadata && Array.isArray(obj.metadata)) {
        allMetadata.push(...obj.metadata);
      }

      if (obj.childObjects && obj.childObjects.length > 0) {
        this.extractMetadataFromObjects(obj.childObjects, allMetadata);
      }
    });
  }

  // Transform metadata response to navigation nodes (updated for structure)
  private transformToNavigationNodes(data: MetadataResponse): NavigationNode[] {
    const nodes: NavigationNode[] = [];

    data.data.products.forEach(product => {
      // Add product as top-level node
      const productNode: NavigationNode = {
        id: product.id,
        name: product.name,
        description: product.description,
        path: `/product/${product.id}`,
        icon: product.icon || this.getIconForType('product'),
        hierarchyLevel: 0,
        children: [],
        isActive: product.isActive,
        type: 'product',
        // Additional product properties
        version: product.version,
        metadata: product.metadata,
        createdAt: product.createdAt,
        createdBy: product.createdBy,
        modifiedAt: product.modifiedAt,
        modifiedBy: product.modifiedBy,
        isDeleted: product.isDeleted,
        isUserImported: product.isUserImported,
        isRoleAssigned: product.isRoleAssigned,
        apiKey: product.apiKey,
        isOnboardCompleted: product.isOnboardCompleted,
        applicationUrl: product.applicationUrl
      };

      // Add root objects directly under product
      if (product.rootObjects) {
        product.rootObjects.forEach(rootObject => {
          const objectNodes = this.transformObject(rootObject, product.id, '');
          if (productNode.children) {
            productNode.children.push(...objectNodes);
          }
        });
      }

      nodes.push(productNode);
    });

    return nodes;
  }

  // Recursively transform objects to navigation nodes (updated for structure)
  private transformObject(
    obj: ObjectHierarchical,
    productId: string,
    parentPath: string = ''
  ): NavigationNode[] {
    const basePath = parentPath || `/product/${productId}`;
    const currentPath = `${basePath}/object/${obj.id}`;

    const node: NavigationNode = {
      id: obj.id,
      name: obj.name,
      description: obj.description,
      path: currentPath,
      icon: obj.icon || this.getIconForType('object'),
      hierarchyLevel: obj.hierarchyLevel + 1, // +1 because product=0
      hierarchyPath: obj.hierarchyPath,
      parentObjectId: obj.parentObjectId,
      children: [],
      isActive: obj.isActive,
      isDeleted: obj.isDeleted,
      type: 'object',
      // Additional unified object properties
      metadata: obj.metadata,
      createdAt: obj.createdAt,
      createdBy: obj.createdBy,
      modifiedAt: obj.modifiedAt,
      modifiedBy: obj.modifiedBy
    };

    // Recursively add child objects
    if (obj.childObjects) {
      obj.childObjects.forEach(childObj => {
        const childNodes = this.transformObject(childObj, productId, currentPath);
        if (node.children) {
          node.children.push(...childNodes);
        }
      });
    }

    return [node];
  }

  // Get appropriate icon with fallback defaults
  private getIconForType(type: 'product' | 'object'): string {
    switch (type) {
      case 'product':
        return '📦';
      case 'object':
        return '📄';
      default:
        return '📄';
    }
  }

  // Manual refresh
  public refresh(force: boolean = false): Promise<void> {
    return this.loadNavigationData(force);
  }

  // Get current navigation data
  public getCurrentData(): NavigationData {
    return this.navigationData;
  }

  // Ensure the navigation service is initialized
  public async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      this.isInitialized = true;
      await this.loadNavigationData();
    } else if (this.navigationData.isLoading) {
      // Wait for current loading to complete
      await new Promise<void>((resolve) => {
        const checkLoading = () => {
          if (!this.navigationData.isLoading) {
            resolve();
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }
  }

  // Get API configuration for debugging
  public getApiConfig(): {
    apiUrl: string;
    headers: Record<string, string>;
    maxRetries: number;
    retryDelay: number;
    fallbackEnabled: boolean;
    fallbackPath: string;
  } {
    try {
      const apiConfig = this.getApiConfiguration();
      return {
        apiUrl: apiConfig.url,
        headers: apiConfig.headers,
        maxRetries: this.MAX_RETRIES,
        retryDelay: this.RETRY_DELAY,
        fallbackEnabled: this.ENABLE_FALLBACK,
        fallbackPath: this.FALLBACK_JSON_PATH,
      };
    } catch (error) {
      // Fallback if configuration is not available
      return {
        apiUrl: 'Configuration not available',
        headers: {},
        maxRetries: this.MAX_RETRIES,
        retryDelay: this.RETRY_DELAY,
        fallbackEnabled: this.ENABLE_FALLBACK,
        fallbackPath: this.FALLBACK_JSON_PATH,
      };
    }
  }

  /**
   * Get the raw metadata response
   * @returns The raw metadata response or null if not loaded
   */
  public getRawMetadataResponse(): MetadataResponse | null {
    return this.rawMetadataResponse;
  }

  public async loadFromApiOnly(): Promise<void> {
    this.navigationData.isLoading = true;
    this.navigationData.error = null;
    this.notifyListeners();

    try {
      // Get dynamic API configuration
      const apiConfig = this.getApiConfiguration();

      const response = await fetch(apiConfig.url, {
        method: 'GET',
        headers: apiConfig.headers,
        signal: AbortSignal.timeout(30000),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const jsonData: MetadataResponse = await response.json();

      if (!jsonData.succeeded) {
        throw new Error(jsonData.message || 'API returned unsuccessful response');
      }

      const navigationNodes = this.transformToNavigationNodes(jsonData);

      // Extract context IDs and fetch bulk lookups after successful API response
      await this.fetchAndCacheBulkLookups(jsonData);

      // Store both the raw metadata response and transformed navigation data
      this.rawMetadataResponse = jsonData;
      this.navigationData = {
        nodes: navigationNodes,
        lastUpdated: new Date(),
        isLoading: false,
        error: null,
      };

      this.lastFetchTime = Date.now(); // Update cache timestamp
    } catch (error) {
      this.navigationData = {
        ...this.navigationData,
        isLoading: false,
        error: error instanceof Error ? error.message : 'API-only load failed',
      };
    }

    this.notifyListeners();
  }
}

export default NavigationService;
