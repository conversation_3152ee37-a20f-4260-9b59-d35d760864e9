/**
 * Subscription API Service
 * Handles all subscription-related API operations using the Primary API (Port 7222)
 *
 * API Routing:
 * - GET /subscriptions - Retrieve subscription data (Primary API)
 * - GET /subscriptions/{id} - Get specific subscription (Primary API)
 * - GET /subscriptions/stats - Get subscription statistics (Primary API)
 * - GET /subscriptions/search - Search subscriptions (Primary API)
 *
 * Note: Subscription creation uses Secondary API (Port 7243) - see subscriptionCreationService
 */

import { BaseApiService } from '../baseApiService';
import type { PaginationParams, PaginatedResponse, ApiResult } from '../types';
import { HttpClientFactory } from '../httpClient';
import { SUBSCRIPTION_ENDPOINTS, EndpointBuilder } from '../../config/endpoints';
import { environment } from '../../config/environment';

// Subscription interfaces
export interface SubscriptionDto {
  id: string;
  tenantId: string;
  tenantName: string | null;
  productId: string;
  productName: string | null;
  subscriptionType: string;
  status: string;
  startDate: string;
  endDate: string | null;
  autoRenew: boolean;
  pricingTier: string | null;
  version: string;
  templateJson: string;
  isActive: boolean;
  metadataCount: number;
  createdAt: string;
  createdBy: string;
  modifiedAt: string | null;
  modifiedBy: string | null;
  isExpired: boolean;
  daysUntilExpiration: number | null;
}

export interface SubscriptionSummary {
  activeSubscriptions: number;
  expiredSubscriptions: number;
  expiringIn30Days: number;
  uniqueTenants: number;
  uniqueProducts: number;
  subscriptionsByStatus: Record<string, number>;
  subscriptionsByType: Record<string, number>;
}

export interface GetSubscriptionsParams extends PaginationParams {
  tenantId?: string;
  productId?: string;
  status?: string;
  subscriptionType?: string;
  isActive?: boolean;
  isExpired?: boolean;
  pricingTier?: string;
  searchTerm?: string;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
  expiringWithinDays?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  includeSummary?: boolean;
}

export interface SubscriptionsResponse extends PaginatedResponse<SubscriptionDto> {
  summary?: SubscriptionSummary;
}

export interface CreateSubscriptionRequest {
  productId: string;
  subscriptionType: string;
  status: string;
  startDate: string;
  endDate?: string;
  autoRenew: boolean;
  pricingTier?: string;
  version: string;
  templateJson: string;
  templateDetails?: Record<string, boolean>;
  isActive: boolean;
}

export interface UpdateSubscriptionRequest extends Partial<CreateSubscriptionRequest> {
  id: string;
}

/**
 * Subscription API Service Class
 */
export class SubscriptionApiService extends BaseApiService {
  constructor() {
    super(HttpClientFactory.subscriptionDataClient, 'SubscriptionAPI');
  }

  /**
   * Get the currently selected tenant from localStorage
   */
  private getCurrentTenant(): string | undefined {
    try {
      return localStorage.getItem('selectedTenantId') || undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * Get comprehensive subscriptions with filtering and pagination
   */
  async getSubscriptions(params: GetSubscriptionsParams = {}): Promise<SubscriptionsResponse> {
    try {
      this.log('getSubscriptions', { params });

      // Build query parameters
      const queryParams = {
        ...this.buildPaginationParams(params),
        ...this.buildSearchParams(params),
        ...this.buildSortParams(params),
      };

      // Add subscription-specific parameters
      if (params.tenantId) queryParams.tenantId = params.tenantId;
      if (params.productId) queryParams.productId = params.productId;
      if (params.status) queryParams.status = params.status;
      if (params.subscriptionType) queryParams.subscriptionType = params.subscriptionType;
      if (params.isExpired !== undefined) queryParams.isExpired = params.isExpired;
      if (params.pricingTier) queryParams.pricingTier = params.pricingTier;
      if (params.startDateFrom) queryParams.startDateFrom = params.startDateFrom;
      if (params.startDateTo) queryParams.startDateTo = params.startDateTo;
      if (params.endDateFrom) queryParams.endDateFrom = params.endDateFrom;
      if (params.endDateTo) queryParams.endDateTo = params.endDateTo;
      if (params.expiringWithinDays !== undefined) queryParams.expiringWithinDays = params.expiringWithinDays;
      if (params.includeSummary !== undefined) queryParams.includeSummary = params.includeSummary;

      const endpoint = EndpointBuilder.buildComprehensiveSubscriptionEndpoint(queryParams);
      const tenant = this.getCurrentTenant();
      this.log('getSubscriptions', { endpoint, queryParams, tenant });
      
      const response = await this.get<ApiResult<SubscriptionsResponse>>(endpoint, {}, tenant);

      return this.transformApiResult(response);
    } catch (error) {
      this.handleError('getSubscriptions', error);
    }
  }

  /**
   * Get subscription by ID
   */
  async getSubscriptionById(id: string, tenant?: string): Promise<SubscriptionDto> {
    try {
      const finalTenant = tenant || this.getCurrentTenant();
      this.log('getSubscriptionById', { id, tenant: finalTenant });

      const endpoint = SUBSCRIPTION_ENDPOINTS.SUBSCRIPTION_BY_ID(id);
      const response = await this.get<ApiResult<SubscriptionDto>>(endpoint, {}, finalTenant);

      return this.transformApiResult(response);
    } catch (error) {
      this.handleError('getSubscriptionById', error);
    }
  }

  /**
   * Create new subscription
   */
  async createSubscription(data: CreateSubscriptionRequest, tenant?: string): Promise<SubscriptionDto> {
    try {
      const finalTenant = tenant || this.getCurrentTenant();
      this.log('createSubscription', { data, tenant: finalTenant });

      const response = await this.post<ApiResult<SubscriptionDto>>(
        SUBSCRIPTION_ENDPOINTS.SUBSCRIPTIONS,
        data,
        finalTenant
      );

      return this.transformApiResult(response);
    } catch (error) {
      this.handleError('createSubscription', error);
    }
  }

  /**
   * Update existing subscription
   */
  async updateSubscription(data: UpdateSubscriptionRequest, tenant?: string): Promise<SubscriptionDto> {
    try {
      const finalTenant = tenant || this.getCurrentTenant();
      this.log('updateSubscription', { data, tenant: finalTenant });

      const endpoint = SUBSCRIPTION_ENDPOINTS.SUBSCRIPTION_BY_ID(data.id);
      const response = await this.put<ApiResult<SubscriptionDto>>(endpoint, data, finalTenant);

      return this.transformApiResult(response);
    } catch (error) {
      this.handleError('updateSubscription', error);
    }
  }

  /**
   * Delete subscription
   */
  async deleteSubscription(id: string, tenant?: string): Promise<void> {
    try {
      const finalTenant = tenant || this.getCurrentTenant();
      this.log('deleteSubscription', { id, tenant: finalTenant });

      const endpoint = SUBSCRIPTION_ENDPOINTS.SUBSCRIPTION_BY_ID(id);
      await this.delete<void>(endpoint, finalTenant);
    } catch (error) {
      this.handleError('deleteSubscription', error);
    }
  }

  /**
   * Get subscription statistics
   */
  async getSubscriptionStats(tenant?: string): Promise<SubscriptionSummary> {
    try {
      const finalTenant = tenant || this.getCurrentTenant();
      this.log('getSubscriptionStats', { tenant: finalTenant });

      const response = await this.get<ApiResult<SubscriptionSummary>>(
        SUBSCRIPTION_ENDPOINTS.SUBSCRIPTION_STATS,
        {},
        finalTenant
      );

      return this.transformApiResult(response);
    } catch (error) {
      this.handleError('getSubscriptionStats', error);
    }
  }

  /**
   * Search subscriptions
   */
  async searchSubscriptions(searchTerm: string, params: GetSubscriptionsParams = {}): Promise<SubscriptionsResponse> {
    try {
      this.log('searchSubscriptions', { searchTerm, params });

      return this.getSubscriptions({
        ...params,
        searchTerm,
      });
    } catch (error) {
      this.handleError('searchSubscriptions', error);
    }
  }

  /**
   * Get subscriptions by tenant
   */
  async getSubscriptionsByTenant(tenantId: string, params: GetSubscriptionsParams = {}): Promise<SubscriptionsResponse> {
    try {
      this.log('getSubscriptionsByTenant', { tenantId, params });

      return this.getSubscriptions({
        ...params,
        tenantId,
      });
    } catch (error) {
      this.handleError('getSubscriptionsByTenant', error);
    }
  }

  /**
   * Get subscriptions by status
   */
  async getSubscriptionsByStatus(status: string, params: GetSubscriptionsParams = {}): Promise<SubscriptionsResponse> {
    try {
      this.log('getSubscriptionsByStatus', { status, params });

      return this.getSubscriptions({
        ...params,
        status,
      });
    } catch (error) {
      this.handleError('getSubscriptionsByStatus', error);
    }
  }

  /**
   * Get expiring subscriptions
   */
  async getExpiringSubscriptions(days: number = 30, params: GetSubscriptionsParams = {}): Promise<SubscriptionsResponse> {
    try {
      this.log('getExpiringSubscriptions', { days, params });

      return this.getSubscriptions({
        ...params,
        expiringWithinDays: days,
      });
    } catch (error) {
      this.handleError('getExpiringSubscriptions', error);
    }
  }
}

// Export singleton instance
export const subscriptionApiService = new SubscriptionApiService();
