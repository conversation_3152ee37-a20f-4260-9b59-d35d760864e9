using Abstraction.Database.Repositories;
using Application.RoleActions.DTOs;
using Application.RoleActions.Specifications;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.RoleActions.Queries;

/// <summary>
/// Get role actions query handler
/// </summary>
public class GetRoleActionsQueryHandler : IRequestHandler<GetRoleActionsQuery, Result<RoleActionsResponse>>
{
    private readonly IRepository<Domain.Entities.RoleActions> _roleActionsRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetRoleActionsQueryHandler(IRepository<Domain.Entities.RoleActions> roleActionsRepository)
    {
        _roleActionsRepository = roleActionsRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<RoleActionsResponse>> Handle(GetRoleActionsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var roleActionsSpec = new RoleActionsByRoleIdSpec(request.RoleId);
            var roleActions = await _roleActionsRepository.ListAsync(roleActionsSpec, cancellationToken);

            var response = new RoleActionsResponse
            {
                RoleId = request.RoleId,
                Actions = roleActions.Adapt<List<RoleActionsDto>>()
            };

            return Result<RoleActionsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<RoleActionsResponse>.Failure($"Error retrieving role actions: {ex.Message}");
        }
    }
}
