import React, { useState, useCallback } from 'react';
import { Card, Button, Form, Row, Col, Badge } from 'react-bootstrap';
import { Plus, ChevronDown, ChevronRight, Trash2, Save, X } from 'lucide-react';
import { generateGuid } from '../../utils';

// Types for Display and Action configuration
export interface ActionConfig {
  id: string;
  name: string;
  description: string;
  type: 'API Call' | 'Navigation';
  endpointTemplate?: string;
  navigationTarget?: string;
  icon: string;
  buttonStyle: 'Primary' | 'Secondary' | 'Danger' | 'Success' | 'Warning';
  confirmationMessage?: string;
  successMessage?: string;
  errorMessage?: string;
  isActive: boolean;
}

export interface DisplayConfig {
  id: string;
  name: string;
  description?: string;
  displayName: string;
  isDefault: boolean;
  routeTemplate: string;
  icon: string;
  sortOrder: number;
  actions: ActionConfig[];
}

interface DisplayActionConfiguratorProps {
  selectedNode: any;
  onConfigurationChange: (displays: DisplayConfig[]) => void;
}

// Available icons for dropdowns
const AVAILABLE_ICONS = [
  'list', 'plus', 'eye', 'edit', 'trash', 'save', 'x', 'settings', 'user', 'home'
];

// Default display types
const DEFAULT_DISPLAY_TYPES = [
  { name: 'List', displayName: 'List View', routeTemplate: '/objects/list', icon: 'list' },
  { name: 'Create', displayName: 'Create New', routeTemplate: '/objects/create', icon: 'plus' },
  { name: 'View', displayName: 'View Details', routeTemplate: '/objects/{id}', icon: 'eye' },
  { name: 'Update', displayName: 'Edit', routeTemplate: '/objects/{id}/edit', icon: 'edit' }
];

export const DisplayActionConfigurator: React.FC<DisplayActionConfiguratorProps> = ({
  selectedNode,
  onConfigurationChange
}) => {
  const [displays, setDisplays] = useState<DisplayConfig[]>(() => {
    // Initialize with existing displays from node metadata or default displays
    const existingDisplays = selectedNode?.displays || [];
    if (existingDisplays.length > 0) {
      return existingDisplays;
    }
    
    // Create default displays
    return DEFAULT_DISPLAY_TYPES.map((defaultDisplay, index) => ({
      id: generateGuid(),
      name: defaultDisplay.name,
      displayName: defaultDisplay.displayName,
      isDefault: index === 0, // First display is default
      routeTemplate: defaultDisplay.routeTemplate,
      icon: defaultDisplay.icon,
      sortOrder: index,
      actions: []
    }));
  });

  const [expandedDisplays, setExpandedDisplays] = useState<Set<string>>(new Set());
  const [editingAction, setEditingAction] = useState<string | null>(null);

  // Toggle display expansion
  const toggleDisplay = useCallback((displayId: string) => {
    setExpandedDisplays(prev => {
      const newSet = new Set(prev);
      if (newSet.has(displayId)) {
        newSet.delete(displayId);
      } else {
        newSet.add(displayId);
      }
      return newSet;
    });
  }, []);

  // Update display configuration
  const updateDisplay = useCallback((displayId: string, updates: Partial<DisplayConfig>) => {
    setDisplays(prev => {
      const updated = prev.map(display => 
        display.id === displayId ? { ...display, ...updates } : display
      );
      onConfigurationChange(updated);
      return updated;
    });
  }, [onConfigurationChange]);

  // Add new action to display
  const addAction = useCallback((displayId: string) => {
    const newAction: ActionConfig = {
      id: generateGuid(),
      name: '',
      description: '',
      type: 'API Call',
      endpointTemplate: '',
      icon: 'save',
      buttonStyle: 'Primary',
      isActive: true
    };

    setDisplays(prev => {
      const updated = prev.map(display => 
        display.id === displayId 
          ? { ...display, actions: [...display.actions, newAction] }
          : display
      );
      onConfigurationChange(updated);
      return updated;
    });

    setEditingAction(newAction.id);
  }, [onConfigurationChange]);

  // Update action configuration
  const updateAction = useCallback((displayId: string, actionId: string, updates: Partial<ActionConfig>) => {
    setDisplays(prev => {
      const updated = prev.map(display => 
        display.id === displayId 
          ? {
              ...display,
              actions: display.actions.map(action =>
                action.id === actionId ? { ...action, ...updates } : action
              )
            }
          : display
      );
      onConfigurationChange(updated);
      return updated;
    });
  }, [onConfigurationChange]);

  // Remove action
  const removeAction = useCallback((displayId: string, actionId: string) => {
    setDisplays(prev => {
      const updated = prev.map(display => 
        display.id === displayId 
          ? { ...display, actions: display.actions.filter(action => action.id !== actionId) }
          : display
      );
      onConfigurationChange(updated);
      return updated;
    });
  }, [onConfigurationChange]);

  // Add new custom display
  const addCustomDisplay = useCallback(() => {
    const newDisplay: DisplayConfig = {
      id: generateGuid(),
      name: 'Custom',
      displayName: 'Custom Display',
      isDefault: false,
      routeTemplate: '/objects/custom',
      icon: 'settings',
      sortOrder: displays.length,
      actions: []
    };

    setDisplays(prev => {
      const updated = [...prev, newDisplay];
      onConfigurationChange(updated);
      return updated;
    });

    setExpandedDisplays(prev => new Set([...prev, newDisplay.id]));
  }, [displays.length, onConfigurationChange]);

  return (
    <div className="display-action-configurator">
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h6 className="mb-0">Display & Action Configuration</h6>
        <Button variant="outline-primary" size="sm" onClick={addCustomDisplay}>
          <Plus size={14} className="me-1" />
          Add Display
        </Button>
      </div>

      <div className="displays-container">
        {displays.map((display) => (
          <Card key={display.id} className="mb-3">
            <Card.Header 
              className="d-flex justify-content-between align-items-center cursor-pointer"
              onClick={() => toggleDisplay(display.id)}
              style={{ cursor: 'pointer' }}
            >
              <div className="d-flex align-items-center">
                {expandedDisplays.has(display.id) ? 
                  <ChevronDown size={16} className="me-2" /> : 
                  <ChevronRight size={16} className="me-2" />
                }
                <strong>{display.displayName}</strong>
                {display.isDefault && <Badge bg="primary" className="ms-2">Default</Badge>}
              </div>
              <Badge bg="secondary">{display.actions.length} actions</Badge>
            </Card.Header>

            {expandedDisplays.has(display.id) && (
              <Card.Body>
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group className="mb-2">
                      <Form.Label className="small">Display Name</Form.Label>
                      <Form.Control
                        size="sm"
                        type="text"
                        value={display.displayName}
                        onChange={(e) => updateDisplay(display.id, { displayName: e.target.value })}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-2">
                      <Form.Label className="small">Route Template</Form.Label>
                      <Form.Control
                        size="sm"
                        type="text"
                        value={display.routeTemplate}
                        onChange={(e) => updateDisplay(display.id, { routeTemplate: e.target.value })}
                        placeholder="/objects/list"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3">
                  <Col md={4}>
                    <Form.Group className="mb-2">
                      <Form.Label className="small">Icon</Form.Label>
                      <Form.Select
                        size="sm"
                        value={display.icon}
                        onChange={(e) => updateDisplay(display.id, { icon: e.target.value })}
                      >
                        {AVAILABLE_ICONS.map(icon => (
                          <option key={icon} value={icon}>{icon}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-2">
                      <Form.Label className="small">Sort Order</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={display.sortOrder}
                        onChange={(e) => updateDisplay(display.id, { sortOrder: parseInt(e.target.value) || 0 })}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-2">
                      <Form.Label className="small">Is Default</Form.Label>
                      <Form.Check
                        type="switch"
                        checked={display.isDefault}
                        onChange={(e) => {
                          // Only one display can be default
                          if (e.target.checked) {
                            setDisplays(prev => prev.map(d => ({ ...d, isDefault: d.id === display.id })));
                          }
                        }}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <div className="actions-section">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <h6 className="mb-0">Actions</h6>
                    <Button variant="outline-success" size="sm" onClick={() => addAction(display.id)}>
                      <Plus size={12} className="me-1" />
                      Add Action
                    </Button>
                  </div>

                  {display.actions.map((action) => (
                    <Card key={action.id} className="mb-2" style={{ backgroundColor: '#f8f9fa' }}>
                      <Card.Body className="p-2">
                        <Row>
                          <Col md={8}>
                            <Form.Group className="mb-1">
                              <Form.Control
                                size="sm"
                                type="text"
                                value={action.name}
                                onChange={(e) => updateAction(display.id, action.id, { name: e.target.value })}
                                placeholder="Action name (e.g., Save, Delete)"
                              />
                            </Form.Group>
                          </Col>
                          <Col md={4} className="text-end">
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => removeAction(display.id, action.id)}
                            >
                              <Trash2 size={12} />
                            </Button>
                          </Col>
                        </Row>

                        {editingAction === action.id && (
                          <div className="mt-2">
                            <Row className="mb-2">
                              <Col md={12}>
                                <Form.Group className="mb-2">
                                  <Form.Label className="small">Description</Form.Label>
                                  <Form.Control
                                    as="textarea"
                                    rows={2}
                                    size="sm"
                                    value={action.description}
                                    onChange={(e) => updateAction(display.id, action.id, { description: e.target.value })}
                                  />
                                </Form.Group>
                              </Col>
                            </Row>

                            <Row className="mb-2">
                              <Col md={6}>
                                <Form.Group className="mb-2">
                                  <Form.Label className="small">Type</Form.Label>
                                  <div>
                                    <Form.Check
                                      inline
                                      type="radio"
                                      name={`type-${action.id}`}
                                      label="API Call"
                                      checked={action.type === 'API Call'}
                                      onChange={() => updateAction(display.id, action.id, { type: 'API Call' })}
                                    />
                                    <Form.Check
                                      inline
                                      type="radio"
                                      name={`type-${action.id}`}
                                      label="Navigation"
                                      checked={action.type === 'Navigation'}
                                      onChange={() => updateAction(display.id, action.id, { type: 'Navigation' })}
                                    />
                                  </div>
                                </Form.Group>
                              </Col>
                              <Col md={6}>
                                <Form.Group className="mb-2">
                                  <Form.Label className="small">Button Style</Form.Label>
                                  <Form.Select
                                    size="sm"
                                    value={action.buttonStyle}
                                    onChange={(e) => updateAction(display.id, action.id, { buttonStyle: e.target.value as any })}
                                  >
                                    <option value="Primary">Primary</option>
                                    <option value="Secondary">Secondary</option>
                                    <option value="Success">Success</option>
                                    <option value="Danger">Danger</option>
                                    <option value="Warning">Warning</option>
                                  </Form.Select>
                                </Form.Group>
                              </Col>
                            </Row>

                            {action.type === 'API Call' && (
                              <Form.Group className="mb-2">
                                <Form.Label className="small">Endpoint Template</Form.Label>
                                <Form.Control
                                  size="sm"
                                  type="text"
                                  value={action.endpointTemplate || ''}
                                  onChange={(e) => updateAction(display.id, action.id, { endpointTemplate: e.target.value })}
                                  placeholder="/api/objects/{id}"
                                />
                              </Form.Group>
                            )}

                            {action.type === 'Navigation' && (
                              <Form.Group className="mb-2">
                                <Form.Label className="small">Navigation Target</Form.Label>
                                <Form.Control
                                  size="sm"
                                  type="text"
                                  value={action.navigationTarget || ''}
                                  onChange={(e) => updateAction(display.id, action.id, { navigationTarget: e.target.value })}
                                  placeholder="/objects/list"
                                />
                              </Form.Group>
                            )}

                            <div className="text-end">
                              <Button
                                variant="success"
                                size="sm"
                                onClick={() => setEditingAction(null)}
                                className="me-1"
                              >
                                <Save size={12} />
                              </Button>
                              <Button
                                variant="outline-secondary"
                                size="sm"
                                onClick={() => setEditingAction(null)}
                              >
                                <X size={12} />
                              </Button>
                            </div>
                          </div>
                        )}

                        {editingAction !== action.id && action.name && (
                          <div className="mt-1">
                            <small className="text-muted">
                              {action.type} • {action.buttonStyle}
                              {action.type === 'API Call' && action.endpointTemplate && ` • ${action.endpointTemplate}`}
                              {action.type === 'Navigation' && action.navigationTarget && ` • ${action.navigationTarget}`}
                            </small>
                            <Button
                              variant="link"
                              size="sm"
                              className="p-0 ms-2"
                              onClick={() => setEditingAction(action.id)}
                            >
                              Edit
                            </Button>
                          </div>
                        )}
                      </Card.Body>
                    </Card>
                  ))}
                </div>
              </Card.Body>
            )}
          </Card>
        ))}
      </div>
    </div>
  );
};
