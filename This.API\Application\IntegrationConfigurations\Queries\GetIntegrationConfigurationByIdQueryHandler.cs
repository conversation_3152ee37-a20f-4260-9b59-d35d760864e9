using Application.IntegrationConfigurations.DTOs;
using Application.IntegrationConfigurations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Queries;

/// <summary>
/// Get integration configuration by ID query handler
/// </summary>
public class GetIntegrationConfigurationByIdQueryHandler : IRequestHandler<GetIntegrationConfigurationByIdQuery, Result<ViewIntegrationConfigurationDto>>
{
    private readonly IReadRepository<IntegrationConfiguration> _configurationRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationConfigurationByIdQueryHandler(IReadRepository<IntegrationConfiguration> configurationRepository)
    {
        _configurationRepository = configurationRepository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ViewIntegrationConfigurationDto>> Handle(GetIntegrationConfigurationByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var spec = new IntegrationConfigurationWithDetailsSpec(request.Id);
            var configuration = await _configurationRepository.GetBySpecAsync(spec, cancellationToken);
            
            if (configuration == null)
            {
                return Result<ViewIntegrationConfigurationDto>.Failure("Integration configuration not found.");
            }

            var viewDto = new ViewIntegrationConfigurationDto
            {
                Id = configuration.Id,
                IntegrationId = configuration.IntegrationId,
                IntegrationName = configuration.Integration?.Name ?? string.Empty,
                IntegrationApiId = configuration.IntegrationApiId,
                IntegrationApiName = configuration.IntegrationApi?.Name ?? string.Empty,
                ObjectId = configuration.ObjectId,
                ObjectName = configuration.Object?.Name ?? string.Empty,
                Direction = configuration.Direction,
                IsActive = configuration.IsActive,
                CreatedAt = configuration.CreatedAt,
                CreatedBy = configuration.CreatedBy,
                ModifiedAt = configuration.ModifiedAt,
                ModifiedBy = configuration.ModifiedBy
            };

            return Result<ViewIntegrationConfigurationDto>.Success(viewDto);
        }
        catch (Exception ex)
        {
            return Result<ViewIntegrationConfigurationDto>.Failure($"Failed to get integration configuration: {ex.Message}");
        }
    }
}
