using Application.ActionManagement.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ActionManagement.Queries;

/// <summary>
/// Get all actions query
/// </summary>
public class GetActionsQuery : IRequest<Result<List<ActionDto>>>
{
    /// <summary>
    /// Search term for filtering
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; } = true;

    /// <summary>
    /// Order by field
    /// </summary>
    public string? OrderBy { get; set; } = "Name";
}
