import React, { useState, useEffect } from 'react';
import { <PERSON>dal, Button, Form, Alert, Spinner } from 'react-bootstrap';
import { Save, X } from 'lucide-react';
import { contextService } from '../../services/contextService';
import type { TenantContext } from '../../types/context';

interface TenantContextModalProps {
  show: boolean;
  onHide: () => void;
  tenantContext: TenantContext | null;
  onSaved: () => void;
  categories: string[];
  tenantId: string;
}

export const TenantContextModal: React.FC<TenantContextModalProps> = ({
  show,
  onHide,
  tenantContext,
  onSaved,
  categories,
  tenantId
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    isActive: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validated, setValidated] = useState(false);

  const isEditing = !!tenantContext;

  useEffect(() => {
    if (show) {
      if (tenantContext) {
        setFormData({
          name: tenantContext.name,
          description: tenantContext.description || '',
          category: tenantContext.category || '',
          isActive: tenantContext.isActive
        });
      } else {
        setFormData({
          name: '',
          description: '',
          category: '',
          isActive: true
        });
      }
      setError(null);
      setValidated(false);
    }
  }, [show, tenantContext]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();

    const form = e.currentTarget;
    if (form.checkValidity() === false) {
      setValidated(true);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = {
        id: tenantContext?.id,
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        category: formData.category.trim() || null,
        isActive: formData.isActive
      };

      if (isEditing) {
        await contextService.updateTenantContext(data, tenantId);
      } else {
        await contextService.createTenantContext(data, tenantId);
      }

      onSaved();
      onHide();
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving the tenant context');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onHide();
    }
  };

  return (
    <Modal show={show} onHide={handleClose} size="lg" backdrop="static">
      <Modal.Header closeButton>
        <Modal.Title>
          {isEditing ? 'Edit TenantContext' : 'Create TenantContext'}
        </Modal.Title>
      </Modal.Header>

      <Form noValidate validated={validated} onSubmit={handleSubmit}>
        <Modal.Body>
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          <Form.Group className="mb-3">
            <Form.Label>Name <span className="text-danger">*</span></Form.Label>
            <Form.Control
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              disabled={loading}
              placeholder="Enter tenant context name"
            />
            <Form.Control.Feedback type="invalid">
              Please provide a valid tenant context name.
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              disabled={loading}
              placeholder="Enter tenant context description (optional)"
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Category</Form.Label>
            <Form.Select
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              disabled={loading}
            >
              <option value="">Select a category (optional)</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </Form.Select>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Check
              type="switch"
              id="tenant-context-active"
              name="isActive"
              label="Active"
              checked={formData.isActive}
              onChange={handleInputChange}
              disabled={loading}
            />
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose} disabled={loading}>
            <X size={16} className="me-2" />
            Cancel
          </Button>
          <Button variant="primary" type="submit" disabled={loading}>
            {loading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                {isEditing ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <Save size={16} className="me-2" />
                {isEditing ? 'Update' : 'Create'}
              </>
            )}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
