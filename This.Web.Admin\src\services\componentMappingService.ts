// Service for mapping uiComponent values to React components
import React from 'react';

// Import all form input components
import ThisDate from '@/features/inputs/datetime/ThisDate';
import ThisDateTime from '@/features/inputs/datetime/ThisDateTime';
import ThisFile from '@/features/inputs/media/ThisFile';
import ThisImage from '@/features/inputs/media/ThisImage';
import ThisVideo from '@/features/inputs/media/ThisVideo';
import ThisCurrency from '@/features/inputs/numeric/ThisCurrency';
import ThisNumber from '@/features/inputs/numeric/ThisNumber';
import ThisPercentage from '@/features/inputs/numeric/ThisPercentage';
import ThisSlider from '@/features/inputs/numeric/ThisSlider';
import ThisCheckbox from '@/features/inputs/selection/ThisCheckbox';
import ThisDropdown from '@/features/inputs/selection/ThisDropdown';
import ThisRadio from '@/features/inputs/selection/ThisRadio';
import ThisEmail from '@/features/inputs/text/ThisEmail';
import ThisPhone from '@/features/inputs/text/ThisPhone';
import ThisRichText from '@/features/inputs/text/ThisRichText';
import ThisText from '@/features/inputs/text/ThisText';
import ThisTextarea from '@/features/inputs/text/ThisTextarea';
import type { FormFieldConfig } from '@/shared/types/forms';
import type { Metadata } from '@/types/metadata';

// Component mapping interface
export interface ComponentMapping {
  component: React.ComponentType<any>;
  defaultProps?: Record<string, any>;
  propsMapper?: (fieldConfig: FormFieldConfig) => Record<string, any>;
}

// Props interface for all form components (standardized)
export interface StandardFormProps {
  id: string;
  label: string;
  value: any;
  onChange: (value: any) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  required?: boolean;
  placeholder?: string;
  helpText?: string;
  // Component-specific props will be added by propsMapper
}

export class ComponentMappingService {
  private static instance: ComponentMappingService;

  // Configuration for fallback component
  private fallbackComponentName: string = 'ThisText';

  // Mapping of uiComponent values to React components (exact match with existing files)
  private readonly componentMap: Record<string, ComponentMapping> = {
    // Map uiComponent values to existing component files
    'ThisText': {
      component: ThisText,
      propsMapper: (config) => ({
        type: config.inputType || 'text',
        maxLength: config.maxLength,
        minLength: config.minLength,
        pattern: config.validationPattern,
        showCharacterCount: !!config.maxLength,
        allowClear: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisTextarea': {
      component: ThisTextarea,
      propsMapper: (config) => ({
        maxLength: config.maxLength,
        minLength: config.minLength,
        showCharacterCount: !!config.maxLength,
        rows: 4,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisEmail': {
      component: ThisEmail,
      propsMapper: (config) => ({
        maxLength: config.maxLength,
        minLength: config.minLength,
        showValidationIcon: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisNumber': {
      component: ThisNumber,
      propsMapper: (config) => ({
        allowDecimals: true,
        allowNegative: true,
        showSteppers: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisPhone': {
      component: ThisPhone,
      propsMapper: (config) => ({
        showCountryCode: true,
        allowInternational: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisDate': {
      component: ThisDate,
      propsMapper: (config) => ({
        showCalendarIcon: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisDateTime': {
      component: ThisDateTime,
      propsMapper: (config) => ({
        showCalendarIcon: true,
        showTimeIcon: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisDropdown': {
      component: ThisDropdown,
      propsMapper: (config) => ({
        options: config.defaultOptions || [],
        searchable: (config.defaultOptions?.length || 0) > 5,
        clearable: !config.isRequired,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisCheckbox': {
      component: ThisCheckbox,
      propsMapper: (config) => ({
        options: config.defaultOptions || [],
        layout: 'vertical',
        allowSelectAll: (config.defaultOptions?.length || 0) > 3,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisRadio': {
      component: ThisRadio,
      propsMapper: (config) => ({
        options: config.defaultOptions || [],
        layout: 'vertical',
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisCurrency': {
      component: ThisCurrency,
      propsMapper: (config) => ({
        currency: 'USD',
        showCurrencySymbol: true,
        allowNegative: false,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisPercentage': {
      component: ThisPercentage,
      propsMapper: (config) => ({
        showPercentageSymbol: true,
        allowDecimals: true,
        min: 0,
        max: 100,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisSlider': {
      component: ThisSlider,
      propsMapper: (config) => ({
        min: 0,
        max: 100,
        step: 1,
        showValue: true,
        showLabels: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisFile': {
      component: ThisFile,
      propsMapper: (config) => ({
        multiple: false,
        allowedTypes: ['*'],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        showPreview: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisImage': {
      component: ThisImage,
      propsMapper: (config) => ({
        multiple: false,
        allowedTypes: ['image/*'],
        maxFileSize: 5 * 1024 * 1024, // 5MB
        showPreview: true,
        allowCrop: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisVideo': {
      component: ThisVideo,
      propsMapper: (config) => ({
        multiple: false,
        allowedTypes: ['video/*'],
        maxFileSize: 100 * 1024 * 1024, // 100MB
        showPreview: true,
        customValidation: this.createCustomValidator(config)
      })
    },
    'ThisRichText': {
      component: ThisRichText,
      propsMapper: (config) => ({
        toolbar: 'basic',
        maxLength: config.maxLength,
        showCharacterCount: !!config.maxLength,
        customValidation: this.createCustomValidator(config)
      })
    },

    // Add common aliases for backward compatibility
    'text': { component: ThisText, propsMapper: (config) => this.componentMap['ThisText'].propsMapper!(config) },
    'textarea': { component: ThisTextarea, propsMapper: (config) => this.componentMap['ThisTextarea'].propsMapper!(config) },
    'email': { component: ThisEmail, propsMapper: (config) => this.componentMap['ThisEmail'].propsMapper!(config) },
    'number': { component: ThisNumber, propsMapper: (config) => this.componentMap['ThisNumber'].propsMapper!(config) },
    'phone': { component: ThisPhone, propsMapper: (config) => this.componentMap['ThisPhone'].propsMapper!(config) },
    'date': { component: ThisDate, propsMapper: (config) => this.componentMap['ThisDate'].propsMapper!(config) },
    'datetime': { component: ThisDateTime, propsMapper: (config) => this.componentMap['ThisDateTime'].propsMapper!(config) },
    'dropdown': { component: ThisDropdown, propsMapper: (config) => this.componentMap['ThisDropdown'].propsMapper!(config) },
    'select': { component: ThisDropdown, propsMapper: (config) => this.componentMap['ThisDropdown'].propsMapper!(config) },
    'checkbox': { component: ThisCheckbox, propsMapper: (config) => this.componentMap['ThisCheckbox'].propsMapper!(config) },
    'radio': { component: ThisRadio, propsMapper: (config) => this.componentMap['ThisRadio'].propsMapper!(config) },
    'currency': { component: ThisCurrency, propsMapper: (config) => this.componentMap['ThisCurrency'].propsMapper!(config) },
    'percentage': { component: ThisPercentage, propsMapper: (config) => this.componentMap['ThisPercentage'].propsMapper!(config) },
    'slider': { component: ThisSlider, propsMapper: (config) => this.componentMap['ThisSlider'].propsMapper!(config) },
    'file': { component: ThisFile, propsMapper: (config) => this.componentMap['ThisFile'].propsMapper!(config) },
    'image': { component: ThisImage, propsMapper: (config) => this.componentMap['ThisImage'].propsMapper!(config) },
    'video': { component: ThisVideo, propsMapper: (config) => this.componentMap['ThisVideo'].propsMapper!(config) },
    'richtext': { component: ThisRichText, propsMapper: (config) => this.componentMap['ThisRichText'].propsMapper!(config) }
  };

  static getInstance(): ComponentMappingService {
    if (!ComponentMappingService.instance) {
      ComponentMappingService.instance = new ComponentMappingService();
    }
    return ComponentMappingService.instance;
  }

  /**
   * Get React component for a given uiComponent value
   * Returns the requested component or falls back to the default fallback component
   */
  getComponent(uiComponent: string): React.ComponentType<any> {
    // First try exact match (for proper cased names like "ThisText")
    let mapping = this.componentMap[uiComponent];

    // Return found component or fallback to default
    return mapping?.component || this.getFallbackComponent();
  }

  /**
   * Get complete component mapping including props
   * Returns the requested mapping or falls back to the default fallback mapping
   */
  getComponentMapping(uiComponent: string): ComponentMapping {
    // First try exact match (for proper cased names like "ThisText")
    let mapping = this.componentMap[uiComponent];

    // Return found mapping or fallback to default
    return mapping || this.getFallbackComponentMapping();
  }

  /**
   * Generate props for a component based on field configuration
   */
  generateComponentProps(fieldConfig: FormFieldConfig, value: any, onChange: (value: any) => void, onValidation?: (errors: string[]) => void): StandardFormProps & Record<string, any> {
    const mapping = this.getComponentMapping(fieldConfig.uiComponent);

    // Base props that all components should have
    const baseProps: StandardFormProps = {
      id: fieldConfig.id,
      label: fieldConfig.displayLabel,
      value: value,
      onChange: onChange,
      onValidation: onValidation,
      required: fieldConfig.isRequired || undefined,
      placeholder: fieldConfig.placeholder || undefined,
      helpText: fieldConfig.helpText || undefined,
      disabled: false,
      readOnly: false
    };

    // Apply component-specific props if mapping exists
    if (mapping?.propsMapper) {
      const componentSpecificProps = mapping.propsMapper(fieldConfig);
      return { ...baseProps, ...componentSpecificProps };
    }

    // Apply default props if available
    if (mapping?.defaultProps) {
      return { ...baseProps, ...mapping.defaultProps };
    }

    return baseProps;
  }

  /**
   * Generate props for a component based on metadata (new method)
   */
  generateComponentPropsFromMetadata(metadata: Metadata, value: any, onChange: (value: any) => void, onValidation?: (errors: string[]) => void): StandardFormProps & Record<string, any> {
    const uiComponent = metadata.uiComponent || 'ThisText';
    const mapping = this.getComponentMapping(uiComponent);

    // Base props that all components should have
    const baseProps: StandardFormProps = {
      id: metadata.id || '',
      label: metadata.displayLabel || metadata.name || '',
      value: value,
      onChange: onChange,
      onValidation: onValidation,
      required: metadata.isRequired || undefined,
      placeholder: metadata.placeholder || undefined,
      helpText: metadata.helpText || undefined,
      disabled: false,
      readOnly: metadata.isReadonly || false
    };

    // Create a FormFieldConfig-like object from metadata for the propsMapper
    const fieldConfigFromMetadata: FormFieldConfig = {
      id: metadata.id || '',
      name: metadata.name || '',
      displayLabel: metadata.displayLabel || metadata.name || '',
      helpText: metadata.helpText || null,
      fieldOrder: metadata.fieldOrder || null,
      isVisible: metadata.isVisible ?? true,
      isReadonly: metadata.isReadonly ?? false,
      validationPattern: metadata.validationPattern || null,
      minLength: metadata.minLength || null,
      maxLength: metadata.maxLength || null,
      minValue: metadata.minValue || null,
      maxValue: metadata.maxValue || null,
      isRequired: metadata.isRequired ?? false,
      placeholder: metadata.placeholder || null,
      defaultOptions: metadata.defaultOptions || null,
      maxSelections: metadata.maxSelections || null,
      allowedFileTypes: metadata.allowedFileTypes ? metadata.allowedFileTypes.split(',') : null,
      maxFileSize: metadata.maxFileSizeBytes || null,
      errorMessage: metadata.errorMessage || null,
      requiredErrorMessage: metadata.requiredErrorMessage || null,
      patternErrorMessage: metadata.patternErrorMessage || null,
      minLengthErrorMessage: metadata.minLengthErrorMessage || null,
      maxLengthErrorMessage: metadata.maxLengthErrorMessage || null,
      minValueErrorMessage: metadata.minValueErrorMessage || null,
      maxValueErrorMessage: metadata.maxValueErrorMessage || null,
      fileTypeErrorMessage: metadata.fileTypeErrorMessage || null,
      maxFileSizeBytes: metadata.maxFileSizeBytes || null,
      inputType: metadata.inputType || null,
      inputMask: metadata.inputMask || null,
      allowsMultiple: metadata.allowsMultiple || null,
      allowsCustomOptions: metadata.allowsCustomOptions || null,
      overrideLookupType: metadata.overrideLookupType || null,
      overrideMasterContextId: metadata.overrideMasterContextId || null,
      overrideTenantContextId: metadata.overrideTenantContextId || null,
      overrideObjectLookupId: metadata.overrideObjectLookupId || null,
      uiComponent: uiComponent,
      category: metadata.category || null,
      decimalPlaces: metadata.decimalPlaces || null,
      stepValue: metadata.stepValue || null,
      htmlAttributes: metadata.htmlAttributes || null,
      metadataLinkId: metadata.metadataLinkId || undefined,
      isUnique: metadata.isUnique || undefined,
      isVisibleInList: metadata.isVisibleInList ?? true,
      isVisibleInEdit: metadata.isVisibleInEdit ?? true,
      isVisibleInCreate: metadata.isVisibleInCreate ?? true,
      isVisibleInView: metadata.isVisibleInView ?? true,
      isCalculated: metadata.isCalculated ?? false
    };

    // Apply component-specific props if mapping exists
    if (mapping?.propsMapper) {
      const componentSpecificProps = mapping.propsMapper(fieldConfigFromMetadata);
      return { ...baseProps, ...componentSpecificProps };
    }

    // Apply default props if available
    if (mapping?.defaultProps) {
      return { ...baseProps, ...mapping.defaultProps };
    }

    return baseProps;
  }

  /**
   * Check if a uiComponent is supported
   */
  isComponentSupported(uiComponent: string): boolean {
    // Check both exact match and lowercase
    return uiComponent in this.componentMap;
  }

  /**
   * Get list of all supported uiComponent values
   */
  getSupportedComponents(): string[] {
    return Object.keys(this.componentMap);
  }

  /**
   * Create custom validator function from field configuration
   */
  private createCustomValidator(config: FormFieldConfig) {
    return (value: any): string | null => {
      // Required validation
      if (config.isRequired && (!value || (typeof value === 'string' && value.trim() === ''))) {
        return config.requiredErrorMessage || `${config.displayLabel} is required`;
      }

      // Only validate format if value exists
      if (value && typeof value === 'string') {
        // Pattern validation
        if (config.validationPattern) {
          const regex = new RegExp(config.validationPattern);
          if (!regex.test(value)) {
            return config.patternErrorMessage || 'Invalid format';
          }
        }

        // Length validations
        if (config.minLength && value.length < config.minLength) {
          return config.minLengthErrorMessage || `Minimum ${config.minLength} characters required`;
        }

        if (config.maxLength && value.length > config.maxLength) {
          return config.maxLengthErrorMessage || `Maximum ${config.maxLength} characters allowed`;
        }
      }

      return null; // No validation errors
    };
  }

  /**
   * Fallback component for unsupported uiComponent values
   */
  getFallbackComponent(): React.ComponentType<any> {
    const fallbackMapping = this.componentMap[this.fallbackComponentName];
    return fallbackMapping?.component || ThisText; // Default to text input for unsupported components
  }

  /**
   * Fallback component mapping for unsupported uiComponent values
   */
  getFallbackComponentMapping(): ComponentMapping {
    const fallbackMapping = this.componentMap[this.fallbackComponentName];
    return fallbackMapping || this.componentMap['ThisText']; // Default to ThisText mapping for unsupported components
  }

  /**
   * Set the fallback component name for unsupported uiComponent values
   */
  setFallbackComponent(componentName: string): void {
    if (this.componentMap[componentName]) {
      this.fallbackComponentName = componentName;
    } else {
      console.warn(`Component "${componentName}" not found in registry. Keeping current fallback: ${this.fallbackComponentName}`);
    }
  }

  /**
   * Get the current fallback component name
   */
  getFallbackComponentName(): string {
    return this.fallbackComponentName;
  }
}

// Create and export singleton instance
export const componentMappingService = ComponentMappingService.getInstance();
