# API Routing Guide

This document outlines the dual API configuration system and which operations use which API endpoint.

## API Configuration Overview

The application uses a dual API system with two distinct endpoints:

### Primary API (Port 7222)
**Base URL**: `https://localhost:7222/api`  
**Environment Variable**: `VITE_API_BaseUrl`  
**Purpose**: Standard data operations and retrieval

### Secondary API (Port 7243)
**Base URL**: `https://localhost:7243/api`  
**Environment Variable**: `VITE_API_Leadrat_BaseUrl`  
**Purpose**: Tenant management and subscription creation workflow

## API Routing by Operation

### Primary API Operations (Port 7222)

#### Subscription Data Operations
- `GET /subscriptions` - Retrieve subscription data
- `GET /subscriptions/{id}` - Get specific subscription details
- `GET /subscriptions/stats` - Get subscription statistics
- `GET /subscriptions/search` - Search subscriptions

#### Template Operations
- `GET /templates` - Retrieve template data
- `GET /templates/{id}` - Get specific template details
- `GET /templates/live` - Get live templates for dropdowns
- `GET /templates/stage/{stage}` - Get templates by stage
- `POST /templates` - Create new template
- `PUT /templates` - Update existing template
- `DELETE /templates/{id}` - Delete template

#### General Data Operations
- `GET /comprehensive-entity` - General entity operations
- `GET /objectvalues` - Object value operations
- `GET /fieldmappings` - Field mapping operations

### Secondary API Operations (Port 7243)

#### Tenant Management
- `GET /tenants` - **Load tenant dropdown options** (specifically for Add Subscription modal)
- `POST /tenants/upsert` - Create or update tenants
- `GET /tenants/{id}` - Get specific tenant details
- `POST /tenants/{id}/validate` - Validate tenant connection
- `POST /tenants` - Create new tenant
- `PUT /tenants` - Update existing tenant

#### Subscription Creation Workflow
- `POST /comprehensive-entity/create-product-structure` - Create product structure from template
- `POST /subscriptions` - Create new subscription (part of creation workflow)

## Service Implementation

### DualApiService
The `DualApiService` class provides a centralized way to route API calls to the correct endpoint:

```typescript
import { dualApiService, apiOperations } from '../services/api/dualApiService';

// Primary API call
const subscriptions = await dualApiService.primary('/subscriptions', 'GET');

// Secondary API call
const tenants = await dualApiService.secondary('/tenants', 'GET');

// Using predefined operations
const tenantOptions = await apiOperations.tenants.getAll();
const subscriptionStats = await apiOperations.subscriptions.getStats();
```

### Service-Specific Routing

#### SubscriptionApiService
- **API Used**: Primary (Port 7222)
- **Purpose**: Data retrieval and statistics
- **Methods**: `getSubscriptions()`, `getSubscriptionById()`, `getSubscriptionStats()`

#### TenantApiService
- **API Used**: Secondary (Port 7243)
- **Purpose**: Tenant management and dropdown loading
- **Key Method**: `getTenantDropdownOptionsForSubscription()` - specifically uses Secondary API
- **Methods**: `getTenants()`, `createTenant()`, `updateTenant()`

#### TemplateApiService
- **API Used**: Primary (Port 7222)
- **Purpose**: Template management and data operations
- **Methods**: `getTemplates()`, `getLiveTemplates()`, `createTemplate()`

#### SubscriptionCreationService
- **API Used**: Secondary (Port 7243)
- **Purpose**: Complete subscription creation workflow
- **Methods**: `createSubscriptionWorkflow()`, `createTenant()`, `createProductStructure()`

## Component Usage Examples

### Add Subscription Modal
```typescript
// This specifically uses Secondary API (Port 7243) for tenant loading
const { loadDropdownOptions } = useTenants();

useEffect(() => {
  // Calls getTenantDropdownOptionsForSubscription() which uses Secondary API
  loadDropdownOptions();
}, []);
```

### Subscription Table
```typescript
// This uses Primary API (Port 7222) for data retrieval
const { loadSubscriptions } = useSubscriptions();

useEffect(() => {
  // Calls getSubscriptions() which uses Primary API
  loadSubscriptions();
}, []);
```

### Template Management
```typescript
// This uses Primary API (Port 7222) for template operations
const { loadTemplates, loadLiveTemplates } = useTemplates();

useEffect(() => {
  // Both calls use Primary API
  loadTemplates();
  loadLiveTemplates();
}, []);
```

## Environment Configuration

### .env File
```bash
# Primary API (Port 7222) - Standard data operations
VITE_API_BaseUrl=https://localhost:7222/api

# Secondary API (Port 7243) - Tenant management operations
VITE_API_Leadrat_BaseUrl=https://localhost:7243/api
```

### Configuration Files
- `src/config/environment.ts` - Environment variable mapping
- `src/config/apiConfig.ts` - API endpoint configuration
- `src/services/api/dualApiService.ts` - Dual API service implementation

## Backward Compatibility

The system maintains backward compatibility with existing API calls through:

1. **Legacy helper functions** in `apiConfig.ts`
2. **Service factory pattern** for dependency injection
3. **Gradual migration path** for existing components

## Testing API Routing

To verify which API endpoint is being used:

1. **Check browser network tab** - URLs will show the correct port (7222 or 7243)
2. **Enable service logging** - Services log which API they're using
3. **Use Redux DevTools** - Actions show API routing information

## Troubleshooting

### Common Issues

1. **Tenant dropdown not loading in Add Subscription modal**
   - Verify Secondary API (Port 7243) is running
   - Check `VITE_API_Leadrat_BaseUrl` environment variable
   - Ensure `getTenantDropdownOptionsForSubscription()` is being called

2. **Subscription data not loading**
   - Verify Primary API (Port 7222) is running
   - Check `VITE_API_BaseUrl` environment variable
   - Ensure subscription service is using Primary API

3. **CORS errors**
   - Verify both API endpoints have CORS configured
   - Check that both ports are accessible from the frontend

### Debug Commands

```typescript
// Check current API configuration
console.log('API Config:', API_CONFIG);

// Check environment variables
console.log('Primary API:', environment.apiBaseUrl);
console.log('Secondary API:', environment.apiLeadratBaseUrl);

// Test API connectivity
await dualApiService.primary('/health', 'GET');
await dualApiService.secondary('/health', 'GET');
```

## Future Considerations

1. **API Versioning**: Consider adding version headers for API evolution
2. **Load Balancing**: May need to distribute load across multiple instances
3. **Caching**: Implement caching strategies for frequently accessed data
4. **Monitoring**: Add API performance monitoring and alerting
5. **Security**: Implement proper authentication and authorization for both APIs
