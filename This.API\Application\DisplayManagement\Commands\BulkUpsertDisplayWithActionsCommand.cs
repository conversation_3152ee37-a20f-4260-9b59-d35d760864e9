using Application.DisplayManagement.DTOs;
using MediatR;
using Shared.Common.Response;
using System.ComponentModel.DataAnnotations;

namespace Application.DisplayManagement.Commands;

/// <summary>
/// Bulk upsert Display with Actions command - hierarchical structure
/// </summary>
public class BulkUpsertDisplayWithActionsCommand : IRequest<Result<BulkDisplayWithActionsResponseDto>>
{
    /// <summary>
    /// List of displays with their associated actions to upsert
    /// </summary>
    [Required]
    [MinLength(1, ErrorMessage = "At least one display must be provided")]
    public List<DisplayWithActionsDto> Displays { get; set; } = new List<DisplayWithActionsDto>();
}
