using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetTenantContextLookup;

/// <summary>
/// Query to get tenant context lookup data for dropdowns
/// </summary>
public class GetTenantContextLookupQuery : IRequest<Result<List<TenantContextLookupDto>>>
{
    /// <summary>
    /// Category filter
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Search term for name
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Whether to include inactive tenant contexts
    /// </summary>
    public bool IncludeInactive { get; set; } = false;
}
