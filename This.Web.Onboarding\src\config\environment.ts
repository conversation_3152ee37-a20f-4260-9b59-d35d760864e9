/**
 * Environment Configuration
 *
 * This module provides a centralized way to access environment variables
 * with type safety, validation, and fallback values.
 *
 * Environment files (.env.dev, .env.qa, .env.prd) are pre-configured
 * for each environment. Use `npm run env:copy <env>` to set up the
 * appropriate environment configuration.
 */

// Environment variable interface for type safety
interface EnvironmentConfig {
  // API Configuration
  API_BASE_URL: string;
  LRB_BASE_URL: string;

  // Application Configuration
  APP_NAME: string;
  APP_VERSION: string;
  APP_ENVIRONMENT: 'development' | 'staging' | 'production';

  // API Endpoints
  TENANT_API_ENDPOINT: string;
  USERS_API_ENDPOINT: string;
  ROLES_API_ENDPOINT: string;

  // Feature Flags
  ENABLE_DEBUG: boolean;
  ENABLE_ANALYTICS: boolean;
  ENABLE_HOT_RELOAD: boolean;
  ENABLE_MOCK_DATA: boolean;
  ENABLE_ERROR_BOUNDARY: boolean;
  ENABLE_PERFORMANCE_MONITORING: boolean;
  ENABLE_STRICT_MODE: boolean;
  ENABLE_CORS_VALIDATION: boolean;
  ENABLE_CSP: boolean;
  ENABLE_COMPRESSION: boolean;
  ENABLE_CACHING: boolean;

  // Logging
  LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';

  // Optional Configuration
  SENTRY_DSN?: string;
  GOOGLE_ANALYTICS_ID?: string;
  CDN_URL?: string;
  API_RATE_LIMIT: number;
  API_TIMEOUT: number;
}

// Helper function to parse boolean environment variables
function parseBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
}

// Helper function to parse number environment variables
function parseNumber(value: string | undefined, defaultValue: number): number {
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

// Helper function to validate required environment variables
function getRequiredEnvVar(key: string, fallback?: string): string {
  const value = import.meta.env[key] || fallback;
  if (!value) {
    console.error(`❌ Missing required environment variable: ${key}`);
    throw new Error(`Missing required environment variable: ${key}`);
  }
  return value;
}

// Helper function to get optional environment variables
function getOptionalEnvVar(key: string, fallback?: string): string | undefined {
  return import.meta.env[key] || fallback;
}

// Create environment configuration object
const createEnvironmentConfig = (): EnvironmentConfig => {
  // Determine environment
  const mode = import.meta.env.MODE || 'development';
  const appEnv = import.meta.env.VITE_APP_ENVIRONMENT || mode;

  // Validate environment
  if (!['development', 'staging', 'production'].includes(appEnv)) {
    console.warn(`⚠️ Unknown environment: ${appEnv}, defaulting to development`);
  }

  return {
    // API Configuration
    API_BASE_URL: getRequiredEnvVar('VITE_API_BASE_URL', 'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net/'),
    LRB_BASE_URL: getRequiredEnvVar('VITE_LRB_BASE_URL', 'https://qa-lrb-webapi.leadrat.info/'),

    // Application Configuration
    APP_NAME: getRequiredEnvVar('VITE_APP_NAME', 'Product Builder App'),
    APP_VERSION: getRequiredEnvVar('VITE_APP_VERSION', '0.1.0'),
    APP_ENVIRONMENT: appEnv as 'development' | 'staging' | 'production',

    // API Endpoints
    TENANT_API_ENDPOINT: getRequiredEnvVar('VITE_TENANT_API_ENDPOINT', '/api/tenants/verify'),
    USERS_API_ENDPOINT: getRequiredEnvVar('VITE_USERS_API_ENDPOINT', '/api/users/forproducts'),
    ROLES_API_ENDPOINT: getRequiredEnvVar('VITE_ROLES_API_ENDPOINT', '/api/roles'),

    // Feature Flags
    ENABLE_DEBUG: parseBoolean(import.meta.env.VITE_ENABLE_DEBUG, appEnv === 'development'),
    ENABLE_ANALYTICS: parseBoolean(import.meta.env.VITE_ENABLE_ANALYTICS, appEnv !== 'development'),
    ENABLE_HOT_RELOAD: parseBoolean(import.meta.env.VITE_ENABLE_HOT_RELOAD, appEnv === 'development'),
    ENABLE_MOCK_DATA: parseBoolean(import.meta.env.VITE_ENABLE_MOCK_DATA, false),
    ENABLE_ERROR_BOUNDARY: parseBoolean(import.meta.env.VITE_ENABLE_ERROR_BOUNDARY, true),
    ENABLE_PERFORMANCE_MONITORING: parseBoolean(import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING, appEnv !== 'development'),
    ENABLE_STRICT_MODE: parseBoolean(import.meta.env.VITE_ENABLE_STRICT_MODE, true),
    ENABLE_CORS_VALIDATION: parseBoolean(import.meta.env.VITE_ENABLE_CORS_VALIDATION, true),
    ENABLE_CSP: parseBoolean(import.meta.env.VITE_ENABLE_CSP, appEnv === 'production'),
    ENABLE_COMPRESSION: parseBoolean(import.meta.env.VITE_ENABLE_COMPRESSION, appEnv === 'production'),
    ENABLE_CACHING: parseBoolean(import.meta.env.VITE_ENABLE_CACHING, appEnv !== 'development'),

    // Logging
    LOG_LEVEL: (import.meta.env.VITE_LOG_LEVEL as 'debug' | 'info' | 'warn' | 'error') ||
               (appEnv === 'development' ? 'debug' : appEnv === 'production' ? 'error' : 'warn'),

    // Optional Configuration
    SENTRY_DSN: getOptionalEnvVar('VITE_SENTRY_DSN'),
    GOOGLE_ANALYTICS_ID: getOptionalEnvVar('VITE_GOOGLE_ANALYTICS_ID'),
    CDN_URL: getOptionalEnvVar('VITE_CDN_URL'),
    API_RATE_LIMIT: parseNumber(import.meta.env.VITE_API_RATE_LIMIT, 1000),
    API_TIMEOUT: parseNumber(import.meta.env.VITE_API_TIMEOUT, 30000),
  };
};

// Create and export the configuration
export const env = createEnvironmentConfig();

// Export utility functions for runtime environment checks
export const isDevelopment = () => env.APP_ENVIRONMENT === 'development';
export const isStaging = () => env.APP_ENVIRONMENT === 'staging';
export const isProduction = () => env.APP_ENVIRONMENT === 'production';

// Export configuration validation function
export const validateEnvironment = (): boolean => {
  try {
    // Check required URLs
    new URL(env.API_BASE_URL);
    new URL(env.LRB_BASE_URL);

    // Log environment info in development
    if (isDevelopment() && env.ENABLE_DEBUG) {
      console.log('🌍 Environment Configuration:', {
        environment: env.APP_ENVIRONMENT,
        apiUrl: env.API_BASE_URL,
        version: env.APP_VERSION,
        debug: env.ENABLE_DEBUG,
      });
    }

    return true;
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    return false;
  }
};

// Validate environment on module load
validateEnvironment();

// Default export
export default env;
