using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetBulkTenantContextWithLookups;

/// <summary>
/// Handler for GetBulkTenantContextWithLookupsQuery
/// </summary>
public class GetBulkTenantContextWithLookupsQueryHandler : IRequestHandler<GetBulkTenantContextWithLookupsQuery, Result<BulkTenantContextWithLookupsDto>>
{
    private readonly IRepository<TenantContext> _tenantContextRepository;
    private readonly IRepository<TenantLookup> _tenantLookupRepository;
    private readonly ILogger<GetBulkTenantContextWithLookupsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetBulkTenantContextWithLookupsQueryHandler(
        IRepository<TenantContext> tenantContextRepository,
        IRepository<TenantLookup> tenantLookupRepository,
        ILogger<GetBulkTenantContextWithLookupsQueryHandler> logger)
    {
        _tenantContextRepository = tenantContextRepository;
        _tenantLookupRepository = tenantLookupRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<BulkTenantContextWithLookupsDto>> Handle(GetBulkTenantContextWithLookupsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting bulk tenant contexts with lookups for {Count} TenantContextIds, IncludeInactiveLookups: {IncludeInactiveLookups}",
                request.TenantContextIds.Count, request.IncludeInactiveLookups);

            if (!request.TenantContextIds.Any())
            {
                return Result<BulkTenantContextWithLookupsDto>.Failure("No tenant context IDs provided");
            }

            var result = new BulkTenantContextWithLookupsDto();

            // Get all tenant contexts by IDs - tenant filtering is handled automatically by the repository
            var tenantContexts = await _tenantContextRepository.ListAsync(
                new TenantContextsByIdsSpec(request.TenantContextIds), 
                cancellationToken);

            // Track which tenant contexts were found
            var foundTenantContextIds = tenantContexts.Select(tc => tc.Id).ToList();
            var notFoundTenantContextIds = request.TenantContextIds.Except(foundTenantContextIds).ToList();
            var deletedTenantContextIds = tenantContexts.Where(tc => tc.IsDeleted).Select(tc => tc.Id).ToList();

            // Filter out deleted tenant contexts for processing
            var activeTenantContexts = tenantContexts.Where(tc => !tc.IsDeleted).ToList();

            // Get all tenant lookups for the active tenant contexts in one query
            if (activeTenantContexts.Any())
            {
                var activeTenantContextIds = activeTenantContexts.Select(tc => tc.Id).ToList();
                var tenantLookupSpec = new TenantLookupsByTenantContextIdsSpec(
                    tenantContextIds: activeTenantContextIds,
                    includeInactive: request.IncludeInactiveLookups);

                var allTenantLookups = await _tenantLookupRepository.ListAsync(tenantLookupSpec, cancellationToken);

                // Group tenant lookups by tenant context ID for efficient processing
                var tenantLookupsByTenantContextId = allTenantLookups.GroupBy(tl => tl.TenantContextId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // Build the result for each active tenant context
                foreach (var tenantContext in activeTenantContexts)
                {
                    var tenantContextDto = tenantContext.Adapt<TenantContextDto>();
                    var tenantContextLookups = tenantLookupsByTenantContextId.GetValueOrDefault(tenantContext.Id, new List<TenantLookup>());
                    var tenantLookupDtos = tenantContextLookups.Adapt<List<TenantLookupDto>>();

                    var tenantContextWithLookups = new TenantContextWithLookupsDto
                    {
                        TenantContext = tenantContextDto,
                        TenantLookups = tenantLookupDtos
                    };

                    result.TenantContextsWithLookups.Add(tenantContextWithLookups);
                }
            }

            // Set tracking information
            result.NotFoundTenantContextIds = notFoundTenantContextIds;
            result.DeletedTenantContextIds = deletedTenantContextIds;

            _logger.LogInformation("Successfully retrieved {FoundCount} tenant contexts with {TotalLookups} total tenant lookups. " +
                "NotFound: {NotFoundCount}, Deleted: {DeletedCount}",
                result.TotalTenantContextsCount, result.TotalTenantLookupsCount, 
                notFoundTenantContextIds.Count, deletedTenantContextIds.Count);

            return Result<BulkTenantContextWithLookupsDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving bulk tenant contexts with lookups for {Count} TenantContextIds", 
                request.TenantContextIds.Count);
            return Result<BulkTenantContextWithLookupsDto>.Failure($"Error retrieving bulk tenant contexts with lookups: {ex.Message}");
        }
    }
}
