/**
 * Base API Service
 * Abstract base class for all API services with common functionality
 */

import { HttpClient } from './httpClient';
import { environment } from '../config/environment';
import type {
  ApiResponse,
  ApiError,
  LoadingState,
  PaginationParams,
  PaginatedResponse,
  ApiResult,
  SearchParams,
  SortParams
} from './types';

// Re-export types for convenience
export type {
  ApiResponse,
  ApiError,
  LoadingState,
  PaginationParams,
  PaginatedResponse,
  ApiResult,
  SearchParams,
  SortParams
};

// Base API Service Class
export abstract class BaseApiService {
  protected httpClient: HttpClient;
  protected serviceName: string;

  constructor(httpClient: HttpClient, serviceName: string) {
    this.httpClient = httpClient;
    this.serviceName = serviceName;
  }

  /**
   * Get loading state for a specific operation
   */
  public getLoadingState(operation: string): LoadingState {
    const key = `${this.serviceName}_${operation}`;
    return this.httpClient.getLoadingState(key);
  }

  /**
   * Clear loading state for a specific operation
   */
  public clearLoadingState(operation: string): void {
    const key = `${this.serviceName}_${operation}`;
    this.httpClient.clearLoadingState(key);
  }

  /**
   * Log service operation if logging is enabled
   */
  protected log(operation: string, data: any): void {
    if (environment.enableApiLogging) {
      console.log(`[${this.serviceName}] ${operation}:`, data);
    }
  }

  /**
   * Handle API errors consistently
   */
  protected handleError(operation: string, error: any): never {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const apiError: ApiError = {
      message: `${this.serviceName} ${operation} failed: ${errorMessage}`,
      originalError: error instanceof Error ? error : undefined,
    };

    this.log(`${operation}_error`, apiError);
    throw apiError;
  }

  /**
   * Build query parameters for API requests
   */
  protected buildQueryParams(params: Record<string, any>): URLSearchParams {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value));
      }
    });
    
    return queryParams;
  }

  /**
   * Build endpoint URL with query parameters
   */
  protected buildEndpoint(baseEndpoint: string, params: Record<string, any> = {}): string {
    const queryParams = this.buildQueryParams(params);
    const queryString = queryParams.toString();
    return queryString ? `${baseEndpoint}?${queryString}` : baseEndpoint;
  }

  /**
   * Make GET request with error handling
   */
  protected async get<T>(endpoint: string, params: Record<string, any> = {}, tenant?: string): Promise<T> {
    try {
      const url = this.buildEndpoint(endpoint, params);
      this.log('GET', { url, params, tenant });
      
      const response = await this.httpClient.get<T>(url, { tenant });
      return response.data;
    } catch (error) {
      this.handleError('GET', error);
    }
  }

  /**
   * Make POST request with error handling
   */
  protected async post<T>(endpoint: string, data?: any, tenant?: string): Promise<T> {
    try {
      this.log('POST', { endpoint, data, tenant });
      
      const response = await this.httpClient.post<T>(endpoint, data, { tenant });
      return response.data;
    } catch (error) {
      this.handleError('POST', error);
    }
  }

  /**
   * Make PUT request with error handling
   */
  protected async put<T>(endpoint: string, data?: any, tenant?: string): Promise<T> {
    try {
      this.log('PUT', { endpoint, data, tenant });
      
      const response = await this.httpClient.put<T>(endpoint, data, { tenant });
      return response.data;
    } catch (error) {
      this.handleError('PUT', error);
    }
  }

  /**
   * Make DELETE request with error handling
   */
  protected async delete<T>(endpoint: string, tenant?: string): Promise<T> {
    try {
      this.log('DELETE', { endpoint, tenant });
      
      const response = await this.httpClient.delete<T>(endpoint, { tenant });
      return response.data;
    } catch (error) {
      this.handleError('DELETE', error);
    }
  }

  /**
   * Make PATCH request with error handling
   */
  protected async patch<T>(endpoint: string, data?: any, tenant?: string): Promise<T> {
    try {
      this.log('PATCH', { endpoint, data, tenant });
      
      const response = await this.httpClient.patch<T>(endpoint, data, { tenant });
      return response.data;
    } catch (error) {
      this.handleError('PATCH', error);
    }
  }

  /**
   * Transform API result to handle different response formats
   */
  protected transformApiResult<T>(response: any): T {
    // Handle ApiResult format
    if (response && typeof response === 'object' && 'succeeded' in response) {
      if (!response.succeeded) {
        throw new Error(response.message || 'API request failed');
      }
      return response.data;
    }
    
    // Handle direct data response
    return response;
  }

  /**
   * Validate pagination parameters
   */
  protected validatePaginationParams(params: PaginationParams): PaginationParams {
    return {
      pageNumber: Math.max(1, params.pageNumber || 1),
      pageSize: Math.min(100, Math.max(1, params.pageSize || 10)),
    };
  }

  /**
   * Build standard pagination parameters
   */
  protected buildPaginationParams(params: PaginationParams): Record<string, any> {
    const validated = this.validatePaginationParams(params);
    return {
      pageNumber: validated.pageNumber,
      pageSize: validated.pageSize,
    };
  }

  /**
   * Build standard search parameters
   */
  protected buildSearchParams(params: SearchParams): Record<string, any> {
    const searchParams: Record<string, any> = {};
    
    if (params.searchTerm) {
      searchParams.searchTerm = params.searchTerm;
    }
    
    if (params.isActive !== undefined) {
      searchParams.isActive = params.isActive;
    }
    
    if (params.includeDeleted !== undefined) {
      searchParams.includeDeleted = params.includeDeleted;
    }
    
    return searchParams;
  }

  /**
   * Build standard sort parameters
   */
  protected buildSortParams(params: SortParams): Record<string, any> {
    const sortParams: Record<string, any> = {};
    
    if (params.orderBy) {
      sortParams.orderBy = params.orderBy;
    }
    
    if (params.orderDirection) {
      sortParams.orderDirection = params.orderDirection;
    }
    
    return sortParams;
  }
}
