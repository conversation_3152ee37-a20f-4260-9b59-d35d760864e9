using Application.DataTypes.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.DataTypes.Queries;

/// <summary>
/// Get DataType by ID query handler
/// </summary>
public class GetDataTypeByIdQueryHandler : IRequestHandler<GetDataTypeByIdQuery, Result<DataTypeDto>>
{
    private readonly IRepository<DataType> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetDataTypeByIdQueryHandler(IRepository<DataType> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<DataTypeDto>> Handle(GetDataTypeByIdQuery request, CancellationToken cancellationToken)
    {
        var dataType = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (dataType == null)
        {
            return Result<DataTypeDto>.Failure($"DataType with ID '{request.Id}' not found.");
        }

        var dto = new DataTypeDto
        {
            Id = dataType.Id,
            Name = dataType.Name,
            DisplayName = dataType.DisplayName,
            Category = dataType.Category,
            UiComponent = dataType.UiComponent,
            ValidationPattern = dataType.ValidationPattern,
            MinLength = dataType.MinLength,
            MaxLength = dataType.MaxLength,
            MinValue = dataType.MinValue,
            MaxValue = dataType.MaxValue,
            DecimalPlaces = dataType.DecimalPlaces,
            StepValue = dataType.StepValue,
            IsRequired = dataType.IsRequired,
            InputType = dataType.InputType,
            InputMask = dataType.InputMask,
            Placeholder = dataType.Placeholder,
            HtmlAttributes = dataType.HtmlAttributes,
            DefaultOptions = dataType.DefaultOptions,
            AllowsMultiple = dataType.AllowsMultiple,
            AllowsCustomOptions = dataType.AllowsCustomOptions,
            MaxSelections = dataType.MaxSelections,
            AllowedFileTypes = dataType.AllowedFileTypes,
            MaxFileSizeBytes = dataType.MaxFileSizeBytes,
            RequiredErrorMessage = dataType.RequiredErrorMessage,
            PatternErrorMessage = dataType.PatternErrorMessage,
            MinLengthErrorMessage = dataType.MinLengthErrorMessage,
            MaxLengthErrorMessage = dataType.MaxLengthErrorMessage,
            MinValueErrorMessage = dataType.MinValueErrorMessage,
            MaxValueErrorMessage = dataType.MaxValueErrorMessage,
            FileTypeErrorMessage = dataType.FileTypeErrorMessage,
            FileSizeErrorMessage = dataType.FileSizeErrorMessage,
            IsActive = dataType.IsActive,
            CreatedAt = dataType.CreatedAt,
            CreatedBy = dataType.CreatedBy ?? Guid.Empty,
            ModifiedAt = dataType.ModifiedAt,
            ModifiedBy = dataType.ModifiedBy
        };

        return Result<DataTypeDto>.Success(dto);
    }
}
