namespace Infrastructure.Database.Repositories;

/// <summary>
/// DTOs for mapping raw SQL query results to strongly-typed objects
/// Used by HierarchicalEntityDataRepository for Dapper mapping
/// </summary>

/// <summary>
/// DTO for mapping product data from SQL queries - ENHANCED with ALL fields
/// </summary>
public class ProductDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? Version { get; set; }
    public bool IsActive { get; set; }

    // Additional Product fields
    public bool IsUserImported { get; set; }
    public bool IsRoleAssigned { get; set; }
    public string? ApiKey { get; set; }
    public bool IsOnboardCompleted { get; set; }
    public string? ApplicationUrl { get; set; }
    public string? Icon { get; set; }

    // Audit fields
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }
    public bool IsDeleted { get; set; }
}

/// <summary>
/// DTO for mapping feature data from SQL queries
/// </summary>
public class FeatureDto
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// DTO for mapping object data from SQL queries - ENHANCED with ALL fields
/// </summary>
public class ObjectDto
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; } // Changed from FeatureId to ProductId
    public Guid? ParentObjectId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public string? Icon { get; set; }

    // Audit fields
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }
    public bool IsDeleted { get; set; }
}

/// <summary>
/// DTO for mapping product metadata - BASED ON ACTUAL ENTITIES (ProductMetadata + Metadata + DataType)
/// </summary>
public class ProductMetadataDto
{
    // ProductMetadata Link Properties (from ProductMetadata entity)
    public Guid ProductId { get; set; }
    public Guid ProductMetadataId { get; set; }
    public bool IsUnique { get; set; }
    public bool MetadataLinkIsActive { get; set; }
    public bool ShouldVisibleInList { get; set; }
    public bool ShouldVisibleInEdit { get; set; }
    public bool ShouldVisibleInCreate { get; set; }
    public bool ShouldVisibleInView { get; set; }
    public bool IsCalculate { get; set; }

    // Metadata Properties (UPDATED from Metadata entity)
    public Guid MetadataId { get; set; }
    public string? Name { get; set; }
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool IsVisible { get; set; }
    public bool IsReadonly { get; set; }
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool? IsRequired { get; set; }
    public string? Placeholder { get; set; }
    public string? DefaultOptions { get; set; }
    public int? MaxSelections { get; set; }
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSizeBytes { get; set; }
    public string? ErrorMessage { get; set; }

    // Additional Error Messages from Metadata entity
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }
    public string? FileSizeErrorMessage { get; set; }

    // Additional UI Properties from Metadata entity
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }

    // Lookup Override Properties from Metadata entity
    public Guid? ContextId { get; set; }
    public Guid? TenantContextId { get; set; }
    public Guid? ObjectLookupId { get; set; }
    public bool? IsEditable { get; set; }
    public bool? IsSearchable { get; set; }
    public bool? IsSortable { get; set; }
    public int? SortOrder { get; set; }
    public string? DisplayFormat { get; set; }



    // DataType Properties (EXACTLY from DataType entity)
    public Guid DataTypeId { get; set; }
    public string? DataTypeName { get; set; }
    public string? DataTypeDisplayName { get; set; }
    public string? DataTypeCategory { get; set; }
    public string? DataTypeUiComponent { get; set; }

    // Validation Rules
    public string? DataTypeValidationPattern { get; set; }
    public int? DataTypeMinLength { get; set; }
    public int? DataTypeMaxLength { get; set; }
    public decimal? DataTypeMinValue { get; set; }
    public decimal? DataTypeMaxValue { get; set; }
    public int? DataTypeDecimalPlaces { get; set; }
    public decimal? DataTypeStepValue { get; set; }
    public bool DataTypeIsRequired { get; set; }

    // UI Properties
    public string? DataTypeInputType { get; set; }
    public string? DataTypeInputMask { get; set; }
    public string? DataTypePlaceholder { get; set; }
    public string? DataTypeHtmlAttributes { get; set; }

    // Choice Options
    public string? DataTypeDefaultOptions { get; set; }
    public bool DataTypeAllowsMultiple { get; set; }
    public bool DataTypeAllowsCustomOptions { get; set; }
    public int? DataTypeMaxSelections { get; set; }

    // File/Media Properties
    public string? DataTypeAllowedFileTypes { get; set; }
    public long? DataTypeMaxFileSizeBytes { get; set; }

    // Error Messages
    public string? DataTypeRequiredErrorMessage { get; set; }
    public string? DataTypePatternErrorMessage { get; set; }
    public string? DataTypeMinLengthErrorMessage { get; set; }
    public string? DataTypeMaxLengthErrorMessage { get; set; }
    public string? DataTypeMinValueErrorMessage { get; set; }
    public string? DataTypeMaxValueErrorMessage { get; set; }
    public string? DataTypeFileTypeErrorMessage { get; set; }
    public string? DataTypeFileSizeErrorMessage { get; set; }
    public string? DataTypeErrorMessage { get; set; }
    public string? DataTypeDisplayLabel { get; set; }
    public string? DataTypeHelpText { get; set; }
    public int? DataTypeFieldOrder { get; set; }
    public bool? DataTypeIsVisible { get; set; }
    public bool? DataTypeIsReadonly { get; set; }



    // Status
    public bool DataTypeIsActive { get; set; }
}

/// <summary>
/// DTO for mapping unified product metadata (consolidates Metadata + DataType with priority logic)
/// </summary>
public class UnifiedProductMetadataDto
{
    // ProductMetadata Link Properties
    public Guid ProductId { get; set; }
    public Guid ProductMetadataId { get; set; }
    public bool IsUnique { get; set; }
    public bool MetadataLinkIsActive { get; set; }
    public bool IsVisibleInList { get; set; }
    public bool IsVisibleInEdit { get; set; }
    public bool IsVisibleInCreate { get; set; }
    public bool IsVisibleInView { get; set; }
    public bool IsCalculated { get; set; }

    // Core Identity
    public Guid MetadataId { get; set; }
    public Guid DataTypeId { get; set; }
    public string? Name { get; set; }

    // DataType-Only Fields
    public string? DataTypeName { get; set; }
    public string? DataTypeDisplayName { get; set; }
    public string? Category { get; set; }
    public string? UiComponent { get; set; }
    public int? DecimalPlaces { get; set; }
    public decimal? StepValue { get; set; }
    public string? HtmlAttributes { get; set; }
    public string? FileSizeErrorMessage { get; set; }

    // Metadata-Only Fields
    public Guid? ContextId { get; set; }
    public Guid? TenantContextId { get; set; }
    public Guid? ObjectLookupId { get; set; }
    public bool? IsEditable { get; set; }
    public bool? IsSearchable { get; set; }
    public bool? IsSortable { get; set; }
    public int? SortOrder { get; set; }
    public string? DisplayFormat { get; set; }

    // CONSOLIDATED FIELDS (already prioritized in SQL)
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool? IsRequired { get; set; }
    public string? Placeholder { get; set; }
    public int? MaxSelections { get; set; }
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSizeBytes { get; set; }
    public string? ErrorMessage { get; set; }
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }
    public string? DefaultOptions { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
}

/// <summary>
/// DTO for mapping unified object metadata (consolidates Metadata + DataType with priority logic)
/// </summary>
public class UnifiedObjectMetadataDto
{
    // ObjectMetadata Link Properties
    public Guid ObjectId { get; set; }
    public Guid ObjectMetadataId { get; set; }
    public bool IsUnique { get; set; }
    public bool MetadataLinkIsActive { get; set; }
    public bool IsVisibleInList { get; set; }
    public bool IsVisibleInEdit { get; set; }
    public bool IsVisibleInCreate { get; set; }
    public bool IsVisibleInView { get; set; }
    public bool IsCalculated { get; set; }

    // Core Identity
    public Guid MetadataId { get; set; }
    public Guid DataTypeId { get; set; }
    public string? Name { get; set; }

    // DataType-Only Fields
    public string? DataTypeName { get; set; }
    public string? DataTypeDisplayName { get; set; }
    public string? Category { get; set; }
    public string? UiComponent { get; set; }
    public int? DecimalPlaces { get; set; }
    public decimal? StepValue { get; set; }
    public string? HtmlAttributes { get; set; }
    public string? FileSizeErrorMessage { get; set; }

    // Metadata-Only Fields
    public Guid? ContextId { get; set; }
    public Guid? TenantContextId { get; set; }
    public Guid? ObjectLookupId { get; set; }
    public bool? IsEditable { get; set; }
    public bool? IsSearchable { get; set; }
    public bool? IsSortable { get; set; }
    public int? SortOrder { get; set; }
    public string? DisplayFormat { get; set; }

    // CONSOLIDATED FIELDS (already prioritized in SQL)
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool? IsRequired { get; set; }
    public string? Placeholder { get; set; }
    public int? MaxSelections { get; set; }
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSizeBytes { get; set; }
    public string? ErrorMessage { get; set; }
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }
    public string? DefaultOptions { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
}

/// <summary>
/// DTO for mapping object metadata with COMPLETE datatype information from SQL queries - ALL PROPERTIES
/// </summary>
public class ObjectMetadataDto
{
    // Object Metadata Link Properties
    public Guid ObjectId { get; set; }
    public Guid ObjectMetadataId { get; set; }
    public bool IsUnique { get; set; }
    public bool MetadataLinkIsActive { get; set; }
    public bool? ShouldVisibleInList { get; set; }
    public bool? ShouldVisibleInEdit { get; set; }
    public bool? ShouldVisibleInCreate { get; set; }
    public bool? ShouldVisibleInView { get; set; }
    public bool? IsCalculate { get; set; }

    // Metadata Properties - ALL FIELDS (UPDATED)
    public Guid MetadataId { get; set; }
    public string? Name { get; set; }
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool? IsRequired { get; set; }
    public string? Placeholder { get; set; }
    public string? DefaultOptions { get; set; }
    public int? MaxSelections { get; set; }
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSizeBytes { get; set; }
    public string? ErrorMessage { get; set; }

    // Additional Error Messages from Metadata entity
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }
    public string? FileSizeErrorMessage { get; set; }

    // Additional UI Properties from Metadata entity
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }

    // Lookup Override Properties from Metadata entity
    public Guid? ContextId { get; set; }
    public Guid? TenantContextId { get; set; }
    public Guid? ObjectLookupId { get; set; }
    public bool? IsEditable { get; set; }
    public bool? IsSearchable { get; set; }
    public bool? IsSortable { get; set; }
    public int? SortOrder { get; set; }
    public string? DisplayFormat { get; set; }



    // DataType Properties - ALL FIELDS
    public Guid DataTypeId { get; set; }
    public string? DataTypeName { get; set; }
    public string? DataTypeDisplayName { get; set; }
    public string? DataTypeCategory { get; set; }
    public string? DataTypeUiComponent { get; set; }
    public string? DataTypeValidationPattern { get; set; }
    public int? DataTypeMinLength { get; set; }
    public int? DataTypeMaxLength { get; set; }
    public decimal? DataTypeMinValue { get; set; }
    public decimal? DataTypeMaxValue { get; set; }
    public int? DataTypeDecimalPlaces { get; set; }
    public decimal? DataTypeStepValue { get; set; }
    public bool? DataTypeIsRequired { get; set; }
    public string? DataTypeInputType { get; set; }
    public string? DataTypeInputMask { get; set; }
    public string? DataTypePlaceholder { get; set; }
    public string? DataTypeHtmlAttributes { get; set; }
    public string? DataTypeDefaultOptions { get; set; }
    public bool? DataTypeAllowsMultiple { get; set; }
    public bool? DataTypeAllowsCustomOptions { get; set; }
    public int? DataTypeMaxSelections { get; set; }
    public string? DataTypeAllowedFileTypes { get; set; }
    public long? DataTypeMaxFileSizeBytes { get; set; }
    public string? DataTypeRequiredErrorMessage { get; set; }
    public string? DataTypePatternErrorMessage { get; set; }
    public string? DataTypeMinLengthErrorMessage { get; set; }
    public string? DataTypeMaxLengthErrorMessage { get; set; }
    public string? DataTypeMinValueErrorMessage { get; set; }
    public string? DataTypeMaxValueErrorMessage { get; set; }
    public string? DataTypeFileTypeErrorMessage { get; set; }
    public string? DataTypeFileSizeErrorMessage { get; set; }
    public string? DataTypeErrorMessage { get; set; }
    public string? DataTypeDisplayLabel { get; set; }
    public string? DataTypeHelpText { get; set; }
    public int? DataTypeFieldOrder { get; set; }
    public bool? DataTypeIsVisible { get; set; }
    public bool? DataTypeIsReadonly { get; set; }

    public bool? DataTypeIsActive { get; set; }
}

/// <summary>
/// DTO for mapping object display action data from SQL queries
/// </summary>
public class ObjectDisplayActionMappingDto
{
    // Object Reference
    public Guid ObjectId { get; set; }

    // Display Properties
    public Guid DisplayId { get; set; }
    public string? DisplayName { get; set; }
    public string? DisplayDescription { get; set; }
    public string? DisplayDisplayName { get; set; }
    public bool DisplayIsDefault { get; set; }
    public string? DisplayRouteTemplate { get; set; }
    public string? DisplayIcon { get; set; }
    public int DisplaySortOrder { get; set; }
    public bool DisplayIsActive { get; set; }
    public DateTime DisplayCreatedAt { get; set; }
    public DateTime? DisplayModifiedAt { get; set; }
    public Guid? DisplayCreatedBy { get; set; }
    public Guid? DisplayModifiedBy { get; set; }
    public bool DisplayIsDeleted { get; set; }

    // Action Properties
    public Guid ActionId { get; set; }
    public string? ActionName { get; set; }
    public string? ActionDescription { get; set; }
    public string? ActionEndpointTemplate { get; set; }
    public string? ActionNavigationTarget { get; set; }
    public string? ActionIcon { get; set; }
    public string? ActionButtonStyle { get; set; }
    public string? ActionConfirmationMessage { get; set; }
    public string? ActionSuccessMessage { get; set; }
    public string? ActionErrorMessage { get; set; }
    public bool ActionIsActive { get; set; }
    public DateTime ActionCreatedAt { get; set; }
    public DateTime? ActionModifiedAt { get; set; }
    public Guid? ActionCreatedBy { get; set; }
    public Guid? ActionModifiedBy { get; set; }
    public bool ActionIsDeleted { get; set; }

    // DisplayAction Properties
    public Guid DisplayActionId { get; set; }
    public string? DisplayActionAccessLevel { get; set; }
    public bool DisplayActionIsDefault { get; set; }
    public int DisplayActionSortOrder { get; set; }
    public bool DisplayActionIsVisibleInToolbar { get; set; }
    public bool DisplayActionIsVisibleInContextMenu { get; set; }
    public bool DisplayActionIsVisibleInRowActions { get; set; }
    public bool DisplayActionIsActive { get; set; }
    public DateTime DisplayActionCreatedAt { get; set; }
    public DateTime? DisplayActionModifiedAt { get; set; }
    public Guid? DisplayActionCreatedBy { get; set; }
    public Guid? DisplayActionModifiedBy { get; set; }
    public bool DisplayActionIsDeleted { get; set; }
}
