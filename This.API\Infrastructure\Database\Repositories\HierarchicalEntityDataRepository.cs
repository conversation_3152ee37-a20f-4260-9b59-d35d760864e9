using Abstraction.Database.Repositories;
using Application.ComprehensiveEntityData.DTOs;
using Domain.MultiTenancy;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Npgsql;
using System.Data;
using Dapper;
using Finbuckle.MultiTenant;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Infrastructure.Database.Repositories;

/// <summary>
/// CLEAN: Repository for hierarchical entity data - ONLY DATA ACCESS
/// NO business logic, NO hardcoded SQL
/// Uses separated query files and proper DTOs based on actual entities
/// </summary>
public class HierarchicalEntityDataRepository : IHierarchicalEntityDataRepository
{
    private readonly string _connectionString;
    private readonly ILogger<HierarchicalEntityDataRepository> _logger;
    private readonly ITenantInfo _tenantInfo;

    public HierarchicalEntityDataRepository(
        IOptions<DatabaseSettings> options,
        ILogger<HierarchicalEntityDataRepository> logger,
        ITenantInfo tenantInfo)
    {
        _connectionString = options.Value.ConnectionString;
        _logger = logger;
        _tenantInfo = tenantInfo;
    }

    /// <summary>
    /// Create a new database connection
    /// </summary>
    private IDbConnection CreateConnection() => new NpgsqlConnection(_connectionString);

    /// <summary>
    /// Get the current tenant ID from the injected tenant info
    /// </summary>
    private string GetCurrentTenantId()
    {
        // Get tenant ID from the injected ITenantInfo
        if (_tenantInfo != null && !string.IsNullOrEmpty(_tenantInfo.Id))
        {
            return _tenantInfo.Id;
        }

        // Fallback to default tenant
        return "default";
    }

    /// <summary>
    /// Get hierarchical entity data with proper parent-child nesting
    /// </summary>
    public async Task<object> GetHierarchicalEntityDataAsync(
        Guid? productId = null,
        Guid? featureId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        // Use the TRUE unified approach that returns the actual unified response
        return await GetHierarchicalEntityDataTrueUnifiedAsync(
            productId, featureId, searchTerm, isActive,
            onlyVisibleMetadata, onlyActiveMetadata,
            pageNumber, pageSize, cancellationToken);
    }

    /// <summary>
    /// Get hierarchical entity data with TRUE UNIFIED metadata response (no backward compatibility conversion)
    /// This method returns the actual unified response format with consolidated metadata
    /// </summary>
    public async Task<object> GetHierarchicalEntityDataTrueUnifiedAsync(
        Guid? productId = null,
        Guid? featureId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Fetching hierarchical entity data with TRUE UNIFIED metadata response");

            using var connection = CreateConnection();
            connection.Open();

            // STEP 1: Get filtered products with bulk query
            var products = await GetProductsAsync(connection, productId, isActive, searchTerm, pageNumber, pageSize);

            if (!products.Any())
            {
                return new UnifiedHierarchicalEntityDataResponseDto();
            }

            var productIds = products.Select(p => p.Id).ToArray();

            // STEP 2: Get ALL objects for these products directly (Features removed)
            var objects = await GetObjectsAsync(connection, productIds, null, isActive, searchTerm);

            // STEP 3: Get UNIFIED metadata for all entities (2 optimized bulk queries with consolidation)
            _logger.LogInformation("Fetching UNIFIED metadata for {ProductCount} products", productIds.Length);

            var productMetadata = await GetProductMetadataUnifiedAsync(connection, productIds, onlyVisibleMetadata, onlyActiveMetadata);
            _logger.LogInformation("Found {ProductMetadataCount} unified product metadata entries", productMetadata.Sum(x => x.Value.Count));

            var objectIds = objects.Select(o => o.Id).ToArray();
            var objectMetadata = await GetObjectMetadataUnifiedAsync(connection, objectIds, onlyVisibleMetadata, onlyActiveMetadata);
            _logger.LogInformation("Found {ObjectMetadataCount} unified object metadata entries for {ObjectCount} objects", objectMetadata.Sum(x => x.Value.Count), objectIds.Length);

            // STEP 5: Get Display/Action data for all objects
            var objectDisplays = await GetObjectDisplaysAsync(connection, objectIds);
            _logger.LogInformation("Found {DisplayCount} displays for {ObjectCount} objects", objectDisplays.Sum(x => x.Value.Count), objectIds.Length);

            // STEP 4: Build hierarchical structure with TRUE UNIFIED metadata (no conversion)
            var response = new UnifiedHierarchicalEntityDataResponseDto();

            foreach (var product in products)
            {
                var productDto = new UnifiedProductHierarchicalDto
                {
                    Id = product.Id,
                    Name = product.Name,
                    Description = product.Description,
                    Version = product.Version,
                    IsActive = product.IsActive,
                    IsUserImported = product.IsUserImported,
                    IsRoleAssigned = product.IsRoleAssigned,
                    ApiKey = product.ApiKey,
                    IsOnboardCompleted = product.IsOnboardCompleted,
                    ApplicationUrl = product.ApplicationUrl,
                    Icon = product.Icon,
                    CreatedAt = product.CreatedAt,
                    CreatedBy = product.CreatedBy,
                    ModifiedAt = product.ModifiedAt,
                    ModifiedBy = product.ModifiedBy,
                    IsDeleted = product.IsDeleted,
                    // Use ACTUAL unified metadata (no conversion to legacy format)
                    Metadata = ConvertToUnifiedResponseMetadata(productMetadata.ContainsKey(product.Id) ? productMetadata[product.Id] : new List<UnifiedMetadataWithValuesDto>()),
                };

                // Features removed - add objects directly to product
                productDto.RootObjects = new List<UnifiedObjectHierarchicalDto>();

                // Get root objects (ParentObjectId = null) for this product directly
                var rootObjects = objects.Where(o => o.ProductId == product.Id && o.ParentObjectId == null).ToList();

                foreach (var rootObject in rootObjects)
                {
                    var rootObjectDto = BuildObjectHierarchyTrueUnified(rootObject, objects.ToList(), objectMetadata, objectDisplays, 0, rootObject.Name);
                    productDto.RootObjects.Add(rootObjectDto);
                }

                response.Products.Add(productDto);
            }

            _logger.LogInformation("Successfully built hierarchical structure with TRUE UNIFIED metadata for {ProductCount} products", products.Count());
            return (object)response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching hierarchical entity data with true unified metadata");
            throw;
        }
    }

    /// <summary>
    /// Get hierarchical entity data with UNIFIED metadata consolidation (Metadata priority over DataType)
    /// This method implements the new priority-based field consolidation logic
    /// </summary>
    public async Task<object> GetHierarchicalEntityDataUnifiedAsync(
        Guid? productId = null,
        Guid? featureId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Fetching hierarchical entity data with UNIFIED metadata consolidation");

            using var connection = CreateConnection();
            connection.Open();

            // STEP 1: Get filtered products with bulk query
            var products = await GetProductsAsync(connection, productId, isActive, searchTerm, pageNumber, pageSize);

            if (!products.Any())
            {
                return new HierarchicalEntityDataResponseDto();
            }

            var productIds = products.Select(p => p.Id).ToArray();

            // STEP 2: Get ALL objects for these products directly (Features removed)
            var objects = await GetObjectsAsync(connection, productIds, null, isActive, searchTerm);

            // STEP 3: Get UNIFIED metadata for all entities (2 optimized bulk queries with consolidation)
            _logger.LogInformation("Fetching UNIFIED metadata for {ProductCount} products", productIds.Length);

            var productMetadata = await GetProductMetadataUnifiedAsync(connection, productIds, onlyVisibleMetadata, onlyActiveMetadata);
            _logger.LogInformation("Found {ProductMetadataCount} unified product metadata entries", productMetadata.Sum(x => x.Value.Count));

            var objectIds = objects.Select(o => o.Id).ToArray();
            var objectMetadata = await GetObjectMetadataUnifiedAsync(connection, objectIds, onlyVisibleMetadata, onlyActiveMetadata);
            _logger.LogInformation("Found {ObjectMetadataCount} unified object metadata entries for {ObjectCount} objects", objectMetadata.Sum(x => x.Value.Count), objectIds.Length);

            // STEP 4: Build hierarchical structure with UNIFIED metadata
            var response = new HierarchicalEntityDataResponseDto();

            foreach (var product in products)
            {
                var productDto = new ProductHierarchicalDto
                {
                    Id = product.Id,
                    Name = product.Name,
                    Description = product.Description,
                    Version = product.Version,
                    IsActive = product.IsActive,
                    IsUserImported = product.IsUserImported,
                    IsRoleAssigned = product.IsRoleAssigned,
                    ApiKey = product.ApiKey,
                    IsOnboardCompleted = product.IsOnboardCompleted,
                    ApplicationUrl = product.ApplicationUrl,
                    Icon = product.Icon,
                    CreatedAt = product.CreatedAt,
                    CreatedBy = product.CreatedBy,
                    ModifiedAt = product.ModifiedAt,
                    ModifiedBy = product.ModifiedBy,
                    IsDeleted = product.IsDeleted,
                    // Convert unified metadata to standard format for backward compatibility
                    Metadata = ConvertUnifiedToStandardMetadata(productMetadata.ContainsKey(product.Id) ? productMetadata[product.Id] : new List<UnifiedMetadataWithValuesDto>()),
                };

                // Features removed - add objects directly to product
                productDto.RootObjects = new List<ObjectHierarchicalDto>();

                // Get root objects (ParentObjectId = null) for this product directly
                var rootObjects = objects.Where(o => o.ProductId == product.Id && o.ParentObjectId == null).ToList();

                foreach (var rootObject in rootObjects)
                {
                    var rootObjectDto = BuildObjectHierarchyUnified(rootObject, objects.ToList(), objectMetadata, 0, rootObject.Name);
                    productDto.RootObjects.Add(rootObjectDto);
                }

                response.Products.Add(productDto);
            }

            _logger.LogInformation("Successfully built hierarchical structure with UNIFIED metadata for {ProductCount} products", products.Count());
            return (object)response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching hierarchical entity data with unified metadata");
            throw;
        }
    }

    /// <summary>
    /// Get hierarchical entity data with LEGACY separate metadata approach (for backward compatibility)
    /// </summary>
    public async Task<object> GetHierarchicalEntityDataLegacyAsync(
        Guid? productId = null,
        Guid? featureId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Fetching hierarchical entity data with optimized queries");

            using var connection = CreateConnection();
            connection.Open();

            // STEP 1: Get filtered products with bulk query
            var products = await GetProductsAsync(connection, productId, isActive, searchTerm, pageNumber, pageSize);

            if (!products.Any())
            {
                return new HierarchicalEntityDataResponseDto();
            }

            var productIds = products.Select(p => p.Id).ToArray();

            // STEP 2: Get ALL objects for these products directly (Features removed)
            var objects = await GetObjectsAsync(connection, productIds, null, isActive, searchTerm);

            // STEP 3: Get metadata for all entities (2 optimized bulk queries) - NO VALUES (Features removed)
            _logger.LogInformation("Fetching metadata for {ProductCount} products", productIds.Length);

            var productMetadata = await GetProductMetadataAsync(connection, productIds, onlyVisibleMetadata, onlyActiveMetadata);
            _logger.LogInformation("Found {ProductMetadataCount} product metadata entries", productMetadata.Sum(x => x.Value.Count));

            var objectIds = objects.Select(o => o.Id).ToArray();
            var objectMetadata = await GetObjectMetadataAsync(connection, objectIds, onlyVisibleMetadata, onlyActiveMetadata);
            _logger.LogInformation("Found {ObjectMetadataCount} object metadata entries for {ObjectCount} objects", objectMetadata.Sum(x => x.Value.Count), objectIds.Length);

            // STEP 5: Build hierarchical structure with metadata
            var response = new HierarchicalEntityDataResponseDto();

            foreach (var product in products)
            {
                var productDto = new ProductHierarchicalDto
                {
                    Id = product.Id,
                    Name = product.Name,
                    Description = product.Description,
                    Version = product.Version,
                    IsActive = product.IsActive,
                    IsUserImported = product.IsUserImported,
                    IsRoleAssigned = product.IsRoleAssigned,
                    ApiKey = product.ApiKey,
                    IsOnboardCompleted = product.IsOnboardCompleted,
                    ApplicationUrl = product.ApplicationUrl,
                    Icon = product.Icon,
                    CreatedAt = product.CreatedAt,
                    CreatedBy = product.CreatedBy,
                    ModifiedAt = product.ModifiedAt,
                    ModifiedBy = product.ModifiedBy,
                    IsDeleted = product.IsDeleted,
                    Metadata = productMetadata.ContainsKey(product.Id) ? productMetadata[product.Id] : new List<MetadataWithValuesDto>(),
                };

                // Features removed - add objects directly to product
                productDto.RootObjects = new List<ObjectHierarchicalDto>();

                // Get root objects (ParentObjectId = null) for this product directly
                var rootObjects = objects.Where(o => o.ProductId == product.Id && o.ParentObjectId == null).ToList();

                foreach (var rootObject in rootObjects)
                {
                    var rootObjectDto = BuildObjectHierarchy(rootObject, objects.ToList(), objectMetadata, 0, rootObject.Name);
                    productDto.RootObjects.Add(rootObjectDto);
                }

                response.Products.Add(productDto);
            }

            _logger.LogInformation("Successfully built hierarchical structure for {ProductCount} products", products.Count());
            return (object)response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching hierarchical entity data");
            throw;
        }
    }

    /// <summary>
    /// Build object hierarchy recursively with proper parent-child nesting and metadata
    /// </summary>
    private ObjectHierarchicalDto BuildObjectHierarchy(
        ObjectDto obj,
        List<ObjectDto> allObjects,
        Dictionary<Guid, List<MetadataWithValuesDto>> objectMetadata,
        int hierarchyLevel,
        string hierarchyPath)
    {
        var objectDto = new ObjectHierarchicalDto
        {
            Id = obj.Id,
            Name = obj.Name,
            Description = obj.Description,
            ParentObjectId = obj.ParentObjectId,
            IsActive = obj.IsActive,
            Icon = obj.Icon,
            CreatedAt = obj.CreatedAt,
            CreatedBy = obj.CreatedBy,
            ModifiedAt = obj.ModifiedAt,
            ModifiedBy = obj.ModifiedBy,
            IsDeleted = obj.IsDeleted,
            HierarchyLevel = hierarchyLevel,
            HierarchyPath = hierarchyPath,
            Metadata = objectMetadata.ContainsKey(obj.Id) ? objectMetadata[obj.Id] : new List<MetadataWithValuesDto>(),
            ChildObjects = new List<ObjectHierarchicalDto>()
        };

        // Get child objects for this object
        var childObjects = allObjects.Where(o => o.ParentObjectId == obj.Id).ToList();

        foreach (var childObject in childObjects)
        {
            var childDto = BuildObjectHierarchy(
                childObject,
                allObjects,
                objectMetadata,
                hierarchyLevel + 1,
                $"{hierarchyPath} > {childObject.Name}");

            objectDto.ChildObjects.Add(childDto);
        }

        return objectDto;
    }

    /// <summary>
    /// Build object hierarchy recursively with UNIFIED metadata and proper parent-child nesting
    /// </summary>
    private ObjectHierarchicalDto BuildObjectHierarchyUnified(
        ObjectDto obj,
        List<ObjectDto> allObjects,
        Dictionary<Guid, List<UnifiedMetadataWithValuesDto>> objectMetadata,
        int hierarchyLevel,
        string hierarchyPath)
    {
        var objectDto = new ObjectHierarchicalDto
        {
            Id = obj.Id,
            Name = obj.Name,
            Description = obj.Description,
            ParentObjectId = obj.ParentObjectId,
            IsActive = obj.IsActive,
            Icon = obj.Icon,
            CreatedAt = obj.CreatedAt,
            CreatedBy = obj.CreatedBy,
            ModifiedAt = obj.ModifiedAt,
            ModifiedBy = obj.ModifiedBy,
            IsDeleted = obj.IsDeleted,
            HierarchyLevel = hierarchyLevel,
            HierarchyPath = hierarchyPath,
            // Convert unified metadata to standard format for backward compatibility
            Metadata = ConvertUnifiedToStandardMetadata(objectMetadata.ContainsKey(obj.Id) ? objectMetadata[obj.Id] : new List<UnifiedMetadataWithValuesDto>()),
            ChildObjects = new List<ObjectHierarchicalDto>()
        };

        // Get child objects for this object
        var childObjects = allObjects.Where(o => o.ParentObjectId == obj.Id).ToList();

        foreach (var childObject in childObjects)
        {
            var childDto = BuildObjectHierarchyUnified(
                childObject,
                allObjects,
                objectMetadata,
                hierarchyLevel + 1,
                $"{hierarchyPath} > {childObject.Name}");

            objectDto.ChildObjects.Add(childDto);
        }

        return objectDto;
    }

    /// <summary>
    /// Convert unified metadata to standard metadata format for backward compatibility
    /// This ensures existing API consumers continue to work without changes
    /// </summary>
    private List<MetadataWithValuesDto> ConvertUnifiedToStandardMetadata(List<UnifiedMetadataWithValuesDto> unifiedMetadata)
    {
        var result = new List<MetadataWithValuesDto>();

        foreach (var unified in unifiedMetadata)
        {
            var standard = new MetadataWithValuesDto
            {
                Metadata = new MetadataInfoDto
                {
                    Id = unified.Metadata.MetadataId,
                    Name = unified.Metadata.Name ?? "",
                    DisplayLabel = unified.Metadata.DisplayLabel,
                    HelpText = unified.Metadata.HelpText,
                    FieldOrder = unified.Metadata.FieldOrder,
                    IsVisible = unified.Metadata.IsVisible,
                    IsReadonly = unified.Metadata.IsReadonly,
                    ValidationPattern = unified.Metadata.ValidationPattern,
                    MinLength = unified.Metadata.MinLength,
                    MaxLength = unified.Metadata.MaxLength,
                    MinValue = unified.Metadata.MinValue,
                    MaxValue = unified.Metadata.MaxValue,
                    IsRequired = unified.Metadata.IsRequired,
                    Placeholder = unified.Metadata.Placeholder,
                    DefaultOptions = unified.Metadata.DefaultOptions,
                    MaxSelections = unified.Metadata.MaxSelections,
                    AllowedFileTypes = unified.Metadata.AllowedFileTypes,
                    MaxFileSizeBytes = unified.Metadata.MaxFileSizeBytes,
                    ErrorMessage = unified.Metadata.ErrorMessage,
                    RequiredErrorMessage = unified.Metadata.RequiredErrorMessage,
                    PatternErrorMessage = unified.Metadata.PatternErrorMessage,
                    MinLengthErrorMessage = unified.Metadata.MinLengthErrorMessage,
                    MaxLengthErrorMessage = unified.Metadata.MaxLengthErrorMessage,
                    MinValueErrorMessage = unified.Metadata.MinValueErrorMessage,
                    MaxValueErrorMessage = unified.Metadata.MaxValueErrorMessage,
                    FileTypeErrorMessage = unified.Metadata.FileTypeErrorMessage,
                    InputType = unified.Metadata.InputType,
                    InputMask = unified.Metadata.InputMask,
                    AllowsMultiple = unified.Metadata.AllowsMultiple,
                    AllowsCustomOptions = unified.Metadata.AllowsCustomOptions,
                    ContextId = unified.Metadata.ContextId,
                    TenantContextId = unified.Metadata.TenantContextId,
                    ObjectLookupId = unified.Metadata.ObjectLookupId,

                    // Create a simplified DataType object with the consolidated values
                    DataType = new DataTypeInfoDto
                    {
                        Id = unified.Metadata.DataTypeId,
                        Name = unified.Metadata.DataTypeName ?? "",
                        DisplayName = unified.Metadata.DataTypeDisplayName ?? "",
                        Category = unified.Metadata.Category,
                        UiComponent = unified.Metadata.UiComponent,
                        DecimalPlaces = unified.Metadata.DecimalPlaces,
                        StepValue = unified.Metadata.StepValue,
                        HtmlAttributes = unified.Metadata.HtmlAttributes,
                        FileSizeErrorMessage = unified.Metadata.FileSizeErrorMessage,

                        // For DataType, show the consolidated values (which are already prioritized)
                        ValidationPattern = unified.Metadata.ValidationPattern,
                        MinLength = unified.Metadata.MinLength,
                        MaxLength = unified.Metadata.MaxLength,
                        MinValue = unified.Metadata.MinValue,
                        MaxValue = unified.Metadata.MaxValue,
                        IsRequired = unified.Metadata.IsRequired,
                        Placeholder = unified.Metadata.Placeholder,
                        DefaultOptions = unified.Metadata.DefaultOptions,
                        MaxSelections = unified.Metadata.MaxSelections,
                        AllowedFileTypes = unified.Metadata.AllowedFileTypes,
                        MaxFileSizeBytes = unified.Metadata.MaxFileSizeBytes,
                        ErrorMessage = unified.Metadata.ErrorMessage,
                        RequiredErrorMessage = unified.Metadata.RequiredErrorMessage,
                        PatternErrorMessage = unified.Metadata.PatternErrorMessage,
                        MinLengthErrorMessage = unified.Metadata.MinLengthErrorMessage,
                        MaxLengthErrorMessage = unified.Metadata.MaxLengthErrorMessage,
                        MinValueErrorMessage = unified.Metadata.MinValueErrorMessage,
                        MaxValueErrorMessage = unified.Metadata.MaxValueErrorMessage,
                        FileTypeErrorMessage = unified.Metadata.FileTypeErrorMessage,
                        InputType = unified.Metadata.InputType,
                        InputMask = unified.Metadata.InputMask,
                        AllowsMultiple = unified.Metadata.AllowsMultiple,
                        AllowsCustomOptions = unified.Metadata.AllowsCustomOptions,
                        DisplayLabel = unified.Metadata.DisplayLabel,
                        HelpText = unified.Metadata.HelpText,
                        FieldOrder = unified.Metadata.FieldOrder,
                        IsVisible = unified.Metadata.IsVisible,
                        IsReadonly = unified.Metadata.IsReadonly,
                        IsActive = true // Default to active
                    },

                    MetadataLink = new MetadataLinkInfoDto
                    {
                        ObjectMetaDataId = unified.Metadata.MetadataLinkId,
                        IsUnique = unified.Metadata.IsUnique ?? false,
                        IsActive = unified.Metadata.MetadataLinkIsActive ?? false,
                        ShouldVisibleInList = unified.Metadata.IsVisibleInList ?? false,
                        ShouldVisibleInEdit = unified.Metadata.IsVisibleInEdit ?? false,
                        ShouldVisibleInCreate = unified.Metadata.IsVisibleInCreate ?? false,
                        ShouldVisibleInView = unified.Metadata.IsVisibleInView ?? false,
                        IsCalculate = unified.Metadata.IsCalculated ?? false
                    }
                },
                Values = unified.Values
            };

            result.Add(standard);
        }

        return result;
    }

    /// <summary>
    /// Get product metadata with datatypes only (no values) using separated query and DTOs
    /// </summary>
    private async Task<Dictionary<Guid, List<MetadataWithValuesDto>>> GetProductMetadataAsync(
        IDbConnection connection,
        Guid[] productIds,
        bool onlyVisible,
        bool onlyActive)
    {
        var sql = HierarchicalEntityDataQueries.AddMetadataFilters(
            HierarchicalEntityDataQueries.GetProductMetadataWithDataTypesSql,
            onlyVisible,
            onlyActive,
            "pm");

        var tenantId = GetCurrentTenantId();
        var metadataData = await connection.QueryAsync<ProductMetadataDto>(sql, new { ProductIds = productIds, TenantId = tenantId });
        _logger.LogInformation("Product metadata query returned {Count} rows for {ProductCount} products", metadataData.Count(), productIds.Length);

        var result = new Dictionary<Guid, List<MetadataWithValuesDto>>();

        foreach (var group in metadataData.GroupBy(x => x.ProductId))
        {
            var metadataList = new List<MetadataWithValuesDto>();

            foreach (var item in group)
            {
                try
                {
                    var metadata = new MetadataWithValuesDto
                    {
                        Metadata = new MetadataInfoDto
                        {
                            Id = item.MetadataId,
                            Name = item.Name ?? "",
                            DisplayLabel = item.DisplayLabel ?? "",
                            HelpText = item.HelpText,
                            FieldOrder = item.FieldOrder,
                            IsVisible = item.IsVisible,
                            IsReadonly = item.IsReadonly,
                            ValidationPattern = item.ValidationPattern, // Updated property name
                            MinLength = item.MinLength, // Updated property name
                            MaxLength = item.MaxLength, // Updated property name
                            MinValue = item.MinValue, // Updated property name
                            MaxValue = item.MaxValue, // Updated property name
                            IsRequired = item.IsRequired, // Updated property name
                            Placeholder = item.Placeholder, // Updated property name
                            DefaultOptions = item.DefaultOptions, // Updated property name
                            MaxSelections = item.MaxSelections, // Updated property name
                            AllowedFileTypes = item.AllowedFileTypes, // Updated property name
                            MaxFileSizeBytes = item.MaxFileSizeBytes, // Updated property name
                            ErrorMessage = item.ErrorMessage, // Updated property name
                            RequiredErrorMessage = item.RequiredErrorMessage,
                            PatternErrorMessage = item.PatternErrorMessage,
                            MinLengthErrorMessage = item.MinLengthErrorMessage,
                            MaxLengthErrorMessage = item.MaxLengthErrorMessage,
                            MinValueErrorMessage = item.MinValueErrorMessage,
                            MaxValueErrorMessage = item.MaxValueErrorMessage,
                            FileTypeErrorMessage = item.FileTypeErrorMessage,
                            InputType = item.InputType,
                            InputMask = item.InputMask,
                            AllowsMultiple = item.AllowsMultiple,
                            AllowsCustomOptions = item.AllowsCustomOptions,
                            ContextId = item.ContextId,
                            TenantContextId = item.TenantContextId,
                            ObjectLookupId = item.ObjectLookupId,
                            DataType = new DataTypeInfoDto
                            {
                                Id = item.DataTypeId,
                                Name = item.DataTypeName ?? "",
                                DisplayName = item.DataTypeDisplayName ?? "",
                                Category = item.DataTypeCategory,
                                UiComponent = item.DataTypeUiComponent,
                                ValidationPattern = item.DataTypeValidationPattern,
                                MinLength = item.DataTypeMinLength,
                                MaxLength = item.DataTypeMaxLength,
                                MinValue = item.DataTypeMinValue,
                                MaxValue = item.DataTypeMaxValue,
                                DecimalPlaces = item.DataTypeDecimalPlaces,
                                StepValue = item.DataTypeStepValue,
                                IsRequired = item.DataTypeIsRequired,
                                InputType = item.DataTypeInputType,
                                InputMask = item.DataTypeInputMask,
                                Placeholder = item.DataTypePlaceholder,
                                HtmlAttributes = item.DataTypeHtmlAttributes,
                                DefaultOptions = item.DataTypeDefaultOptions,
                                AllowsMultiple = item.DataTypeAllowsMultiple,
                                AllowsCustomOptions = item.DataTypeAllowsCustomOptions,
                                MaxSelections = item.DataTypeMaxSelections,
                                AllowedFileTypes = item.DataTypeAllowedFileTypes,
                                MaxFileSizeBytes = item.DataTypeMaxFileSizeBytes,
                                RequiredErrorMessage = item.DataTypeRequiredErrorMessage,
                                PatternErrorMessage = item.DataTypePatternErrorMessage,
                                MinLengthErrorMessage = item.DataTypeMinLengthErrorMessage,
                                MaxLengthErrorMessage = item.DataTypeMaxLengthErrorMessage,
                                MinValueErrorMessage = item.DataTypeMinValueErrorMessage,
                                MaxValueErrorMessage = item.DataTypeMaxValueErrorMessage,
                                FileTypeErrorMessage = item.DataTypeFileTypeErrorMessage,
                                FileSizeErrorMessage = item.DataTypeFileSizeErrorMessage,
                                ErrorMessage = item.DataTypeErrorMessage,
                                DisplayLabel = item.DataTypeDisplayLabel,
                                HelpText = item.DataTypeHelpText,
                                FieldOrder = item.DataTypeFieldOrder,
                                IsVisible = item.DataTypeIsVisible,
                                IsReadonly = item.DataTypeIsReadonly,
                                IsActive = item.DataTypeIsActive
                            },
                            MetadataLink = new MetadataLinkInfoDto
                            {
                                ObjectMetaDataId = item.ProductMetadataId,
                                IsUnique = item.IsUnique,
                                IsActive = item.MetadataLinkIsActive,
                                ShouldVisibleInList = item.ShouldVisibleInList,
                                ShouldVisibleInEdit = item.ShouldVisibleInEdit,
                                ShouldVisibleInCreate = item.ShouldVisibleInCreate,
                                ShouldVisibleInView = item.ShouldVisibleInView,
                                IsCalculate = item.IsCalculate
                            }
                        },
                        Values = new List<ValueInfoDto>() // Empty values list
                    };

                    metadataList.Add(metadata);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Error processing product metadata item: {Error}", ex.Message);
                    // Skip this item and continue
                }
            }

            result[group.Key] = metadataList;
        }

        return result;
    }

    /// <summary>
    /// Get object metadata with datatypes only (no values) using separated query and DTOs
    /// </summary>
    private async Task<Dictionary<Guid, List<MetadataWithValuesDto>>> GetObjectMetadataAsync(
        IDbConnection connection,
        Guid[] objectIds,
        bool onlyVisible,
        bool onlyActive)
    {
        var sql = HierarchicalEntityDataQueries.AddMetadataFilters(
            HierarchicalEntityDataQueries.GetObjectMetadataWithDataTypesSql,
            onlyVisible,
            onlyActive,
            "om");

        var tenantId = GetCurrentTenantId();
        var metadataData = await connection.QueryAsync<ObjectMetadataDto>(sql, new { ObjectIds = objectIds, TenantId = tenantId });
        _logger.LogInformation("Object metadata query returned {Count} rows for {ObjectCount} objects", metadataData.Count(), objectIds.Length);

        var result = new Dictionary<Guid, List<MetadataWithValuesDto>>();

        foreach (var group in metadataData.GroupBy(x => x.ObjectId))
        {
            var metadataList = new List<MetadataWithValuesDto>();

            foreach (var item in group)
            {
                try
                {
                    var metadata = new MetadataWithValuesDto
                    {
                        Metadata = new MetadataInfoDto
                        {
                            Id = item.MetadataId,
                            Name = item.Name ?? "",
                            DisplayLabel = item.DisplayLabel ?? "",
                            IsVisible = item.IsVisible ?? false,
                            Placeholder = item.Placeholder,
                            DefaultOptions = item.DefaultOptions,
                            IsRequired = item.IsRequired,
                            FieldOrder = item.FieldOrder,
                            ValidationPattern = item.ValidationPattern,
                            MinLength = item.MinLength,
                            MaxLength = item.MaxLength,
                            MinValue = item.MinValue,
                            MaxValue = item.MaxValue,
                            MaxSelections = item.MaxSelections,
                            AllowedFileTypes = item.AllowedFileTypes,
                            MaxFileSizeBytes = item.MaxFileSizeBytes,
                            ErrorMessage = item.ErrorMessage,
                            RequiredErrorMessage = item.RequiredErrorMessage,
                            PatternErrorMessage = item.PatternErrorMessage,
                            MinLengthErrorMessage = item.MinLengthErrorMessage,
                            MaxLengthErrorMessage = item.MaxLengthErrorMessage,
                            MinValueErrorMessage = item.MinValueErrorMessage,
                            MaxValueErrorMessage = item.MaxValueErrorMessage,
                            FileTypeErrorMessage = item.FileTypeErrorMessage,
                            InputType = item.InputType,
                            InputMask = item.InputMask,
                            AllowsMultiple = item.AllowsMultiple,
                            AllowsCustomOptions = item.AllowsCustomOptions,
                            ContextId = item.ContextId,
                            TenantContextId = item.TenantContextId,
                            ObjectLookupId = item.ObjectLookupId,
                            IsReadonly = item.IsReadonly,
                            HelpText = item.HelpText,
                            DataType = new DataTypeInfoDto
                            {
                                Id = item.DataTypeId,
                                Name = item.DataTypeName ?? "",
                                DisplayName = item.DataTypeDisplayName ?? "",
                                Category = item.DataTypeCategory,
                                UiComponent = item.DataTypeUiComponent,
                                ValidationPattern = item.DataTypeValidationPattern,
                                MinLength = item.DataTypeMinLength,
                                MaxLength = item.DataTypeMaxLength,
                                MinValue = item.DataTypeMinValue,
                                MaxValue = item.DataTypeMaxValue,
                                DecimalPlaces = item.DataTypeDecimalPlaces,
                                StepValue = item.DataTypeStepValue,
                                IsRequired = item.DataTypeIsRequired,
                                InputType = item.DataTypeInputType,
                                InputMask = item.DataTypeInputMask,
                                Placeholder = item.DataTypePlaceholder,
                                HtmlAttributes = item.DataTypeHtmlAttributes,
                                DefaultOptions = item.DataTypeDefaultOptions,
                                AllowsMultiple = item.DataTypeAllowsMultiple,
                                AllowsCustomOptions = item.DataTypeAllowsCustomOptions,
                                MaxSelections = item.DataTypeMaxSelections,
                                AllowedFileTypes = item.DataTypeAllowedFileTypes,
                                MaxFileSizeBytes = item.DataTypeMaxFileSizeBytes,
                                RequiredErrorMessage = item.DataTypeRequiredErrorMessage,
                                PatternErrorMessage = item.DataTypePatternErrorMessage,
                                MinLengthErrorMessage = item.DataTypeMinLengthErrorMessage,
                                MaxLengthErrorMessage = item.DataTypeMaxLengthErrorMessage,
                                MinValueErrorMessage = item.DataTypeMinValueErrorMessage,
                                MaxValueErrorMessage = item.DataTypeMaxValueErrorMessage,
                                FileTypeErrorMessage = item.DataTypeFileTypeErrorMessage,
                                FileSizeErrorMessage = item.DataTypeFileSizeErrorMessage,
                                ErrorMessage = item.DataTypeErrorMessage,
                                DisplayLabel = item.DataTypeDisplayLabel,
                                HelpText = item.DataTypeHelpText,
                                FieldOrder = item.DataTypeFieldOrder,
                                IsVisible = item.DataTypeIsVisible,
                                IsReadonly = item.DataTypeIsReadonly,
                                IsActive = item.DataTypeIsActive
                            },
                            MetadataLink = new MetadataLinkInfoDto
                            {
                                ObjectMetaDataId = item.ObjectMetadataId,
                                IsUnique = item.IsUnique,
                                IsActive = item.MetadataLinkIsActive,
                                ShouldVisibleInList = item.ShouldVisibleInList,
                                ShouldVisibleInEdit = item.ShouldVisibleInEdit,
                                ShouldVisibleInCreate = item.ShouldVisibleInCreate,
                                ShouldVisibleInView = item.ShouldVisibleInView,
                                IsCalculate = item.IsCalculate
                            }
                        },
                        Values = new List<ValueInfoDto>() // Empty values list
                    };

                    metadataList.Add(metadata);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Error processing metadata item: {Error}", ex.Message);
                    // Skip this item and continue
                }
            }

            result[group.Key] = metadataList;
        }

        return result;
    }

    #region Private Data Access Methods

    /// <summary>
    /// Get products with filters using separated query
    /// </summary>
    private async Task<IEnumerable<ProductDto>> GetProductsAsync(
        IDbConnection connection,
        Guid? productId,
        bool? isActive,
        string? searchTerm,
        int pageNumber,
        int pageSize)
    {
        var sql = HierarchicalEntityDataQueries.AddProductFilters(
            hasProductId: productId.HasValue,
            hasIsActive: isActive.HasValue,
            hasSearchTerm: !string.IsNullOrWhiteSpace(searchTerm),
            hasPagination: true);

        var parameters = new DynamicParameters();
        var tenantId = GetCurrentTenantId();
        parameters.Add("TenantId", tenantId);

        if (productId.HasValue)
            parameters.Add("ProductId", productId.Value);

        if (isActive.HasValue)
            parameters.Add("IsActive", isActive.Value);

        if (!string.IsNullOrWhiteSpace(searchTerm))
            parameters.Add("SearchTerm", $"%{searchTerm}%");

        parameters.Add("Offset", (pageNumber - 1) * pageSize);
        parameters.Add("Limit", pageSize);

        return await connection.QueryAsync<ProductDto>(sql, parameters);
    }

    /// <summary>
    /// Get objects for products using separated query (Features removed)
    /// </summary>
    private async Task<IEnumerable<ObjectDto>> GetObjectsAsync(
        IDbConnection connection,
        Guid[] productIds,
        Guid? objectId = null,
        bool? isActive = null,
        string? searchTerm = null)
    {
        var sql = HierarchicalEntityDataQueries.AddObjectFilters(
            hasObjectId: objectId.HasValue,
            hasIsActive: isActive.HasValue,
            hasSearchTerm: !string.IsNullOrWhiteSpace(searchTerm));

        var parameters = new DynamicParameters();
        var tenantId = GetCurrentTenantId();
        parameters.Add("TenantId", tenantId);
        parameters.Add("ProductIds", productIds);

        if (objectId.HasValue)
            parameters.Add("ObjectId", objectId.Value);

        if (isActive.HasValue)
            parameters.Add("IsActive", isActive.Value);

        if (!string.IsNullOrWhiteSpace(searchTerm))
            parameters.Add("SearchTerm", $"%{searchTerm}%");

        return await connection.QueryAsync<ObjectDto>(sql, parameters);
    }

    #endregion

    #region Unified Metadata Methods

    /// <summary>
    /// Get product metadata with unified consolidation (Metadata priority over DataType)
    /// </summary>
    private async Task<Dictionary<Guid, List<UnifiedMetadataWithValuesDto>>> GetProductMetadataUnifiedAsync(
        IDbConnection connection,
        Guid[] productIds,
        bool onlyVisible,
        bool onlyActive)
    {
        var sql = HierarchicalEntityDataQueries.AddMetadataFilters(
            HierarchicalEntityDataQueries.GetProductMetadataUnifiedSql,
            onlyVisible,
            onlyActive,
            "pm");

        var tenantId = GetCurrentTenantId();
        var metadataData = await connection.QueryAsync<UnifiedProductMetadataDto>(sql, new { ProductIds = productIds, TenantId = tenantId });
        _logger.LogInformation("Unified product metadata query returned {Count} rows for {ProductCount} products", metadataData.Count(), productIds.Length);

        var result = new Dictionary<Guid, List<UnifiedMetadataWithValuesDto>>();

        foreach (var group in metadataData.GroupBy(x => x.ProductId))
        {
            var metadataList = new List<UnifiedMetadataWithValuesDto>();

            foreach (var item in group)
            {
                try
                {
                    var metadata = new UnifiedMetadataWithValuesDto
                    {
                        Metadata = new UnifiedMetadataDto
                        {
                            // Core Identity
                            MetadataId = item.MetadataId,
                            DataTypeId = item.DataTypeId,
                            Name = item.Name,

                            // DataType-Only Fields
                            DataTypeName = item.DataTypeName,
                            DataTypeDisplayName = item.DataTypeDisplayName,
                            Category = item.Category,
                            UiComponent = item.UiComponent,
                            DecimalPlaces = item.DecimalPlaces,
                            StepValue = item.StepValue,
                            HtmlAttributes = item.HtmlAttributes,
                            FileSizeErrorMessage = item.FileSizeErrorMessage,

                            // Metadata-Only Fields
                            ContextId = item.ContextId,
                            TenantContextId = item.TenantContextId,
                            ObjectLookupId = item.ObjectLookupId,
                            IsEditable = item.IsEditable,
                            IsSearchable = item.IsSearchable,
                            IsSortable = item.IsSortable,
                            SortOrder = item.SortOrder,
                            DisplayFormat = item.DisplayFormat,

                            // CONSOLIDATED FIELDS (already prioritized in SQL)
                            ValidationPattern = item.ValidationPattern,
                            MinLength = item.MinLength,
                            MaxLength = item.MaxLength,
                            MinValue = item.MinValue,
                            MaxValue = item.MaxValue,
                            IsRequired = item.IsRequired,
                            Placeholder = item.Placeholder,
                            MaxSelections = item.MaxSelections,
                            AllowedFileTypes = item.AllowedFileTypes,
                            MaxFileSizeBytes = item.MaxFileSizeBytes,
                            ErrorMessage = item.ErrorMessage,
                            DisplayLabel = item.DisplayLabel,
                            HelpText = item.HelpText,
                            FieldOrder = item.FieldOrder,
                            IsVisible = item.IsVisible,
                            IsReadonly = item.IsReadonly,
                            RequiredErrorMessage = item.RequiredErrorMessage,
                            PatternErrorMessage = item.PatternErrorMessage,
                            MinLengthErrorMessage = item.MinLengthErrorMessage,
                            MaxLengthErrorMessage = item.MaxLengthErrorMessage,
                            MinValueErrorMessage = item.MinValueErrorMessage,
                            MaxValueErrorMessage = item.MaxValueErrorMessage,
                            FileTypeErrorMessage = item.FileTypeErrorMessage,
                            DefaultOptions = item.DefaultOptions,
                            AllowsMultiple = item.AllowsMultiple,
                            AllowsCustomOptions = item.AllowsCustomOptions,
                            InputType = item.InputType,
                            InputMask = item.InputMask,

                            // Link Information
                            MetadataLinkId = item.ProductMetadataId,
                            IsUnique = item.IsUnique,
                            MetadataLinkIsActive = item.MetadataLinkIsActive,
                            IsVisibleInList = item.IsVisibleInList,
                            IsVisibleInEdit = item.IsVisibleInEdit,
                            IsVisibleInCreate = item.IsVisibleInCreate,
                            IsVisibleInView = item.IsVisibleInView,
                            IsCalculated = item.IsCalculated
                        },
                        Values = new List<ValueInfoDto>() // Empty values list
                    };

                    metadataList.Add(metadata);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Error processing unified product metadata item: {Error}", ex.Message);
                    // Skip this item and continue
                }
            }

            result[group.Key] = metadataList;
        }

        return result;
    }

    /// <summary>
    /// Get object metadata with unified consolidation (Metadata priority over DataType)
    /// </summary>
    private async Task<Dictionary<Guid, List<UnifiedMetadataWithValuesDto>>> GetObjectMetadataUnifiedAsync(
        IDbConnection connection,
        Guid[] objectIds,
        bool onlyVisible,
        bool onlyActive)
    {
        var sql = HierarchicalEntityDataQueries.AddMetadataFilters(
            HierarchicalEntityDataQueries.GetObjectMetadataUnifiedSql,
            onlyVisible,
            onlyActive,
            "om");

        var tenantId = GetCurrentTenantId();
        var metadataData = await connection.QueryAsync<UnifiedObjectMetadataDto>(sql, new { ObjectIds = objectIds, TenantId = tenantId });
        _logger.LogInformation("Unified object metadata query returned {Count} rows for {ObjectCount} objects", metadataData.Count(), objectIds.Length);

        var result = new Dictionary<Guid, List<UnifiedMetadataWithValuesDto>>();

        foreach (var group in metadataData.GroupBy(x => x.ObjectId))
        {
            var metadataList = new List<UnifiedMetadataWithValuesDto>();

            foreach (var item in group)
            {
                try
                {
                    var metadata = new UnifiedMetadataWithValuesDto
                    {
                        Metadata = new UnifiedMetadataDto
                        {
                            // Core Identity
                            MetadataId = item.MetadataId,
                            DataTypeId = item.DataTypeId,
                            Name = item.Name,

                            // DataType-Only Fields
                            DataTypeName = item.DataTypeName,
                            DataTypeDisplayName = item.DataTypeDisplayName,
                            Category = item.Category,
                            UiComponent = item.UiComponent,
                            DecimalPlaces = item.DecimalPlaces,
                            StepValue = item.StepValue,
                            HtmlAttributes = item.HtmlAttributes,
                            FileSizeErrorMessage = item.FileSizeErrorMessage,

                            // Metadata-Only Fields
                            ContextId = item.ContextId,
                            TenantContextId = item.TenantContextId,
                            ObjectLookupId = item.ObjectLookupId,
                            IsEditable = item.IsEditable,
                            IsSearchable = item.IsSearchable,
                            IsSortable = item.IsSortable,
                            SortOrder = item.SortOrder,
                            DisplayFormat = item.DisplayFormat,

                            // CONSOLIDATED FIELDS (already prioritized in SQL)
                            ValidationPattern = item.ValidationPattern,
                            MinLength = item.MinLength,
                            MaxLength = item.MaxLength,
                            MinValue = item.MinValue,
                            MaxValue = item.MaxValue,
                            IsRequired = item.IsRequired,
                            Placeholder = item.Placeholder,
                            MaxSelections = item.MaxSelections,
                            AllowedFileTypes = item.AllowedFileTypes,
                            MaxFileSizeBytes = item.MaxFileSizeBytes,
                            ErrorMessage = item.ErrorMessage,
                            DisplayLabel = item.DisplayLabel,
                            HelpText = item.HelpText,
                            FieldOrder = item.FieldOrder,
                            IsVisible = item.IsVisible,
                            IsReadonly = item.IsReadonly,
                            RequiredErrorMessage = item.RequiredErrorMessage,
                            PatternErrorMessage = item.PatternErrorMessage,
                            MinLengthErrorMessage = item.MinLengthErrorMessage,
                            MaxLengthErrorMessage = item.MaxLengthErrorMessage,
                            MinValueErrorMessage = item.MinValueErrorMessage,
                            MaxValueErrorMessage = item.MaxValueErrorMessage,
                            FileTypeErrorMessage = item.FileTypeErrorMessage,
                            DefaultOptions = item.DefaultOptions,
                            AllowsMultiple = item.AllowsMultiple,
                            AllowsCustomOptions = item.AllowsCustomOptions,
                            InputType = item.InputType,
                            InputMask = item.InputMask,

                            // Link Information
                            MetadataLinkId = item.ObjectMetadataId,
                            IsUnique = item.IsUnique,
                            MetadataLinkIsActive = item.MetadataLinkIsActive,
                            IsVisibleInList = item.IsVisibleInList,
                            IsVisibleInEdit = item.IsVisibleInEdit,
                            IsVisibleInCreate = item.IsVisibleInCreate,
                            IsVisibleInView = item.IsVisibleInView,
                            IsCalculated = item.IsCalculated
                        },
                        Values = new List<ValueInfoDto>() // Empty values list
                    };

                    metadataList.Add(metadata);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Error processing unified object metadata item: {Error}", ex.Message);
                    // Skip this item and continue
                }
            }

            result[group.Key] = metadataList;
        }

        return result;
    }

    /// <summary>
    /// Build object hierarchy recursively with TRUE UNIFIED metadata (no conversion to legacy format)
    /// </summary>
    private UnifiedObjectHierarchicalDto BuildObjectHierarchyTrueUnified(
        ObjectDto obj,
        List<ObjectDto> allObjects,
        Dictionary<Guid, List<UnifiedMetadataWithValuesDto>> objectMetadata,
        Dictionary<Guid, List<ObjectDisplayDto>> objectDisplays,
        int hierarchyLevel,
        string hierarchyPath)
    {
        var objectDto = new UnifiedObjectHierarchicalDto
        {
            Id = obj.Id,
            Name = obj.Name,
            Description = obj.Description,
            ParentObjectId = obj.ParentObjectId,
            IsActive = obj.IsActive,
            Icon = obj.Icon,
            CreatedAt = obj.CreatedAt,
            CreatedBy = obj.CreatedBy,
            ModifiedAt = obj.ModifiedAt,
            ModifiedBy = obj.ModifiedBy,
            IsDeleted = obj.IsDeleted,
            HierarchyLevel = hierarchyLevel,
            HierarchyPath = hierarchyPath,
            // Use ACTUAL unified metadata (no conversion to legacy format)
            Metadata = ConvertToUnifiedResponseMetadata(objectMetadata.ContainsKey(obj.Id) ? objectMetadata[obj.Id] : new List<UnifiedMetadataWithValuesDto>()),
            ChildObjects = new List<UnifiedObjectHierarchicalDto>(),
            // Add displays for this object
            Displays = objectDisplays.ContainsKey(obj.Id) ? objectDisplays[obj.Id] : new List<ObjectDisplayDto>()
        };

        // Get child objects for this object
        var childObjects = allObjects.Where(o => o.ParentObjectId == obj.Id).ToList();

        foreach (var childObject in childObjects)
        {
            var childDto = BuildObjectHierarchyTrueUnified(
                childObject,
                allObjects,
                objectMetadata,
                objectDisplays,
                hierarchyLevel + 1,
                $"{hierarchyPath} > {childObject.Name}");

            objectDto.ChildObjects.Add(childDto);
        }

        return objectDto;
    }

    /// <summary>
    /// Convert unified metadata to unified response format (no legacy conversion)
    /// This provides the actual unified metadata structure for API responses
    /// </summary>
    private List<UnifiedMetadataWithValuesResponseDto> ConvertToUnifiedResponseMetadata(List<UnifiedMetadataWithValuesDto> unifiedMetadata)
    {
        var result = new List<UnifiedMetadataWithValuesResponseDto>();

        foreach (var unified in unifiedMetadata)
        {
            var response = new UnifiedMetadataWithValuesResponseDto
            {
                Metadata = new UnifiedMetadataResponseDto
                {
                    // Core Identity
                    Id = unified.Metadata.MetadataId,
                    Name = unified.Metadata.Name,

                    // Essential UI Properties (consolidated)
                    DisplayLabel = unified.Metadata.DisplayLabel,
                    HelpText = unified.Metadata.HelpText,
                    FieldOrder = unified.Metadata.FieldOrder,
                    IsVisible = unified.Metadata.IsVisible,
                    IsReadonly = unified.Metadata.IsReadonly,

                    // Essential Validation Properties (consolidated)
                    ValidationPattern = unified.Metadata.ValidationPattern,
                    MinLength = unified.Metadata.MinLength,
                    MaxLength = unified.Metadata.MaxLength,
                    MinValue = unified.Metadata.MinValue,
                    MaxValue = unified.Metadata.MaxValue,
                    IsRequired = unified.Metadata.IsRequired,

                    // Essential Input Properties (consolidated)
                    Placeholder = unified.Metadata.Placeholder,
                    InputType = unified.Metadata.InputType,
                    InputMask = unified.Metadata.InputMask,

                    // Essential Choice Properties (consolidated)
                    DefaultOptions = unified.Metadata.DefaultOptions,
                    AllowsMultiple = unified.Metadata.AllowsMultiple,
                    AllowsCustomOptions = unified.Metadata.AllowsCustomOptions,
                    MaxSelections = unified.Metadata.MaxSelections,

                    // Essential File Properties (consolidated)
                    AllowedFileTypes = unified.Metadata.AllowedFileTypes,
                    MaxFileSizeBytes = unified.Metadata.MaxFileSizeBytes,

                    // Essential Error Messages (consolidated)
                    ErrorMessage = unified.Metadata.ErrorMessage,
                    RequiredErrorMessage = unified.Metadata.RequiredErrorMessage,
                    PatternErrorMessage = unified.Metadata.PatternErrorMessage,
                    MinLengthErrorMessage = unified.Metadata.MinLengthErrorMessage,
                    MaxLengthErrorMessage = unified.Metadata.MaxLengthErrorMessage,
                    MinValueErrorMessage = unified.Metadata.MinValueErrorMessage,
                    MaxValueErrorMessage = unified.Metadata.MaxValueErrorMessage,
                    FileTypeErrorMessage = unified.Metadata.FileTypeErrorMessage,

                    // DataType Information (for reference)
                    DataTypeName = unified.Metadata.DataTypeName,
                    Category = unified.Metadata.Category,
                    UiComponent = unified.Metadata.UiComponent,
                    DecimalPlaces = unified.Metadata.DecimalPlaces,
                    StepValue = unified.Metadata.StepValue,

                    // Link Information
                    MetadataLinkId = unified.Metadata.MetadataLinkId,
                    IsUnique = unified.Metadata.IsUnique,
                    IsVisibleInList = unified.Metadata.IsVisibleInList,
                    IsVisibleInEdit = unified.Metadata.IsVisibleInEdit,
                    IsVisibleInCreate = unified.Metadata.IsVisibleInCreate,
                    IsVisibleInView = unified.Metadata.IsVisibleInView,
                    IsCalculated = unified.Metadata.IsCalculated,

                    // Override References (for advanced use cases)
                    ContextId = unified.Metadata.ContextId,
                    TenantContextId = unified.Metadata.TenantContextId,
                    ObjectLookupId = unified.Metadata.ObjectLookupId,

                    // Display and Action Management Fields
                    IsEditable = unified.Metadata.IsEditable,
                    IsSearchable = unified.Metadata.IsSearchable,
                    IsSortable = unified.Metadata.IsSortable,
                    SortOrder = unified.Metadata.SortOrder,
                    DisplayFormat = unified.Metadata.DisplayFormat
                },
                Values = unified.Values
            };

            result.Add(response);
        }

        return result;
    }

    /// <summary>
    /// Get displays and actions for objects
    /// </summary>
    private async Task<Dictionary<Guid, List<ObjectDisplayDto>>> GetObjectDisplaysAsync(
        IDbConnection connection,
        Guid[] objectIds)
    {
        var sql = @"
            SELECT
                -- Display Properties
                d.""Id"" as DisplayId,
                d.""Name"" as DisplayName,
                d.""Description"" as DisplayDescription,
                d.""DisplayName"" as DisplayDisplayName,
                d.""IsDefault"" as DisplayIsDefault,
                d.""RouteTemplate"" as DisplayRouteTemplate,
                d.""Icon"" as DisplayIcon,
                d.""SortOrder"" as DisplaySortOrder,
                d.""IsActive"" as DisplayIsActive,
                d.""CreatedAt"" as DisplayCreatedAt,
                d.""ModifiedAt"" as DisplayModifiedAt,
                d.""CreatedBy"" as DisplayCreatedBy,
                d.""ModifiedBy"" as DisplayModifiedBy,
                d.""IsDeleted"" as DisplayIsDeleted,

                -- Action Properties
                a.""Id"" as ActionId,
                a.""Name"" as ActionName,
                a.""Description"" as ActionDescription,
                a.""EndpointTemplate"" as ActionEndpointTemplate,
                a.""NavigationTarget"" as ActionNavigationTarget,
                a.""Icon"" as ActionIcon,
                a.""ButtonStyle"" as ActionButtonStyle,
                a.""ConfirmationMessage"" as ActionConfirmationMessage,
                a.""SuccessMessage"" as ActionSuccessMessage,
                a.""ErrorMessage"" as ActionErrorMessage,
                a.""IsActive"" as ActionIsActive,
                a.""CreatedAt"" as ActionCreatedAt,
                a.""ModifiedAt"" as ActionModifiedAt,
                a.""CreatedBy"" as ActionCreatedBy,
                a.""ModifiedBy"" as ActionModifiedBy,
                a.""IsDeleted"" as ActionIsDeleted,

                -- DisplayAction Properties
                da.""Id"" as DisplayActionId,
                da.""ObjectId"" as ObjectId,
                da.""AccessLevel"" as DisplayActionAccessLevel,
                da.""IsDefault"" as DisplayActionIsDefault,
                da.""SortOrder"" as DisplayActionSortOrder,
                da.""IsVisibleInToolbar"" as DisplayActionIsVisibleInToolbar,
                da.""IsVisibleInContextMenu"" as DisplayActionIsVisibleInContextMenu,
                da.""IsVisibleInRowActions"" as DisplayActionIsVisibleInRowActions,
                da.""IsActive"" as DisplayActionIsActive,
                da.""CreatedAt"" as DisplayActionCreatedAt,
                da.""ModifiedAt"" as DisplayActionModifiedAt,
                da.""CreatedBy"" as DisplayActionCreatedBy,
                da.""ModifiedBy"" as DisplayActionModifiedBy,
                da.""IsDeleted"" as DisplayActionIsDeleted
            FROM ""Genp"".""DisplayActions"" da
            INNER JOIN ""Genp"".""Displays"" d ON da.""DisplayId"" = d.""Id""
            INNER JOIN ""Genp"".""Actions"" a ON da.""ActionId"" = a.""Id""
            WHERE da.""TenantId"" = @TenantId
                AND da.""ObjectId"" = ANY(@ObjectIds)
                AND da.""IsDeleted"" = false
                AND d.""IsDeleted"" = false
                AND a.""IsDeleted"" = false
            ORDER BY d.""SortOrder"", da.""SortOrder""";

        var tenantId = GetCurrentTenantId();
        var displayActionData = await connection.QueryAsync<ObjectDisplayActionMappingDto>(sql, new { ObjectIds = objectIds, TenantId = tenantId });

        var result = new Dictionary<Guid, List<ObjectDisplayDto>>();

        // Group by ObjectId, then by DisplayId
        foreach (var objectGroup in displayActionData.GroupBy(x => x.ObjectId))
        {
            var displays = new List<ObjectDisplayDto>();

            foreach (var displayGroup in objectGroup.GroupBy(x => x.DisplayId))
            {
                var firstItem = displayGroup.First();
                var display = new ObjectDisplayDto
                {
                    Id = firstItem.DisplayId,
                    Name = firstItem.DisplayName ?? "",
                    Description = firstItem.DisplayDescription,
                    DisplayName = firstItem.DisplayDisplayName ?? "",
                    IsDefault = firstItem.DisplayIsDefault,
                    RouteTemplate = firstItem.DisplayRouteTemplate,
                    Icon = firstItem.DisplayIcon,
                    SortOrder = firstItem.DisplaySortOrder,
                    IsActive = firstItem.DisplayIsActive,
                    CreatedAt = firstItem.DisplayCreatedAt,
                    ModifiedAt = firstItem.DisplayModifiedAt ?? firstItem.DisplayCreatedAt,
                    CreatedBy = firstItem.DisplayCreatedBy,
                    ModifiedBy = firstItem.DisplayModifiedBy,
                    IsDeleted = firstItem.DisplayIsDeleted,
                    Actions = new List<ObjectDisplayActionDto>()
                };

                // Add all actions for this display
                foreach (var item in displayGroup)
                {
                    var action = new ObjectDisplayActionDto
                    {
                        // Action Properties
                        ActionId = item.ActionId,
                        Name = item.ActionName ?? "",
                        Description = item.ActionDescription,
                        EndpointTemplate = item.ActionEndpointTemplate,
                        NavigationTarget = item.ActionNavigationTarget,
                        Icon = item.ActionIcon,
                        ButtonStyle = item.ActionButtonStyle,
                        ConfirmationMessage = item.ActionConfirmationMessage,
                        SuccessMessage = item.ActionSuccessMessage,
                        ErrorMessage = item.ActionErrorMessage,
                        ActionIsActive = item.ActionIsActive,
                        ActionCreatedAt = item.ActionCreatedAt,
                        ActionModifiedAt = item.ActionModifiedAt ?? item.ActionCreatedAt,
                        ActionCreatedBy = item.ActionCreatedBy,
                        ActionModifiedBy = item.ActionModifiedBy,
                        ActionIsDeleted = item.ActionIsDeleted,

                        // DisplayAction Properties
                        DisplayActionId = item.DisplayActionId,
                        AccessLevel = item.DisplayActionAccessLevel ?? "Public",
                        IsDefault = item.DisplayActionIsDefault,
                        SortOrder = item.DisplayActionSortOrder,
                        IsVisibleInToolbar = item.DisplayActionIsVisibleInToolbar,
                        IsVisibleInContextMenu = item.DisplayActionIsVisibleInContextMenu,
                        IsVisibleInRowActions = item.DisplayActionIsVisibleInRowActions,
                        DisplayActionIsActive = item.DisplayActionIsActive,
                        DisplayActionCreatedAt = item.DisplayActionCreatedAt,
                        DisplayActionModifiedAt = item.DisplayActionModifiedAt ?? item.DisplayActionCreatedAt,
                        DisplayActionCreatedBy = item.DisplayActionCreatedBy,
                        DisplayActionModifiedBy = item.DisplayActionModifiedBy,
                        DisplayActionIsDeleted = item.DisplayActionIsDeleted
                    };

                    display.Actions.Add(action);
                }

                displays.Add(display);
            }

            result[objectGroup.Key] = displays;
        }

        return result;
    }

    #endregion
}
