using Ardalis.Specification;
using Domain.Entities;

namespace Application.IntegrationConfigurations.Specifications;

/// <summary>
/// Specification to get integration configurations for multiple integrations with related data
/// </summary>
public class IntegrationConfigurationsForIntegrationsSpec : Specification<IntegrationConfiguration>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public IntegrationConfigurationsForIntegrationsSpec(List<Guid> integrationIds)
    {
        Query.Where(ic => integrationIds.Contains(ic.IntegrationId))
             .Include(ic => ic.Integration)
             .Include(ic => ic.IntegrationApi)
             .Include(ic => ic.Object)
             .OrderBy(ic => ic.Integration!.Name)
             .ThenBy(ic => ic.IntegrationApi!.Name);
    }
}
