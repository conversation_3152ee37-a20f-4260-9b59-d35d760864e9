/**
 * ActionBar Molecule
 * Reusable action bar component with buttons and controls
 */

import React from 'react';
import { Row, Col } from 'react-bootstrap';
import { ActionButton, ActionButtonProps } from '../atoms/ActionButton';

export interface ActionBarAction extends Omit<ActionButtonProps, 'children'> {
  key: string;
  label: string;
  icon?: string | React.ReactNode;
  primary?: boolean;
  hidden?: boolean;
}

export interface ActionBarProps {
  actions: ActionBarAction[];
  title?: string;
  subtitle?: string;
  loading?: boolean;
  className?: string;
  justify?: 'start' | 'center' | 'end' | 'between';
  align?: 'start' | 'center' | 'end';
}

export const ActionBar: React.FC<ActionBarProps> = ({
  actions,
  title,
  subtitle,
  loading = false,
  className = '',
  justify = 'end',
  align = 'center'
}) => {
  const visibleActions = actions.filter(action => !action.hidden);
  const primaryActions = visibleActions.filter(action => action.primary);
  const secondaryActions = visibleActions.filter(action => !action.primary);

  const renderActions = (actionList: ActionBarAction[]) => {
    return actionList.map(({ key, label, primary, hidden, ...actionProps }) => (
      <ActionButton
        key={key}
        disabled={loading || actionProps.disabled}
        className={`${primary ? 'fw-semibold' : ''} ${actionProps.className || ''}`}
        {...actionProps}
      >
        {label}
      </ActionButton>
    ));
  };

  return (
    <div className={`action-bar ${className}`}>
      <Row className={`g-2 align-items-${align}`}>
        {/* Title Section */}
        {(title || subtitle) && (
          <Col>
            {title && (
              <h6 className="mb-0 fw-semibold">
                {title}
              </h6>
            )}
            {subtitle && (
              <small className="text-muted">
                {subtitle}
              </small>
            )}
          </Col>
        )}

        {/* Actions Section */}
        <Col xs="auto">
          <div className={`d-flex gap-2 justify-content-${justify}`}>
            {/* Secondary Actions */}
            {secondaryActions.length > 0 && (
              <div className="d-flex gap-2">
                {renderActions(secondaryActions)}
              </div>
            )}

            {/* Primary Actions */}
            {primaryActions.length > 0 && (
              <div className="d-flex gap-2">
                {renderActions(primaryActions)}
              </div>
            )}
          </div>
        </Col>
      </Row>
    </div>
  );
};

// Predefined action bar configurations
export const createSubscriptionActions = (
  onRefresh: () => void,
  onAdd: () => void,
  loading: boolean = false
): ActionBarAction[] => [
  {
    key: 'refresh',
    label: 'Refresh',
    icon: '🔄',
    variant: 'outline-primary',
    onClick: onRefresh,
    loading,
    loadingText: 'Refreshing...'
  },
  {
    key: 'add',
    label: 'Add Subscription',
    icon: '+',
    variant: 'primary',
    onClick: onAdd,
    primary: true
  }
];

export const createTemplateActions = (
  onRefresh: () => void,
  onCreate: () => void,
  onImport?: () => void,
  loading: boolean = false
): ActionBarAction[] => [
  {
    key: 'refresh',
    label: 'Refresh',
    icon: '🔄',
    variant: 'outline-primary',
    onClick: onRefresh,
    loading,
    loadingText: 'Refreshing...'
  },
  ...(onImport ? [{
    key: 'import',
    label: 'Import',
    icon: '📥',
    variant: 'outline-secondary',
    onClick: onImport
  }] : []),
  {
    key: 'create',
    label: 'Create Template',
    icon: '+',
    variant: 'primary',
    onClick: onCreate,
    primary: true
  }
];

export const createTenantActions = (
  onRefresh: () => void,
  onAdd: () => void,
  loading: boolean = false
): ActionBarAction[] => [
  {
    key: 'refresh',
    label: 'Refresh',
    icon: '🔄',
    variant: 'outline-primary',
    onClick: onRefresh,
    loading,
    loadingText: 'Refreshing...'
  },
  {
    key: 'add',
    label: 'Add Tenant',
    icon: '+',
    variant: 'primary',
    onClick: onAdd,
    primary: true
  }
];
