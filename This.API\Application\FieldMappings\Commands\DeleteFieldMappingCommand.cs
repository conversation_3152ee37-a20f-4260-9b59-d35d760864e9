using MediatR;
using Shared.Common.Response;

namespace Application.FieldMappings.Commands;

/// <summary>
/// Delete field mapping command
/// </summary>
public class DeleteFieldMappingCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Field Mapping ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteFieldMappingCommand(Guid id)
    {
        Id = id;
    }
}
