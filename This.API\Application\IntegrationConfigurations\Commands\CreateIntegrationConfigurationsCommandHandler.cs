using Application.IntegrationConfigurations.DTOs;
using Application.IntegrationConfigurations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Commands;

/// <summary>
/// Create multiple integration configurations command handler
/// </summary>
public class CreateIntegrationConfigurationsCommandHandler : IRequestHandler<CreateIntegrationConfigurationsCommand, Result<List<ViewIntegrationConfigurationDto>>>
{
    private readonly IRepository<IntegrationConfiguration> _configurationRepository;
    private readonly IReadRepository<Integration> _integrationRepository;
    private readonly IReadRepository<IntegrationApi> _integrationApiRepository;
    private readonly IReadRepository<Domain.Entities.Object> _objectRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateIntegrationConfigurationsCommandHandler(
        IRepository<IntegrationConfiguration> configurationRepository,
        IReadRepository<Integration> integrationRepository,
        IReadRepository<IntegrationApi> integrationApiRepository,
        IReadRepository<Domain.Entities.Object> objectRepository)
    {
        _configurationRepository = configurationRepository;
        _integrationRepository = integrationRepository;
        _integrationApiRepository = integrationApiRepository;
        _objectRepository = objectRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<List<ViewIntegrationConfigurationDto>>> Handle(CreateIntegrationConfigurationsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (!request.Configurations.Any())
            {
                return Result<List<ViewIntegrationConfigurationDto>>.Failure("No configurations provided.");
            }

            var createdConfigurations = new List<IntegrationConfiguration>();
            var errors = new List<string>();

            // Cache related entities
            var integrations = new Dictionary<Guid, Integration>();
            var integrationApis = new Dictionary<Guid, IntegrationApi>();
            var objects = new Dictionary<Guid, Domain.Entities.Object>();

            // Validate all related entities exist
            var integrationIds = request.Configurations.Select(c => c.IntegrationId).Distinct();
            var integrationApiIds = request.Configurations.Select(c => c.IntegrationApiId).Distinct();
            var objectIds = request.Configurations.Select(c => c.ObjectId).Distinct();

            foreach (var id in integrationIds)
            {
                var integration = await _integrationRepository.GetByIdAsync(id, cancellationToken);
                if (integration == null)
                {
                    errors.Add($"Integration with ID {id} not found.");
                }
                else
                {
                    integrations[id] = integration;
                }
            }

            foreach (var id in integrationApiIds)
            {
                var api = await _integrationApiRepository.GetByIdAsync(id, cancellationToken);
                if (api == null)
                {
                    errors.Add($"Integration API with ID {id} not found.");
                }
                else
                {
                    integrationApis[id] = api;
                }
            }

            foreach (var id in objectIds)
            {
                var obj = await _objectRepository.GetByIdAsync(id, cancellationToken);
                if (obj == null)
                {
                    errors.Add($"Object with ID {id} not found.");
                }
                else
                {
                    objects[id] = obj;
                }
            }

            if (errors.Any())
            {
                return Result<List<ViewIntegrationConfigurationDto>>.Failure(string.Join("; ", errors));
            }

            // Process each configuration
            foreach (var configRequest in request.Configurations)
            {
                try
                {
                    // Check if configuration already exists
                    var existingConfigSpec = new IntegrationConfigurationByIntegrationApiObjectSpec(
                        configRequest.IntegrationId, configRequest.IntegrationApiId, configRequest.ObjectId);
                    var existingConfig = await _configurationRepository.GetBySpecAsync(existingConfigSpec, cancellationToken);

                    if (existingConfig != null)
                    {
                        errors.Add($"Configuration already exists for Integration {configRequest.IntegrationId}, API {configRequest.IntegrationApiId}, Object {configRequest.ObjectId}.");
                        continue;
                    }

                    var configuration = new IntegrationConfiguration
                    {
                        IntegrationId = configRequest.IntegrationId,
                        IntegrationApiId = configRequest.IntegrationApiId,
                        ObjectId = configRequest.ObjectId,
                        Direction = configRequest.Direction,
                        IsActive = configRequest.IsActive
                    };

                    createdConfigurations.Add(configuration);
                }
                catch (Exception ex)
                {
                    errors.Add($"Failed to process configuration: {ex.Message}");
                }
            }

            if (!createdConfigurations.Any())
            {
                return Result<List<ViewIntegrationConfigurationDto>>.Failure($"No configurations were created. Errors: {string.Join("; ", errors)}");
            }

            // Bulk insert
            var savedConfigurations = await _configurationRepository.AddRangeAsync(createdConfigurations, cancellationToken);

            // Create view DTOs
            var viewDtos = savedConfigurations.Select(config => new ViewIntegrationConfigurationDto
            {
                Id = config.Id,
                IntegrationId = config.IntegrationId,
                IntegrationName = integrations[config.IntegrationId].Name,
                IntegrationApiId = config.IntegrationApiId,
                IntegrationApiName = integrationApis[config.IntegrationApiId].Name,
                ObjectId = config.ObjectId,
                ObjectName = objects[config.ObjectId].Name,
                Direction = config.Direction,
                IsActive = config.IsActive,
                CreatedAt = config.CreatedAt,
                CreatedBy = config.CreatedBy,
                ModifiedAt = config.ModifiedAt,
                ModifiedBy = config.ModifiedBy
            }).ToList();

            var result = Result<List<ViewIntegrationConfigurationDto>>.Success(viewDtos);
            if (errors.Any())
            {
                var warningMessage = string.Join("; ", errors.Select(e => $"Warning: {e}"));
                result.Message = warningMessage;
            }

            return result;
        }
        catch (Exception ex)
        {
            return Result<List<ViewIntegrationConfigurationDto>>.Failure($"Failed to create configurations: {ex.Message}");
        }
    }
}
