using Shared.Common;

namespace Abstraction.Database.Repositories;

/// <summary>
/// Repository interface for comprehensive subscription data across all tenants
/// </summary>
public interface IComprehensiveSubscriptionRepository : ITransientRepository
{
    /// <summary>
    /// Get comprehensive subscription data across all tenants with filtering and pagination
    /// </summary>
    /// <param name="tenantId">Filter by specific tenant ID (optional)</param>
    /// <param name="productId">Filter by product ID (optional)</param>
    /// <param name="status">Filter by subscription status (optional)</param>
    /// <param name="subscriptionType">Filter by subscription type (optional)</param>
    /// <param name="isActive">Filter by active status (optional)</param>
    /// <param name="isExpired">Filter by expired status (optional)</param>
    /// <param name="pricingTier">Filter by pricing tier (optional)</param>
    /// <param name="searchTerm">Search term for subscription type, product name, or tenant name (optional)</param>
    /// <param name="startDateFrom">Filter by start date from (optional)</param>
    /// <param name="startDateTo">Filter by start date to (optional)</param>
    /// <param name="endDateFrom">Filter by end date from (optional)</param>
    /// <param name="endDateTo">Filter by end date to (optional)</param>
    /// <param name="expiringWithinDays">Filter subscriptions expiring within specified days (optional)</param>
    /// <param name="pageNumber">Page number for pagination</param>
    /// <param name="pageSize">Page size for pagination</param>
    /// <param name="orderBy">Order by field</param>
    /// <param name="orderDirection">Order direction</param>
    /// <param name="includeSummary">Include summary statistics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Comprehensive subscription data with tenant information and summary statistics</returns>
    Task<object> GetComprehensiveSubscriptionsAsync(
        string? tenantId = null,
        Guid? productId = null,
        string? status = null,
        string? subscriptionType = null,
        bool? isActive = null,
        bool? isExpired = null,
        string? pricingTier = null,
        string? searchTerm = null,
        DateTime? startDateFrom = null,
        DateTime? startDateTo = null,
        DateTime? endDateFrom = null,
        DateTime? endDateTo = null,
        int? expiringWithinDays = null,
        int pageNumber = 1,
        int pageSize = 50,
        string orderBy = "CreatedAt",
        string orderDirection = "desc",
        bool includeSummary = true,
        CancellationToken cancellationToken = default);
}
