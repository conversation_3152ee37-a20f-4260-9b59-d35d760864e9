/**
 * SubscriptionModal Organism
 * Modal component for viewing/editing subscription details
 */

import React, { useState, useEffect } from 'react';
import { Modal, Form, Row, Col, Alert } from 'react-bootstrap';
import { ActionButton } from '../atoms/ActionButton';
import { StatusBadge } from '../atoms/StatusBadge';
import { LoadingSpinner } from '../atoms/LoadingSpinner';
import { SearchableDropdown } from '../atoms/SearchableDropdown';
import { useSubscriptions, useTenants, useTemplates, useUI } from '../../store/hooks';
import type { SubscriptionDto, CreateSubscriptionRequest, UpdateSubscriptionRequest } from '../../services/api';

export interface SubscriptionModalProps {
  show: boolean;
  onHide: () => void;
  mode: 'view' | 'edit' | 'create';
  subscription?: SubscriptionDto;
}

export const SubscriptionModal: React.FC<SubscriptionModalProps> = ({
  show,
  onHide,
  mode,
  subscription
}) => {
  const { createNewSubscription, updateExistingSubscription, loading } = useSubscriptions();
  const { dropdownOptions: tenantOptions } = useTenants();
  const { liveTemplates } = useTemplates();
  const { showNotification } = useUI();

  // Form state
  const [formData, setFormData] = useState<Partial<CreateSubscriptionRequest>>({
    productId: '',
    subscriptionType: 'standard',
    status: 'active',
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    autoRenew: false,
    pricingTier: 'standard',
    version: '1.0.0',
    templateJson: '{}',
    isActive: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when subscription changes
  useEffect(() => {
    if (subscription && (mode === 'edit' || mode === 'view')) {
      setFormData({
        productId: subscription.productId,
        subscriptionType: subscription.subscriptionType,
        status: subscription.status,
        startDate: subscription.startDate.split('T')[0],
        endDate: subscription.endDate ? subscription.endDate.split('T')[0] : '',
        autoRenew: subscription.autoRenew,
        pricingTier: subscription.pricingTier || 'standard',
        version: subscription.version,
        templateJson: subscription.templateJson,
        isActive: subscription.isActive
      });
    } else if (mode === 'create') {
      setFormData({
        productId: '',
        subscriptionType: 'standard',
        status: 'active',
        startDate: new Date().toISOString().split('T')[0],
        endDate: '',
        autoRenew: false,
        pricingTier: 'standard',
        version: '1.0.0',
        templateJson: '{}',
        isActive: true
      });
    }
    setErrors({});
  }, [subscription, mode, show]);

  // Handle form field changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.productId) {
      newErrors.productId = 'Product ID is required';
    }
    if (!formData.subscriptionType) {
      newErrors.subscriptionType = 'Subscription type is required';
    }
    if (!formData.status) {
      newErrors.status = 'Status is required';
    }
    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    }
    if (formData.endDate && formData.startDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
      newErrors.endDate = 'End date must be after start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (mode === 'create') {
        await createNewSubscription(formData as CreateSubscriptionRequest);
        showNotification({
          type: 'success',
          title: 'Success',
          message: 'Subscription created successfully'
        });
      } else if (mode === 'edit' && subscription) {
        await updateExistingSubscription({
          ...formData,
          id: subscription.id
        } as UpdateSubscriptionRequest);
        showNotification({
          type: 'success',
          title: 'Success',
          message: 'Subscription updated successfully'
        });
      }
      onHide();
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Error',
        message: `Failed to ${mode === 'create' ? 'create' : 'update'} subscription`
      });
    }
  };

  const isReadOnly = mode === 'view';
  const isLoading = loading.create || loading.update;

  return (
    <Modal show={show} onHide={onHide} size="lg" backdrop="static">
      <Modal.Header closeButton>
        <Modal.Title>
          {mode === 'create' && 'Create Subscription'}
          {mode === 'edit' && 'Edit Subscription'}
          {mode === 'view' && 'Subscription Details'}
        </Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {isLoading && <LoadingSpinner overlay text="Processing..." />}
        
        <Form>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Product ID</Form.Label>
                <Form.Control
                  type="text"
                  value={formData.productId || ''}
                  onChange={(e) => handleChange('productId', e.target.value)}
                  disabled={isReadOnly}
                  isInvalid={!!errors.productId}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.productId}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Subscription Type</Form.Label>
                <SearchableDropdown
                  value={formData.subscriptionType || ''}
                  onChange={(value) => handleChange('subscriptionType', value)}
                  options={[
                    { value: 'basic', label: 'Basic' },
                    { value: 'standard', label: 'Standard' },
                    { value: 'premium', label: 'Premium' },
                    { value: 'enterprise', label: 'Enterprise' }
                  ]}
                  placeholder="Select type..."
                  disabled={isReadOnly}
                  showSearch={false}
                  className={errors.subscriptionType ? 'is-invalid' : ''}
                />
                {errors.subscriptionType && (
                  <div className="invalid-feedback d-block">
                    {errors.subscriptionType}
                  </div>
                )}
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Status</Form.Label>
                {isReadOnly ? (
                  <div>
                    <StatusBadge 
                      status={formData.status || ''} 
                      variant="subscription" 
                    />
                  </div>
                ) : (
                  <SearchableDropdown
                    value={formData.status || ''}
                    onChange={(value) => handleChange('status', value)}
                    options={[
                      { value: 'active', label: 'Active' },
                      { value: 'inactive', label: 'Inactive' },
                      { value: 'suspended', label: 'Suspended' },
                      { value: 'cancelled', label: 'Cancelled' }
                    ]}
                    placeholder="Select status..."
                    showSearch={false}
                    className={errors.status ? 'is-invalid' : ''}
                  />
                )}
                <Form.Control.Feedback type="invalid">
                  {errors.status}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Pricing Tier</Form.Label>
                <SearchableDropdown
                  value={formData.pricingTier || ''}
                  onChange={(value) => handleChange('pricingTier', value)}
                  options={[
                    { value: 'starter', label: 'Starter' },
                    { value: 'standard', label: 'Standard' },
                    { value: 'professional', label: 'Professional' },
                    { value: 'enterprise', label: 'Enterprise' }
                  ]}
                  placeholder="Select pricing tier..."
                  disabled={isReadOnly}
                  showSearch={false}
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Start Date</Form.Label>
                <Form.Control
                  type="date"
                  value={formData.startDate || ''}
                  onChange={(e) => handleChange('startDate', e.target.value)}
                  disabled={isReadOnly}
                  isInvalid={!!errors.startDate}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.startDate}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>End Date (Optional)</Form.Label>
                <Form.Control
                  type="date"
                  value={formData.endDate || ''}
                  onChange={(e) => handleChange('endDate', e.target.value)}
                  disabled={isReadOnly}
                  isInvalid={!!errors.endDate}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.endDate}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Version</Form.Label>
                <Form.Control
                  type="text"
                  value={formData.version || ''}
                  onChange={(e) => handleChange('version', e.target.value)}
                  disabled={isReadOnly}
                />
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Check
                  type="checkbox"
                  label="Auto Renew"
                  checked={formData.autoRenew || false}
                  onChange={(e) => handleChange('autoRenew', e.target.checked)}
                  disabled={isReadOnly}
                />
                <Form.Check
                  type="checkbox"
                  label="Active"
                  checked={formData.isActive || false}
                  onChange={(e) => handleChange('isActive', e.target.checked)}
                  disabled={isReadOnly}
                />
              </Form.Group>
            </Col>
          </Row>

          {mode === 'view' && subscription && (
            <Alert variant="info">
              <strong>Additional Information:</strong>
              <ul className="mb-0 mt-2">
                <li>Created: {new Date(subscription.createdAt).toLocaleString()}</li>
                <li>Created by: {subscription.createdBy}</li>
                {subscription.modifiedAt && (
                  <li>Last modified: {new Date(subscription.modifiedAt).toLocaleString()}</li>
                )}
                <li>Metadata count: {subscription.metadataCount}</li>
              </ul>
            </Alert>
          )}
        </Form>
      </Modal.Body>

      <Modal.Footer>
        <ActionButton
          variant="secondary"
          onClick={onHide}
          disabled={isLoading}
        >
          {mode === 'view' ? 'Close' : 'Cancel'}
        </ActionButton>
        
        {!isReadOnly && (
          <ActionButton
            variant="primary"
            onClick={handleSubmit}
            loading={isLoading}
            loadingText={mode === 'create' ? 'Creating...' : 'Updating...'}
          >
            {mode === 'create' ? 'Create' : 'Update'}
          </ActionButton>
        )}
      </Modal.Footer>
    </Modal>
  );
};
