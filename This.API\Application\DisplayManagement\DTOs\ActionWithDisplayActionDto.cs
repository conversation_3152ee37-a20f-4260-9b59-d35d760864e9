using System.ComponentModel.DataAnnotations;

namespace Application.DisplayManagement.DTOs;

/// <summary>
/// Action DTO with embedded DisplayAction relationship data
/// </summary>
public class ActionWithDisplayActionDto
{
    #region Action Properties

    /// <summary>
    /// Action name
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the action
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// API endpoint template for API actions
    /// </summary>
    [MaxLength(500)]
    public string? EndpointTemplate { get; set; }

    /// <summary>
    /// Navigation target for Navigation actions
    /// </summary>
    [MaxLength(500)]
    public string? NavigationTarget { get; set; }

    /// <summary>
    /// Icon for the action
    /// </summary>
    [MaxLength(100)]
    public string? Icon { get; set; }

    /// <summary>
    /// Button style - 'Primary', 'Secondary', 'Danger', etc.
    /// </summary>
    [MaxLength(50)]
    public string? ButtonStyle { get; set; }

    /// <summary>
    /// Confirmation dialog message
    /// </summary>
    public string? ConfirmationMessage { get; set; }

    /// <summary>
    /// Success message to display after action completion
    /// </summary>
    public string? SuccessMessage { get; set; }

    /// <summary>
    /// Error message to display on action failure
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether the action is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    #endregion

    #region DisplayAction Relationship Properties

    /// <summary>
    /// Object ID - Foreign key to Object entity (required for DisplayAction relationship)
    /// </summary>
    [Required]
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Access level - 'Public', 'Protected', 'Private'
    /// </summary>
    [MaxLength(50)]
    public string AccessLevel { get; set; } = "Public";

    /// <summary>
    /// Whether this is a default action for the display
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Sort order for action ordering within the display
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// Whether the action is visible in toolbar
    /// </summary>
    public bool IsVisibleInToolbar { get; set; } = true;

    /// <summary>
    /// Whether the action is visible in context menu
    /// </summary>
    public bool IsVisibleInContextMenu { get; set; } = false;

    /// <summary>
    /// Whether the action is visible in row actions
    /// </summary>
    public bool IsVisibleInRowActions { get; set; } = false;

    /// <summary>
    /// Whether the DisplayAction relationship is active
    /// </summary>
    public bool DisplayActionIsActive { get; set; } = true;

    #endregion
}
