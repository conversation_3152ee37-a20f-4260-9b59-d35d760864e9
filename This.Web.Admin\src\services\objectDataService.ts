// Service for fetching object-specific data for AG-Grid
import NavigationService from './navigationService';
import type { NavigationNode, MetadataResponse, MetadataWithValues, Metadata } from '../types/metadata';
export interface ObjectDataRow {
  id: string;
  name: string;
  type: string;
  status: string;
  createdDate: string;
  lastModified: string;
  owner: string;
  description: string;
  value?: number;
  category?: string;
  tags?: string[];
}

export interface ObjectDataResponse {
  success: boolean;
  data: any[]; // Changed from ObjectDataRow[] to any[] for dynamic data
  totalCount: number;
  error?: string;
  columnDefinitions?: DynamicColumnDefinition[]; // Add column definitions
}

// Dynamic column definition for AG-Grid
export interface DynamicColumnDefinition {
  // Core properties
  id?: string;
  field: string;
  headerName: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  sortable: boolean;
  filter: boolean | string;
  width?: number;
  flex?: number;

  // Form field properties
  displayLabel?: string;
  helpText?: string | null;
  fieldOrder?: number | null;
  isVisible?: boolean;
  isReadonly?: boolean;
  validationPattern?: string | null;
  minLength?: number | null;
  maxLength?: number | null;
  minValue?: number | null;
  maxValue?: number | null;
  isRequired?: boolean | null;
  placeholder?: string | null;
  defaultOptions?: any | null;
  maxSelections?: number | null;
  allowedFileTypes?: string[] | null;
  maxFileSize?: number | null;
  errorMessage?: string | null;
  requiredErrorMessage?: string | null;
  patternErrorMessage?: string | null;
  minLengthErrorMessage?: string | null;
  maxLengthErrorMessage?: string | null;
  minValueErrorMessage?: string | null;
  maxValueErrorMessage?: string | null;
  fileTypeErrorMessage?: string | null;
  maxFileSizeBytes?: number | null;
  inputType?: string | null;
  inputMask?: string | null;
  allowsMultiple?: boolean | null;
  allowsCustomOptions?: boolean | null;
  // Context lookup fields (from backend UnifiedMetadataDto)
  contextId?: string | null;
  tenantContextId?: string | null;
  objectLookupId?: string | null;

  // Metadata configuration (replaces separate dataType)
  metadata?: Metadata;

  // Metadata link
  metadataLink?: {
    objectMetaDataId: string;
    isUnique: boolean;
    isActive: boolean;
    shouldVisibleInList?: boolean | null;
    shouldVisibleInEdit?: boolean | null;
    shouldVisibleInCreate?: boolean | null;
    shouldVisibleInView?: boolean | null;
    isCalculate?: boolean | null;
  };

  // Backward compatibility
  editable?: boolean;
  required?: boolean;
  label?: string;
  uiComponent?: string;

  // Complete API metadata for direct access
  _apiMetadata?: any;
}

export interface ObjectDataFilter {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

// Metadata interfaces for column filtering (updated to use unified metadata)
export interface ChildMetadata {
  id: string;
  displayLabel: string;
  isVisible: boolean;
  fieldOrder?: number;
  uiComponent?: string; // Now directly from unified metadata
  category?: string;
  name?: string;
  // ADDED: Context lookup fields
  contextId?: string;
  tenantContextId?: string;
  objectLookupId?: string;
}

export interface ObjectMetadata {
  id: string;
  name: string;
  metadata: MetadataWithValues[]; // Updated to use metadata structure
}



// API Response interface based on actual API response structure
export interface ApiObjectDataResponse {
  objectName: string;
  tenantId: string;
  viewName: string;
  viewCreationResult: string;
  columnNames: string[];
  viewData: Array<{ [key: string]: any }>; // Array of objects, not 2D array
  totalRows: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  message: string;
}

export class ObjectDataService {
  private static instance: ObjectDataService;

  // API Configuration
  private readonly API_BASE_URL = 'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net';
  private readonly API_HEADERS = {
    'accept': '*/*',
    'tenant': 'kitchsync'
  };
  private readonly REQUEST_TIMEOUT = 30000; // 30 seconds
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1 second base delay

  // Navigation service instance for metadata access
  private readonly navigationService = NavigationService.getInstance();

  static getInstance(): ObjectDataService {
    if (!ObjectDataService.instance) {
      ObjectDataService.instance = new ObjectDataService();
    }
    return ObjectDataService.instance;
  }

  // Validate that the object name exists in the navigation data
  private validateObjectName(objectName: string): boolean {
    const availableObjectNames = this.getObjectNamesFromNavigation();

    // Perform case-insensitive matching
    const lowerCaseObjectName = objectName.toLowerCase();
    return availableObjectNames.some(name => name.toLowerCase() === lowerCaseObjectName);
  }

  // Get object names from cached navigation data
  private getObjectNamesFromNavigation(): string[] {
    try {
      const navigationService = NavigationService.getInstance();
      const navigationData = navigationService.getCurrentData();

      if (!navigationData?.nodes?.length) {
        return [];
      }

      const objectNames = new Set<string>();

      // Extract object names directly from products
      navigationData.nodes.forEach((productNode: NavigationNode) => {
        if (productNode.type === 'product' && productNode.children) {
          productNode.children.forEach((objectNode: NavigationNode) => {
            if (objectNode.type === 'object' && objectNode.name) {
              objectNames.add(objectNode.name);
            }
          });
        }
      });

      return Array.from(objectNames);
    } catch (error) {
      console.error('Error getting object names from navigation:', error);
      return [];
    }
  }

  // Fetch metadata for an object from the NavigationService (reuses existing metadata)
  private async fetchObjectMetadata(objectName: string): Promise<ChildMetadata[]> {
    try {
      // Ensure NavigationService is initialized and has data
      await this.navigationService.ensureInitialized();

      // Get the raw metadata response from NavigationService
      const metadataResponse = this.navigationService.getRawMetadataResponse();

      if (!metadataResponse) {
        return [];
      }

      // Find the object metadata in the response
      const objectMetadata = this.findObjectInMetadata(metadataResponse, objectName);

      if (!objectMetadata) {
        return [];
      }

      // Extract child metadata with visibility settings
      const childMetadata = this.extractChildMetadata(objectMetadata);

      return childMetadata;

    } catch (error) {
      console.error(`Failed to fetch metadata for object "${objectName}" from NavigationService:`, error);
      return []; // Return empty array on failure
    }
  }

  // Find object metadata in the comprehensive entity response
  private findObjectInMetadata(metadataResponse: MetadataResponse, objectName: string): ObjectMetadata | null {
    for (const product of metadataResponse.data.products) {
      // Search directly in product's rootObjects
      const rootObject = this.searchObjectInHierarchy(product.rootObjects || [], objectName);
      if (rootObject) return rootObject;
    }
    return null;
  }

  // Recursively search for object in the hierarchy
  private searchObjectInHierarchy(objects: any[], objectName: string): ObjectMetadata | null {
    for (const obj of objects) {
      if (obj.name === objectName) {
        return obj;
      }

      // Search in child objects recursively
      if (obj.childObjects && obj.childObjects.length > 0) {
        const found = this.searchObjectInHierarchy(obj.childObjects, objectName);
        if (found) return found;
      }
    }
    return null;
  }

  // Extract child metadata from object metadata (updated for unified metadata)
  private extractChildMetadata(objectMetadata: ObjectMetadata): ChildMetadata[] {
    try {
      if (!objectMetadata?.metadata || !Array.isArray(objectMetadata.metadata)) {
        console.warn('No metadata array found in objectMetadata:', objectMetadata);
        return [];
      }

      // Process unified metadata items
      const validItems = objectMetadata.metadata
        .map(item => {
          try {
            // item is now MetadataWithValues
            const meta = item.metadata; // Get the Metadata from MetadataWithValues
            if (!meta || typeof meta !== 'object') {
              console.warn('Invalid metadata item:', item);
              return null;
            }

            return {
              id: meta.id || `meta-${Math.random().toString(36).substring(2, 11)}`,
              displayLabel: meta.displayLabel || meta.name || '',
              isVisible: meta.isVisible !== false, // Default to true if not specified
              fieldOrder: typeof meta.fieldOrder === 'number' ? meta.fieldOrder : 9999,
              uiComponent: meta.uiComponent || 'ThisText', // Direct from metadata
              category: meta.category || 'string',
              name: meta.name || '',
              // ADDED: Include context lookup fields from metadata
              contextId: meta.contextId || undefined,
              tenantContextId: meta.tenantContextId || undefined,
              objectLookupId: meta.objectLookupId || undefined
            } as ChildMetadata;
          } catch (error) {
            console.error('Error processing metadata item:', error, item);
            return null;
          }
        })
        .filter((item): item is ChildMetadata => item !== null);

      // Sort by fieldOrder
      return [...validItems].sort((a, b) => (a.fieldOrder ?? 9999) - (b.fieldOrder ?? 9999));

    } catch (error) {
      console.error('Error in extractChildMetadata:', error);
      return [];
    }
  }

  // Filter column definitions based on metadata visibility and ensure unique headers
  private filterColumnsByMetadata(
    columnDefinitions: DynamicColumnDefinition[],
    metadata: ChildMetadata[]
  ): DynamicColumnDefinition[] {
    try {
      // Filter out non-visible metadata first
      const visibleMetadata = metadata.filter(meta => meta.isVisible !== false);

      if (visibleMetadata.length === 0) {
        return columnDefinitions;
      }

      // Create a map of metadata by field name for exact matching
      const metadataByField = new Map<string, ChildMetadata>();
      const metadataByDisplayLabel = new Map<string, ChildMetadata>();
      const usedDisplayLabels = new Set<string>();

      // First pass: build maps of metadata
      visibleMetadata.forEach(meta => {
        if (!meta.id) return;

        // Ensure display label is unique
        let uniqueLabel = meta.displayLabel || meta.id;
        let counter = 1;
        while (usedDisplayLabels.has(uniqueLabel.toLowerCase())) {
          uniqueLabel = `${meta.displayLabel || meta.id}_${counter++}`;
        }
        usedDisplayLabels.add(uniqueLabel.toLowerCase());

        // Update metadata with unique label
        const uniqueMeta = {
          ...meta,
          displayLabel: uniqueLabel
        };

        // Map by field name (ID) and display label
        metadataByField.set(meta.id.toLowerCase(), uniqueMeta);
        if (uniqueLabel) {
          metadataByDisplayLabel.set(uniqueLabel.toLowerCase(), uniqueMeta);
        }
      });

      // Process and filter column definitions based on metadata
      const processedColumns: DynamicColumnDefinition[] = [];

      columnDefinitions.forEach((column, _index) => {
        try {
          if (!column?.field) {
            console.warn('Column definition missing field:', column);
            return;
          }

          const fieldKey = (column.field || '').toString().toLowerCase().trim();
          if (!fieldKey) {
            console.warn('Empty field key in column:', column);
            return;
          }

          // Try to find matching metadata by field name or display label
          let matchingMeta = metadataByField.get(fieldKey) ||
            metadataByDisplayLabel.get((column.headerName || '').toLowerCase());

          // If no direct match, try to find by similarity
          if (!matchingMeta) {
            matchingMeta = this.findMatchingMetadata(column, visibleMetadata) || undefined;
          }

          // If still no match, skip this column
          if (!matchingMeta) {
            return;
          }

          // Mark this metadata as used
          metadataByField.delete(matchingMeta.id.toLowerCase());
          if (matchingMeta.displayLabel) {
            metadataByDisplayLabel.delete(matchingMeta.displayLabel.toLowerCase());
          }

          // Create enhanced column with metadata
          const enhancedColumn: DynamicColumnDefinition = {
            ...column,
            headerName: matchingMeta.displayLabel || column.headerName || column.field,
            fieldOrder: typeof matchingMeta.fieldOrder === 'number' ? matchingMeta.fieldOrder : 9999,
            uiComponent: matchingMeta.uiComponent || column.uiComponent,
            contextId: matchingMeta.contextId,
            tenantContextId: matchingMeta.tenantContextId,
            objectLookupId: matchingMeta.objectLookupId
          };

          processedColumns.push(enhancedColumn);
        } catch (error) {
          console.error('Error processing column:', column, error);
        }
      });

      // Sort columns by fieldOrder
      const result = processedColumns.sort((a, b) => {
        const aOrder = typeof a.fieldOrder === 'number' ? a.fieldOrder : 9999;
        const bOrder = typeof b.fieldOrder === 'number' ? b.fieldOrder : 9999;
        return aOrder - bOrder;
      });

      return result;
    } catch (error) {
      console.error('Error in filterColumnsByMetadata:', error);
      return columnDefinitions; // Fallback to original columns on error
    }
  }

  // Helper method to find matching metadata for a column
  private findMatchingMetadata(
    column: DynamicColumnDefinition,
    metadataList: ChildMetadata[]
  ): ChildMetadata | null {
    const columnName = (column.field || '').toLowerCase().trim();
    const columnHeader = (column.headerName || '').toLowerCase().trim();

    // Try exact matches first
    const exactMatch = metadataList.find(meta =>
      meta.id?.toLowerCase() === columnName ||
      meta.displayLabel?.toLowerCase() === columnName ||
      meta.id?.toLowerCase() === columnHeader ||
      meta.displayLabel?.toLowerCase() === columnHeader
    );

    if (exactMatch) return exactMatch;

    // Try partial matches if no exact match
    const partialMatch = metadataList
      .map(meta => ({
        meta,
        score: Math.max(
          this.calculateStringSimilarity(meta.id?.toLowerCase() || '', columnName),
          this.calculateStringSimilarity(meta.displayLabel?.toLowerCase() || '', columnName),
          this.calculateStringSimilarity(meta.id?.toLowerCase() || '', columnHeader),
          this.calculateStringSimilarity(meta.displayLabel?.toLowerCase() || '', columnHeader)
        )
      }))
      .sort((a, b) => b.score - a.score)
      .find(match => match.score > 0.5)?.meta;

    return partialMatch || null;
  }

  // Update fetchObjectData to handle async transformApiResponse
  async fetchObjectData(objectName: string, filters?: ObjectDataFilter): Promise<ObjectDataResponse> {
    // Validate that the object name exists in navigation data
    const isValid = this.validateObjectName(objectName);

    // TEMPORARY: Bypass validation for testing
    if (!isValid) {
      // Object name validation failed but proceeding for testing
    }
    const page = filters?.page || 1;
    const pageSize = filters?.pageSize || 50;

    // Fetch metadata for column filtering (parallel with data fetch)
    const metadataPromise = this.fetchObjectMetadata(objectName);

    let lastError: Error | null = null;

    // Retry logic for API calls
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        // Construct API URL with query parameters
        const apiUrl = `${this.API_BASE_URL}/api/objectvalues/instances-view/${encodeURIComponent(objectName)}`;
        const queryParams = new URLSearchParams({
          pageNumber: page.toString(),
          pageSize: pageSize.toString()
        });
        const fullUrl = `${apiUrl}?${queryParams}`;

        const response = await fetch(fullUrl, {
          method: 'GET',
          headers: this.API_HEADERS,
          signal: AbortSignal.timeout(this.REQUEST_TIMEOUT)
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const apiData: ApiObjectDataResponse = await response.json();

        // Transform the API response to our internal format
        const { data: transformedData, columnDefinitions } = await this.transformApiResponse(apiData, objectName);

        // Filter columns by metadata if available
        const metadata = await metadataPromise;
        const filteredColumnDefinitions = metadata.length > 0
          ? this.filterColumnsByMetadata(columnDefinitions, metadata)
          : columnDefinitions;

        return {
          success: true,
          data: transformedData,
          totalCount: apiData.totalRows || 0,
          columnDefinitions: filteredColumnDefinitions,
        };

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error occurred');
        // If this is not the last attempt, wait before retrying
        if (attempt < this.MAX_RETRIES) {
          const delay = this.RETRY_DELAY * attempt; // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // All retries failed
    return {
      success: false,
      data: [],
      totalCount: 0,
      error: lastError?.message || 'Failed to fetch object data after multiple attempts'
    };
  }

  // Helper method to get type from metadata (updated for metadata)
  private getTypeFromMetadata(meta: ChildMetadata): 'string' | 'number' | 'date' | 'boolean' {
    // Use category or uiComponent to determine type
    const category = meta.category?.toLowerCase() || '';
    const uiComponent = meta.uiComponent?.toLowerCase() || '';

    // Check category first
    if (category.includes('int') || category.includes('float') || category.includes('double') || category.includes('decimal') || category.includes('number')) {
      return 'number';
    } else if (category.includes('date') || category.includes('time')) {
      return 'date';
    } else if (category.includes('bool')) {
      return 'boolean';
    }

    // Check uiComponent as fallback
    if (uiComponent.includes('number') || uiComponent.includes('currency') || uiComponent.includes('percentage') || uiComponent.includes('slider')) {
      return 'number';
    } else if (uiComponent.includes('date') || uiComponent.includes('time')) {
      return 'date';
    } else if (uiComponent.includes('checkbox') && !uiComponent.includes('group')) {
      return 'boolean';
    }

    return 'string';
  }

  // Transform API response to dynamic data format
  private async transformApiResponse(apiData: ApiObjectDataResponse, objectName: string): Promise<{ data: any[]; columnDefinitions: DynamicColumnDefinition[] }> {
    // Always generate column definitions first, whether we have data or not
    let columnDefinitions: DynamicColumnDefinition[] = [];

    try {
      // First try to get columns from columnNames
      if (apiData.columnNames && apiData.columnNames.length > 0) {
        columnDefinitions = this.generateColumnDefinitionsFromNames(apiData.columnNames);
      }
      // If no column names but we have data, generate from data
      else if (apiData.viewData && apiData.viewData.length > 0) {
        columnDefinitions = this.generateColumnDefinitionsFromData(apiData.viewData);
      }
      // If still no column definitions, try to get from metadata
      else {
        try {
          const metadata = await this.fetchObjectMetadata(objectName);
          if (metadata && metadata.length > 0) {
            // Filter out non-visible metadata and create column definitions
            const visibleMetadata = metadata.filter(meta => meta.isVisible !== false);
            columnDefinitions = visibleMetadata.map(meta => {
              const type = this.getTypeFromMetadata(meta);
              return {
                field: meta.id,
                headerName: meta.displayLabel || meta.id,
                type,
                sortable: true,
                filter: this.getFilterType(type),
                width: this.getColumnWidth(meta.id, type),
                // ADDED: Include context lookup fields from metadata
                uiComponent: meta.uiComponent,
                contextId: meta.contextId,
                tenantContextId: meta.tenantContextId,
                objectLookupId: meta.objectLookupId
              };
            });
          }
        } catch (error) {
          console.warn('Failed to fetch metadata for column definitions:', error);
          // If metadata fetch fails, return empty column definitions
          return { data: [], columnDefinitions: [] };
        }
      }

      // If still no column definitions, return empty result
      if (columnDefinitions.length === 0) {
        console.warn('No column definitions could be generated from columnNames, viewData, or metadata');
        return { data: [], columnDefinitions: [] };
      }

      // If no view data, return empty data array with column definitions
      if (!apiData.viewData || apiData.viewData.length === 0) {
        return { data: [], columnDefinitions };
      }

      // Transform viewData array of objects to AG-Grid compatible format
      const transformedData: any[] = [];

      try {
        apiData.viewData.forEach((dataObject, rowIndex) => {
          try {
            const transformedRow: any = {};

            // Process each property in the data object
            Object.keys(dataObject).forEach(propertyName => {
              const rawValue = dataObject[propertyName];
              let columnDef = columnDefinitions.find(def => def.field === propertyName);

              // If no column definition exists for this property, create one
              if (!columnDef) {
                const inferredType = this.inferDataTypeFromProperty(apiData.viewData, propertyName);
                // Ensure the type is one of the allowed values
                const columnType: 'string' | 'number' | 'date' | 'boolean' =
                  (['string', 'number', 'date', 'boolean'].includes(inferredType)
                    ? inferredType
                    : 'string') as 'string' | 'number' | 'date' | 'boolean';

                const newColumn: DynamicColumnDefinition = {
                  field: propertyName,
                  headerName: this.formatHeaderName(propertyName),
                  type: columnType,
                  sortable: true,
                  filter: this.getFilterType(columnType),
                  width: this.getColumnWidth(propertyName, columnType)
                };
                columnDefinitions.push(newColumn);
                columnDef = newColumn;
              }

              // Transform the value based on its type
              transformedRow[propertyName] = this.transformValue(rawValue, columnDef.type);
            });

            // Add row ID for AG-Grid
            transformedRow.id = `row-${rowIndex}`;
            transformedData.push(transformedRow);
          } catch (rowError) {
            console.error(`Error transforming row ${rowIndex}:`, rowError);
          }
        });
      } catch (dataError) {
        console.error('Error transforming view data:', dataError);
        return { data: [], columnDefinitions };
      }

      return { data: transformedData, columnDefinitions };
    } catch (error) {
      console.error('Error in transformApiResponse:', error);
      return { data: [], columnDefinitions: [] };
    }

    // If no view data, return empty data array with column definitions
    if (!apiData.viewData || apiData.viewData.length === 0) {
      return { data: [], columnDefinitions };
    }

    // Transform viewData array of objects to AG-Grid compatible format
    const transformedData: any[] = [];

    try {
      apiData.viewData.forEach((dataObject, rowIndex) => {
        try {
          const transformedRow: any = {};

          // Process each property in the data object
          Object.keys(dataObject).forEach(propertyName => {
            const rawValue = dataObject[propertyName];
            const columnDef = columnDefinitions.find(def => def.field === propertyName);

            // If no column definition exists for this property, create one
            if (!columnDef) {
              const newColumn: DynamicColumnDefinition = {
                field: propertyName,
                headerName: this.formatHeaderName(propertyName),
                type: 'string' as const,
                sortable: true,
                filter: true,
                width: this.getColumnWidth(propertyName, 'string')
              };
              columnDefinitions.push(newColumn);
            }


            // Transform value based on inferred data type
            transformedRow[propertyName] = this.transformValue(rawValue, columnDef?.type || 'string');
          });

          // Ensure we have a unique ID for AG-Grid
          if (!transformedRow.id && !transformedRow.Id && !transformedRow.ID) {
            transformedRow._generatedId = `${objectName}-row-${rowIndex + 1}`;
          }

          transformedData.push(transformedRow);
        } catch (rowError) {
          console.error(`Error processing row ${rowIndex}:`, rowError);
        }
      });
    } catch (error) {
      console.error('Error transforming data:', error);
      // Return empty data but keep column definitions
      return { data: [], columnDefinitions };
    }

    return { data: transformedData, columnDefinitions };
  }

  // Generate column definitions from column names only (for empty datasets)
  private generateColumnDefinitionsFromNames(columnNames: string[]): DynamicColumnDefinition[] {
    return columnNames.map(columnName => ({
      field: columnName,
      headerName: this.formatHeaderName(columnName),
      type: 'string' as const, // Default to string for empty datasets
      sortable: true,
      filter: true,
      width: this.getColumnWidth(columnName, 'string')
    }));
  }

  // Generate column definitions from actual data objects
  private generateColumnDefinitionsFromData(viewData: Array<{ [key: string]: any }>, columnNames?: string[]): DynamicColumnDefinition[] {
    if (viewData.length === 0) {
      return columnNames ? this.generateColumnDefinitionsFromNames(columnNames) : [];
    }

    // Get all unique property names from the data
    const allProperties = new Set<string>();
    viewData.forEach(obj => {
      Object.keys(obj).forEach(key => allProperties.add(key));
    });

    // Use columnNames order if provided, otherwise use alphabetical order
    const orderedProperties = columnNames && columnNames.length > 0
      ? [...columnNames, ...Array.from(allProperties).filter(prop => !columnNames.includes(prop))]
      : Array.from(allProperties).sort();

    return orderedProperties.map(propertyName => {
      // Special handling for ID fields - always treat as strings
      let dataType: 'string' | 'number' | 'date' | 'boolean';
      if (propertyName.toLowerCase().includes('id') ||
        propertyName.toLowerCase().includes('refid') ||
        propertyName.toLowerCase().includes('tenantid')) {
        dataType = 'string';
      } else {
        // Infer data type by examining sample values from this property
        dataType = this.inferDataTypeFromProperty(viewData, propertyName);
      }

      return {
        field: propertyName,
        headerName: this.formatHeaderName(propertyName),
        type: dataType,
        sortable: true,
        filter: this.getFilterType(dataType),
        width: this.getColumnWidth(propertyName, dataType)
      };
    });
  }

  // Infer data type from sample values in a specific property
  private inferDataTypeFromProperty(viewData: Array<{ [key: string]: any }>, propertyName: string): 'string' | 'number' | 'date' | 'boolean' {
    const sampleSize = Math.min(10, viewData.length); // Check first 10 rows
    const samples: any[] = [];

    // Collect non-null sample values for this property
    for (let i = 0; i < sampleSize; i++) {
      if (viewData[i] && viewData[i][propertyName] != null) {
        samples.push(viewData[i][propertyName]);
      }
    }

    if (samples.length === 0) return 'string';

    // Check for boolean values
    const booleanCount = samples.filter(val =>
      typeof val === 'boolean' ||
      (typeof val === 'string' && /^(true|false|yes|no|0|1)$/i.test(val.toString().trim()))
    ).length;

    if (booleanCount > samples.length * 0.7) return 'boolean';

    // Check for numeric values
    const numericCount = samples.filter(val => {
      if (typeof val === 'number') return true;
      if (typeof val === 'string') {
        const cleaned = val.toString().replace(/[,$%\s]/g, '');
        return !isNaN(parseFloat(cleaned)) && isFinite(parseFloat(cleaned));
      }
      return false;
    }).length;

    if (numericCount > samples.length * 0.7) return 'number';

    // Check for date values
    const dateCount = samples.filter(val => {
      if (val instanceof Date) return true;
      if (typeof val === 'string') {
        const dateStr = val.toString().trim();
        // Common date patterns
        const datePatterns = [
          /^\d{4}-\d{2}-\d{2}/, // YYYY-MM-DD
          /^\d{2}\/\d{2}\/\d{4}/, // MM/DD/YYYY
          /^\d{2}-\d{2}-\d{4}/, // MM-DD-YYYY
          /^\d{4}\/\d{2}\/\d{2}/, // YYYY/MM/DD
        ];
        return datePatterns.some(pattern => pattern.test(dateStr)) && !isNaN(Date.parse(dateStr));
      }
      return false;
    }).length;

    if (dateCount > samples.length * 0.7) return 'date';

    // Default to string
    return 'string';
  }

  // Transform value based on inferred data type
  private transformValue(rawValue: any, dataType: 'string' | 'number' | 'date' | 'boolean'): any {
    if (rawValue == null) return null;

    switch (dataType) {
      case 'number':
        if (typeof rawValue === 'number') return rawValue;
        const cleaned = rawValue.toString().replace(/[,$%\s]/g, '');
        const numValue = parseFloat(cleaned);
        // Don't convert UUIDs or very long strings to numbers
        if (isNaN(numValue) || cleaned.length > 15) {
          return rawValue.toString(); // Keep as string if conversion seems wrong
        }
        return numValue;

      case 'boolean':
        if (typeof rawValue === 'boolean') return rawValue;
        const strValue = rawValue.toString().toLowerCase().trim();
        return ['true', 'yes', '1'].includes(strValue);

      case 'date':
        if (rawValue instanceof Date) return rawValue.toISOString().split('T')[0];
        const dateValue = new Date(rawValue);
        return isNaN(dateValue.getTime()) ? rawValue.toString() : dateValue.toISOString().split('T')[0];

      default:
        return rawValue.toString();
    }
  }

  // Format column name for display
  private formatHeaderName(columnName: string): string {
    return columnName
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/[_-]/g, ' ') // Replace underscores and dashes with spaces
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
      .trim();
  }

  // Get appropriate filter type for data type
  private getFilterType(dataType: 'string' | 'number' | 'date' | 'boolean'): boolean | string {
    switch (dataType) {
      case 'number':
        return 'agNumberColumnFilter';
      case 'date':
        return 'agDateColumnFilter';
      case 'boolean':
        return 'agSetColumnFilter';
      default:
        return true; // Default text filter
    }
  }

  // Get appropriate column width based on column name and data type
  private getColumnWidth(columnName: string, dataType: 'string' | 'number' | 'date' | 'boolean'): number {
    const lowerName = columnName.toLowerCase();

    // Special width rules for common column types
    if (lowerName.includes('id')) return 100;
    if (lowerName.includes('name') || lowerName.includes('title')) return 200;
    if (lowerName.includes('description') || lowerName.includes('comment')) return 300;
    if (lowerName.includes('email')) return 200;
    if (lowerName.includes('phone')) return 150;
    if (lowerName.includes('address')) return 250;

    // Width based on data type
    switch (dataType) {
      case 'boolean':
        return 80;
      case 'number':
        return 120;
      case 'date':
        return 120;
      default:
        return 150;
    }
  }

  // Add debugging method to get API configuration
  public getApiConfig() {
    return {
      baseUrl: this.API_BASE_URL,
      headers: this.API_HEADERS,
      timeout: this.REQUEST_TIMEOUT,
      maxRetries: this.MAX_RETRIES,
      retryDelay: this.RETRY_DELAY
    };
  }

  // Test API connectivity
  public async testApiConnection(objectName: string = 'Organization'): Promise<boolean> {
    try {
      const apiUrl = `${this.API_BASE_URL}/api/objectvalues/instances-view/${encodeURIComponent(objectName)}`;
      const queryParams = new URLSearchParams({
        pageNumber: '1',
        pageSize: '1'
      });
      const fullUrl = `${apiUrl}?${queryParams}`;

      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: this.API_HEADERS,
        signal: AbortSignal.timeout(10000) // 10 second timeout for test
      });

      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // Debug method to get available object names from navigation
  public getAvailableObjectNames(): {
    fromNavigation: string[],
    navigationDataAvailable: boolean
  } {
    const fromNavigation = this.getObjectNamesFromNavigation();

    return {
      fromNavigation,
      navigationDataAvailable: fromNavigation.length > 0
    };
  }

  /**
   * Calculate string similarity between two strings (0 to 1)
   * @param str1 First string
   * @param str2 Second string
   * @returns Similarity score between 0 and 1
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    // Implementation of Levenshtein distance algorithm
    const len1 = str1.length;
    const len2 = str2.length;

    // Create a 2D array to store distances
    const dp: number[][] = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(0));

    // Initialize the first row and column
    for (let i = 0; i <= len1; i++) dp[i][0] = i;
    for (let j = 0; j <= len2; j++) dp[0][j] = j;

    // Fill the DP table
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        dp[i][j] = Math.min(
          dp[i - 1][j] + 1,          // Deletion
          dp[i][j - 1] + 1,          // Insertion
          dp[i - 1][j - 1] + cost    // Substitution
        );
      }
    }

    // Calculate similarity ratio (0 to 1)
    const maxLen = Math.max(len1, len2);
    return maxLen === 0 ? 1 : (1 - dp[len1][len2] / maxLen);
  }

  // Debug method to test object name validation
  public testObjectNameValidation(objectName: string): {
    isValid: boolean,
    availableNames: string[],
    navigationDataAvailable: boolean
  } {
    const availableNames = this.getObjectNamesFromNavigation();
    const isValid = this.validateObjectName(objectName);

    return {
      isValid,
      availableNames,
      navigationDataAvailable: availableNames.length > 0
    };
  }
}

// Create and export a singleton instance
export const objectDataService = ObjectDataService.getInstance();
