import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [react()],
    optimizeDeps: {
      exclude: ['lucide-react'],
    },
    // Define global constants for environment variables
    define: {
      __APP_ENV__: JSON.stringify(env.NODE_ENV || mode),
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '0.1.0'),
    },
    // Environment-specific build configurations
    build: {
      outDir: 'dist',
      sourcemap: mode === 'development',
      minify: mode === 'production' ? 'esbuild' : false,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            router: ['react-router-dom'],
            ui: ['@headlessui/react', 'lucide-react', 'react-select'],
          },
        },
      },
    },
    // Development server configuration
    server: {
      port: 5175,
      host: true,
      open: false,
    },
    // Preview server configuration
    preview: {
      port: 8080,
      host: true,
    },
  };
});
