/**
 * Subscription Slice
 * Redux Toolkit slice for subscription state management
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import { subscriptionApiService } from '../../services/api';
import type { 
  SubscriptionDto, 
  SubscriptionsResponse, 
  GetSubscriptionsParams,
  CreateSubscriptionRequest,
  UpdateSubscriptionRequest,
  SubscriptionSummary
} from '../../services/api';

// State interface
export interface SubscriptionState {
  // Data
  subscriptions: SubscriptionDto[];
  currentSubscription: SubscriptionDto | null;
  summary: SubscriptionSummary | null;
  
  // Pagination
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  
  // Filters
  filters: {
    searchTerm: string;
    status: string;
    tenantId: string;
    subscriptionType: string;
    isActive: boolean | null;
  };
  
  // Loading states
  loading: {
    list: boolean;
    create: boolean;
    update: boolean;
    delete: boolean;
    stats: boolean;
  };
  
  // Error states
  errors: {
    list: string | null;
    create: string | null;
    update: string | null;
    delete: string | null;
    stats: string | null;
  };
  
  // UI state
  selectedIds: string[];
  lastUpdated: string | null;
}

// Initial state
const initialState: SubscriptionState = {
  subscriptions: [],
  currentSubscription: null,
  summary: null,
  totalCount: 0,
  pageNumber: 1,
  pageSize: 10,
  totalPages: 0,
  hasNextPage: false,
  hasPreviousPage: false,
  filters: {
    searchTerm: '',
    status: 'all',
    tenantId: '',
    subscriptionType: '',
    isActive: null,
  },
  loading: {
    list: false,
    create: false,
    update: false,
    delete: false,
    stats: false,
  },
  errors: {
    list: null,
    create: null,
    update: null,
    delete: null,
    stats: null,
  },
  selectedIds: [],
  lastUpdated: null,
};

// Async thunks
export const fetchSubscriptions = createAsyncThunk(
  'subscriptions/fetchSubscriptions',
  async (params: GetSubscriptionsParams = {}, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { subscriptions: SubscriptionState };
      const { filters, pageNumber, pageSize } = state.subscriptions;

      // Build parameters from current state and override with provided params
      const apiParams: GetSubscriptionsParams = {
        pageNumber,
        pageSize,
        searchTerm: filters.searchTerm || undefined,
        status: filters.status === 'all' ? undefined : filters.status,
        tenantId: filters.tenantId || undefined, // Only include if not empty (empty = "All Tenants")
        subscriptionType: filters.subscriptionType || undefined,
        isActive: filters.isActive,
        ...params, // Allow override of any parameter
      };

      const response = await subscriptionApiService.getSubscriptions(apiParams);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch subscriptions');
    }
  }
);

export const fetchSubscriptionById = createAsyncThunk(
  'subscriptions/fetchSubscriptionById',
  async ({ id, tenant }: { id: string; tenant?: string }, { rejectWithValue }) => {
    try {
      const subscription = await subscriptionApiService.getSubscriptionById(id, tenant);
      return subscription;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch subscription');
    }
  }
);

export const createSubscription = createAsyncThunk(
  'subscriptions/createSubscription',
  async ({ data, tenant }: { data: CreateSubscriptionRequest; tenant?: string }, { rejectWithValue }) => {
    try {
      const subscription = await subscriptionApiService.createSubscription(data, tenant);
      return subscription;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create subscription');
    }
  }
);

export const updateSubscription = createAsyncThunk(
  'subscriptions/updateSubscription',
  async ({ data, tenant }: { data: UpdateSubscriptionRequest; tenant?: string }, { rejectWithValue }) => {
    try {
      const subscription = await subscriptionApiService.updateSubscription(data, tenant);
      return subscription;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update subscription');
    }
  }
);

export const deleteSubscription = createAsyncThunk(
  'subscriptions/deleteSubscription',
  async ({ id, tenant }: { id: string; tenant?: string }, { rejectWithValue }) => {
    try {
      await subscriptionApiService.deleteSubscription(id, tenant);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete subscription');
    }
  }
);

export const fetchSubscriptionStats = createAsyncThunk(
  'subscriptions/fetchSubscriptionStats',
  async (tenant?: string, { rejectWithValue }) => {
    try {
      const stats = await subscriptionApiService.getSubscriptionStats(tenant);
      return stats;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch subscription stats');
    }
  }
);

// Slice
const subscriptionSlice = createSlice({
  name: 'subscriptions',
  initialState,
  reducers: {
    // Filter actions
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.filters.searchTerm = action.payload;
      state.pageNumber = 1; // Reset to first page when search changes
    },
    setStatusFilter: (state, action: PayloadAction<string>) => {
      state.filters.status = action.payload;
      state.pageNumber = 1; // Reset to first page when filter changes
    },
    setTenantFilter: (state, action: PayloadAction<string>) => {
      state.filters.tenantId = action.payload;
      state.pageNumber = 1; // Reset to first page when filter changes
    },
    setSubscriptionTypeFilter: (state, action: PayloadAction<string>) => {
      state.filters.subscriptionType = action.payload;
      state.pageNumber = 1; // Reset to first page when filter changes
    },
    setActiveFilter: (state, action: PayloadAction<boolean | null>) => {
      state.filters.isActive = action.payload;
      state.pageNumber = 1; // Reset to first page when filter changes
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Pagination actions
    setPageNumber: (state, action: PayloadAction<number>) => {
      state.pageNumber = action.payload;
    },
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pageSize = action.payload;
      state.pageNumber = 1; // Reset to first page when page size changes
    },
    
    // Selection actions
    setSelectedIds: (state, action: PayloadAction<string[]>) => {
      state.selectedIds = action.payload;
    },
    toggleSelection: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      const index = state.selectedIds.indexOf(id);
      if (index > -1) {
        state.selectedIds.splice(index, 1);
      } else {
        state.selectedIds.push(id);
      }
    },
    selectAll: (state) => {
      state.selectedIds = state.subscriptions.map(sub => sub.id);
    },
    clearSelection: (state) => {
      state.selectedIds = [];
    },
    
    // Current subscription
    setCurrentSubscription: (state, action: PayloadAction<SubscriptionDto | null>) => {
      state.currentSubscription = action.payload;
    },
    
    // Error clearing
    clearError: (state, action: PayloadAction<keyof SubscriptionState['errors']>) => {
      state.errors[action.payload] = null;
    },
    clearAllErrors: (state) => {
      state.errors = initialState.errors;
    },
    
    // Reset state
    resetState: () => initialState,
  },
  extraReducers: (builder) => {
    // Fetch subscriptions
    builder
      .addCase(fetchSubscriptions.pending, (state) => {
        state.loading.list = true;
        state.errors.list = null;
      })
      .addCase(fetchSubscriptions.fulfilled, (state, action) => {
        state.loading.list = false;
        state.subscriptions = action.payload.data;
        state.summary = action.payload.summary || null;
        state.totalCount = action.payload.totalCount;
        state.pageNumber = action.payload.pageNumber;
        state.pageSize = action.payload.pageSize;
        state.totalPages = action.payload.totalPages;
        state.hasNextPage = action.payload.hasNextPage;
        state.hasPreviousPage = action.payload.hasPreviousPage;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchSubscriptions.rejected, (state, action) => {
        state.loading.list = false;
        state.errors.list = action.payload as string;
      });

    // Fetch subscription by ID
    builder
      .addCase(fetchSubscriptionById.fulfilled, (state, action) => {
        state.currentSubscription = action.payload;
        // Update in list if exists
        const index = state.subscriptions.findIndex(sub => sub.id === action.payload.id);
        if (index > -1) {
          state.subscriptions[index] = action.payload;
        }
      });

    // Create subscription
    builder
      .addCase(createSubscription.pending, (state) => {
        state.loading.create = true;
        state.errors.create = null;
      })
      .addCase(createSubscription.fulfilled, (state, action) => {
        state.loading.create = false;
        state.subscriptions.unshift(action.payload);
        state.totalCount += 1;
      })
      .addCase(createSubscription.rejected, (state, action) => {
        state.loading.create = false;
        state.errors.create = action.payload as string;
      });

    // Update subscription
    builder
      .addCase(updateSubscription.pending, (state) => {
        state.loading.update = true;
        state.errors.update = null;
      })
      .addCase(updateSubscription.fulfilled, (state, action) => {
        state.loading.update = false;
        const index = state.subscriptions.findIndex(sub => sub.id === action.payload.id);
        if (index > -1) {
          state.subscriptions[index] = action.payload;
        }
        if (state.currentSubscription?.id === action.payload.id) {
          state.currentSubscription = action.payload;
        }
      })
      .addCase(updateSubscription.rejected, (state, action) => {
        state.loading.update = false;
        state.errors.update = action.payload as string;
      });

    // Delete subscription
    builder
      .addCase(deleteSubscription.pending, (state) => {
        state.loading.delete = true;
        state.errors.delete = null;
      })
      .addCase(deleteSubscription.fulfilled, (state, action) => {
        state.loading.delete = false;
        state.subscriptions = state.subscriptions.filter(sub => sub.id !== action.payload);
        state.selectedIds = state.selectedIds.filter(id => id !== action.payload);
        state.totalCount = Math.max(0, state.totalCount - 1);
        if (state.currentSubscription?.id === action.payload) {
          state.currentSubscription = null;
        }
      })
      .addCase(deleteSubscription.rejected, (state, action) => {
        state.loading.delete = false;
        state.errors.delete = action.payload as string;
      });

    // Fetch stats
    builder
      .addCase(fetchSubscriptionStats.pending, (state) => {
        state.loading.stats = true;
        state.errors.stats = null;
      })
      .addCase(fetchSubscriptionStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.summary = action.payload;
      })
      .addCase(fetchSubscriptionStats.rejected, (state, action) => {
        state.loading.stats = false;
        state.errors.stats = action.payload as string;
      });
  },
});

// Export actions
export const {
  setSearchTerm,
  setStatusFilter,
  setTenantFilter,
  setSubscriptionTypeFilter,
  setActiveFilter,
  clearFilters,
  setPageNumber,
  setPageSize,
  setSelectedIds,
  toggleSelection,
  selectAll,
  clearSelection,
  setCurrentSubscription,
  clearError,
  clearAllErrors,
  resetState,
} = subscriptionSlice.actions;

// Selectors
export const selectSubscriptions = (state: { subscriptions: SubscriptionState }) => state.subscriptions.subscriptions;
export const selectCurrentSubscription = (state: { subscriptions: SubscriptionState }) => state.subscriptions.currentSubscription;
export const selectSubscriptionSummary = (state: { subscriptions: SubscriptionState }) => state.subscriptions.summary;
export const selectSubscriptionFilters = (state: { subscriptions: SubscriptionState }) => state.subscriptions.filters;
export const selectSubscriptionLoading = (state: { subscriptions: SubscriptionState }) => state.subscriptions.loading;
export const selectSubscriptionErrors = (state: { subscriptions: SubscriptionState }) => state.subscriptions.errors;
export const selectSelectedSubscriptionIds = (state: { subscriptions: SubscriptionState }) => state.subscriptions.selectedIds;
export const selectSubscriptionPagination = (state: { subscriptions: SubscriptionState }) => ({
  pageNumber: state.subscriptions.pageNumber,
  pageSize: state.subscriptions.pageSize,
  totalCount: state.subscriptions.totalCount,
  totalPages: state.subscriptions.totalPages,
  hasNextPage: state.subscriptions.hasNextPage,
  hasPreviousPage: state.subscriptions.hasPreviousPage,
});

// Export reducer
export default subscriptionSlice.reducer;
