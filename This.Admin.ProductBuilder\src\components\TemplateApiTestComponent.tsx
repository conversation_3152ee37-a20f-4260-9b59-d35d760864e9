import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ge } from 'react-bootstrap';
import { templateService, TemplateUtils } from '../services/templateService';

// Local template interface to avoid import issues
interface TemplateResponse {
  id: string;
  name: string;
  version: string;
  stage: string;
  templateJson: any;
  createdAt: string;
  createdBy: string;
  publishedAt?: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface ApiTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export const TemplateApiTestComponent: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<ApiTestResult[]>([]);
  const [liveTemplates, setLiveTemplates] = useState<TemplateResponse[]>([]);

  const runTemplateApiTest = async () => {
    setIsLoading(true);
    setTestResults([]);
    setLiveTemplates([]);
    const results: ApiTestResult[] = [];

    try {
      results.push({ success: true, message: 'Starting Template API tests...' });
      setTestResults([...results]);

      // Test 1: Get all templates
      console.log('📡 Test 1: Get all templates');
      const allTemplatesResponse = await templateService.getTemplates({
        pageSize: 100,
        pageNumber: 1
      });

      results.push({
        success: true,
        message: 'All templates API: SUCCESS',
        data: {
          totalCount: allTemplatesResponse.totalCount,
          templatesCount: allTemplatesResponse.data.length,
          stages: [...new Set(allTemplatesResponse.data.map(t => t.stage))]
        }
      });
      setTestResults([...results]);

      // Test 2: Get live templates specifically
      console.log('📡 Test 2: Get live templates');
      const liveTemplatesResponse = await templateService.getLiveTemplates();

      results.push({
        success: true,
        message: 'Live templates API: SUCCESS',
        data: {
          liveTemplatesCount: liveTemplatesResponse.length,
          templateNames: liveTemplatesResponse.map(t => t.name)
        }
      });
      setTestResults([...results]);
      setLiveTemplates(liveTemplatesResponse);

      // Test 3: Test template sorting
      console.log('📡 Test 3: Test template sorting');
      const sortedTemplates = TemplateUtils.sortTemplatesForDropdown(liveTemplatesResponse);
      
      results.push({
        success: true,
        message: 'Template sorting: SUCCESS',
        data: {
          originalOrder: liveTemplatesResponse.map(t => `${t.name} v${t.version}`),
          sortedOrder: sortedTemplates.map(t => `${t.name} v${t.version}`)
        }
      });
      setTestResults([...results]);

      // Test 4: Test template formatting
      console.log('📡 Test 4: Test template formatting');
      const formattedTemplates = liveTemplatesResponse.map(template => ({
        id: template.id,
        original: template,
        formatted: TemplateUtils.formatTemplateForDropdown(template)
      }));

      results.push({
        success: true,
        message: 'Template formatting: SUCCESS',
        data: {
          formattedExamples: formattedTemplates.slice(0, 3).map(t => t.formatted)
        }
      });
      setTestResults([...results]);

      // Test 5: Test stage filtering
      console.log('📡 Test 5: Test stage filtering');
      const stageFilterResponse = await templateService.getTemplatesByStage('live');
      
      results.push({
        success: true,
        message: 'Stage filtering: SUCCESS',
        data: {
          filteredCount: stageFilterResponse.length,
          allLive: stageFilterResponse.every(t => t.stage.toLowerCase() === 'live')
        }
      });
      setTestResults([...results]);

    } catch (error) {
      results.push({
        success: false,
        message: 'Template API test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      setTestResults([...results]);
    }

    setIsLoading(false);
  };
};
