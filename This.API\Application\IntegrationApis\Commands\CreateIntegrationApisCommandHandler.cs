using Application.IntegrationApis.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationApis.Commands;

/// <summary>
/// Create multiple integration APIs command handler
/// </summary>
public class CreateIntegrationApisCommandHandler : IRequestHandler<CreateIntegrationApisCommand, Result<List<ViewIntegrationApiDto>>>
{
    private readonly IRepositoryWithEvents<IntegrationApi> _integrationApiRepository;
    private readonly IReadRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateIntegrationApisCommandHandler(
        IRepositoryWithEvents<IntegrationApi> integrationApiRepository,
        IReadRepository<Product> productRepository)
    {
        _integrationApiRepository = integrationApiRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<List<ViewIntegrationApiDto>>> Handle(CreateIntegrationApisCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var errors = new List<string>();
            var createdIntegrationApis = new List<IntegrationApi>();

            // Validate all requests first
            var productIds = request.IntegrationApis.Select(ia => ia.ProductId).Distinct().ToList();
            var allProducts = await _productRepository.ListAsync(cancellationToken);
            var products = allProducts.Where(p => productIds.Contains(p.Id)).ToList();
            var existingProductIds = products.Select(p => p.Id).ToHashSet();

            foreach (var integrationApiRequest in request.IntegrationApis)
            {
                // Validate product exists
                if (!existingProductIds.Contains(integrationApiRequest.ProductId))
                {
                    errors.Add($"Product with ID {integrationApiRequest.ProductId} not found for integration API '{integrationApiRequest.Name}'.");
                    continue;
                }

                // Check for duplicate names within the request
                var duplicateInRequest = request.IntegrationApis
                    .Where(ia => ia.ProductId == integrationApiRequest.ProductId && ia.Name == integrationApiRequest.Name)
                    .Count() > 1;

                if (duplicateInRequest)
                {
                    errors.Add($"Duplicate integration API name '{integrationApiRequest.Name}' found in request for product {integrationApiRequest.ProductId}.");
                    continue;
                }

                // Check if integration API with same name already exists for this product
                var allIntegrationApis = await _integrationApiRepository.ListAsync(cancellationToken);
                var existingApi = allIntegrationApis.FirstOrDefault(ia =>
                    ia.ProductId == integrationApiRequest.ProductId &&
                    ia.Name == integrationApiRequest.Name &&
                    !ia.IsDeleted);

                if (existingApi != null)
                {
                    errors.Add($"Integration API with name '{integrationApiRequest.Name}' already exists for product {integrationApiRequest.ProductId}.");
                    continue;
                }

                // Create integration API
                var integrationApi = new IntegrationApi
                {
                    Id = Guid.NewGuid(),
                    ProductId = integrationApiRequest.ProductId,
                    Name = integrationApiRequest.Name,
                    EndpointUrl = integrationApiRequest.EndpointUrl,
                    Schema = integrationApiRequest.Schema,
                    IsActive = integrationApiRequest.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    ModifiedAt = DateTime.UtcNow
                };

                createdIntegrationApis.Add(integrationApi);
            }

            // If there are validation errors, return them
            if (errors.Any() && !createdIntegrationApis.Any())
            {
                return Result<List<ViewIntegrationApiDto>>.Failure(string.Join("; ", errors));
            }

            // Save all valid integration APIs
            var savedIntegrationApis = await _integrationApiRepository.AddRangeAsync(createdIntegrationApis, cancellationToken);

            // Load with products for DTO mapping
            var integrationApiDtos = new List<ViewIntegrationApiDto>();
            foreach (var integrationApi in savedIntegrationApis)
            {
                var product = await _productRepository.GetByIdAsync(integrationApi.ProductId, cancellationToken);
                var dto = integrationApi.Adapt<ViewIntegrationApiDto>();
                dto.ProductName = product?.Name ?? string.Empty;
                integrationApiDtos.Add(dto);
            }

            var result = Result<List<ViewIntegrationApiDto>>.Success(integrationApiDtos);
            if (errors.Any())
            {
                var warningMessage = string.Join("; ", errors.Select(e => $"Warning: {e}"));
                result.Message = warningMessage;
            }

            return result;
        }
        catch (Exception ex)
        {
            return Result<List<ViewIntegrationApiDto>>.Failure($"Failed to create integration APIs: {ex.Message}");
        }
    }
}
