using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Commands;

/// <summary>
/// Delete integration command
/// </summary>
public class DeleteIntegrationCommand : IRequest<Result<bool>>
{
    /// <summary>
    /// Integration ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteIntegrationCommand(Guid id)
    {
        Id = id;
    }
}
