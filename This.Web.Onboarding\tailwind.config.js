/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['D<PERSON> Sans', 'Lexend Deca', 'sans-serif'],
        lexend: ['Lexend Deca', 'sans-serif'],
        dm: ['DM Sans', 'sans-serif'],
      },
      colors: {
        primary: {
          50: '#e5f5f2',    // $light-green-100
          100: '#d3f3ec',   // $light-green-300
          200: '#c7e8e1',   // $light-green-200
          300: '#b3dfd6',   // $accent-green-40
          400: '#76ccb9',   // $light-green-400
          500: '#50bea7',   // $accent-green (main color)
          600: '#5dc3ad',   // $light-green-500
          700: '#399b86',   // $accent-green-300
          800: '#348d7a',   // $accent-green-200
          900: '#28544B',   // $dark-green-100
          950: '#1a3d36',   // Darker variant for contrast
        },
        // Additional green variants for specific use cases
        'light-green': {
          100: '#e5f5f2',
          200: '#c7e8e1',
          300: '#d3f3ec',
          400: '#76ccb9',
          500: '#5dc3ad',
          600: '#38dab8',
        },
        'accent-green': {
          DEFAULT: '#50bea7',
          40: '#b3dfd6',
          60: '#50bea7b3',
          100: '#50bfa8',
          200: '#348d7a',
          300: '#399b86',
        },
        'dark-green': {
          DEFAULT: '#50bea7e5',
          100: '#28544B',
        },
      },
      boxShadow: {
        card: '0 2px 8px rgba(0, 0, 0, 0.05)',
      },
      fontSize: {
        xxs: '0.625rem', // 10px - smaller than xs (0.75rem)
      },
    },
  },
  plugins: [],
  corePlugins: {
    fontSize: true, // Ensure font size utilities are generated
  },
};