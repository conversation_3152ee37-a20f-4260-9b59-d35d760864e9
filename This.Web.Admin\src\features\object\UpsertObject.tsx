import { Button } from '@/shared/components/atoms/Button/Button';
import type { FormFieldConfig } from '@/shared/types/forms';
import { <PERSON><PERSON><PERSON><PERSON>cle, Loader2, Refresh<PERSON><PERSON>, Save, X } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useSimpleObjectBreadcrumb } from '../../hooks/useObjectBreadcrumb';
import { metadataService } from '../../services/metadataService';
import type { DynamicColumnDefinition } from '../../services/objectDataService';
import { objectValuesService } from '../../services/objectValuesService';

// Import form input components for dynamic rendering
import ContextLookupDropdown from '@/features/form/ContextLookupDropdown';
import { componentMappingService } from '../../services/componentMappingService';

// API metadata structure interfaces (updated for unified metadata)
// Note: ApiDataType interface removed - now using UnifiedMetadata directly

// Note: ApiMetadataLink interface removed - now using unified metadata structure

// Note: Interfaces removed - now using UnifiedMetadata and UnifiedMetadataWithValues directly


interface UpsertObjectProps { }

// Type for notification system
type NotificationType = 'success' | 'error' | 'warning' | 'info';

export const UpsertObject: React.FC<UpsertObjectProps> = () => {
  const { objectName } = useParams<{ objectName: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();

  // Get the RefId from search params for edit mode
  const refId = searchParams.get('refId');
  const isEditMode = !!refId;

  // Get row data from navigation state (passed from ObjectDetailPage grid)
  const navigationRowData = location.state?.rowData;
  const editMode = location.state?.mode;

  // State management
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [lastSavedData, setLastSavedData] = useState<Record<string, any>>({});
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [formFields, setFormFields] = useState<FormFieldConfig[]>([]);

  // Validation state management
  const [fieldValidationErrors, setFieldValidationErrors] = useState<Record<string, string[]>>({});
  const [formValidationErrors, setFormValidationErrors] = useState<string[]>([]);

  // Dynamic height calculation state
  const [availableHeight, setAvailableHeight] = useState<number>(window.innerHeight);

  // Get breadcrumb navigation data
  useSimpleObjectBreadcrumb();

  // Validation helper functions
  const hasValidationErrors = () => {
    // Check for field-level validation errors
    const hasFieldErrors = Object.keys(fieldValidationErrors).some(
      fieldName => fieldValidationErrors[fieldName].length > 0
    );

    // Check for form-level validation errors
    const hasFormErrors = formValidationErrors.length > 0;

    return hasFieldErrors || hasFormErrors;
  };

  const getRequiredFieldsWithErrors = () => {
    const requiredFields = formFields.filter(field => field.isRequired);
    const fieldsWithErrors: string[] = [];

    requiredFields.forEach(field => {
      const fieldErrors = fieldValidationErrors[field.name] || [];
      const hasRequiredError = fieldErrors.some(error =>
        error.toLowerCase().includes('required') ||
        error.toLowerCase().includes('is required')
      );

      // Also check if required field is empty
      const fieldValue = formData[field.name];
      const isEmpty = fieldValue === undefined || fieldValue === null ||
        (typeof fieldValue === 'string' && fieldValue.trim() === '') ||
        (Array.isArray(fieldValue) && fieldValue.length === 0);

      if (hasRequiredError || isEmpty) {
        fieldsWithErrors.push(field.displayLabel || field.name);
      }
    });

    return fieldsWithErrors;
  };

  const validateForm = () => {
    const errors: string[] = [];
    const requiredFieldsWithErrors = getRequiredFieldsWithErrors();

    if (requiredFieldsWithErrors.length > 0) {
      errors.push(`Please fill in all required fields: ${requiredFieldsWithErrors.join(', ')}`);
    }

    // Check for any field validation errors
    const fieldErrorCount = Object.keys(fieldValidationErrors).reduce(
      (count, fieldName) => count + fieldValidationErrors[fieldName].length, 0
    );

    if (fieldErrorCount > 0) {
      errors.push('Please fix all validation errors before saving');
    }

    setFormValidationErrors(errors);
    return errors.length === 0;
  };

  const isFormValid = !hasValidationErrors();
  const canSave = isFormValid && hasUnsavedChanges && !isSaving;

  // Calculate available height accounting for header/navigation elements
  useEffect(() => {
    const calculateAvailableHeight = () => {
      // Common header selectors to check for
      const headerSelectors = [
        'header',
        'nav',
        '.header',
        '.navbar',
        '[role="banner"]',
        '.app-header',
        '.main-header',
        '.top-nav',
        '.navigation'
      ];

      let totalHeaderHeight = 0;

      // Find and measure all header elements
      headerSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          const rect = element.getBoundingClientRect();
          // Only count elements that are visible and positioned at the top
          if (rect.height > 0 && rect.top >= 0 && rect.top < 100) {
            totalHeaderHeight = Math.max(totalHeaderHeight, rect.bottom);
          }
        });
      });

      // Fallback: check for any fixed positioned elements at the top
      if (totalHeaderHeight === 0) {
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
          const styles = window.getComputedStyle(element);
          const rect = element.getBoundingClientRect();

          if (
            (styles.position === 'fixed' || styles.position === 'sticky') &&
            rect.top >= 0 && rect.top < 50 && rect.height > 0 && rect.height < 200
          ) {
            totalHeaderHeight = Math.max(totalHeaderHeight, rect.bottom);
          }
        });
      }

      const newAvailableHeight = window.innerHeight - totalHeaderHeight;

      setAvailableHeight(newAvailableHeight);
    };

    // Calculate on mount
    calculateAvailableHeight();

    // Recalculate on window resize
    const handleResize = () => {
      calculateAvailableHeight();
    };

    window.addEventListener('resize', handleResize);

    // Also recalculate after a short delay to catch dynamically loaded headers
    const timeoutId = setTimeout(calculateAvailableHeight, 500);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timeoutId);
    };
  }, []);

  // Simple notification logger
  const addNotification = useCallback((_type: NotificationType, _title: string, _message: string) => {
    // Notification system placeholder
  }, []);

  // Handle cancel button click
  const handleCancel = useCallback(() => {
    if (hasUnsavedChanges && !window.confirm('You have unsaved changes. Are you sure you want to leave?')) {
      return;
    }
    navigate(-1);
  }, [hasUnsavedChanges, navigate]);

  // Handle save operation
  const handleSave = useCallback(async () => {
    if (!objectName) {
      addNotification('error', 'Error', 'Object name is missing');
      throw new Error('Object name is missing');
    }

    // Validate form before saving
    const isValid = validateForm();
    if (!isValid) {
      addNotification('error', 'Validation Error', 'Please fix all validation errors before saving');
      return; // Don't proceed with save
    }

    setIsSaving(true);
    try {
      if (isEditMode && refId) {
        await objectValuesService.updateObject(objectName, refId, formData);
        addNotification('success', 'Success', 'Record updated successfully');
      } else {
        await objectValuesService.createObject(objectName, formData);
        addNotification('success', 'Success', 'Record created successfully');
        navigate(`/objects/${objectName}`);
      }
      setLastSavedData({ ...formData });
      setHasUnsavedChanges(false);
      // Clear validation errors on successful save
      setFieldValidationErrors({});
      setFormValidationErrors([]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      addNotification('error', 'Error', errorMessage);
      throw err;
    } finally {
      setIsSaving(false);
    }
  }, [formData, isEditMode, objectName, refId, navigate, addNotification, validateForm]);

  // Track unsaved changes
  useEffect(() => {
    if (Object.keys(lastSavedData).length > 0) {
      const hasChanges = JSON.stringify(formData) !== JSON.stringify(lastSavedData);
      setHasUnsavedChanges(hasChanges);
    }
  }, [formData, lastSavedData]);

  useEffect(() => {
    const checkForUnsavedChanges = () => {
      if (Object.keys(lastSavedData).length > 0) {
        const hasChanges = JSON.stringify(formData) !== JSON.stringify(lastSavedData);
        setHasUnsavedChanges(hasChanges);
      }
    };
    checkForUnsavedChanges();
  }, [formData, lastSavedData]);

  // Validate form when form data or field validation errors change
  useEffect(() => {
    if (formFields.length > 0) {
      validateForm();
    }
  }, [formData, fieldValidationErrors, formFields]);

  // Load form metadata and data
  useEffect(() => {

    // LEGACY: Map DynamicColumnDefinition to FormFieldConfig (for backward compatibility)
    const mapToFormFieldConfig = (columnDef: DynamicColumnDefinition): FormFieldConfig => {
      // Determine field type and UI component
      const fieldType = columnDef.type || 'string';
      const componentType = columnDef.uiComponent ||
        (fieldType === 'date' ? 'ThisDate' :
          fieldType === 'boolean' ? 'ThisCheckbox' : 'ThisText');

      // Common field configuration with defaults
      const baseFieldConfig = {
        name: columnDef.field || '',
        displayLabel: columnDef.headerName || 'Unnamed Field',
        helpText: columnDef.helpText ?? null,
        fieldOrder: columnDef.fieldOrder ?? null,
        isVisible: columnDef.isVisible ?? true,
        isReadonly: columnDef.isReadonly ?? columnDef.editable === false ?? false,
        isRequired: columnDef.isRequired ?? columnDef.required ?? false,
        placeholder: columnDef.placeholder ?? `Enter ${(columnDef.headerName || 'value').toLowerCase()}`,
        // Backward compatibility
        label: columnDef.label ?? columnDef.headerName ?? 'Unnamed Field'
      };

      // Validation and error messages - use only what comes from API metadata
      const validationConfig = {
        validationPattern: columnDef.validationPattern ?? null,
        minLength: columnDef.minLength ?? null,
        maxLength: columnDef.maxLength ?? null,
        minValue: columnDef.minValue ?? null,
        maxValue: columnDef.maxValue ?? null,
        requiredErrorMessage: columnDef.requiredErrorMessage ?? null, // No manual defaults
        patternErrorMessage: columnDef.patternErrorMessage ?? null,
        minLengthErrorMessage: columnDef.minLengthErrorMessage ?? null,
        maxLengthErrorMessage: columnDef.maxLengthErrorMessage ?? null,
        minValueErrorMessage: columnDef.minValueErrorMessage ?? null,
        maxValueErrorMessage: columnDef.maxValueErrorMessage ?? null,
      };

      // File and options configuration
      const fileAndOptionsConfig = {
        defaultOptions: columnDef.defaultOptions ?? null,
        maxSelections: columnDef.maxSelections ?? null,
        allowedFileTypes: columnDef.allowedFileTypes ?? null,
        maxFileSize: columnDef.maxFileSize ?? null,
        fileTypeErrorMessage: columnDef.fileTypeErrorMessage ?? null,
        maxFileSizeBytes: columnDef.maxFileSizeBytes ?? null,
        errorMessage: null,
      };

      // Combine all configurations (updated for unified metadata)
      const baseConfig: FormFieldConfig = {
        id: columnDef.field || `field-${Math.random().toString(36).substring(2, 11)}`,
        ...baseFieldConfig,
        ...validationConfig,
        ...fileAndOptionsConfig,
        inputType: fieldType === 'number' ? 'number' : 'text',
        inputMask: null,
        allowsMultiple: false,
        allowsCustomOptions: false,
        // REMOVED: Old properties no longer used
        // overrideLookupType, overrideMasterContextId, overrideTenantContextId, overrideObjectLookupId
        // Unified metadata properties (replaces separate dataType and metadataLink)
        category: 'Input',
        decimalPlaces: fieldType === 'number' ? 2 : null,
        stepValue: fieldType === 'number' ? 1 : null,
        htmlAttributes: null,
        metadataLinkId: columnDef.field || `generated-${Math.random().toString(36).substring(2, 11)}`,
        isUnique: columnDef.metadataLink?.isUnique ?? false,
        isVisibleInList: columnDef.metadataLink?.shouldVisibleInList ?? true,
        isVisibleInEdit: columnDef.metadataLink?.shouldVisibleInEdit ?? true,
        isVisibleInCreate: columnDef.metadataLink?.shouldVisibleInCreate ?? true,
        isVisibleInView: columnDef.metadataLink?.shouldVisibleInView ?? true,
        isCalculated: columnDef.metadataLink?.isCalculate ?? false,
        contextId: columnDef.contextId || undefined,
        tenantContextId: columnDef.tenantContextId || undefined,
        objectLookupId: columnDef.objectLookupId || undefined,
        // Backward compatibility with legacy props
        uiComponent: componentType
      };

      // Copy any additional properties from columnDef that aren't explicitly handled
      const additionalProps = Object.entries(columnDef).reduce((acc, [key, value]) => {
        if (!(key in baseConfig) && value !== undefined) {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      // Map additional properties if they exist in the column definition
      Object.keys(columnDef).forEach(key => {
        if (!(key in baseConfig) && key !== 'field' && key !== 'headerName' && key !== 'editable' && key !== 'required' && key !== 'width') {
          (baseConfig as any)[key] = (columnDef as any)[key];
        }
      });

      return {
        ...baseConfig,
        ...additionalProps
      };
    };

    const loadFormData = async () => {
      if (!objectName) return;

      // OPTIMIZATION: Skip loading state for immediate data binding when using navigation row data
      const hasImmediateData = isEditMode && refId && navigationRowData && editMode === 'edit';

      if (!hasImmediateData) {
        setLoading(true);
      }
      setError(null);

      try {
        // FIRST: Try to get cached metadata from ObjectDetailPage (localStorage)
        const cachedMetadataKey = `object-metadata-${objectName}`;
        const cachedMetadataStr = localStorage.getItem(cachedMetadataKey);

        let response: any = null;

        if (cachedMetadataStr) {
          try {
            const cachedMetadata = JSON.parse(cachedMetadataStr);

            // Convert cached data to the expected response format
            response = {
              success: true,
              metadata: {
                columnDefinitions: cachedMetadata.columnDefinitions
              },
              timestamp: cachedMetadata.timestamp,
              fromCache: true
            };
          } catch (parseError) {
            console.warn('Failed to parse cached metadata, falling back to API:', parseError);
          }
        }

        // FALLBACK: If no cached data, use metadataService API
        if (!response) {
          response = await metadataService.getObjectMetadata(objectName);
        }

        // Handle the response format
        let fields: FormFieldConfig[] = [];

        if (response && response.metadata) {
          // Use columnDefinitions from cached data or metadataService
          if (Array.isArray(response.metadata.columnDefinitions)) {
            fields = response.metadata.columnDefinitions.map(mapToFormFieldConfig);
          } else if (Array.isArray(response.metadata)) {
            // If metadata is directly an array, assume it's already FormFieldConfig
            fields = response.metadata;
          }
        } else if (Array.isArray(response)) {
          // If response is directly an array, assume it's already FormFieldConfig
          fields = response;
        }

        // Sort fields by fieldOrder if available, otherwise keep original order
        fields = fields.sort((a, b) => {
          const orderA = a.fieldOrder !== undefined && a.fieldOrder !== null ? a.fieldOrder : Number.MAX_SAFE_INTEGER;
          const orderB = b.fieldOrder !== undefined && b.fieldOrder !== null ? b.fieldOrder : Number.MAX_SAFE_INTEGER;
          return orderA - orderB;
        });

        setFormFields(fields);

        // Initialize form data with default values
        const defaultData: Record<string, any> = {};
        fields.forEach(field => {
          if (field.defaultValue !== undefined) {
            defaultData[field.name] = field.defaultValue;
          }
        });

        // OPTIMIZATION: Load existing data if in edit mode
        if (isEditMode && refId) {
          // OPTIMIZATION: Use navigation row data if available (passed from ObjectDetailPage grid)
          // This eliminates the API call and improves performance by using existing data
          if (navigationRowData && editMode === 'edit') {

            // Transform row data to match form field structure if needed
            // Most ag-grid row data should map directly to form fields since they come from the same API
            // The row data structure should already match the expected form field names
            const transformedRowData = { ...navigationRowData };

            // Optional: Add any specific field transformations here if needed
            // For example, if ag-grid uses different field names than the form:
            // if (transformedRowData.someGridField) {
            //   transformedRowData.someFormField = transformedRowData.someGridField;
            //   delete transformedRowData.someGridField;
            // }

            // Merge navigation data with default values (navigation data takes precedence)
            const mergedData = { ...defaultData, ...transformedRowData };
            setFormData(mergedData);
            setLastSavedData(mergedData);
          } else {
            // FALLBACK: Use API call when no navigation data is available
            // This handles cases like direct URL access or page refresh

            const data = await objectValuesService.getObjectInstance(objectName, refId);
            // Merge with default values
            const mergedData = { ...defaultData, ...data };
            setFormData(mergedData);
            setLastSavedData(mergedData);
          }
        } else {
          // Create mode - use default values
          setFormData(defaultData);
          setLastSavedData(defaultData);
        }
      } catch (err) {
        console.error('Error loading form data:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to load form data';
        setError(errorMessage);

        // Remove the duration parameter as addNotification only accepts 3 arguments
        addNotification('error', 'Error', errorMessage);
      } finally {
        setLoading(false);
      }
    };

    loadFormData();
  }, [objectName, isEditMode, refId, navigationRowData, editMode]);

  const handleFieldChange = useCallback((fieldName: string, value: any) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [fieldName]: value
      };

      // Check if the value has actually changed
      const hasChanged = JSON.stringify(prev[fieldName]) !== JSON.stringify(value);
      if (hasChanged) {
        setHasUnsavedChanges(true);
      }

      return newData;
    });
  }, []);

  // Handle validation errors from form components
  const handleFieldValidation = useCallback((fieldName: string, errors: string[]) => {
    setFieldValidationErrors(prev => ({
      ...prev,
      [fieldName]: errors
    }));
  }, []);

  // Handle form submission with validation
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form before submission
    const isValid = validateForm();
    if (!isValid) {
      console.warn('Form submission blocked due to validation errors');
      return; // Block form submission
    }

    try {
      await handleSave();
    } catch (err) {
      console.error('Error saving record:', err);
      // Error is already handled in handleSave
    }
  };



  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        // Standard for most browsers
        const message = 'You have unsaved changes. Are you sure you want to leave?';
        (e as any).returnValue = message;
        // For older browsers
        return message;
      }
      return undefined;
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // Simplified component resolution using componentMappingService
  const getFormComponent = (uiComponentName: string) => {
    // Use exact API component name without transformations
    // The service now handles fallback internally
    return componentMappingService.getComponent(uiComponentName);
  };

  const renderFormField = (field: FormFieldConfig) => {
    const fieldValue = formData[field.name] ?? field.defaultValue ?? '';

    // Check if this field should use Context/TenantContext lookup
    const hasContextLookup = field.contextId || field.tenantContextId;

    // Priority check: Use ContextLookupDropdown if contextId or tenantContextId are present
    if (hasContextLookup) {

      // Handle validation for ContextLookupDropdown manually since it doesn't have onValidation callback
      const validateContextLookup = (value: any) => {
        const errors: string[] = [];
        if (field.isRequired && (!value || value === '')) {
          errors.push(field.requiredErrorMessage || `${field.displayLabel} is required`);
        }
        handleFieldValidation(field.name, errors);
      };

      // Validate on mount and value change
      useEffect(() => {
        validateContextLookup(fieldValue);
      }, [fieldValue]);

      return (
        <ContextLookupDropdown
          key={field.name}
          id={field.name}
          label={field.displayLabel}
          value={fieldValue}
          onChange={(value: string) => {
            handleFieldChange(field.name, value);
            validateContextLookup(value);
          }}
          contextId={field.contextId}
          tenantContextId={field.tenantContextId}
          includeInactiveLookups={false}
          required={field.isRequired || false}
          disabled={false}
          readOnly={field.isReadonly}
          placeholder={field.placeholder || 'Select an option...'}
          helpText={field.helpText || undefined}
          requiredErrorMessage={field.requiredErrorMessage || undefined}
          errorMessage={field.errorMessage || undefined}
          allowsMultiple={field.allowsMultiple || false}
          maxSelections={field.maxSelections || undefined}
          allowsCustomOptions={field.allowsCustomOptions || false}
        />
      );
    }

    // Use componentMappingService for simplified component resolution and prop generation
    const fieldUiComponent = field.uiComponent || 'ThisText';
    const FormComponent = getFormComponent(fieldUiComponent);

    // Generate all props using componentMappingService for consistency
    const componentProps = componentMappingService.generateComponentProps(
      field,
      fieldValue,
      (value: any) => handleFieldChange(field.name, value),
      (errors: string[]) => handleFieldValidation(field.name, errors)
    );

    // Render the dynamic component with generated props
    return (
      <FormComponent
        key={field.name}
        {...componentProps}
      />
    );
  };

  // Debug helper function
  const isDevelopment = import.meta.env.DEV;

  if (loading) {
    return (
      <div className="h-full overflow-hidden bg-background">
        <div
          className="relative flex items-center justify-center"
          style={{
            height: `${availableHeight}px`
          }}
        >
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading form...</p>

            {/* Debug info - only shown in development */}
            {isDevelopment && (
              <div className="mt-8 p-4 bg-gray-50 rounded-lg max-w-4xl">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Debug Info</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h4 className="text-xs font-medium text-gray-500 mb-1">Form Data</h4>
                    <pre className="text-xs p-2 bg-white rounded border border-gray-200 overflow-auto max-h-40">
                      {JSON.stringify(formData, null, 2)}
                    </pre>
                  </div>
                  <div>
                    <h4 className="text-xs font-medium text-gray-500 mb-1">Form Fields</h4>
                    <pre className="text-xs p-2 bg-white rounded border border-gray-200 overflow-auto max-h-40">
                      {JSON.stringify(formFields.map(f => ({ name: f.name, required: f.isRequired, uiComponent: f.uiComponent })), null, 2)}
                    </pre>
                  </div>
                  <div>
                    <h4 className="text-xs font-medium text-gray-500 mb-1">Validation Status</h4>
                    <div className="text-xs p-2 bg-white rounded border border-gray-200 space-y-1">
                      <div className={`font-medium ${isFormValid ? 'text-green-600' : 'text-red-600'}`}>
                        Form Valid: {isFormValid ? 'Yes' : 'No'}
                      </div>
                      <div className="text-gray-600">
                        Can Save: {canSave ? 'Yes' : 'No'}
                      </div>
                      <div className="text-gray-600">
                        Has Changes: {hasUnsavedChanges ? 'Yes' : 'No'}
                      </div>
                      <div className="text-gray-600">
                        Field Errors: {Object.keys(fieldValidationErrors).filter(k => fieldValidationErrors[k].length > 0).length}
                      </div>
                      <div className="text-gray-600">
                        Form Errors: {formValidationErrors.length}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full overflow-hidden bg-background">
        <div
          className="relative flex items-center justify-center p-4"
          style={{
            height: `${availableHeight}px`
          }}
        >
          <div className="bg-red-50 border-l-4 border-red-400 p-6 rounded-lg max-w-md w-full">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 mb-2">Error Loading Form</h3>
                <p className="text-sm text-red-700 mb-4">{error}</p>
                <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                    className="w-full sm:w-auto"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Retry
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={handleCancel}
                    className="w-full sm:w-auto"
                  >
                    <X className="mr-2 h-4 w-4" />
                    Go Back
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Determine if form has action buttons (for conditional height management)
  const hasActionButtons = formFields.length > 0;

  // Calculate button container height (estimate)
  const buttonContainerHeight = hasActionButtons ? 80 : 0; // Approximate height including padding
  const formContentHeight = availableHeight - buttonContainerHeight;

  return (
    <div className="h-full overflow-hidden bg-background">
      <div
        className="relative flex flex-col"
        style={{
          height: `${availableHeight}px`
        }}
      >
        <form onSubmit={handleSubmit} className="flex-1 flex flex-col min-h-0 relative">
          {/* Scrollable Form Content Area - Single scroll context */}
          <div
            className="flex-1 overflow-y-auto overflow-x-hidden"
            style={{
              height: hasActionButtons ? `${formContentHeight}px` : '100%'
            }}
          >
            <div className="px-4 py-5 sm:p-6">
              {/* Form Validation Errors Display */}
              {formValidationErrors.length > 0 && (
                <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4 rounded-lg">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertCircle className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800 mb-2">Validation Errors</h3>
                      <ul className="text-sm text-red-700 space-y-1">
                        {formValidationErrors.map((error, index) => (
                          <li key={index}>• {error}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {formFields.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No form fields available for this object.</p>
                  {objectName && (
                    <p className="mt-2 text-sm text-gray-400">
                      No form fields are configured for the '{objectName}' object.
                    </p>
                  )}
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {formFields.map((field) => (
                    <div key={field.name} className="col-span-full md:col-span-1">
                      {renderFormField(field)}
                    </div>
                  ))}
                </div>
              )}
              {/* Add bottom padding to prevent content from being hidden behind sticky buttons */}
              {hasActionButtons && <div className="h-4" />}
            </div>
          </div>

          {/* Sticky Action Buttons Container - Positioned within form container */}
          {hasActionButtons && (
            <div className="sticky bottom-0 left-0 right-0 border-t border-border bg-background/95 backdrop-blur-sm shadow-lg z-10 flex-shrink-0">
              <div className="px-4 py-4 sm:px-6">
                <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2 sm:justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isSaving}
                    className="w-full sm:w-auto"
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={!canSave}
                    className="w-full sm:w-auto"
                    title={!isFormValid ? 'Please fix validation errors before saving' :
                      !hasUnsavedChanges ? 'No changes to save' : ''}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        {isEditMode ? 'Update' : 'Create'}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};
