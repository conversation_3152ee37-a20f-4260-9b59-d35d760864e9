/**
 * UI Slice
 * Redux Toolkit slice for UI state management
 */

import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';

// Toast notification interface
export interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: string;
}

// Modal state interface
export interface ModalState {
  isOpen: boolean;
  type: string | null;
  data: any;
  size?: 'sm' | 'lg' | 'xl';
}

// Loading overlay interface
export interface LoadingOverlay {
  isVisible: boolean;
  message: string;
  progress?: number;
}

// State interface
export interface UIState {
  // Global loading
  globalLoading: boolean;
  loadingMessage: string;
  
  // Sidebar
  sidebarCollapsed: boolean;
  sidebarMobile: boolean;
  
  // Theme
  theme: 'light' | 'dark' | 'auto';
  
  // Notifications
  notifications: ToastNotification[];
  maxNotifications: number;
  
  // Modals
  modals: Record<string, ModalState>;
  
  // Loading overlays
  loadingOverlays: Record<string, LoadingOverlay>;
  
  // Page state
  currentPage: string;
  breadcrumbs: Array<{ label: string; path?: string }>;
  
  // Table/Grid state
  tableSettings: Record<string, {
    pageSize: number;
    sortColumn: string;
    sortDirection: 'asc' | 'desc';
    visibleColumns: string[];
  }>;
  
  // Form state
  unsavedChanges: Record<string, boolean>;
  
  // Search/Filter state
  searchHistory: string[];
  maxSearchHistory: number;
  
  // Layout preferences
  preferences: {
    compactMode: boolean;
    showTooltips: boolean;
    autoRefresh: boolean;
    refreshInterval: number;
  };
}

// Initial state
const initialState: UIState = {
  globalLoading: false,
  loadingMessage: '',
  sidebarCollapsed: false,
  sidebarMobile: false,
  theme: 'light',
  notifications: [],
  maxNotifications: 5,
  modals: {},
  loadingOverlays: {},
  currentPage: '',
  breadcrumbs: [],
  tableSettings: {},
  unsavedChanges: {},
  searchHistory: [],
  maxSearchHistory: 10,
  preferences: {
    compactMode: false,
    showTooltips: true,
    autoRefresh: false,
    refreshInterval: 30000, // 30 seconds
  },
};

// Utility functions
const generateId = () => Math.random().toString(36).substr(2, 9);

// Slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Global loading
    setGlobalLoading: (state, action: PayloadAction<{ loading: boolean; message?: string }>) => {
      state.globalLoading = action.payload.loading;
      state.loadingMessage = action.payload.message || '';
    },
    
    // Sidebar
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
    setSidebarMobile: (state, action: PayloadAction<boolean>) => {
      state.sidebarMobile = action.payload;
    },
    
    // Theme
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {
      state.theme = action.payload;
    },
    
    // Notifications
    addNotification: (state, action: PayloadAction<Omit<ToastNotification, 'id' | 'timestamp'>>) => {
      const notification: ToastNotification = {
        ...action.payload,
        id: generateId(),
        timestamp: new Date().toISOString(),
      };
      
      state.notifications.unshift(notification);
      
      // Keep only max notifications
      if (state.notifications.length > state.maxNotifications) {
        state.notifications = state.notifications.slice(0, state.maxNotifications);
      }
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // Modals
    openModal: (state, action: PayloadAction<{ type: string; data?: any; size?: 'sm' | 'lg' | 'xl' }>) => {
      state.modals[action.payload.type] = {
        isOpen: true,
        type: action.payload.type,
        data: action.payload.data || null,
        size: action.payload.size,
      };
    },
    closeModal: (state, action: PayloadAction<string>) => {
      if (state.modals[action.payload]) {
        state.modals[action.payload].isOpen = false;
      }
    },
    updateModalData: (state, action: PayloadAction<{ type: string; data: any }>) => {
      if (state.modals[action.payload.type]) {
        state.modals[action.payload.type].data = action.payload.data;
      }
    },
    clearModals: (state) => {
      state.modals = {};
    },
    
    // Loading overlays
    showLoadingOverlay: (state, action: PayloadAction<{ key: string; message: string; progress?: number }>) => {
      state.loadingOverlays[action.payload.key] = {
        isVisible: true,
        message: action.payload.message,
        progress: action.payload.progress,
      };
    },
    hideLoadingOverlay: (state, action: PayloadAction<string>) => {
      if (state.loadingOverlays[action.payload]) {
        state.loadingOverlays[action.payload].isVisible = false;
      }
    },
    updateLoadingOverlay: (state, action: PayloadAction<{ key: string; message?: string; progress?: number }>) => {
      if (state.loadingOverlays[action.payload.key]) {
        const overlay = state.loadingOverlays[action.payload.key];
        if (action.payload.message !== undefined) {
          overlay.message = action.payload.message;
        }
        if (action.payload.progress !== undefined) {
          overlay.progress = action.payload.progress;
        }
      }
    },
    clearLoadingOverlays: (state) => {
      state.loadingOverlays = {};
    },
    
    // Page state
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
    },
    setBreadcrumbs: (state, action: PayloadAction<Array<{ label: string; path?: string }>>) => {
      state.breadcrumbs = action.payload;
    },
    
    // Table settings
    setTableSettings: (state, action: PayloadAction<{ 
      tableId: string; 
      settings: Partial<UIState['tableSettings'][string]> 
    }>) => {
      const { tableId, settings } = action.payload;
      state.tableSettings[tableId] = {
        ...state.tableSettings[tableId],
        ...settings,
      };
    },
    resetTableSettings: (state, action: PayloadAction<string>) => {
      delete state.tableSettings[action.payload];
    },
    
    // Form state
    setUnsavedChanges: (state, action: PayloadAction<{ formId: string; hasChanges: boolean }>) => {
      state.unsavedChanges[action.payload.formId] = action.payload.hasChanges;
    },
    clearUnsavedChanges: (state, action: PayloadAction<string>) => {
      delete state.unsavedChanges[action.payload];
    },
    clearAllUnsavedChanges: (state) => {
      state.unsavedChanges = {};
    },
    
    // Search history
    addToSearchHistory: (state, action: PayloadAction<string>) => {
      const term = action.payload.trim();
      if (term && !state.searchHistory.includes(term)) {
        state.searchHistory.unshift(term);
        
        // Keep only max history items
        if (state.searchHistory.length > state.maxSearchHistory) {
          state.searchHistory = state.searchHistory.slice(0, state.maxSearchHistory);
        }
      }
    },
    removeFromSearchHistory: (state, action: PayloadAction<string>) => {
      state.searchHistory = state.searchHistory.filter(term => term !== action.payload);
    },
    clearSearchHistory: (state) => {
      state.searchHistory = [];
    },
    
    // Preferences
    setPreferences: (state, action: PayloadAction<Partial<UIState['preferences']>>) => {
      state.preferences = {
        ...state.preferences,
        ...action.payload,
      };
    },
    toggleCompactMode: (state) => {
      state.preferences.compactMode = !state.preferences.compactMode;
    },
    toggleTooltips: (state) => {
      state.preferences.showTooltips = !state.preferences.showTooltips;
    },
    toggleAutoRefresh: (state) => {
      state.preferences.autoRefresh = !state.preferences.autoRefresh;
    },
    setRefreshInterval: (state, action: PayloadAction<number>) => {
      state.preferences.refreshInterval = action.payload;
    },
    
    // Reset state
    resetUIState: () => initialState,
  },
});

// Export actions
export const {
  setGlobalLoading,
  toggleSidebar,
  setSidebarCollapsed,
  setSidebarMobile,
  setTheme,
  addNotification,
  removeNotification,
  clearNotifications,
  openModal,
  closeModal,
  updateModalData,
  clearModals,
  showLoadingOverlay,
  hideLoadingOverlay,
  updateLoadingOverlay,
  clearLoadingOverlays,
  setCurrentPage,
  setBreadcrumbs,
  setTableSettings,
  resetTableSettings,
  setUnsavedChanges,
  clearUnsavedChanges,
  clearAllUnsavedChanges,
  addToSearchHistory,
  removeFromSearchHistory,
  clearSearchHistory,
  setPreferences,
  toggleCompactMode,
  toggleTooltips,
  toggleAutoRefresh,
  setRefreshInterval,
  resetUIState,
} = uiSlice.actions;

// Selectors
export const selectGlobalLoading = (state: { ui: UIState }) => state.ui.globalLoading;
export const selectLoadingMessage = (state: { ui: UIState }) => state.ui.loadingMessage;
export const selectSidebarCollapsed = (state: { ui: UIState }) => state.ui.sidebarCollapsed;
export const selectSidebarMobile = (state: { ui: UIState }) => state.ui.sidebarMobile;
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications;
export const selectModals = (state: { ui: UIState }) => state.ui.modals;
export const selectModal = (type: string) => (state: { ui: UIState }) => state.ui.modals[type];
export const selectLoadingOverlays = (state: { ui: UIState }) => state.ui.loadingOverlays;
export const selectLoadingOverlay = (key: string) => (state: { ui: UIState }) => state.ui.loadingOverlays[key];
export const selectCurrentPage = (state: { ui: UIState }) => state.ui.currentPage;
export const selectBreadcrumbs = (state: { ui: UIState }) => state.ui.breadcrumbs;
export const selectTableSettings = (tableId: string) => (state: { ui: UIState }) => state.ui.tableSettings[tableId];
export const selectUnsavedChanges = (state: { ui: UIState }) => state.ui.unsavedChanges;
export const selectHasUnsavedChanges = (formId: string) => (state: { ui: UIState }) => state.ui.unsavedChanges[formId] || false;
export const selectSearchHistory = (state: { ui: UIState }) => state.ui.searchHistory;
export const selectPreferences = (state: { ui: UIState }) => state.ui.preferences;

// Export reducer
export default uiSlice.reducer;
