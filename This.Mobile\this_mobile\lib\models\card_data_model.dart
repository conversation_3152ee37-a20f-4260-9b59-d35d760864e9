/// Models for card-based data display
class CardDataModel {
  final String id;
  final String title;
  final String subtitle;
  final List<CardFieldModel> fields;
  final String objectType;
  final Map<String, dynamic> metadata;

  const CardDataModel({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.fields,
    required this.objectType,
    required this.metadata,
  });

  /// Get primary fields (first 3-4 most important fields)
  List<CardFieldModel> get primaryFields {
    return fields.take(4).toList();
  }

  /// Get secondary fields (remaining fields)
  List<CardFieldModel> get secondaryFields {
    return fields.skip(4).toList();
  }

  /// Check if there are secondary fields to show
  bool get hasSecondaryFields {
    return fields.length > 4;
  }

  /// Get field count
  int get fieldCount {
    return fields.length;
  }

  /// Check if any field has long values
  bool get hasLongValues {
    return metadata['hasLongValues'] as bool? ?? false;
  }
}

/// Model for individual field in a card
class CardFieldModel {
  final String key;
  final String value;
  final String displayValue;
  final CardFieldType type;
  final bool isLong;
  final bool isEmpty;

  const Card<PERSON>ieldModel({
    required this.key,
    required this.value,
    required this.displayValue,
    required this.type,
    required this.isLong,
    required this.isEmpty,
  });

  /// Get formatted key for display
  String get displayKey {
    // Convert camelCase and snake_case to readable format
    return key
        .replaceAllMapped(RegExp(r'([a-z])([A-Z])'), (match) => '${match.group(1)} ${match.group(2)}')
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}' : '')
        .join(' ');
  }

  /// Check if field should be highlighted
  bool get isHighlighted {
    return type == CardFieldType.email || 
           type == CardFieldType.phone || 
           type == CardFieldType.url ||
           key.toLowerCase().contains('name') ||
           key.toLowerCase().contains('title');
  }
}

/// Enum for different field types
enum CardFieldType {
  text,
  email,
  phone,
  url,
  date,
  currency,
  number,
  boolean,
}

/// Extension for CardFieldType to get display properties
extension CardFieldTypeExtension on CardFieldType {
  /// Get icon for the field type
  String get icon {
    switch (this) {
      case CardFieldType.email:
        return '📧';
      case CardFieldType.phone:
        return '📞';
      case CardFieldType.url:
        return '🔗';
      case CardFieldType.date:
        return '📅';
      case CardFieldType.currency:
        return '💰';
      case CardFieldType.number:
        return '🔢';
      case CardFieldType.boolean:
        return '✓';
      case CardFieldType.text:
      return '📝';
    }
  }

  /// Get color for the field type
  String get colorHex {
    switch (this) {
      case CardFieldType.email:
        return '#3B82F6'; // Blue
      case CardFieldType.phone:
        return '#10B981'; // Green
      case CardFieldType.url:
        return '#8B5CF6'; // Purple
      case CardFieldType.date:
        return '#F59E0B'; // Amber
      case CardFieldType.currency:
        return '#EF4444'; // Red
      case CardFieldType.number:
        return '#6366F1'; // Indigo
      case CardFieldType.boolean:
        return '#059669'; // Emerald
      case CardFieldType.text:
      return '#6B7280'; // Gray
    }
  }
}
