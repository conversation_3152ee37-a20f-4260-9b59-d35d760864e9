﻿using Application.Integrations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Commands;

/// <summary>
/// Command to create an Integration with associated IntegrationApi and IntegrationConfiguration
/// </summary>
public class CreateIntegrationWithApiCommand : IRequest<Result<CreateIntegrationWithApiResponseDto>>
{
    /// <summary>
    /// Product ID this integration belongs to
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Authentication type (e.g., "OAuth2", "ApiKey", "Basic")
    /// </summary>
    public string AuthType { get; set; } = string.Empty;

    /// <summary>
    /// Authentication configuration stored as JSON
    /// </summary>
    public string AuthConfig { get; set; } = string.Empty;

    /// <summary>
    /// Whether the integration is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Sync frequency for automatic synchronization (in minutes)
    /// </summary>
    public int? SyncFrequencyMinutes { get; set; }

    /// <summary>
    /// Integration API data to create (optional)
    /// </summary>
    public CreateIntegrationApiRequestDto? IntegrationApi { get; set; }

    /// <summary>
    /// Integration configuration data (optional)
    /// </summary>
    public CreateIntegrationConfigurationRequestDto? Configuration { get; set; }
}

/// <summary>
/// DTO for creating IntegrationApi in bulk operation
/// </summary>
public class CreateIntegrationApiRequestDto
{
    /// <summary>
    /// API name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// API endpoint URL
    /// </summary>
    public string EndpointUrl { get; set; } = string.Empty;

    /// <summary>
    /// API schema definition stored as JSON
    /// </summary>
    public string? Schema { get; set; }

    /// <summary>
    /// Whether the API is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// DTO for creating IntegrationConfiguration in bulk operation
/// </summary>
public class CreateIntegrationConfigurationRequestDto
{
    /// <summary>
    /// Object ID this configuration applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Data flow direction (e.g., "In", "Out", "Both")
    /// </summary>
    public string? Direction { get; set; }

    /// <summary>
    /// Whether the configuration is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}