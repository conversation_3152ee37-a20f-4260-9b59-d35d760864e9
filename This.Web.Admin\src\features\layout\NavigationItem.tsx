import React, { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/shared/utils/utils';
import type { NavigationNode } from '../../types/metadata';

interface NavigationItemProps {
  node: NavigationNode;
  collapsed: boolean;
  level?: number;
}

export const NavigationItem: React.FC<NavigationItemProps> = ({
  node,
  collapsed,
  level = 0
}) => {
  // Single state for expand/collapse - default to expanded
  const [isExpanded, setIsExpanded] = useState(true);

  const navigate = useNavigate();
  const location = useLocation();

  const hasChildren = node.children && node.children.length > 0;

  // Calculate dynamic left padding based on hierarchy level
  // Base padding: 12px (pl-3), then +12px per level (3px * 4 = 12px in Tailwind)
  const calculateLeftPadding = (level: number): number => {
    const basePadding = 12; // pl-3 = 12px
    const levelIncrement = 12; // 12px per level
    return basePadding + (level * levelIncrement);
  };

  const leftPadding = calculateLeftPadding(level);

  // Check if this navigation item is active
  const isActive = node.type === 'object' && node.name
    ? location.pathname === `/object/${encodeURIComponent(node.name)}`
    : location.pathname === node.path;

  const handleClick = () => {
    // Handle object navigation - route to object detail page using object name
    if (node.type === 'object' && node.name) {
      const targetPath = `/object/${encodeURIComponent(node.name)}`;
      navigate(targetPath);
    } else if (node.path) {
      // For other types, use the original path
      navigate(node.path);
    } else if (hasChildren) {
      // If no path but has children, toggle expansion
      setIsExpanded(!isExpanded);
    }
  };

  // Expand/collapse handler for arrow button
  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  if (!node.isActive) {
    return null;
  }

  return (
    <div className="w-full">
      {/* Main navigation item */}
      <button
        type="button"
        onClick={handleClick}
        className={cn(
          'w-full flex items-center gap-2 py-2 text-xs font-normal rounded-lg transition-all duration-200 ease-in-out',
          'hover:bg-muted hover:text-foreground',
          'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1',
          isActive && 'bg-primary text-primary-foreground hover:bg-primary/90',
          collapsed ? 'justify-center px-2' : 'pr-3 navigation-item'
        )}
        style={{
          // Dynamic left padding using CSS custom property
          // Note: Inline styles are necessary here for dynamic values that scale to unlimited levels
          '--nav-left-padding': collapsed ? '8px' : `${leftPadding}px`
        } as React.CSSProperties}
        title={collapsed ? `${node.name} - ${node.description}` : node.description}
      >
        {/* Expand/Collapse control for items with children */}
        {hasChildren && !collapsed && (
          <div
            onClick={handleToggle}
            className={cn(
              "p-0.5 hover:bg-muted-foreground/20 rounded cursor-pointer transition-colors flex-shrink-0",
              isExpanded && "bg-secondary/50"
            )}
            title={isExpanded ? "Collapse children" : "Expand children"}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </div>
        )}

        {/* Icon */}
        <span className="text-sm leading-none flex-shrink-0">
          {node.icon}
        </span>

        {/* Label */}
        {!collapsed && (
          <span className="truncate text-left flex-1">{node.name}</span>
        )}

        {/* Active indicator for collapsed state */}
        {collapsed && isActive && (
          <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-primary rounded-l-full" />
        )}

        {/* Children count indicator when collapsed */}
        {collapsed && hasChildren && (
          <div className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full h-4 w-4 flex items-center justify-center">
            {node.children?.length || 0}
          </div>
        )}
      </button>

      {/* Child items - Show when expanded */}
      {hasChildren && !collapsed && isExpanded && (
        <div className="mt-1 space-y-1">
          {node.children?.map((childNode) => (
            <NavigationItem
              key={childNode.id}
              node={childNode}
              collapsed={collapsed}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};
