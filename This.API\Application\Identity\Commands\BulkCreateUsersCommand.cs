using Application.Identity.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Identity.Commands;

/// <summary>
/// Command for creating multiple users in bulk
/// </summary>
public class BulkCreateUsersCommand : IRequest<ApiResponse<BulkCreateUsersResponse>>
{
    /// <summary>
    /// List of users to create
    /// </summary>
    public List<CreateUserCommand> Users { get; set; } = new();

    /// <summary>
    /// Whether to continue processing if some users fail to create
    /// </summary>
    public bool ContinueOnError { get; set; } = true;

    /// <summary>
    /// Whether to validate all users before creating any
    /// </summary>
    public bool ValidateBeforeCreate { get; set; } = true;

    /// <summary>
    /// For update the prodect
    /// </summary>
    public Guid ProductId { get; set; }
}

/// <summary>
/// Response for bulk user creation
/// </summary>
public class BulkCreateUsersResponse
{
    /// <summary>
    /// Total number of users requested to be created
    /// </summary>
    public int TotalRequested { get; set; }

    /// <summary>
    /// Number of users successfully created
    /// </summary>
    public int SuccessfullyCreated { get; set; }

    /// <summary>
    /// Number of users that failed to create
    /// </summary>
    public int Failed { get; set; }

    /// <summary>
    /// List of successfully created users
    /// </summary>
    public List<UserDto> CreatedUsers { get; set; } = new();

    /// <summary>
    /// List of errors for failed user creations
    /// </summary>
    public List<BulkUserCreationError> Errors { get; set; } = new();

    /// <summary>
    /// Overall success message
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Error information for failed user creation
/// </summary>
public class BulkUserCreationError
{
    /// <summary>
    /// Index of the user in the original request
    /// </summary>
    public int UserIndex { get; set; }

    /// <summary>
    /// Email of the user that failed to create
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Username of the user that failed to create
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Error message
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// Error details
    /// </summary>
    public string? ErrorDetails { get; set; }
}
