namespace Abstraction.Identity.Dtos;

/// <summary>
/// Data transfer object for user details
/// </summary>
public class UserDetailsDto
{
    /// <summary>
    /// User ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// User name
    /// </summary>
    public string UserName { get; set; } = default!;

    /// <summary>
    /// First name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// Last name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Normalized username
    /// </summary>
    public string? NormalizedUserName { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    public string Email { get; set; } = default!;

    /// <summary>
    /// Normalized email
    /// </summary>
    public string? NormalizedEmail { get; set; }

    /// <summary>
    /// Whether the email is confirmed
    /// </summary>
    public bool EmailConfirmed { get; set; }

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Whether the phone number is confirmed
    /// </summary>
    public bool PhoneNumberConfirmed { get; set; }

    /// <summary>
    /// Whether two factor authentication is enabled
    /// </summary>
    public bool TwoFactorEnabled { get; set; }

    /// <summary>
    /// When lockout ends
    /// </summary>
    public DateTimeOffset? LockoutEnd { get; set; }

    /// <summary>
    /// Whether lockout is enabled
    /// </summary>
    public bool LockoutEnabled { get; set; }

    /// <summary>
    /// Access failed count
    /// </summary>
    public int AccessFailedCount { get; set; }

    /// <summary>
    /// External user ID (for integration with external systems)
    /// </summary>
    public string? ExternalUserId { get; set; }

    /// <summary>
    /// Whether the user is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Last login timestamp
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// Refresh token for JWT authentication
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// Refresh token expiry time
    /// </summary>
    public DateTime? RefreshTokenExpiryTime { get; set; }

    /// <summary>
    /// When the user was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who created the user
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// When the user was last modified
    /// </summary>
    public DateTime ModifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Who last modified the user
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Whether the user is soft deleted
    /// </summary>
    public bool IsDeleted { get; set; } = false;

    /// <summary>
    /// User roles
    /// </summary>
    public List<string> Roles { get; set; } = new();
}
