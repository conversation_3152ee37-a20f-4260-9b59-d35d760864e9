/**
 * Redux Store Selectors
 * Computed selectors for complex state derivations
 */

import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from './index';
import { SubscriptionUtils } from '../services/utils/subscriptionUtils';

// Base selectors
const selectSubscriptionState = (state: RootState) => state.subscriptions;
const selectTemplateState = (state: RootState) => state.templates;
const selectTenantState = (state: RootState) => state.tenants;
const selectUIState = (state: RootState) => state.ui;

// Subscription selectors
export const selectFilteredSubscriptions = createSelector(
  [selectSubscriptionState],
  (subscriptionState) => {
    const { subscriptions, filters } = subscriptionState;
    let filtered = [...subscriptions];

    // Apply search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(sub => 
        (sub.productName?.toLowerCase().includes(searchLower)) ||
        (sub.tenantName?.toLowerCase().includes(searchLower)) ||
        sub.id.toLowerCase().includes(searchLower) ||
        sub.subscriptionType.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (filters.status && filters.status !== 'all') {
      filtered = SubscriptionUtils.filterByStatus(filtered, filters.status);
    }

    // Apply tenant filter
    if (filters.tenantId) {
      filtered = filtered.filter(sub => sub.tenantId === filters.tenantId);
    }

    // Apply subscription type filter
    if (filters.subscriptionType) {
      filtered = filtered.filter(sub => sub.subscriptionType === filters.subscriptionType);
    }

    // Apply active filter
    if (filters.isActive !== null) {
      filtered = filtered.filter(sub => sub.isActive === filters.isActive);
    }

    return filtered;
  }
);

export const selectSubscriptionStats = createSelector(
  [selectFilteredSubscriptions],
  (subscriptions) => {
    return SubscriptionUtils.calculateStats(subscriptions);
  }
);

export const selectSubscriptionsByStatus = createSelector(
  [selectFilteredSubscriptions],
  (subscriptions) => {
    return {
      active: subscriptions.filter(sub => sub.status === 'active' && !SubscriptionUtils.isExpired(sub)),
      expired: subscriptions.filter(sub => SubscriptionUtils.isExpired(sub)),
      expiring: subscriptions.filter(sub => SubscriptionUtils.isExpiringSoon(sub)),
      inactive: subscriptions.filter(sub => sub.status === 'inactive'),
    };
  }
);

export const selectSubscriptionsByTenant = createSelector(
  [selectFilteredSubscriptions],
  (subscriptions) => {
    return SubscriptionUtils.groupByTenant(subscriptions);
  }
);

export const selectSubscriptionsByProduct = createSelector(
  [selectFilteredSubscriptions],
  (subscriptions) => {
    return SubscriptionUtils.groupByProduct(subscriptions);
  }
);

// Template selectors
export const selectFilteredTemplates = createSelector(
  [selectTemplateState],
  (templateState) => {
    const { templates, filters } = templateState;
    let filtered = [...templates];

    // Apply search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(template => 
        template.name.toLowerCase().includes(searchLower) ||
        template.id.toLowerCase().includes(searchLower) ||
        template.version.toLowerCase().includes(searchLower) ||
        template.stage.toLowerCase().includes(searchLower)
      );
    }

    // Apply stage filter
    if (filters.stage && filters.stage !== 'all') {
      filtered = filtered.filter(template => template.stage.toLowerCase() === filters.stage.toLowerCase());
    }

    // Apply status filter
    if (filters.status && filters.status !== 'all') {
      if (filters.status === 'active') {
        filtered = filtered.filter(template => template.isActive);
      } else if (filters.status === 'inactive') {
        filtered = filtered.filter(template => !template.isActive);
      }
    }

    // Apply active filter
    if (filters.isActive !== null) {
      filtered = filtered.filter(template => template.isActive === filters.isActive);
    }

    // Apply product ID filter
    if (filters.productId) {
      // This would need to be implemented based on how templates relate to products
      // For now, we'll skip this filter
    }

    return filtered;
  }
);

export const selectFilteredGroupedTemplates = createSelector(
  [selectTemplateState, selectFilteredTemplates],
  (templateState, filteredTemplates) => {
    const { filters } = templateState;
    
    // Group the filtered templates
    const grouped = templateState.groupedTemplates.filter(group => {
      // Check if any template in the group matches the current filters
      return group.allTemplates.some(template => 
        filteredTemplates.some(filtered => filtered.id === template.id)
      );
    });

    return grouped;
  }
);

export const selectTemplatesByStage = createSelector(
  [selectFilteredTemplates],
  (templates) => {
    return templates.reduce((acc, template) => {
      const stage = template.stage || 'unknown';
      if (!acc[stage]) {
        acc[stage] = [];
      }
      acc[stage].push(template);
      return acc;
    }, {} as Record<string, typeof templates>);
  }
);

export const selectTemplateDropdownOptions = createSelector(
  [selectTemplateState],
  (templateState) => {
    return templateState.liveTemplates.map(template => ({
      value: template.id,
      label: `${template.name} (${template.stage}) - v${template.version}`,
      template,
    }));
  }
);

// Tenant selectors
export const selectFilteredTenants = createSelector(
  [selectTenantState],
  (tenantState) => {
    const { tenants, filters } = tenantState;
    let filtered = [...tenants];

    // Apply search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(tenant => 
        tenant.name.toLowerCase().includes(searchLower) ||
        tenant.id.toLowerCase().includes(searchLower) ||
        tenant.adminEmail.toLowerCase().includes(searchLower)
      );
    }

    // Apply active filter
    if (filters.isActive !== null) {
      filtered = filtered.filter(tenant => tenant.isActive === filters.isActive);
    }

    // Apply admin email filter
    if (filters.adminEmail) {
      const emailLower = filters.adminEmail.toLowerCase();
      filtered = filtered.filter(tenant => 
        tenant.adminEmail.toLowerCase().includes(emailLower)
      );
    }

    return filtered;
  }
);

export const selectTenantById = (tenantId: string) => createSelector(
  [selectTenantState],
  (tenantState) => {
    return tenantState.tenants.find(tenant => tenant.id === tenantId) || null;
  }
);

// UI selectors
export const selectIsAnyLoading = createSelector(
  [selectSubscriptionState, selectTemplateState, selectTenantState, selectUIState],
  (subscriptionState, templateState, tenantState, uiState) => {
    return (
      uiState.globalLoading ||
      Object.values(subscriptionState.loading).some(loading => loading) ||
      Object.values(templateState.loading).some(loading => loading) ||
      Object.values(tenantState.loading).some(loading => loading)
    );
  }
);

export const selectAllErrors = createSelector(
  [selectSubscriptionState, selectTemplateState, selectTenantState],
  (subscriptionState, templateState, tenantState) => {
    const errors: Record<string, any> = {};

    // Collect subscription errors
    const subscriptionErrors = Object.entries(subscriptionState.errors)
      .filter(([_, error]) => error !== null)
      .reduce((acc, [key, error]) => ({ ...acc, [key]: error }), {});
    
    if (Object.keys(subscriptionErrors).length > 0) {
      errors.subscriptions = subscriptionErrors;
    }

    // Collect template errors
    const templateErrors = Object.entries(templateState.errors)
      .filter(([_, error]) => error !== null)
      .reduce((acc, [key, error]) => ({ ...acc, [key]: error }), {});
    
    if (Object.keys(templateErrors).length > 0) {
      errors.templates = templateErrors;
    }

    // Collect tenant errors
    const tenantErrors = Object.entries(tenantState.errors)
      .filter(([_, error]) => error !== null)
      .reduce((acc, [key, error]) => ({ ...acc, [key]: error }), {});
    
    if (Object.keys(tenantErrors).length > 0) {
      errors.tenants = tenantErrors;
    }

    return errors;
  }
);

export const selectModalByType = (modalType: string) => createSelector(
  [selectUIState],
  (uiState) => {
    return uiState.modals[modalType] || null;
  }
);

export const selectLoadingOverlayByKey = (key: string) => createSelector(
  [selectUIState],
  (uiState) => {
    return uiState.loadingOverlays[key] || null;
  }
);

// Combined data selectors
export const selectDashboardData = createSelector(
  [selectSubscriptionStats, selectTenantState, selectTemplateState],
  (subscriptionStats, tenantState, templateState) => {
    return {
      subscriptions: {
        total: subscriptionStats.total,
        active: subscriptionStats.active,
        expired: subscriptionStats.expired,
        expiring: subscriptionStats.expiring,
      },
      tenants: {
        total: tenantState.stats.totalTenants,
        active: tenantState.stats.activeTenants,
        inactive: tenantState.stats.inactiveTenants,
      },
      templates: {
        total: templateState.templates.length,
        live: templateState.liveTemplates.length,
        grouped: templateState.groupedTemplates.length,
      },
    };
  }
);

// Performance selectors (memoized)
export const selectSubscriptionTableData = createSelector(
  [selectFilteredSubscriptions, selectSubscriptionState],
  (subscriptions, subscriptionState) => {
    const { pageNumber, pageSize } = subscriptionState;
    const startIndex = (pageNumber - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    return {
      data: subscriptions.slice(startIndex, endIndex),
      totalCount: subscriptions.length,
      pageNumber,
      pageSize,
      totalPages: Math.ceil(subscriptions.length / pageSize),
      hasNextPage: endIndex < subscriptions.length,
      hasPreviousPage: pageNumber > 1,
    };
  }
);

export const selectTemplateTableData = createSelector(
  [selectFilteredTemplates, selectTemplateState],
  (templates, templateState) => {
    const { pageNumber, pageSize } = templateState;
    const startIndex = (pageNumber - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    return {
      data: templates.slice(startIndex, endIndex),
      totalCount: templates.length,
      pageNumber,
      pageSize,
      totalPages: Math.ceil(templates.length / pageSize),
      hasNextPage: endIndex < templates.length,
      hasPreviousPage: pageNumber > 1,
    };
  }
);
