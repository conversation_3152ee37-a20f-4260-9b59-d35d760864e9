import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile/core/theme/color_palette.dart';
import 'package:this_mobile/core/theme/text_styles.dart';
import 'package:intl/intl.dart';

/// A customizable number input widget following the 'this_componentName_relatedTo' naming convention
/// This widget handles numeric input with validation, formatting, and various number constraints
/// Supports comprehensive customization including prefix icons, currency symbols, and validation
class ThisNumberInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final String value;
  final ValueChanged<String> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final double? min;
  final double? max;
  final double? step;
  final int? decimals;
  final bool allowNegative;
  final bool thousandsSeparator;
  final String? currency;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool autoFocus;
  final String? Function(String)? customValidation;

  // Enhanced prefix icon customization
  final bool showPrefixIcon;
  final IconData? prefixIcon;
  final Color? prefixIconColor;
  final double? prefixIconSize;
  final Widget? customPrefixIcon;
  final String? prefixText;
  final TextStyle? prefixTextStyle;

  // Enhanced suffix customization
  final Widget? customSuffixIcon;
  final String? suffixText;
  final TextStyle? suffixTextStyle;

  // Enhanced validation error messages
  final String? requiredErrorMessage;
  final String? invalidNumberErrorMessage;
  final String? negativeNotAllowedErrorMessage;
  final String? minValueErrorMessage;
  final String? maxValueErrorMessage;
  final String? decimalPlacesErrorMessage;
  final String? stepErrorMessage;

  const ThisNumberInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.min,
    this.max,
    this.step,
    this.decimals,
    this.allowNegative = true,
    this.thousandsSeparator = false,
    this.currency,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.autoFocus = false,
    this.customValidation,

    // Enhanced prefix icon customization
    this.showPrefixIcon = true,
    this.prefixIcon,
    this.prefixIconColor,
    this.prefixIconSize,
    this.customPrefixIcon,
    this.prefixText,
    this.prefixTextStyle,

    // Enhanced suffix customization
    this.customSuffixIcon,
    this.suffixText,
    this.suffixTextStyle,

    // Enhanced validation error messages
    this.requiredErrorMessage,
    this.invalidNumberErrorMessage,
    this.negativeNotAllowedErrorMessage,
    this.minValueErrorMessage,
    this.maxValueErrorMessage,
    this.decimalPlacesErrorMessage,
    this.stepErrorMessage,
  });

  @override
  State<ThisNumberInput> createState() => _ThisNumberInputState();
}

class _ThisNumberInputState extends State<ThisNumberInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
    _focusNode = FocusNode();

    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void didUpdateWidget(ThisNumberInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // Helper function to parse number from string
  double? _parseNumber(String val) {
    if (val.trim().isEmpty) return null;

    // Remove thousands separators and currency symbols for parsing
    String cleanValue = val.replaceAll(',', '');
    if (widget.currency != null) {
      cleanValue = cleanValue.replaceAll(widget.currency!, '');
    }
    cleanValue = cleanValue.trim();

    final num = double.tryParse(cleanValue);
    return num;
  }

  List<String> _validateValue(String value) {
    final errors = <String>[];

    // 1. Required validation (highest priority)
    if (widget.required && value.trim().isEmpty) {
      errors.add(widget.requiredErrorMessage ?? '${widget.label} is required');
      return errors;
    }

    // Only validate format if field has content or is required
    if (widget.required || value.trim().isNotEmpty) {
      // 2. Number format validation
      final num = _parseNumber(value);
      if (num == null && value.trim().isNotEmpty) {
        errors.add(widget.invalidNumberErrorMessage ?? 'Enter a valid number');
        return errors;
      }

      if (num != null) {
        // 3. Negative number validation
        if (!widget.allowNegative && num < 0) {
          errors.add(widget.negativeNotAllowedErrorMessage ?? 'Negative numbers are not allowed');
          return errors;
        }

        // 4. Minimum value validation
        if (widget.min != null && num < widget.min!) {
          errors.add(widget.minValueErrorMessage ?? 'Minimum value is ${widget.min}');
          return errors;
        }

        // 5. Maximum value validation
        if (widget.max != null && num > widget.max!) {
          errors.add(widget.maxValueErrorMessage ?? 'Maximum value is ${widget.max}');
          return errors;
        }

        // 6. Decimal places validation
        if (widget.decimals != null) {
          final decimalPart = value.split('.').length > 1 ? value.split('.')[1] : '';
          if (decimalPart.length > widget.decimals!) {
            errors.add(widget.decimalPlacesErrorMessage ?? 'Maximum ${widget.decimals} decimal places allowed');
            return errors;
          }
        }

        // 7. Step validation
        if (widget.step != null && widget.min != null) {
          final remainder = (num - widget.min!) % widget.step!;
          if (remainder.abs() >= 0.0001) {
            // Account for floating point precision
            errors.add(widget.stepErrorMessage ?? 'Value must be in increments of ${widget.step}');
            return errors;
          }
        }
      }

      // 8. Custom validation
      if (widget.customValidation != null) {
        final customError = widget.customValidation!(value);
        if (customError != null) {
          errors.add(customError);
          return errors;
        }
      }
    }

    return errors;
  }

  // Format number for display
  String _formatNumber(String val) {
    if (val.trim().isEmpty) return val;

    final num = _parseNumber(val);
    if (num == null) return val;

    String formatted = num.toString();

    // Add thousands separators
    if (widget.thousandsSeparator) {
      final formatter = NumberFormat('#,##0.##');
      formatted = formatter.format(num);
    }

    // Add currency symbol
    if (widget.currency != null) {
      formatted = '${widget.currency}$formatted';
    }

    return formatted;
  }

  void _handleChange(String value) {
    widget.onChanged(value);

    // Real-time validation (only if not validating on blur)
    if (!widget.validateOnBlur) {
      final errors = _validateValue(value);
      setState(() {
        _errors = errors;
        _isValidated = value.trim().isNotEmpty;
      });

      // Notify parent of validation state
      widget.onValidation?.call(errors);
    }
  }

  void _handleBlur() {
    // Format the number on blur if it's valid
    String formattedValue = widget.value;
    final num = _parseNumber(widget.value);

    if (num != null && _errors.isEmpty) {
      formattedValue = _formatNumber(widget.value);
      if (formattedValue != widget.value) {
        widget.onChanged(formattedValue);
      }
    }

    // Validate on blur if enabled
    if (widget.validateOnBlur) {
      final errors = _validateValue(formattedValue);
      setState(() {
        _errors = errors;
        _isValidated = formattedValue.trim().isNotEmpty;
      });

      // Notify parent of validation state
      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated || widget.value.trim().isEmpty) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  Widget? _getPrefixIcon() {
    // Priority: customPrefixIcon > prefixIcon > showIcon (legacy)
    if (widget.customPrefixIcon != null) {
      return widget.customPrefixIcon;
    }

    if (widget.prefixIcon != null && widget.showPrefixIcon) {
      return Icon(
        widget.prefixIcon,
        size: widget.prefixIconSize ?? 20,
        color: widget.prefixIconColor ?? ColorPalette.placeHolderTextColor,
      );
    }

    // Legacy support for showIcon
    if (widget.showIcon && widget.showPrefixIcon) {
      return Icon(
        Icons.numbers,
        size: widget.prefixIconSize ?? 20,
        color: widget.prefixIconColor ?? ColorPalette.placeHolderTextColor,
      );
    }

    return null;
  }

  Widget? _getSuffixIcon() {
    // Priority: customSuffixIcon > validation icon
    if (widget.customSuffixIcon != null) {
      return widget.customSuffixIcon;
    }

    return _getValidationIcon();
  }

  String? _getPrefixText() {
    return widget.prefixText;
  }

  String? _getSuffixText() {
    return widget.suffixText;
  }

  String _getPlaceholder() {
    if (widget.placeholder != null) return widget.placeholder!;

    String placeholderText = '';
    if (widget.currency != null) placeholderText += widget.currency!;

    if (widget.min != null && widget.max != null) {
      placeholderText += '${widget.min} - ${widget.max}';
    } else if (widget.min != null) {
      placeholderText += 'Min: ${widget.min}';
    } else if (widget.max != null) {
      placeholderText += 'Max: ${widget.max}';
    } else {
      placeholderText += 'Enter a number';
    }

    return placeholderText;
  }

  List<String> _getHelperTexts() {
    final helpers = <String>[];

    if (widget.step != null) {
      helpers.add('Step: ${widget.step}');
    }

    if (widget.decimals != null) {
      helpers.add('Max ${widget.decimals} decimal places');
    }

    if (!widget.allowNegative) {
      helpers.add('Positive numbers only');
    }

    if (widget.thousandsSeparator) {
      helpers.add('Thousands separator enabled');
    }

    return helpers;
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors && widget.value.trim().isNotEmpty;
    final helperTexts = _getHelperTexts();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Input Field
        TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          enabled: !widget.disabled,
          readOnly: widget.readOnly,
          keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
          textInputAction: TextInputAction.next,
          onChanged: _handleChange,
          onFieldSubmitted: (_) => _handleBlur(),
          onTapOutside: (_) => _handleBlur(),
          decoration: InputDecoration(
            hintText: _getPlaceholder(),
            hintStyle: LexendTextStyles.lexend14Regular.copyWith(
              color: ColorPalette.placeHolderTextColor,
            ),
            prefixIcon: _getPrefixIcon(),
            prefixText: _getPrefixText(),
            prefixStyle: widget.prefixTextStyle ??
                LexendTextStyles.lexend14Regular.copyWith(
                  color: ColorPalette.placeHolderTextColor,
                ),
            suffixIcon: _getSuffixIcon(),
            suffixText: _getSuffixText(),
            suffixStyle: widget.suffixTextStyle ??
                LexendTextStyles.lexend14Regular.copyWith(
                  color: ColorPalette.placeHolderTextColor,
                ),
            errorText: hasErrors ? _errors.first : null,
            errorStyle: LexendTextStyles.lexend12Regular.copyWith(
              color: const Color(0xFFC73E1D),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.gray300),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.gray300),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.black),
                width: 2,
              ),
            ),
          ),
          style: LexendTextStyles.lexend14Regular.copyWith(
            color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
          ),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9\-\.\,]')),
          ],
        ),

        // Helper texts (only show when no errors)
        if (!hasErrors && helperTexts.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: helperTexts
                  .map((text) => Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Text(
                          text,
                          style: LexendTextStyles.lexend12Regular.copyWith(
                            color: ColorPalette.placeHolderTextColor,
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
      ],
    );
  }
}
