using MediatR;
using Shared.Common.Response;

namespace Application.Identity.Commands;

/// <summary>
/// Command for removing roles from a user
/// </summary>
public class RemoveRolesFromUserCommand : IRequest<ApiResponse<string>>
{
    /// <summary>
    /// User ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// List of role names to remove
    /// </summary>
    public List<string> RoleNames { get; set; } = new();
}
