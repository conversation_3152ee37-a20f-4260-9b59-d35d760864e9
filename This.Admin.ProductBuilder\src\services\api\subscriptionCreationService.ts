/**
 * Subscription Creation Service
 * Handles subscription creation workflow using the Secondary API (Port 7243)
 * 
 * API Routing:
 * - POST /tenants - Create tenant (Secondary API)
 * - POST /comprehensive-entity/create-product-structure - Create product structure (Secondary API)
 * - POST /subscriptions - Create subscription (Secondary API)
 * 
 * This service implements the three-step subscription creation workflow:
 * 1. Create/validate tenant
 * 2. Create product structure from template
 * 3. Create subscription
 */

import { BaseApiService } from '../baseApiService';
import type { ApiResult } from '../types';
import { HttpClientFactory } from '../httpClient';
import { dualApiService, apiOperations } from './dualApiService';
import type { CreateSubscriptionRequest, SubscriptionDto } from './subscriptionApiService';
import type { CreateTenantRequest, TenantDto } from './tenantApiService';

// Subscription creation workflow interfaces
export interface CreateProductStructureRequest {
  products: any[]; // Array of ProductStructureDto objects from parsed templateJson
}

export interface CreatedProductInfo {
  productId: string;
  name: string;
  wasCreated: boolean;
  objectsCreated: number;
  objectsExisting: number;
  metadataFieldsCreated: number;
  metadataFieldsExisting: number;
}

export interface CreateProductStructureResponse {
  success: boolean;
  message: string;
  totalProductsCreated: number;
  totalObjectsCreated: number;
  totalMetadataCreated: number;
  createdProducts: CreatedProductInfo[];
  errors: string[];
  warnings: string[];
  processingTimeMs: number;
}

export interface SubscriptionCreationWorkflowRequest {
  // Step 1: Tenant information
  tenant: CreateTenantRequest;
  
  // Step 2: Product structure
  templateJson: any;
  productInfo?: {
    name?: string;
    description?: string;
    version?: string;
  };
  
  // Step 3: Subscription details
  subscription: Omit<CreateSubscriptionRequest, 'productId'>;
}

export interface SubscriptionCreationWorkflowResponse {
  success: boolean;
  tenantId: string;
  productId: string;
  subscriptionId: string;
  subscription: SubscriptionDto;
  steps: {
    tenantCreated: boolean;
    productStructureCreated: boolean;
    subscriptionCreated: boolean;
  };
  errors?: string[];
}

/**
 * Subscription Creation Service Class
 * Uses Primary API (Port 7222) for tenant upsert, product structure creation, and subscription creation
 * Uses Secondary API (Port 7243) for tenant read operations only
 */
export class SubscriptionCreationService extends BaseApiService {
  constructor() {
    super(HttpClientFactory.subscriptionCreationClient, 'SubscriptionCreationAPI');
  }

  /**
   * Step 1: Create or validate tenant
   */
  async createTenant(data: CreateTenantRequest): Promise<string> {
    try {
      this.log('createTenant', { data, api: 'Primary (Port 7222) via Dual API Service' });

      const response = await apiOperations.tenants.upsert(data);
      
      if (typeof response === 'string') {
        return response;
      }
      
      if (response.succeeded && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to create tenant');
    } catch (error) {
      this.handleError('createTenant', error);
    }
  }

  /**
   * Step 2: Create product structure from template
   */
  async createProductStructure(data: CreateProductStructureRequest, tenant?: string): Promise<CreateProductStructureResponse> {
    try {
      this.log('createProductStructure', {
        productsCount: data.products?.length || 0,
        tenant: tenant,
        api: 'Primary (Port 7222) via Dual API Service'
      });

      // Send the products array directly to the API with tenant header
      const response = await apiOperations.subscriptionCreation.createProductStructure(data, tenant);

      if (response.succeeded && response.data) {
        // Validate that we have created products with productId
        if (!response.data.createdProducts || response.data.createdProducts.length === 0) {
          throw new Error('No products were created - createdProducts array is empty');
        }

        if (!response.data.createdProducts[0].productId) {
          throw new Error('Product was created but productId is missing from response');
        }

        this.log('createProductStructure_success', {
          productId: response.data.createdProducts[0].productId,
          totalProductsCreated: response.data.totalProductsCreated,
          totalObjectsCreated: response.data.totalObjectsCreated,
          totalMetadataCreated: response.data.totalMetadataCreated
        });

        return response.data;
      }

      throw new Error(response.message || 'Failed to create product structure');
    } catch (error) {
      this.handleError('createProductStructure', error);
    }
  }

  /**
   * Step 3: Create subscription
   */
  async createSubscription(data: CreateSubscriptionRequest, tenant?: string): Promise<SubscriptionDto> {
    try {
      this.log('createSubscription', { data, tenant, api: 'Primary (Port 7222) via Dual API Service' });

      const response = await apiOperations.subscriptionCreation.createSubscription(data, tenant);
      
      if (response.succeeded && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to create subscription');
    } catch (error) {
      this.handleError('createSubscription', error);
    }
  }

  /**
   * Complete subscription creation workflow
   * Executes all three steps in sequence with proper error handling
   */
  async createSubscriptionWorkflow(data: SubscriptionCreationWorkflowRequest): Promise<SubscriptionCreationWorkflowResponse> {
    const result: SubscriptionCreationWorkflowResponse = {
      success: false,
      tenantId: '',
      productId: '',
      subscriptionId: '',
      subscription: null as any,
      steps: {
        tenantCreated: false,
        productStructureCreated: false,
        subscriptionCreated: false
      },
      errors: []
    };

    try {
      this.log('createSubscriptionWorkflow', { 
        workflow: 'Starting three-step creation process',
        api: 'Secondary (Port 7243)'
      });

      // Step 1: Create tenant
      try {
        result.tenantId = await this.createTenant(data.tenant);
        result.steps.tenantCreated = true;
        this.log('createSubscriptionWorkflow_step1', { 
          success: true, 
          tenantId: result.tenantId 
        });
      } catch (error) {
        const errorMessage = `Step 1 failed: ${error.message}`;
        result.errors?.push(errorMessage);
        this.log('createSubscriptionWorkflow_step1_failed', { error: errorMessage });
        return result;
      }

      // Step 2: Create product structure
      try {
        // Parse templateJson to extract products array
        let parsedTemplate;
        try {
          parsedTemplate = typeof data.templateJson === 'string'
            ? JSON.parse(data.templateJson)
            : data.templateJson;
        } catch (parseError) {
          throw new Error(`Failed to parse templateJson: ${parseError.message}`);
        }

        if (!parsedTemplate.products || !Array.isArray(parsedTemplate.products)) {
          throw new Error('templateJson must contain a products array');
        }

        const productStructure = await this.createProductStructure({
          products: parsedTemplate.products
        }, result.tenantId);

        // Extract productId from the first created product
        if (!productStructure.createdProducts || productStructure.createdProducts.length === 0) {
          throw new Error('No products were created in the product structure response');
        }

        result.productId = productStructure.createdProducts[0].productId;
        result.steps.productStructureCreated = true;
        this.log('createSubscriptionWorkflow_step2', {
          success: true,
          productId: result.productId,
          totalProductsCreated: productStructure.totalProductsCreated,
          totalObjectsCreated: productStructure.totalObjectsCreated,
          totalMetadataCreated: productStructure.totalMetadataCreated,
          processingTimeMs: productStructure.processingTimeMs
        });
      } catch (error) {
        const errorMessage = `Step 2 failed: ${error.message}`;
        result.errors?.push(errorMessage);
        this.log('createSubscriptionWorkflow_step2_failed', { error: errorMessage });
        return result;
      }

      // Step 3: Create subscription
      try {
        const subscriptionData: CreateSubscriptionRequest = {
          ...data.subscription,
          productId: result.productId
        };

        result.subscription = await this.createSubscription(subscriptionData, result.tenantId);
        result.subscriptionId = result.subscription.id;
        result.steps.subscriptionCreated = true;
        result.success = true;
        
        this.log('createSubscriptionWorkflow_step3', {
          success: true,
          subscriptionId: result.subscriptionId,
          productId: subscriptionData.productId,
          templateDetails: subscriptionData.templateDetails,
          api: 'Primary (Port 7222) via Dual API Service'
        });
      } catch (error) {
        const errorMessage = `Step 3 failed: ${error.message}`;
        result.errors?.push(errorMessage);
        this.log('createSubscriptionWorkflow_step3_failed', { error: errorMessage });
        return result;
      }

      this.log('createSubscriptionWorkflow_completed', { 
        success: true,
        tenantId: result.tenantId,
        productId: result.productId,
        subscriptionId: result.subscriptionId
      });

      return result;
    } catch (error) {
      const errorMessage = `Workflow failed: ${error.message}`;
      result.errors?.push(errorMessage);
      this.handleError('createSubscriptionWorkflow', error);
      return result;
    }
  }

  /**
   * Validate template JSON before creating product structure
   */
  validateTemplateJson(templateJson: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!templateJson) {
      errors.push('Template JSON is required');
      return { valid: false, errors };
    }

    if (typeof templateJson === 'string') {
      try {
        JSON.parse(templateJson);
      } catch (e) {
        errors.push('Invalid JSON format');
        return { valid: false, errors };
      }
    }

    // Add more validation rules as needed
    if (!templateJson.products || !Array.isArray(templateJson.products)) {
      errors.push('Template must contain a products array');
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Get creation workflow status
   */
  async getWorkflowStatus(workflowId: string): Promise<any> {
    try {
      // This would be implemented if the API supports workflow tracking
      this.log('getWorkflowStatus', { workflowId, note: 'Not implemented yet' });
      return { status: 'unknown' };
    } catch (error) {
      this.handleError('getWorkflowStatus', error);
    }
  }
}

// Export singleton instance
export const subscriptionCreationService = new SubscriptionCreationService();
