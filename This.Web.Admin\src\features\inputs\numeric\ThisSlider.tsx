// components/ThisSlider.tsx
import React, { useState, useRef, useCallback } from 'react';

interface SliderMark {
  value: number;
  label?: string;
}

interface ThisSliderProps {
  id: string;
  label: string;
  value: number | number[];
  onChange: (value: number | number[]) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  required?: boolean;
  min?: number;
  max?: number;
  step?: number;
  range?: boolean;
  marks?: SliderMark[];
  showMarks?: boolean;
  showValue?: boolean;
  showMinMax?: boolean;
  showTicks?: boolean;
  tickCount?: number;
  formatValue?: (value: number) => string;
  orientation?: 'horizontal' | 'vertical';
  size?: 'small' | 'medium' | 'large';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  customValidation?: (value: number | number[]) => string | null;
}

interface ValidationRule {
  test: (value: number | number[]) => boolean;
  message: string;
}

const ThisSlider: React.FC<ThisSliderProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  required = false,
  min = 0,
  max = 100,
  step = 1,
  range = false,
  marks = [],
  showMarks = false,
  showValue = true,
  showMinMax = false,
  showTicks = false,
  tickCount = 10,
  formatValue = (val) => val.toString(),
  orientation = 'horizontal',
  size = 'medium',
  color = 'primary',
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [activeThumb, setActiveThumb] = useState<number | null>(null);
  const sliderRef = useRef<HTMLDivElement>(null);

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (val) => {
          if (range && Array.isArray(val)) {
            return val.length === 2 && val[0] !== val[1];
          }
          return typeof val === 'number' && !isNaN(val);
        },
        message: `${label} is required`
      });
    }

    // 2. Range validation
    rules.push({
      test: (val) => {
        if (range && Array.isArray(val)) {
          return val.every(v => v >= min && v <= max);
        }
        return typeof val === 'number' && val >= min && val <= max;
      },
      message: `${label} must be between ${min} and ${max}`
    });

    // 3. Step validation
    if (step > 0) {
      rules.push({
        test: (val) => {
          if (range && Array.isArray(val)) {
            return val.every(v => (v - min) % step === 0);
          }
          return typeof val === 'number' && (val - min) % step === 0;
        },
        message: `${label} must be in increments of ${step}`
      });
    }

    // 4. Range order validation (for range sliders)
    if (range) {
      rules.push({
        test: (val) => {
          if (Array.isArray(val) && val.length === 2) {
            return val[0] <= val[1];
          }
          return true;
        },
        message: 'Range start must be less than or equal to range end'
      });
    }

    // 5. Custom validation
    if (customValidation) {
      rules.push({
        test: (val) => {
          const customError = customValidation(val);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateValue = (val: number | number[]): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(val)) {
        return [rule.message];
      }
    }

    return [];
  };

  const normalizeValue = (val: number): number => {
    // Clamp to min/max
    const clamped = Math.max(min, Math.min(max, val));

    // Snap to step
    if (step > 0) {
      const steps = Math.round((clamped - min) / step);
      return min + steps * step;
    }

    return clamped;
  };

  const getValueFromPosition = useCallback((clientX: number, clientY: number): number => {
    if (!sliderRef.current) return min;

    const rect = sliderRef.current.getBoundingClientRect();
    let percentage: number;

    if (orientation === 'vertical') {
      percentage = 1 - (clientY - rect.top) / rect.height;
    } else {
      percentage = (clientX - rect.left) / rect.width;
    }

    percentage = Math.max(0, Math.min(1, percentage));
    const rawValue = min + percentage * (max - min);
    return normalizeValue(rawValue);
  }, [min, max, step, orientation]);

  const handleMouseDown = (e: React.MouseEvent, thumbIndex?: number) => {
    if (disabled || readOnly) return;

    e.preventDefault();

    const newValue = getValueFromPosition(e.clientX, e.clientY);

    if (range && Array.isArray(value)) {
      if (thumbIndex !== undefined) {
        setActiveThumb(thumbIndex);
        const newRangeValue = [...value];
        newRangeValue[thumbIndex] = newValue;

        // Ensure proper order
        if (thumbIndex === 0 && newRangeValue[0] > newRangeValue[1]) {
          newRangeValue[0] = newRangeValue[1];
        } else if (thumbIndex === 1 && newRangeValue[1] < newRangeValue[0]) {
          newRangeValue[1] = newRangeValue[0];
        }

        onChange(newRangeValue);
      } else {
        // Determine which thumb to move based on proximity
        const distances = value.map(v => Math.abs(v - newValue));
        const closestIndex = distances[0] <= distances[1] ? 0 : 1;
        setActiveThumb(closestIndex);

        const newRangeValue = [...value];
        newRangeValue[closestIndex] = newValue;
        onChange(newRangeValue);
      }
    } else {
      onChange(newValue);
    }

    // Real-time validation
    const newErrors = validateValue(range && Array.isArray(value) ? value : newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleKeyDown = (e: React.KeyboardEvent, thumbIndex?: number) => {
    if (disabled || readOnly) return;

    let delta = 0;
    switch (e.key) {
      case 'ArrowLeft':
      case 'ArrowDown':
        delta = -step;
        break;
      case 'ArrowRight':
      case 'ArrowUp':
        delta = step;
        break;
      case 'PageDown':
        delta = -step * 10;
        break;
      case 'PageUp':
        delta = step * 10;
        break;
      case 'Home':
        delta = min - (typeof value === 'number' ? value : value[thumbIndex || 0]);
        break;
      case 'End':
        delta = max - (typeof value === 'number' ? value : value[thumbIndex || 0]);
        break;
      default:
        return;
    }

    e.preventDefault();

    if (range && Array.isArray(value) && thumbIndex !== undefined) {
      const newRangeValue = [...value];
      newRangeValue[thumbIndex] = normalizeValue(value[thumbIndex] + delta);

      // Ensure proper order
      if (thumbIndex === 0 && newRangeValue[0] > newRangeValue[1]) {
        newRangeValue[0] = newRangeValue[1];
      } else if (thumbIndex === 1 && newRangeValue[1] < newRangeValue[0]) {
        newRangeValue[1] = newRangeValue[0];
      }

      onChange(newRangeValue);

      // Real-time validation
      const newErrors = validateValue(newRangeValue);
      setErrors(newErrors);
      onValidation?.(newErrors);
    } else if (!range && typeof value === 'number') {
      const newValue = normalizeValue(value + delta);
      onChange(newValue);

      // Real-time validation
      const newErrors = validateValue(newValue);
      setErrors(newErrors);
      onValidation?.(newErrors);
    }
  };

  const getPercentage = (val: number): number => {
    return ((val - min) / (max - min)) * 100;
  };

  const generateTicks = (): number[] => {
    if (!showTicks) return [];

    const ticks: number[] = [];
    const tickStep = (max - min) / (tickCount - 1);

    for (let i = 0; i < tickCount; i++) {
      ticks.push(min + i * tickStep);
    }

    return ticks;
  };

  const hasErrors = errors.length > 0;
  const ticks = generateTicks();

  // Get size classes
  const getSizeClass = (): string => {
    switch (size) {
      case 'small':
        return 'slider-size-small';
      case 'large':
        return 'slider-size-large';
      default:
        return 'slider-size-medium';
    }
  };

  // Get color classes
  const getColorClass = (): string => {
    return `slider-color-${color}`;
  };

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label">
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Slider */}
      <div className="text-input-wrapper">
        <div className={`slider-container ${getSizeClass()} ${getColorClass()} ${orientation} ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''}`}>

          {/* Min/Max Labels */}
          {showMinMax && (
            <div className="slider-minmax">
              <span className="slider-min-label">{formatValue(min)}</span>
              <span className="slider-max-label">{formatValue(max)}</span>
            </div>
          )}

          {/* Slider Track */}
          <div
            ref={sliderRef}
            className="slider-track"
            onMouseDown={handleMouseDown}
            role="slider"
            aria-valuemin={min}
            aria-valuemax={max}
            aria-valuenow={typeof value === 'number' ? value : value[0]}
            aria-label={label}
            tabIndex={disabled || readOnly ? -1 : 0}
            onKeyDown={(e) => handleKeyDown(e, 0)}
          >
            {/* Track Background */}
            <div className="slider-track-bg" />

            {/* Track Fill */}
            <div
              className="slider-track-fill"
              style={{
                [orientation === 'vertical' ? 'height' : 'width']:
                  range && Array.isArray(value)
                    ? `${getPercentage(value[1]) - getPercentage(value[0])}%`
                    : `${getPercentage(typeof value === 'number' ? value : value[0])}%`,
                [orientation === 'vertical' ? 'bottom' : 'left']:
                  range && Array.isArray(value)
                    ? `${getPercentage(value[0])}%`
                    : '0%'
              }}
            />

            {/* Ticks */}
            {showTicks && ticks.map((tick, index) => (
              <div
                key={index}
                className="slider-tick"
                style={{
                  [orientation === 'vertical' ? 'bottom' : 'left']: `${getPercentage(tick)}%`
                }}
              />
            ))}

            {/* Marks */}
            {showMarks && marks.map((mark, index) => (
              <div
                key={index}
                className="slider-mark"
                style={{
                  [orientation === 'vertical' ? 'bottom' : 'left']: `${getPercentage(mark.value)}%`
                }}
              >
                {mark.label && <span className="slider-mark-label">{mark.label}</span>}
              </div>
            ))}

            {/* Thumbs */}
            {range && Array.isArray(value) ? (
              value.map((val, index) => (
                <div
                  key={index}
                  className={`slider-thumb ${activeThumb === index ? 'active' : ''}`}
                  style={{
                    [orientation === 'vertical' ? 'bottom' : 'left']: `${getPercentage(val)}%`
                  }}
                  onMouseDown={(e) => handleMouseDown(e, index)}
                  onKeyDown={(e) => handleKeyDown(e, index)}
                  role="slider"
                  aria-valuemin={min}
                  aria-valuemax={max}
                  aria-valuenow={val}
                  aria-label={`${label} ${index === 0 ? 'start' : 'end'}`}
                  tabIndex={disabled || readOnly ? -1 : 0}
                />
              ))
            ) : (
              <div
                className="slider-thumb"
                style={{
                  [orientation === 'vertical' ? 'bottom' : 'left']: `${getPercentage(typeof value === 'number' ? value : value[0])}%`
                }}
                onMouseDown={handleMouseDown}
                onKeyDown={handleKeyDown}
                role="slider"
                aria-valuemin={min}
                aria-valuemax={max}
                aria-valuenow={typeof value === 'number' ? value : value[0]}
                aria-label={label}
                tabIndex={disabled || readOnly ? -1 : 0}
              />
            )}
          </div>

          {/* Value Display */}
          {showValue && (
            <div className="slider-value">
              {range && Array.isArray(value)
                ? `${formatValue(value[0])} - ${formatValue(value[1])}`
                : formatValue(typeof value === 'number' ? value : value[0])
              }
            </div>
          )}
        </div>

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisSlider;
