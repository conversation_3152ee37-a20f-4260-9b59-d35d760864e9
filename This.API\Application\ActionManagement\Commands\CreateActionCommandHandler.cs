using Abstraction.Database.Repositories;
using Application.ActionManagement.DTOs;
using Application.ActionManagement.Specifications;
using Domain.Entities;
using Finbuckle.MultiTenant;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.ActionManagement.Commands;

/// <summary>
/// Create Action command handler
/// </summary>
public class CreateActionCommandHandler : IRequestHandler<CreateActionCommand, Result<ActionDto>>
{
    private readonly IRepository<Domain.Entities.Action> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateActionCommandHandler(
        IRepository<Domain.Entities.Action> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ActionDto>> Handle(CreateActionCommand request, CancellationToken cancellationToken)
    {
        // Check if Action with same name already exists for this tenant
        var existingAction = await _repository.GetBySpecAsync(
            new ActionByNameSpec(request.Name), 
            cancellationToken);
        
        if (existingAction != null)
        {
            return Result<ActionDto>.Failure($"Action with name '{request.Name}' already exists.");
        }

        // Create new Action
        var action = request.Adapt<Domain.Entities.Action>();

        var createdAction = await _repository.AddAsync(action, cancellationToken);

        var dto = createdAction.Adapt<ActionDto>();

        return Result<ActionDto>.Success(dto);
    }
}
