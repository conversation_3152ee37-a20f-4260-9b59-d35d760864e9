using Application.IntegrationConfigurations.DTOs;
using Application.IntegrationConfigurations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Commands;

/// <summary>
/// Create integration configuration command handler
/// </summary>
public class CreateIntegrationConfigurationCommandHandler : IRequestHandler<CreateIntegrationConfigurationCommand, Result<ViewIntegrationConfigurationDto>>
{
    private readonly IRepository<IntegrationConfiguration> _configurationRepository;
    private readonly IReadRepository<Integration> _integrationRepository;
    private readonly IReadRepository<IntegrationApi> _integrationApiRepository;
    private readonly IReadRepository<Domain.Entities.Object> _objectRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateIntegrationConfigurationCommandHandler(
        IRepository<IntegrationConfiguration> configurationRepository,
        IReadRepository<Integration> integrationRepository,
        IReadRepository<IntegrationApi> integrationApiRepository,
        IReadRepository<Domain.Entities.Object> objectRepository)
    {
        _configurationRepository = configurationRepository;
        _integrationRepository = integrationRepository;
        _integrationApiRepository = integrationApiRepository;
        _objectRepository = objectRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<ViewIntegrationConfigurationDto>> Handle(CreateIntegrationConfigurationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Verify integration exists
            var integration = await _integrationRepository.GetByIdAsync(request.IntegrationId, cancellationToken);
            if (integration == null)
            {
                return Result<ViewIntegrationConfigurationDto>.Failure("Integration not found.");
            }

            // Verify integration API exists
            var integrationApi = await _integrationApiRepository.GetByIdAsync(request.IntegrationApiId, cancellationToken);
            if (integrationApi == null)
            {
                return Result<ViewIntegrationConfigurationDto>.Failure("Integration API not found.");
            }

            // Verify object exists
            var obj = await _objectRepository.GetByIdAsync(request.ObjectId, cancellationToken);
            if (obj == null)
            {
                return Result<ViewIntegrationConfigurationDto>.Failure("Object not found.");
            }

            // Check if configuration already exists for this combination
            var existingConfigSpec = new IntegrationConfigurationByIntegrationApiObjectSpec(
                request.IntegrationId, request.IntegrationApiId, request.ObjectId);
            var existingConfig = await _configurationRepository.GetBySpecAsync(existingConfigSpec, cancellationToken);

            if (existingConfig != null)
            {
                return Result<ViewIntegrationConfigurationDto>.Failure("Configuration already exists for this combination.");
            }

            // Create new configuration
            var configuration = new IntegrationConfiguration
            {
                IntegrationId = request.IntegrationId,
                IntegrationApiId = request.IntegrationApiId,
                ObjectId = request.ObjectId,
                Direction = request.Direction,
                IsActive = request.IsActive
            };

            var createdConfiguration = await _configurationRepository.AddAsync(configuration, cancellationToken);

            // Create view DTO with related entity names
            var viewDto = new ViewIntegrationConfigurationDto
            {
                Id = createdConfiguration.Id,
                IntegrationId = createdConfiguration.IntegrationId,
                IntegrationName = integration.Name,
                IntegrationApiId = createdConfiguration.IntegrationApiId,
                IntegrationApiName = integrationApi.Name,
                ObjectId = createdConfiguration.ObjectId,
                ObjectName = obj.Name,
                Direction = createdConfiguration.Direction,
                IsActive = createdConfiguration.IsActive,
                CreatedAt = createdConfiguration.CreatedAt,
                CreatedBy = createdConfiguration.CreatedBy,
                ModifiedAt = createdConfiguration.ModifiedAt,
                ModifiedBy = createdConfiguration.ModifiedBy
            };

            return Result<ViewIntegrationConfigurationDto>.Success(viewDto);
        }
        catch (Exception ex)
        {
            return Result<ViewIntegrationConfigurationDto>.Failure($"Failed to create integration configuration: {ex.Message}");
        }
    }
}
