/**
 * Global Tenant Context Hook
 * Manages the currently selected tenant across the entire application
 */

import { useSelector, useDispatch } from 'react-redux';
import { useCallback, useEffect } from 'react';
import type { RootState } from '../store';
import { setSelectedTenantId, fetchActiveTenants } from '../store/slices/tenantSlice';

const TENANT_STORAGE_KEY = 'selectedTenantId';

export interface TenantContextValue {
  selectedTenantId: string | null;
  selectedTenant: any | null;
  availableTenants: any[];
  isLoading: boolean;
  error: string | null;
  setSelectedTenant: (tenantId: string | null) => void;
  refreshTenants: () => void;
}

/**
 * Hook to manage global tenant selection
 */
export const useTenantContext = (): TenantContextValue => {
  const dispatch = useDispatch();
  
  // Get state from Redux
  const {
    selectedTenantId,
    activeTenants,
    loading,
    errors
  } = useSelector((state: RootState) => state.tenants);

  // Get selected tenant object
  const selectedTenant = selectedTenantId 
    ? activeTenants.find(tenant => tenant.id === selectedTenantId || tenant.name === selectedTenantId) || null
    : null;

  // Initialize tenant from localStorage on app load
  useEffect(() => {
    const storedTenantId = localStorage.getItem(TENANT_STORAGE_KEY);
    if (storedTenantId && !selectedTenantId) {
      dispatch(setSelectedTenantId(storedTenantId));
    }
  }, [dispatch, selectedTenantId]);

  // Load available tenants if not loaded
  useEffect(() => {
    if (activeTenants.length === 0 && !loading.active && !errors.active) {
      dispatch(fetchActiveTenants());
    }
  }, [dispatch, activeTenants.length, loading.active, errors.active]);

  // Set selected tenant and persist to localStorage
  const setSelectedTenant = useCallback((tenantId: string | null) => {
    dispatch(setSelectedTenantId(tenantId));
    
    if (tenantId) {
      localStorage.setItem(TENANT_STORAGE_KEY, tenantId);
    } else {
      localStorage.removeItem(TENANT_STORAGE_KEY);
    }
  }, [dispatch]);

  // Refresh available tenants
  const refreshTenants = useCallback(() => {
    dispatch(fetchActiveTenants());
  }, [dispatch]);

  return {
    selectedTenantId,
    selectedTenant,
    availableTenants: activeTenants,
    isLoading: loading.active || loading.dropdown,
    error: errors.active || errors.dropdown,
    setSelectedTenant,
    refreshTenants
  };
};

/**
 * Hook to get the current tenant for API calls
 * Returns the tenant ID that should be used in API headers
 */
export const useCurrentTenant = (): string | undefined => {
  const { selectedTenantId } = useTenantContext();
  
  // Return undefined if no tenant is selected (for APIs that should get all tenants)
  // Return the tenant ID if one is selected
  return selectedTenantId || undefined;
};