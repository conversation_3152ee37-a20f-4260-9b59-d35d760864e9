using Application.Identity.Commands;
using Abstraction.Identity;
using MediatR;
using Shared.Common.Response;

namespace Application.Identity.Handlers;

/// <summary>
/// Handler for AssignRolesToUserCommand
/// </summary>
public class AssignRolesToUserCommandHandler : IRequestHandler<AssignRolesToUserCommand, ApiResponse<string>>
{
    private readonly IIdentityService _identityService;

    /// <summary>
    /// Constructor
    /// </summary>
    public AssignRolesToUserCommandHandler(IIdentityService identityService)
    {
        _identityService = identityService;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<ApiResponse<string>> Handle(AssignRolesToUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate the request
            if (string.IsNullOrWhiteSpace(request.UserId))
            {
                return new ApiResponse<string>(false, "User ID is required.", string.Empty);
            }

            if (request.RoleNames == null || !request.RoleNames.Any())
            {
                return new ApiResponse<string>(false, "At least one role name is required.", string.Empty);
            }

            // Check if user exists
            var user = await _identityService.GetUserByIdAsync(request.UserId);
            if (user == null)
            {
                return new ApiResponse<string>(false, "User not found.", string.Empty);
            }

            // Assign roles to user
            var result = await _identityService.AssignRolesToUserAsync(request.UserId, request.RoleNames);

            if (result.Succeeded)
            {
                return new ApiResponse<string>(true, "Roles assigned successfully.", request.UserId);
            }
            else
            {
                return new ApiResponse<string>(false, result.Message, string.Empty);
            }
        }
        catch (Exception ex)
        {
            return new ApiResponse<string>(false, $"Error assigning roles: {ex.Message}", string.Empty);
        }
    }
}
