import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';
import { subscriptionService } from '../services/subscriptionService';

interface ApiTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export const ApiTestComponent: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<ApiTestResult[]>([]);

  const runApiTest = async () => {
    setIsLoading(true);
    setTestResults([]);
    const results: ApiTestResult[] = [];

    // Test 1: Basic API connectivity
    try {
      results.push({ success: true, message: 'Starting API tests...' });
      setTestResults([...results]);

      // Test comprehensive subscriptions endpoint
      const response = await subscriptionService.getComprehensiveSubscriptions({
        pageSize: 5,
        includeSummary: true
      });

      if (response.succeeded) {
        results.push({
          success: true,
          message: 'Comprehensive subscriptions API: SUCCESS',
          data: {
            subscriptionCount: response.data.subscriptions.length,
            totalCount: response.data.totalCount,
            hasSummary: !!response.data.summary
          }
        });
      } else {
        results.push({
          success: false,
          message: 'Comprehensive subscriptions API: FAILED',
          error: response.message
        });
      }
    } catch (error) {
      results.push({
        success: false,
        message: 'Comprehensive subscriptions API: ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 2: Legacy compatibility
    try {
      const legacyResponse = await subscriptionService.getSubscriptions({
        pageSize: 3
      }, 'lrbnewqa');

      if (legacyResponse.succeeded) {
        results.push({
          success: true,
          message: 'Legacy subscriptions API: SUCCESS',
          data: {
            subscriptionCount: legacyResponse.data.length,
            totalItems: legacyResponse.totalItems
          }
        });
      } else {
        results.push({
          success: false,
          message: 'Legacy subscriptions API: FAILED',
          error: legacyResponse.message
        });
      }
    } catch (error) {
      results.push({
        success: false,
        message: 'Legacy subscriptions API: ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 3: Summary endpoint
    try {
      const summary = await subscriptionService.getSubscriptionSummary();
      results.push({
        success: true,
        message: 'Summary API: SUCCESS',
        data: {
          activeSubscriptions: summary.activeSubscriptions,
          expiredSubscriptions: summary.expiredSubscriptions,
          uniqueTenants: summary.uniqueTenants
        }
      });
    } catch (error) {
      results.push({
        success: false,
        message: 'Summary API: ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 4: Available tenants
    try {
      const tenants = await subscriptionService.getAvailableTenants();
      results.push({
        success: true,
        message: 'Available tenants: SUCCESS',
        data: { tenants }
      });
    } catch (error) {
      results.push({
        success: false,
        message: 'Available tenants: ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    setTestResults(results);
    setIsLoading(false);
  };

  return (
    <Card className="mb-4">
      <Card.Header>
        <h5 className="mb-0">API Integration Test</h5>
      </Card.Header>
      <Card.Body>
        <div className="d-flex gap-2 mb-3">
          <Button 
            variant="primary" 
            onClick={runApiTest} 
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Running Tests...
              </>
            ) : (
              'Run API Tests'
            )}
          </Button>
        </div>

        {testResults.length > 0 && (
          <div>
            <h6>Test Results:</h6>
            {testResults.map((result, index) => (
              <Alert 
                key={index} 
                variant={result.success ? 'success' : 'danger'}
                className="mb-2"
              >
                <div className="fw-bold">{result.message}</div>
                {result.data && (
                  <div className="small mt-1">
                    <pre>{JSON.stringify(result.data, null, 2)}</pre>
                  </div>
                )}
                {result.error && (
                  <div className="small mt-1 text-danger">
                    Error: {result.error}
                  </div>
                )}
              </Alert>
            ))}
          </div>
        )}

        <div className="mt-3">
          <h6>API Endpoint Information:</h6>
          <ul className="small">
            <li><strong>Base URL:</strong> https://localhost:7222/api</li>
            <li><strong>Comprehensive Endpoint:</strong> /comprehensive-entity/subscriptions</li>
            <li><strong>Authentication:</strong> None required</li>
            <li><strong>CORS:</strong> Must be configured for localhost:3000</li>
          </ul>
        </div>

        <div className="mt-3">
          <h6>Troubleshooting:</h6>
          <ul className="small">
            <li>Ensure the API server is running on https://localhost:7222</li>
            <li>Check that CORS is configured to allow requests from this domain</li>
            <li>Verify SSL certificate is valid for localhost</li>
            <li>Check browser console for detailed error messages</li>
          </ul>
        </div>
      </Card.Body>
    </Card>
  );
};
