/**
 * Tenant API Utilities
 * Helper functions for making API calls with automatic tenant header injection
 */

import { subscriptionApiService } from '../services/api/subscriptionApiService';
import { apiOperations } from '../services/api/dualApiService';
import { comprehensiveEntityService } from '../services/tenantAwareApiService';

/**
 * Get the currently selected tenant from localStorage
 */
export const getCurrentTenant = (): string | undefined => {
  try {
    return localStorage.getItem('selectedTenantId') || undefined;
  } catch {
    return undefined;
  }
};

/**
 * Set the current tenant in localStorage
 */
export const setCurrentTenant = (tenantId: string | null): void => {
  try {
    if (tenantId) {
      localStorage.setItem('selectedTenantId', tenantId);
    } else {
      localStorage.removeItem('selectedTenantId');
    }
  } catch (error) {
    console.error('Failed to set current tenant:', error);
  }
};

/**
 * Tenant-aware API call wrapper
 * Automatically includes the selected tenant header in API calls
 */
export const withTenantHeader = {
  /**
   * Get subscriptions with automatic tenant header
   */
  getSubscriptions: async (params?: any) => {
    const tenant = getCurrentTenant();
    console.log('🔄 API Call: GET /subscriptions with tenant:', tenant);
    
    try {
      return await subscriptionApiService.getSubscriptions(params);
    } catch (error) {
      console.error('❌ Error getting subscriptions:', error);
      throw error;
    }
  },

  /**
   * Get subscription by ID with automatic tenant header
   */
  getSubscriptionById: async (id: string) => {
    const tenant = getCurrentTenant();
    console.log('🔄 API Call: GET /subscriptions/' + id + ' with tenant:', tenant);
    
    try {
      return await subscriptionApiService.getSubscriptionById(id);
    } catch (error) {
      console.error('❌ Error getting subscription by ID:', error);
      throw error;
    }
  },

  /**
   * Create subscription with automatic tenant header
   */
  createSubscription: async (data: any) => {
    const tenant = getCurrentTenant();
    console.log('🔄 API Call: POST /subscriptions with tenant:', tenant);
    
    try {
      return await subscriptionApiService.createSubscription(data);
    } catch (error) {
      console.error('❌ Error creating subscription:', error);
      throw error;
    }
  },

  /**
   * Create product structure with automatic tenant header
   */
  createProductStructure: async (data: any) => {
    const tenant = getCurrentTenant();
    console.log('🔄 API Call: POST /comprehensive-entity/create-product-structure with tenant:', tenant);
    
    try {
      return await apiOperations.subscriptionCreation.createProductStructure(data);
    } catch (error) {
      console.error('❌ Error creating product structure:', error);
      throw error;
    }
  },

  /**
   * Get subscription statistics with automatic tenant header
   */
  getSubscriptionStats: async () => {
    const tenant = getCurrentTenant();
    console.log('🔄 API Call: GET /subscriptions/stats with tenant:', tenant);
    
    try {
      return await subscriptionApiService.getSubscriptionStats();
    } catch (error) {
      console.error('❌ Error getting subscription stats:', error);
      throw error;
    }
  },

  /**
   * Search subscriptions with automatic tenant header
   */
  searchSubscriptions: async (searchTerm: string, params?: any) => {
    const tenant = getCurrentTenant();
    console.log('🔄 API Call: GET /subscriptions/search with tenant:', tenant);
    
    try {
      return await subscriptionApiService.searchSubscriptions(searchTerm, params);
    } catch (error) {
      console.error('❌ Error searching subscriptions:', error);
      throw error;
    }
  }
};

/**
 * Debug utility to show current tenant and generate curl commands
 */
export const debugTenantApi = {
  /**
   * Get current tenant info for debugging
   */
  getCurrentTenantInfo: () => {
    const tenantId = getCurrentTenant();
    return {
      tenantId,
      source: tenantId ? 'localStorage' : 'none',
      timestamp: new Date().toISOString()
    };
  },

  /**
   * Generate curl command for subscription API calls
   */
  generateSubscriptionsCurl: (params?: any) => {
    const tenant = getCurrentTenant();
    const baseUrl = 'https://localhost:7222/api';
    let curl = `curl --location '${baseUrl}/subscriptions' \\\n`;
    curl += `--header 'accept: application/json' \\\n`;
    
    if (tenant) {
      curl += `--header 'tenant: ${tenant}' \\\n`;
    }
    
    if (params) {
      const queryString = new URLSearchParams(params).toString();
      curl = curl.replace('/subscriptions', `/subscriptions?${queryString}`);
    }
    
    return curl.slice(0, -4); // Remove trailing " \\\n"
  },

  /**
   * Generate curl command for create product structure
   */
  generateCreateProductStructureCurl: (data: any) => {
    const tenant = getCurrentTenant();
    const baseUrl = 'https://localhost:7222/api';
    let curl = `curl --location '${baseUrl}/comprehensive-entity/create-product-structure' \\\n`;
    curl += `--header 'accept: application/json' \\\n`;
    curl += `--header 'Content-Type: application/json' \\\n`;
    
    if (tenant) {
      curl += `--header 'tenant: ${tenant}' \\\n`;
    }
    
    curl += `--data '${JSON.stringify(data, null, 2)}'`;
    
    return curl;
  },

  /**
   * Log current API configuration
   */
  logApiConfig: () => {
    const tenantInfo = debugTenantApi.getCurrentTenantInfo();
    console.group('🔧 Tenant API Configuration');
    console.log('Current Tenant:', tenantInfo.tenantId || 'None selected');
    console.log('Source:', tenantInfo.source);
    console.log('Timestamp:', tenantInfo.timestamp);
    console.log('Base URL:', 'https://localhost:7222/api');
    console.groupEnd();
  }
};

/**
 * Utility to copy API calls to clipboard for debugging
 */
export const copyApiCall = {
  /**
   * Copy curl command to clipboard
   */
  toCurl: async (endpoint: string, method: string = 'GET', data?: any) => {
    const tenant = getCurrentTenant();
    const baseUrl = 'https://localhost:7222/api';
    
    let curl = `curl --location '${baseUrl}${endpoint}' \\\n`;
    curl += `--header 'accept: application/json' \\\n`;
    
    if (tenant) {
      curl += `--header 'tenant: ${tenant}' \\\n`;
    }
    
    if (method !== 'GET') {
      curl += `--header 'Content-Type: application/json' \\\n`;
      if (data) {
        curl += `--data '${JSON.stringify(data, null, 2)}'`;
      }
    }
    
    try {
      await navigator.clipboard.writeText(curl);
      console.log('✅ Copied to clipboard:', curl);
    } catch (error) {
      console.error('❌ Failed to copy to clipboard:', error);
    }
  }
};

/**
 * Export commonly used functions
 */
export {
  subscriptionApiService,
  apiOperations,
  comprehensiveEntityService
};