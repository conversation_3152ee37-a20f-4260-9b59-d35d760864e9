using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetTenantContextLookup;

/// <summary>
/// Handler for GetTenantContextLookupQuery
/// </summary>
public class GetTenantContextLookupQueryHandler : IRequestHandler<GetTenantContextLookupQuery, Result<List<TenantContextLookupDto>>>
{
    private readonly IRepository<TenantContext> _tenantContextRepository;
    private readonly ILogger<GetTenantContextLookupQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetTenantContextLookupQueryHandler(
        IRepository<TenantContext> tenantContextRepository,
        ILogger<GetTenantContextLookupQueryHandler> logger)
    {
        _tenantContextRepository = tenantContextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<List<TenantContextLookupDto>>> Handle(GetTenantContextLookupQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting tenant context lookup data with filters: Category={Category}, SearchTerm={SearchTerm}, IncludeInactive={IncludeInactive}",
                request.Category, request.SearchTerm, request.IncludeInactive);

            // Create specification - tenant filtering is handled automatically by the repository
            var spec = new TenantContextLookupSpec(
                searchTerm: request.SearchTerm,
                category: request.Category,
                includeInactive: request.IncludeInactive);

            // Execute query using specification
            var tenantContexts = await _tenantContextRepository.ListAsync(spec, cancellationToken);

            // Map to lookup DTOs
            var lookupDtos = tenantContexts.Adapt<List<TenantContextLookupDto>>();

            _logger.LogInformation("Successfully retrieved {Count} tenant context lookup items", lookupDtos.Count);

            return Result<List<TenantContextLookupDto>>.Success(lookupDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting tenant context lookup data");
            return Result<List<TenantContextLookupDto>>.Failure($"Error retrieving tenant context lookup data: {ex.Message}");
        }
    }
}
