namespace Application.RoleActions.DTOs;

/// <summary>
/// RoleActions DTO
/// </summary>
public class RoleActionsDto
{
    /// <summary>
    /// Unique identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Role ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// Action ID
    /// </summary>
    public Guid ActionId { get; set; }

    /// <summary>
    /// Whether this role-action relationship is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// When the relationship was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Who created the relationship
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// When the relationship was last modified
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Who last modified the relationship
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}

/// <summary>
/// Update role actions request
/// </summary>
public class UpdateRoleActionsRequest
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// List of action IDs to associate with the role
    /// </summary>
    public List<Guid> ActionIds { get; set; } = new();
}

/// <summary>
/// Bulk update role actions request
/// </summary>
public class BulkUpdateRoleActionsRequest
{
    /// <summary>
    /// List of role-action updates
    /// </summary>
    public List<UpdateRoleActionsRequest> RoleActions { get; set; } = new();
}

/// <summary>
/// Role actions response
/// </summary>
public class RoleActionsResponse
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// List of associated actions
    /// </summary>
    public List<RoleActionsDto> Actions { get; set; } = new();
}
