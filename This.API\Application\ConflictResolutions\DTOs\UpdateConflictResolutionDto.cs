namespace Application.ConflictResolutions.DTOs;

/// <summary>
/// Update Conflict Resolution DTO
/// </summary>
public class UpdateConflictResolutionDto
{
    /// <summary>
    /// Conflict Resolution ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Object ID this conflict resolution applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Resolution strategy (e.g., "SourceWins", "TargetWins", "Manual", "Merge")
    /// </summary>
    public string ResolutionStrategy { get; set; } = string.Empty;

    /// <summary>
    /// Merge rules configuration stored as JSON
    /// </summary>
    public string? MergeRules { get; set; }
}
