using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Commands.DeleteTenantLookup;

/// <summary>
/// Handler for DeleteTenantLookupCommand
/// </summary>
public class DeleteTenantLookupCommandHandler : IRequestHandler<DeleteTenantLookupCommand, Result<bool>>
{
    private readonly IRepository<TenantLookup> _tenantLookupRepository;
    private readonly ILogger<DeleteTenantLookupCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteTenantLookupCommandHandler(
        IRepository<TenantLookup> tenantLookupRepository,
        ILogger<DeleteTenantLookupCommandHandler> logger)
    {
        _tenantLookupRepository = tenantLookupRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteTenantLookupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing tenant lookup
            var tenantLookup = await _tenantLookupRepository.GetByIdAsync(request.Id, cancellationToken);
            if (tenantLookup == null)
            {
                return Result<bool>.Failure($"TenantLookup with ID '{request.Id}' not found.");
            }

            // Soft delete the tenant lookup
            await _tenantLookupRepository.DeleteAsync(tenantLookup, cancellationToken);
            
            _logger.LogInformation("Deleted tenant lookup with ID: {TenantLookupId}", request.Id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant lookup with ID: {TenantLookupId}", request.Id);
            return Result<bool>.Failure("An error occurred while deleting the tenant lookup.");
        }
    }
}
