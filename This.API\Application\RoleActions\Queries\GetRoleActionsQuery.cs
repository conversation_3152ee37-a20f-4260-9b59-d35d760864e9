using Application.RoleActions.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.RoleActions.Queries;

/// <summary>
/// Get role actions query
/// </summary>
public class GetRoleActionsQuery : IRequest<Result<RoleActionsResponse>>
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetRoleActionsQuery(Guid roleId)
    {
        RoleId = roleId;
    }
}
