using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Action entity
/// </summary>
public class ActionConfig : IEntityTypeConfiguration<Domain.Entities.Action>
{
    public void Configure(EntityTypeBuilder<Domain.Entities.Action> builder)
    {
        builder.ToTable("Actions", "Genp");

        // Properties
        builder.Property(e => e.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnType("TEXT");

        builder.Property(e => e.EndpointTemplate)
            .HasMaxLength(500);

        builder.Property(e => e.NavigationTarget)
            .HasMaxLength(500);

        builder.Property(e => e.Icon)
            .HasMaxLength(100);

        builder.Property(e => e.ButtonStyle)
            .HasMaxLength(50);

        builder.Property(e => e.ConfirmationMessage)
            .HasColumnType("TEXT");

        builder.Property(e => e.SuccessMessage)
            .HasColumnType("TEXT");

        builder.Property(e => e.ErrorMessage)
            .HasColumnType("TEXT");

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.Id)
            .HasDatabaseName("IX_Actions_Id");

        builder.HasIndex(e => e.Name)
            .HasDatabaseName("IX_Actions_Name");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Actions_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasMany(e => e.DisplayActions)
            .WithOne(e => e.Action)
            .HasForeignKey(e => e.ActionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
