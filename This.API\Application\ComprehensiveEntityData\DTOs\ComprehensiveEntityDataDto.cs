namespace Application.ComprehensiveEntityData.DTOs;

/// <summary>
/// STREAMLINED: Hierarchical Entity Data Response DTO (Legacy format)
/// Returns data in proper parent-child nested structure with separate Metadata and DataType objects
/// </summary>
public class HierarchicalEntityDataResponseDto
{
    /// <summary>
    /// List of products with their complete hierarchical data
    /// </summary>
    public List<ProductHierarchicalDto> Products { get; set; } = new();

    /// <summary>
    /// Total count of all objects across all products (including nested)
    /// </summary>
    public int TotalObjectsCount => Products.Sum(p => p.GetTotalObjectsCount());

    /// <summary>
    /// Total count of all metadata entries across all products
    /// </summary>
    public int TotalMetadataCount => Products.Sum(p => p.GetTotalMetadataCount());
}

/// <summary>
/// UNIFIED: Hierarchical Entity Data Response DTO (New format)
/// Returns data with UNIFIED metadata that consolidates Metadata and DataType fields
/// </summary>
public class UnifiedHierarchicalEntityDataResponseDto
{
    /// <summary>
    /// List of products with their complete hierarchical data using unified metadata
    /// </summary>
    public List<UnifiedProductHierarchicalDto> Products { get; set; } = new();

    /// <summary>
    /// Total count of all objects across all products (including nested)
    /// </summary>
    public int TotalObjectsCount => Products.Sum(p => p.GetTotalObjectsCount());

    /// <summary>
    /// Total count of all metadata entries across all products
    /// </summary>
    public int TotalMetadataCount => Products.Sum(p => p.GetTotalMetadataCount());

    /// <summary>
    /// Maximum hierarchy depth across all products
    /// </summary>
    public int MaxHierarchyDepth => Products.SelectMany(f => f.RootObjects).DefaultIfEmpty().Max(o => o?.GetMaxDepth() ?? 0);
}

/// <summary>
/// STREAMLINED: Product with hierarchical structure - ENHANCED with ALL fields
/// </summary>
public class ProductHierarchicalDto
{
    /// <summary>
    /// Product ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Product description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Product version
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// Whether the product is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether user has been imported
    /// </summary>
    public bool IsUserImported { get; set; }

    /// <summary>
    /// Whether role has been assigned
    /// </summary>
    public bool IsRoleAssigned { get; set; }

    /// <summary>
    /// API key for the product
    /// </summary>
    public string? ApiKey { get; set; }

    /// <summary>
    /// Whether onboarding process is completed
    /// </summary>
    public bool IsOnboardCompleted { get; set; }

    /// <summary>
    /// Application URL for the product
    /// </summary>
    public string? ApplicationUrl { get; set; }

    /// <summary>
    /// Icon for the product
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Whether the product is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Product metadata with values
    /// </summary>
    public List<MetadataWithValuesDto> Metadata { get; set; } = new();

    /// <summary>
    /// ROOT OBJECTS ONLY (ParentObjectId = null) - Direct objects under product (Features removed)
    /// Child objects are nested within their parents
    /// </summary>
    public List<ObjectHierarchicalDto> RootObjects { get; set; } = new();

    /// <summary>
    /// Get total objects count (including all nested objects)
    /// </summary>
    public int GetTotalObjectsCount() => RootObjects.Sum(o => o.GetTotalObjectsCount());

    /// <summary>
    /// Get total metadata count (including all nested metadata)
    /// </summary>
    public int GetTotalMetadataCount() => Metadata.Count + RootObjects.Sum(o => o.GetTotalMetadataCount());
}

/// <summary>
/// STREAMLINED: Object with proper hierarchical nesting structure - ENHANCED with ALL fields
/// Child objects are nested within their parent objects
/// </summary>
public class ObjectHierarchicalDto
{
    /// <summary>
    /// Object ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Object description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Parent object ID (null for root objects)
    /// </summary>
    public Guid? ParentObjectId { get; set; }

    /// <summary>
    /// Whether the object is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Icon for the object
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Created at
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// Modified at
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Whether the object is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Hierarchy level (0 = root, 1 = first child, etc.)
    /// </summary>
    public int HierarchyLevel { get; set; }

    /// <summary>
    /// Full hierarchy path (e.g., "Organization > Building > Floor > Unit")
    /// </summary>
    public string HierarchyPath { get; set; } = string.Empty;

    /// <summary>
    /// Object metadata with values and data types
    /// </summary>
    public List<MetadataWithValuesDto> Metadata { get; set; } = new();

    /// <summary>
    /// CHILD OBJECTS - Nested within their parent (recursive structure)
    /// </summary>
    public List<ObjectHierarchicalDto> ChildObjects { get; set; } = new();

    /// <summary>
    /// Get total objects count (1 + all nested children)
    /// </summary>
    public int GetTotalObjectsCount() => 1 + ChildObjects.Sum(c => c.GetTotalObjectsCount());

    /// <summary>
    /// Get total metadata count (including all nested metadata)
    /// </summary>
    public int GetTotalMetadataCount() => Metadata.Count + ChildObjects.Sum(c => c.GetTotalMetadataCount());

    /// <summary>
    /// Get maximum depth of nested hierarchy
    /// </summary>
    public int GetMaxDepth()
    {
        if (!ChildObjects.Any())
            return HierarchyLevel;

        return ChildObjects.Max(c => c.GetMaxDepth());
    }
}

/// <summary>
/// Metadata with values DTO (Legacy format with separate Metadata and DataType)
/// </summary>
public class MetadataWithValuesDto
{
    /// <summary>
    /// Metadata information (includes nested dataType and metadataLink)
    /// </summary>
    public MetadataInfoDto Metadata { get; set; } = new();

    /// <summary>
    /// Values for this metadata
    /// </summary>
    public List<ValueInfoDto> Values { get; set; } = new();
}

/// <summary>
/// UNIFIED Metadata with values DTO (New format with consolidated fields)
/// This replaces the old MetadataWithValuesDto to provide a single consolidated object
/// </summary>
public class UnifiedMetadataWithValuesResponseDto
{
    /// <summary>
    /// Unified metadata information (consolidates Metadata and DataType with priority logic)
    /// </summary>
    public UnifiedMetadataResponseDto Metadata { get; set; } = new();

    /// <summary>
    /// Values for this metadata
    /// </summary>
    public List<ValueInfoDto> Values { get; set; } = new();
}

/// <summary>
/// UNIFIED Metadata response DTO (Simplified version for API responses)
/// Contains only the essential consolidated fields for API consumers
/// </summary>
public class UnifiedMetadataResponseDto
{
    // Core Identity
    public Guid? Id { get; set; }
    public string? Name { get; set; }

    // Essential UI Properties (consolidated)
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }

    // Essential Validation Properties (consolidated)
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool? IsRequired { get; set; }

    // Essential Input Properties (consolidated)
    public string? Placeholder { get; set; }
    public string? InputType { get; set; }
    public string? InputMask { get; set; }

    // Essential Choice Properties (consolidated)
    public string? DefaultOptions { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }
    public int? MaxSelections { get; set; }

    // Essential File Properties (consolidated)
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSizeBytes { get; set; }

    // Essential Error Messages (consolidated)
    public string? ErrorMessage { get; set; }
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }

    // DataType Information (for reference)
    public string? DataTypeName { get; set; }
    public string? Category { get; set; }
    public string? UiComponent { get; set; }
    public int? DecimalPlaces { get; set; }
    public decimal? StepValue { get; set; }

    // Link Information (using correct ObjectMetadata entity field names)
    public Guid? MetadataLinkId { get; set; }
    public bool? IsUnique { get; set; }
    public bool? IsVisibleInList { get; set; }
    public bool? IsVisibleInEdit { get; set; }
    public bool? IsVisibleInCreate { get; set; }
    public bool? IsVisibleInView { get; set; }
    public bool? IsCalculated { get; set; }

    // Override References (for advanced use cases)
    public Guid? ContextId { get; set; }
    public Guid? TenantContextId { get; set; }
    public Guid? ObjectLookupId { get; set; }

    // Display and Action Management Fields
    public bool? IsEditable { get; set; }
    public bool? IsSearchable { get; set; }
    public bool? IsSortable { get; set; }
    public int? SortOrder { get; set; }
    public string? DisplayFormat { get; set; }
}

/// <summary>
/// Metadata information DTO - COMPLETE with ALL properties
/// </summary>
public class MetadataInfoDto
{
    public Guid? Id { get; set; }
    public string? Name { get; set; }

    // UI Display Properties - ALL FIELDS
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }

    // Validation overrides - UPDATED FIELDS
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool? IsRequired { get; set; }

    // UI overrides - UPDATED FIELDS
    public string? Placeholder { get; set; }
    public string? DefaultOptions { get; set; }
    public int? MaxSelections { get; set; }
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSizeBytes { get; set; }

    // Error messages - UPDATED FIELDS
    public string? ErrorMessage { get; set; }
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }
    public string? MaxFileSizeBytesErrorMessage { get; set; }

    // Additional UI Properties
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }

    // Lookup Override Properties
    public Guid? ContextId { get; set; }
    public Guid? TenantContextId { get; set; }
    public Guid? ObjectLookupId { get; set; }

    // UNIFIED APPROACH: Essential DataType fields directly in Metadata (eliminates separate DataType object)
    public string? DataTypeName { get; set; }
    public string? Category { get; set; }
    public string? UiComponent { get; set; }
    public int? DecimalPlaces { get; set; }
    public decimal? StepValue { get; set; }

    /// <summary>
    /// Data type information (nested) - COMPLETE
    /// NOTE: In unified approach, this can be null to eliminate redundancy
    /// </summary>
    public DataTypeInfoDto? DataType { get; set; }

    /// <summary>
    /// Metadata link information (nested) - COMPLETE
    /// </summary>
    public MetadataLinkInfoDto? MetadataLink { get; set; }
}

/// <summary>
/// Data type information DTO - COMPLETE with ALL properties
/// </summary>
public class DataTypeInfoDto
{
    public Guid? Id { get; set; }
    public string? Name { get; set; }
    public string? DisplayName { get; set; }
    public string? Category { get; set; }
    public string? UiComponent { get; set; }

    // Validation Rules - ALL FIELDS
    public string? ValidationPattern { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public int? DecimalPlaces { get; set; }
    public decimal? StepValue { get; set; }
    public bool? IsRequired { get; set; }

    // UI Properties - ALL FIELDS
    public string? InputType { get; set; }
    public string? InputMask { get; set; }
    public string? Placeholder { get; set; }
    public string? HtmlAttributes { get; set; }

    // Choice Options - ALL FIELDS
    public string? DefaultOptions { get; set; }
    public bool? AllowsMultiple { get; set; }
    public bool? AllowsCustomOptions { get; set; }
    public int? MaxSelections { get; set; }

    // File/Media Properties - ALL FIELDS
    public string? AllowedFileTypes { get; set; }
    public long? MaxFileSizeBytes { get; set; }

    // Error Messages - ALL FIELDS
    public string? RequiredErrorMessage { get; set; }
    public string? PatternErrorMessage { get; set; }
    public string? MinLengthErrorMessage { get; set; }
    public string? MaxLengthErrorMessage { get; set; }
    public string? MinValueErrorMessage { get; set; }
    public string? MaxValueErrorMessage { get; set; }
    public string? FileTypeErrorMessage { get; set; }
    public string? FileSizeErrorMessage { get; set; }
    public string? ErrorMessage { get; set; }

    // Additional UI Properties
    public string? DisplayLabel { get; set; }
    public string? HelpText { get; set; }
    public int? FieldOrder { get; set; }
    public bool? IsVisible { get; set; }
    public bool? IsReadonly { get; set; }



    // Status
    public bool? IsActive { get; set; }
}

/// <summary>
/// Metadata link information DTO - COMPLETE with ALL properties
/// </summary>
public class MetadataLinkInfoDto
{
    /// <summary>
    /// The ID of the specific metadata link (ProductMetadataId or ObjectMetadataId - FeatureMetadataId removed)
    /// </summary>
    public Guid? ObjectMetaDataId { get; set; }

    /// <summary>
    /// Whether this metadata is unique for the entity
    /// </summary>
    public bool IsUnique { get; set; }

    /// <summary>
    /// Whether this metadata link is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Visibility settings - ALL FIELDS from ProductMetadata/ObjectMetadata (FeatureMetadata removed)
    /// </summary>
    public bool? ShouldVisibleInList { get; set; }
    public bool? ShouldVisibleInEdit { get; set; }
    public bool? ShouldVisibleInCreate { get; set; }
    public bool? ShouldVisibleInView { get; set; }
    public bool? IsCalculate { get; set; }
}

/// <summary>
/// Value information DTO with hierarchy support
/// </summary>
public class ValueInfoDto
{
    public Guid Id { get; set; }
    public Guid RefId { get; set; }
    public string? Value { get; set; }

    /// <summary>
    /// Parent object value ID (for nested values)
    /// </summary>
    public Guid? ParentObjectValueId { get; set; }
}

/// <summary>
/// UNIFIED Product hierarchical DTO (uses unified metadata)
/// </summary>
public class UnifiedProductHierarchicalDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? Version { get; set; }
    public bool IsActive { get; set; }
    public bool IsUserImported { get; set; }
    public bool IsRoleAssigned { get; set; }
    public string? ApiKey { get; set; }
    public bool IsOnboardCompleted { get; set; }
    public string? ApplicationUrl { get; set; }
    public string? Icon { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Product metadata with UNIFIED consolidation (Metadata priority over DataType)
    /// </summary>
    public List<UnifiedMetadataWithValuesResponseDto> Metadata { get; set; } = new();

    /// <summary>
    /// Root objects directly under this product (Features removed)
    /// </summary>
    public List<UnifiedObjectHierarchicalDto> RootObjects { get; set; } = new();

    /// <summary>
    /// Get total objects count (including all nested objects)
    /// </summary>
    public int GetTotalObjectsCount() => RootObjects.Sum(o => o.GetTotalObjectsCount());

    /// <summary>
    /// Get total metadata count (including all nested metadata)
    /// </summary>
    public int GetTotalMetadataCount() => Metadata.Count + RootObjects.Sum(o => o.GetTotalMetadataCount());
}

/// <summary>
/// UNIFIED Object hierarchical DTO (uses unified metadata)
/// </summary>
public class UnifiedObjectHierarchicalDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public Guid? ParentObjectId { get; set; }
    public bool IsActive { get; set; }
    public string? Icon { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Hierarchy level (0 = root, 1 = first level child, etc.)
    /// </summary>
    public int HierarchyLevel { get; set; }

    /// <summary>
    /// Full hierarchy path (e.g., "Organization > Building > Floor > Unit")
    /// </summary>
    public string HierarchyPath { get; set; } = string.Empty;

    /// <summary>
    /// Object metadata with UNIFIED consolidation (Metadata priority over DataType)
    /// </summary>
    public List<UnifiedMetadataWithValuesResponseDto> Metadata { get; set; } = new();

    /// <summary>
    /// CHILD OBJECTS - Nested within their parent (recursive structure)
    /// </summary>
    public List<UnifiedObjectHierarchicalDto> ChildObjects { get; set; } = new();

    /// <summary>
    /// DISPLAYS - List of displays associated with this object, each containing actions and their relationships
    /// </summary>
    public List<ObjectDisplayDto> Displays { get; set; } = new();

    /// <summary>
    /// Get total objects count (1 + all nested children)
    /// </summary>
    public int GetTotalObjectsCount() => 1 + ChildObjects.Sum(c => c.GetTotalObjectsCount());

    /// <summary>
    /// Get total metadata count (including all nested metadata)
    /// </summary>
    public int GetTotalMetadataCount() => Metadata.Count + ChildObjects.Sum(c => c.GetTotalMetadataCount());

    /// <summary>
    /// Get maximum depth of nested hierarchy
    /// </summary>
    public int GetMaxDepth()
    {
        if (!ChildObjects.Any())
            return HierarchyLevel;

        return ChildObjects.Max(c => c.GetMaxDepth());
    }
}

/// <summary>
/// Object Display DTO - represents a Display associated with an Object
/// </summary>
public class ObjectDisplayDto
{
    /// <summary>
    /// Display ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Display name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the display
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Display name for UI
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default display for this type
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// URL route template for navigation
    /// </summary>
    public string? RouteTemplate { get; set; }

    /// <summary>
    /// Icon for the display
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Sort order for display ordering
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// Whether the display is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Last modification timestamp
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified by user ID
    /// </summary>
    public Guid? ModifiedBy { get; set; }

    /// <summary>
    /// Whether the display is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// List of actions associated with this display for this object
    /// </summary>
    public List<ObjectDisplayActionDto> Actions { get; set; } = new();
}

/// <summary>
/// Object Display Action DTO - represents an Action with its DisplayAction relationship data
/// </summary>
public class ObjectDisplayActionDto
{
    #region Action Properties

    /// <summary>
    /// Action ID
    /// </summary>
    public Guid ActionId { get; set; }

    /// <summary>
    /// Action name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the action
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// API endpoint template for API actions
    /// </summary>
    public string? EndpointTemplate { get; set; }

    /// <summary>
    /// Navigation target for Navigation actions
    /// </summary>
    public string? NavigationTarget { get; set; }

    /// <summary>
    /// Icon for the action
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// Button style - 'Primary', 'Secondary', 'Danger', etc.
    /// </summary>
    public string? ButtonStyle { get; set; }

    /// <summary>
    /// Confirmation dialog message
    /// </summary>
    public string? ConfirmationMessage { get; set; }

    /// <summary>
    /// Success message to display after action completion
    /// </summary>
    public string? SuccessMessage { get; set; }

    /// <summary>
    /// Error message to display on action failure
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether the action is active
    /// </summary>
    public bool ActionIsActive { get; set; }

    /// <summary>
    /// Action creation timestamp
    /// </summary>
    public DateTime ActionCreatedAt { get; set; }

    /// <summary>
    /// Action last modification timestamp
    /// </summary>
    public DateTime ActionModifiedAt { get; set; }

    /// <summary>
    /// Action created by user ID
    /// </summary>
    public Guid? ActionCreatedBy { get; set; }

    /// <summary>
    /// Action modified by user ID
    /// </summary>
    public Guid? ActionModifiedBy { get; set; }

    /// <summary>
    /// Whether the action is deleted
    /// </summary>
    public bool ActionIsDeleted { get; set; }

    #endregion

    #region DisplayAction Relationship Properties

    /// <summary>
    /// DisplayAction ID
    /// </summary>
    public Guid DisplayActionId { get; set; }

    /// <summary>
    /// Access level - 'Public', 'Protected', 'Private'
    /// </summary>
    public string AccessLevel { get; set; } = "Public";

    /// <summary>
    /// Whether this is a default action for the display
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Sort order for action ordering within the display
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// Whether the action is visible in toolbar
    /// </summary>
    public bool IsVisibleInToolbar { get; set; }

    /// <summary>
    /// Whether the action is visible in context menu
    /// </summary>
    public bool IsVisibleInContextMenu { get; set; }

    /// <summary>
    /// Whether the action is visible in row actions
    /// </summary>
    public bool IsVisibleInRowActions { get; set; }

    /// <summary>
    /// Whether the DisplayAction relationship is active
    /// </summary>
    public bool DisplayActionIsActive { get; set; }

    /// <summary>
    /// DisplayAction creation timestamp
    /// </summary>
    public DateTime DisplayActionCreatedAt { get; set; }

    /// <summary>
    /// DisplayAction last modification timestamp
    /// </summary>
    public DateTime DisplayActionModifiedAt { get; set; }

    /// <summary>
    /// DisplayAction created by user ID
    /// </summary>
    public Guid? DisplayActionCreatedBy { get; set; }

    /// <summary>
    /// DisplayAction modified by user ID
    /// </summary>
    public Guid? DisplayActionModifiedBy { get; set; }

    /// <summary>
    /// Whether the DisplayAction relationship is deleted
    /// </summary>
    public bool DisplayActionIsDeleted { get; set; }

    #endregion
}