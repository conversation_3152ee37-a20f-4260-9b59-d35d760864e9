import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:this_mobile/core/theme/color_palette.dart';
import 'package:this_mobile/core/theme/text_styles.dart';

/// Video model for video input
class SelectedVideo {
  final String name;
  final String path;
  final int size;
  final Duration? duration;
  final DateTime dateModified;

  const SelectedVideo({
    required this.name,
    required this.path,
    required this.size,
    this.duration,
    required this.dateModified,
  });

  String get sizeFormatted {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String get durationFormatted {
    if (duration == null) return 'Unknown duration';
    
    final minutes = duration!.inMinutes;
    final seconds = duration!.inSeconds % 60;
    return '${minutes}:${seconds.toString().padLeft(2, '0')}';
  }
}

/// A customizable video input widget following the 'this_componentName_input' naming convention
/// This widget handles video upload with validation based on API configuration
class ThisVideoInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final List<SelectedVideo> value;
  final ValueChanged<List<SelectedVideo>> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  
  // API-based validation parameters
  final String? validationPattern;
  final String? requiredErrorMessage;
  final String? patternErrorMessage;
  final String? fileTypeErrorMessage;
  final String? fileSizeErrorMessage;
  
  // Video-specific parameters from API
  final bool allowsMultiple;
  final List<String>? allowedFileTypes; // e.g., ['mp4', 'mov', 'avi']
  final int? maxFileSizeBytes;
  final int? maxSelections;
  final Duration? maxDuration;
  final Duration? minDuration;
  final bool showPreview;
  final bool allowCamera;
  final bool allowGallery;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool autoFocus;
  final String? Function(List<SelectedVideo>)? customValidation;

  const ThisVideoInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.validationPattern,
    this.requiredErrorMessage,
    this.patternErrorMessage,
    this.fileTypeErrorMessage,
    this.fileSizeErrorMessage,
    this.allowsMultiple = false,
    this.allowedFileTypes,
    this.maxFileSizeBytes,
    this.maxSelections,
    this.maxDuration,
    this.minDuration,
    this.showPreview = true,
    this.allowCamera = true,
    this.allowGallery = true,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisVideoInput> createState() => _ThisVideoInputState();
}

class _ThisVideoInputState extends State<ThisVideoInput> {
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;
  bool _isUploading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  List<String> get _allowedExtensions {
    if (widget.allowedFileTypes == null || widget.allowedFileTypes!.isEmpty) {
      return ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm'];
    }
    return widget.allowedFileTypes!.map((type) => type.toLowerCase()).toList();
  }

  List<String> _validateValue(List<SelectedVideo> videos) {
    final errors = <String>[];

    // 1. Required validation
    if (widget.required && videos.isEmpty) {
      errors.add(widget.requiredErrorMessage ?? '${widget.label} is required');
      return errors;
    }

    // Skip other validations if empty and not required
    if (videos.isEmpty && !widget.required) {
      return errors;
    }

    // 2. Multiple videos validation
    if (!widget.allowsMultiple && videos.length > 1) {
      errors.add('Only one video is allowed');
      return errors;
    }

    // 3. Max selections validation
    if (widget.maxSelections != null && videos.length > widget.maxSelections!) {
      errors.add('Maximum ${widget.maxSelections} videos allowed');
      return errors;
    }

    // 4. File size validation
    if (widget.maxFileSizeBytes != null) {
      for (final video in videos) {
        if (video.size > widget.maxFileSizeBytes!) {
          errors.add(widget.fileSizeErrorMessage ?? 'Video size exceeds limit: ${video.name}');
          return errors;
        }
      }
    }

    // 5. Duration validation
    for (final video in videos) {
      if (widget.minDuration != null && video.duration != null && video.duration! < widget.minDuration!) {
        errors.add('Video too short: ${video.name} (min: ${widget.minDuration!.inSeconds}s)');
        return errors;
      }
      
      if (widget.maxDuration != null && video.duration != null && video.duration! > widget.maxDuration!) {
        errors.add('Video too long: ${video.name} (max: ${widget.maxDuration!.inSeconds}s)');
        return errors;
      }
    }

    // 6. Pattern validation (for file names)
    if (widget.validationPattern != null) {
      final regex = RegExp(widget.validationPattern!);
      for (final video in videos) {
        if (!regex.hasMatch(video.name)) {
          errors.add(widget.patternErrorMessage ?? 'Invalid video name: ${video.name}');
          return errors;
        }
      }
    }

    // 7. Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(videos);
      if (customError != null) {
        errors.add(customError);
        return errors;
      }
    }

    return errors;
  }

  Future<SelectedVideo?> _createSelectedVideo(XFile file) async {
    try {
      final fileObj = File(file.path);
      final stat = await fileObj.stat();
      
      // For now, we can't easily get video duration without additional packages
      // In a real implementation, you might use video_player or similar
      Duration? duration;
      
      return SelectedVideo(
        name: file.name,
        path: file.path,
        size: stat.size,
        duration: duration,
        dateModified: stat.modified,
      );
    } catch (e) {
      return null;
    }
  }

  Future<void> _pickVideo(ImageSource source) async {
    if (widget.disabled || widget.readOnly || _isUploading) return;

    setState(() {
      _isUploading = true;
    });

    try {
      final XFile? file = await _picker.pickVideo(source: source);
      if (file != null) {
        final selectedVideo = await _createSelectedVideo(file);
        if (selectedVideo != null) {
          List<SelectedVideo> newVideos;
          if (widget.allowsMultiple) {
            newVideos = [...widget.value, selectedVideo];
          } else {
            newVideos = [selectedVideo];
          }

          widget.onChanged(newVideos);

          // Validate
          final errors = _validateValue(newVideos);
          setState(() {
            _errors = errors;
            _isValidated = true;
          });
          
          widget.onValidation?.call(errors);
        }
      }
    } catch (e) {
      setState(() {
        _errors = ['Error picking video: $e'];
        _isValidated = true;
      });
      widget.onValidation?.call(_errors);
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _showVideoSourceDialog() async {
    if (!widget.allowCamera && !widget.allowGallery) return;
    
    if (widget.allowCamera && widget.allowGallery) {
      await showModalBottomSheet(
        context: context,
        backgroundColor: ColorPalette.darkToneInk,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (context) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Video Source',
                style: LexendTextStyles.lexend16Bold.copyWith(
                  color: ColorPalette.black,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.videocam, color: ColorPalette.black),
                title: Text(
                  'Camera',
                  style: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.black,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickVideo(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.video_library, color: ColorPalette.black),
                title: Text(
                  'Gallery',
                  style: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.black,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickVideo(ImageSource.gallery);
                },
              ),
            ],
          ),
        ),
      );
    } else if (widget.allowCamera) {
      await _pickVideo(ImageSource.camera);
    } else if (widget.allowGallery) {
      await _pickVideo(ImageSource.gallery);
    }
  }

  void _removeVideo(int index) {
    final newVideos = List<SelectedVideo>.from(widget.value);
    newVideos.removeAt(index);
    widget.onChanged(newVideos);

    // Validate
    final errors = _validateValue(newVideos);
    setState(() {
      _errors = errors;
      _isValidated = true;
    });
    
    widget.onValidation?.call(errors);
  }

  void _handleBlur() {
    if (widget.validateOnBlur) {
      final errors = _validateValue(widget.value);
      setState(() {
        _errors = errors;
        _isValidated = widget.value.isNotEmpty;
      });
      
      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated || widget.value.isEmpty) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  String _getDisplayText() {
    if (widget.value.isEmpty) {
      return widget.placeholder ?? 'Choose video${widget.allowsMultiple ? 's' : ''}...';
    }

    if (widget.value.length == 1) {
      return widget.value.first.name;
    } else {
      return '${widget.value.length} videos selected';
    }
  }

  String _getAllowedTypesText() {
    return 'Allowed: ${_allowedExtensions.join(', ').toUpperCase()}';
  }

  String _getMaxSizeText() {
    if (widget.maxFileSizeBytes == null) return '';
    
    final maxSize = widget.maxFileSizeBytes!;
    if (maxSize < 1024) return 'Max size: ${maxSize}B';
    if (maxSize < 1024 * 1024) return 'Max size: ${(maxSize / 1024).toStringAsFixed(1)}KB';
    if (maxSize < 1024 * 1024 * 1024) return 'Max size: ${(maxSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return 'Max size: ${(maxSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String _getDurationText() {
    final parts = <String>[];
    
    if (widget.minDuration != null) {
      parts.add('Min: ${widget.minDuration!.inSeconds}s');
    }
    
    if (widget.maxDuration != null) {
      parts.add('Max: ${widget.maxDuration!.inSeconds}s');
    }
    
    return parts.join(', ');
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors && widget.value.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled 
                    ? ColorPalette.placeHolderTextColor 
                    : ColorPalette.black,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        
        // Video picker button
        Focus(
          focusNode: _focusNode,
          onFocusChange: (focused) {
            if (!focused) _handleBlur();
          },
          child: GestureDetector(
            onTap: _showVideoSourceDialog,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: hasErrors 
                      ? const Color(0xFFC73E1D)
                      : (isValid ? ColorPalette.green : ColorPalette.gray300),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(6),
                color: _isUploading 
                    ? ColorPalette.gray300.withValues(alpha: 0.1)
                    : null,
              ),
              child: Column(
                children: [
                  if (_isUploading) ...[
                    const CircularProgressIndicator(),
                    const SizedBox(height: 8),
                    Text(
                      'Processing...',
                      style: LexendTextStyles.lexend14Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                  ] else ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (widget.showIcon) ...[
                          Icon(
                            Icons.video_library,
                            size: 24,
                            color: ColorPalette.placeHolderTextColor,
                          ),
                          const SizedBox(width: 8),
                        ],
                        Expanded(
                          child: Text(
                            _getDisplayText(),
                            style: LexendTextStyles.lexend14Regular.copyWith(
                              color: widget.value.isEmpty
                                  ? ColorPalette.placeHolderTextColor
                                  : ColorPalette.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        if (_getValidationIcon() != null) _getValidationIcon()!,
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to select video${widget.allowsMultiple ? 's' : ''}',
                      style: LexendTextStyles.lexend12Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
        
        // Selected videos list
        if (widget.value.isNotEmpty) ...[
          const SizedBox(height: 12),
          ...widget.value.asMap().entries.map((entry) {
            final index = entry.key;
            final video = entry.value;
            
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorPalette.darkToneInk.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: ColorPalette.gray300.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: ColorPalette.gray300.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.play_circle_fill,
                      size: 24,
                      color: ColorPalette.black,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          video.name,
                          style: LexendTextStyles.lexend14Regular.copyWith(
                            color: ColorPalette.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          '${video.sizeFormatted} • ${video.durationFormatted}',
                          style: LexendTextStyles.lexend12Regular.copyWith(
                            color: ColorPalette.placeHolderTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!widget.disabled && !widget.readOnly)
                    IconButton(
                      icon: const Icon(Icons.close, size: 16),
                      color: const Color(0xFFC73E1D),
                      onPressed: () => _removeVideo(index),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: const EdgeInsets.all(4),
                    ),
                ],
              ),
            );
          }),
        ],
        
        // Error message
        if (hasErrors)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _errors.first,
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
            ),
          ),
        
        // Helper text
        if (!hasErrors) ...[
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getAllowedTypesText(),
                  style: LexendTextStyles.lexend12Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
                if (widget.maxFileSizeBytes != null)
                  Text(
                    _getMaxSizeText(),
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                if (_getDurationText().isNotEmpty)
                  Text(
                    _getDurationText(),
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                if (widget.allowsMultiple && widget.maxSelections != null)
                  Text(
                    'Maximum ${widget.maxSelections} videos',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
