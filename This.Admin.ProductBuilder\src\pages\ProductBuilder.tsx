import React, { useState, useCallback, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { TreeView, type TreeNode } from '../components/TreeView/TreeView';
import { MetadataEditor } from '../components/MetadataEditor/MetadataEditor';
import { TemplateSaveModal } from '../components/TemplateSaveModal/TemplateSaveModal';
import { ProductBuilderHeader } from '../components/ProductBuilderHeader/ProductBuilderHeader';

import { templateService, TemplateValidator } from '../services/templateService';
import { convertTreeDataToTemplate, convertTemplateToTreeNode } from '../utils/templateConverters';
import { getDefaultMetadata, type MetadataItem } from '../utils/index';

export const ProductBuilder: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Core state
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [selectedNode, setSelectedNode] = useState<TreeNode | null>(null);
  const [tempMetadata, setTempMetadata] = useState<MetadataItem[]>([]);
  const [, setHasUnsavedChanges] = useState(false);
  
  // Template state
  const [templateInfo, setTemplateInfo] = useState<any>(null);
  const [originalStage, setOriginalStage] = useState<string | null>(null);
  
  // UI state
  const [isSaving, setIsSaving] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [saveFormData, setSaveFormData] = useState({
    templateName: '',
    version: '*******',
    stage: 'draft' as 'draft' | 'beta' | 'live' | 'archived',
    description: ''
  });

  // Load template data if provided
  useEffect(() => {
    const templateData = location.state?.templateData;
    if (templateData && templateData.templateJson?.products) {
      // Store template info
      setTemplateInfo(templateData);
      setOriginalStage(templateData.stage || 'draft');

      // Convert template JSON to tree data
      const convertedTreeData = templateData.templateJson.products.map((product: any) =>
        convertTemplateToTreeNode(product)
      );
      setTreeData(convertedTreeData);
    } else {
      // If no template data provided, start with empty tree
      setTreeData([]);
      setTemplateInfo(null);
      setOriginalStage(null);
    }
  }, [location.state]);

  // Handle node selection
  const handleSelect = useCallback((node: TreeNode) => {
    setSelectedNode(node);
    setHasUnsavedChanges(false);
    if (node.type) {
      // Use the node's metadata if it exists, otherwise use default metadata
      const nodeMetadata = Array.isArray(node.metadata) && node.metadata.length > 0
        ? node.metadata
        : getDefaultMetadata(node.type);

      // Initialize temporary metadata with current node metadata
      setTempMetadata([...nodeMetadata]);
    }
  }, []);

  // Add new folder/node
  const addFolder = useCallback((parentId: string, name: string, type: any) => {
    const newId = crypto.randomUUID();
    const defaultMetadata = getDefaultMetadata(type);

    const newFolder: TreeNode = {
      id: newId,
      name,
      type,
      children: [],
      isOpen: true,
      metadata: [...defaultMetadata]
    };

    setHasUnsavedChanges(true);

    setTreeData(prevData => {
      const data = JSON.parse(JSON.stringify(prevData));

      if (parentId === 'root') {
        return [...data, newFolder];
      }
      
      const findAndAdd = (nodes: TreeNode[]): boolean => {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].id === parentId) {
            if (!nodes[i].children) {
              nodes[i].children = [];
            }
            nodes[i].children!.push(newFolder);
            nodes[i].isOpen = true;
            return true;
          }
          
          if (nodes[i].children && nodes[i].children!.length > 0) {
            if (findAndAdd(nodes[i].children!)) {
              return true;
            }
          }
        }
        return false;
      };
      
      findAndAdd(data);
      return [...data];
    });
  }, []);

  // Show save template modal
  const handleShowSaveModal = () => {
    const currentStage = templateInfo?.stage || 'draft';

    let defaultTemplateName = '';
    if (templateInfo?.name) {
      defaultTemplateName = templateInfo.name;
    } else if (treeData.length > 0) {
      defaultTemplateName = treeData[0].name;
    }

    setSaveFormData({
      templateName: defaultTemplateName,
      version: templateInfo?.version || '*******',
      stage: currentStage,
      description: templateInfo?.description || ''
    });

    setOriginalStage(currentStage);
    setShowSaveModal(true);
  };

  // Save template function
  const handleSaveTemplate = async () => {
    setIsSaving(true);

    try {
      let templateJson;
      if (treeData.length === 0) {
        const productName = templateInfo?.templateJson?.products?.[0]?.name || 'Unnamed Product';
        templateJson = {
          products: [
            {
              name: productName,
              type: "product",
              metadata: [],
              objects: []
            }
          ]
        };
      } else {
        templateJson = convertTreeDataToTemplate(treeData);
      }

      console.log('🔍 Complete template JSON structure being saved:', JSON.stringify(templateJson, null, 2));

      const validationErrors = TemplateValidator.validateTemplateJson(templateJson);
      if (validationErrors.length > 0) {
        toast.error(`Validation errors:\n${validationErrors.join('\n')}`);
        return;
      }

      const isNewTemplate = !templateInfo?.id || templateInfo.id.startsWith('template-');
      const isStatusChange = !isNewTemplate && originalStage && originalStage !== saveFormData.stage;

      let result;
      if (isNewTemplate || isStatusChange) {
        result = await templateService.createTemplate({
          name: saveFormData.templateName,
          version: saveFormData.version,
          stage: saveFormData.stage,
          templateJson: typeof templateJson === 'string' ? templateJson : JSON.stringify(templateJson),
          isActive: true
        });
      } else {
        result = await templateService.updateTemplate(templateInfo.id, {
          name: saveFormData.templateName,
          version: saveFormData.version,
          stage: saveFormData.stage,
          templateJson: typeof templateJson === 'string' ? templateJson : JSON.stringify(templateJson),
          isActive: true
        });
      }

      setTemplateInfo((prev: any) => ({
        ...prev,
        name: saveFormData.templateName,
        version: saveFormData.version,
        stage: saveFormData.stage,
        description: saveFormData.description,
        publishedAt: new Date().toISOString(),
        id: result.id || prev?.id
      }));

      if (treeData.length === 0) {
        const newTreeData = templateJson.products.map((product: any) =>
          convertTemplateToTreeNode(product)
        );
        setTreeData(newTreeData);
      }

      let actionText = 'saved';
      if (isNewTemplate) {
        actionText = 'created';
      } else if (isStatusChange) {
        actionText = `created with new status (${saveFormData.stage})`;
      } else {
        actionText = 'updated';
      }
      
      toast.success(`Template ${actionText} successfully! Version: ${saveFormData.version}`);
      setHasUnsavedChanges(false);
      setShowSaveModal(false);

    } catch (error) {
      console.error('Error saving template:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error(`Error saving template: ${errorMessage}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="d-flex flex-column min-vh-100">
      <ProductBuilderHeader
        templateInfo={templateInfo}
        locationState={location.state}
        onSave={handleShowSaveModal}
        onBackToTemplates={() => navigate('/templates')}
        isSaving={isSaving}
      />

      <main className="flex-grow-1 py-4">
        <Container fluid className="px-0">
          <Row className="g-4">
            <Col md={4} className="order-2 order-md-1 ps-4 pe-2" style={{ paddingLeft: '20px', paddingTop: '20px', paddingBottom: '20px' }}>
              <div className="bg-white rounded shadow-sm h-100 d-flex flex-column" style={{ minHeight: '70vh' }}>
                <TreeView
                  data={treeData}
                  selectedNode={selectedNode}
                  onSelect={handleSelect}
                  onAddFolder={addFolder}
                />
              </div>
            </Col>

            <Col md={8} className="order-1 order-md-2 ps-2 pe-4" style={{ paddingRight: '20px', paddingTop: '20px', paddingBottom: '20px' }}>
              <div className="bg-white rounded shadow-sm h-100 d-flex flex-column" style={{ minHeight: '70vh' }}>
                <MetadataEditor
                  selectedNode={selectedNode}
                  tempMetadata={tempMetadata}
                  setTempMetadata={setTempMetadata}
                  setHasUnsavedChanges={setHasUnsavedChanges}
                  setTreeData={setTreeData}
                  setSelectedNode={setSelectedNode}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </main>

      <TemplateSaveModal
        show={showSaveModal}
        onHide={() => setShowSaveModal(false)}
        saveFormData={saveFormData}
        setSaveFormData={setSaveFormData}
        onSave={handleSaveTemplate}
        isSaving={isSaving}
      />

      <ToastContainer position="top-right" autoClose={3000} />
    </div>
  );
};
