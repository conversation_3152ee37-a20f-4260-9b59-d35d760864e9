using Shared.Common;

namespace Abstraction.Database.Repositories;

/// <summary>
/// STREAMLINED: Repository interface for hierarchical entity data with proper parent-child nesting
/// </summary>
public interface IHierarchicalEntityDataRepository : ITransientRepository
{
    /// <summary>
    /// Get hierarchical entity data with proper parent-child nesting
    /// Child objects are nested within their parent objects, not returned as flat arrays
    /// </summary>
    /// <param name="productId">Product ID filter (optional)</param>
    /// <param name="featureId">Feature ID filter (optional)</param>
    /// <param name="searchTerm">Search term for names and descriptions (optional)</param>
    /// <param name="isActive">Filter by active status (optional)</param>
    /// <param name="onlyVisibleMetadata">Include only visible metadata</param>
    /// <param name="onlyActiveMetadata">Include only active metadata</param>
    /// <param name="pageNumber">Page number for pagination</param>
    /// <param name="pageSize">Page size for pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Hierarchical entity data with proper parent-child nesting</returns>
    Task<object> GetHierarchicalEntityDataAsync(
        Guid? productId = null,
        Guid? featureId = null,
        string? searchTerm = null,
        bool? isActive = null,
        bool onlyVisibleMetadata = true,
        bool onlyActiveMetadata = true,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default);
}
