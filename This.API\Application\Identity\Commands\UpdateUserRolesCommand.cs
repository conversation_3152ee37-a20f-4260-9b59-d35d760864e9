using MediatR;
using Shared.Common.Response;
using Application.Identity.DTOs;

namespace Application.Identity.Commands;

/// <summary>
/// Command for updating user roles
/// </summary>
public class UpdateUserRolesCommand : IRequest<ApiResponse<string>>
{
    /// <summary>
    /// User ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// List of user roles with their enabled status
    /// </summary>
    public List<UserRoleDto> UserRoles { get; set; } = new();
}
