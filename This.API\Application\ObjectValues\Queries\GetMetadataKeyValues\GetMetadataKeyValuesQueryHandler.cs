using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using Abstraction.Database.Repositories;

namespace Application.ObjectValues.Queries.GetMetadataKeyValues;

/// <summary>
/// Handler for GetMetadataKeyValuesQuery
/// </summary>
public class GetMetadataKeyValuesQueryHandler : IRequestHandler<GetMetadataKeyValuesQuery, Result<ObjectLookUpValuesResponse>>
{
    private readonly IDapperRepository _dapperRepository;
    private readonly ILogger<GetMetadataKeyValuesQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetMetadataKeyValuesQueryHandler(IDapperRepository dapperRepository, ILogger<GetMetadataKeyValuesQueryHandler> logger)
    {
        _dapperRepository = dapperRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<ObjectLookUpValuesResponse>> Handle(GetMetadataKeyValuesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting metadata key values for Object: {ObjectName}, MetadataKey: {MetadataKey}, Tenant: {TenantId}",
                request.ObjectName, request.MetadataKey, request.TenantId);

            // Create the view name based on tenant + object name
            var viewName = $"{request.TenantId}{request.ObjectName.Replace(" ", "_")}";

            // Query the metadata key values using DapperRepository with pagination
            var (values, totalCount) = await _dapperRepository.QueryMetadataKeyValuesAsync(
                request.ObjectName,
                request.MetadataKey,
                request.TenantId,
                request.CreateView,
                request.PageNumber,
                request.PageSize,
                cancellationToken);

            // Convert to list
            var valuesList = values.ToList();

            // Calculate pagination info
            var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
            var hasPreviousPage = request.PageNumber > 1;
            var hasNextPage = request.PageNumber < totalPages;

            var response = new ObjectLookUpValuesResponse
            {
                ObjectName = request.ObjectName,
                MetadataKey = request.MetadataKey,
                TenantId = request.TenantId,
                ViewName = viewName,
                ViewCreationResult = request.CreateView ? "View created and queried successfully" : "View queried directly",
                Values = valuesList,
                TotalValues = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = totalPages,
                HasPreviousPage = hasPreviousPage,
                HasNextPage = hasNextPage,
                Message = $"Successfully retrieved {valuesList.Count} of {totalCount} values for metadata key '{request.MetadataKey}' from object '{request.ObjectName}' (Page {request.PageNumber} of {totalPages})"
            };

            _logger.LogInformation("Successfully retrieved {ValueCount} of {TotalCount} values for metadata key: {MetadataKey} from object: {ObjectName} (Page {PageNumber} of {TotalPages})",
                valuesList.Count, totalCount, request.MetadataKey, request.ObjectName, request.PageNumber, totalPages);

            return Result<ObjectLookUpValuesResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metadata key values for Object: {ObjectName}, MetadataKey: {MetadataKey}, Tenant: {TenantId}",
                request.ObjectName, request.MetadataKey, request.TenantId);
            return Result<ObjectLookUpValuesResponse>.Failure($"Error retrieving metadata key values: {ex.Message}");
        }
    }
}
