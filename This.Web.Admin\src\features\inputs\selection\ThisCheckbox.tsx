// components/ThisCheckbox.tsx
import React, { useState } from 'react';
import { Check } from 'lucide-react';

interface CheckboxOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface ThisCheckboxProps {
  id: string;
  label: string;
  options?: CheckboxOption[];
  value: string[];
  onChange: (value: string[]) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  required?: boolean;
  minSelected?: number;
  maxSelected?: number;
  layout?: 'vertical' | 'horizontal' | 'grid';
  gridColumns?: number;
  allowSelectAll?: boolean;
  selectAllText?: string;
  customValidation?: (values: string[]) => string | null;
}

interface ValidationRule {
  test: (values: string[]) => boolean;
  message: string;
}

const ThisCheckbox: React.FC<ThisCheckboxProps> = ({
  id,
  label,
  options = [],
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  required = false,
  minSelected,
  maxSelected,
  layout = 'vertical',
  gridColumns = 2,
  allowSelectAll = false,
  selectAllText = 'Select All',
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (values) => values.length > 0,
        message: `${label} is required`
      });
    }

    // 2. Minimum selection validation
    if (minSelected !== undefined) {
      rules.push({
        test: (values) => values.length >= minSelected,
        message: `Select at least ${minSelected} option${minSelected !== 1 ? 's' : ''}`
      });
    }

    // 3. Maximum selection validation
    if (maxSelected !== undefined) {
      rules.push({
        test: (values) => values.length <= maxSelected,
        message: `Select at most ${maxSelected} option${maxSelected !== 1 ? 's' : ''}`
      });
    }

    // 4. Custom validation
    if (customValidation) {
      rules.push({
        test: (values) => {
          const customError = customValidation(values);
          return customError === null;
        },
        message: customValidation(value) || ''
      });
    }

    return rules;
  };

  const validateValue = (values: string[]): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(values)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleChange = (optionValue: string, checked: boolean) => {
    if (disabled || readOnly) return;

    let newValue: string[];
    if (checked) {
      newValue = [...value, optionValue];
    } else {
      newValue = value.filter(v => v !== optionValue);
    }

    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const handleSelectAll = () => {
    if (disabled || readOnly) return;

    const allValues = options.filter(opt => !opt.disabled).map(opt => opt.value);
    const isAllSelected = allValues.every(val => value.includes(val));
    
    const newValue = isAllSelected ? [] : allValues;
    onChange(newValue);

    // Real-time validation
    const newErrors = validateValue(newValue);
    setErrors(newErrors);
    onValidation?.(newErrors);
  };

  const isChecked = (optionValue: string): boolean => {
    return value.includes(optionValue);
  };

  const isAllSelected = (): boolean => {
    const enabledOptions = options.filter(opt => !opt.disabled);
    return enabledOptions.length > 0 && enabledOptions.every(opt => value.includes(opt.value));
  };

  const isSomeSelected = (): boolean => {
    const enabledOptions = options.filter(opt => !opt.disabled);
    return enabledOptions.some(opt => value.includes(opt.value)) && !isAllSelected();
  };

  const hasErrors = errors.length > 0;

  // Get layout classes
  const getLayoutClasses = (): string => {
    switch (layout) {
      case 'horizontal':
        return 'checkbox-options-horizontal';
      case 'grid':
        return `checkbox-options-grid checkbox-grid-${gridColumns}`;
      default:
        return 'checkbox-options-vertical';
    }
  };

  return (
    <div className="text-input-container">
      {/* Label */}
      <label className="text-input-label">
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Checkbox Options */}
      <div className="text-input-wrapper">
        <div className={`checkbox-input-container ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''}`}>
          {/* Select All Option */}
          {allowSelectAll && options.length > 1 && (
            <div className="checkbox-option select-all-option">
              <label className="checkbox-label">
                <div className="checkbox-wrapper">
                  <input
                    type="checkbox"
                    checked={isAllSelected()}
                    ref={(input) => {
                      if (input) {
                        input.indeterminate = isSomeSelected();
                      }
                    }}
                    onChange={handleSelectAll}
                    disabled={disabled || readOnly}
                    className="checkbox-input"
                    aria-label={selectAllText}
                  />
                  <div className={`checkbox-custom ${isAllSelected() ? 'checked' : ''} ${isSomeSelected() ? 'indeterminate' : ''}`}>
                    {isAllSelected() && <Check size={14} className="checkbox-icon" />}
                    {isSomeSelected() && <div className="checkbox-indeterminate-icon" />}
                  </div>
                </div>
                <span className="checkbox-text select-all-text">{selectAllText}</span>
              </label>
            </div>
          )}

          {/* Individual Options */}
          <div className={getLayoutClasses()}>
            {options.map((option) => (
              <div key={option.value} className="checkbox-option">
                <label className="checkbox-label">
                  <div className="checkbox-wrapper">
                    <input
                      type="checkbox"
                      value={option.value}
                      checked={isChecked(option.value)}
                      onChange={(e) => handleChange(option.value, e.target.checked)}
                      disabled={disabled || readOnly || option.disabled}
                      className="checkbox-input"
                      aria-describedby={hasErrors ? `${id}-error` : undefined}
                    />
                    <div className={`checkbox-custom ${isChecked(option.value) ? 'checked' : ''} ${option.disabled ? 'disabled' : ''}`}>
                      {isChecked(option.value) && <Check size={14} className="checkbox-icon" />}
                    </div>
                  </div>
                  <span className={`checkbox-text ${option.disabled ? 'disabled' : ''}`}>
                    {option.label}
                  </span>
                </label>
              </div>
            ))}
          </div>

          {/* Selection Count */}
          {(minSelected !== undefined || maxSelected !== undefined) && (
            <div className="checkbox-selection-count">
              <span className="selection-count-text">
                {value.length} selected
                {minSelected !== undefined && ` (min: ${minSelected})`}
                {maxSelected !== undefined && ` (max: ${maxSelected})`}
              </span>
            </div>
          )}
        </div>

        {/* Error Message */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisCheckbox;
