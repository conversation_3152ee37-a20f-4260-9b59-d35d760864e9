﻿using Application.Context.Commands.UpsertContext;
using Application.Context.Commands.UpsertTenantContext;
using Application.Context.Commands.UpsertBulkContext;
using Application.Context.Commands.UpsertBulkTenantContext;
using Application.Context.Commands.UpsertLookup;
using Application.Context.Commands.DeleteLookup;
using Application.Context.Commands.UpsertBulkLookups;
using Application.Context.Commands.UpsertTenantLookup;
using Application.Context.Commands.DeleteTenantLookup;
using Application.Context.DTOs;
using Application.Context.Queries.GetAllContexts;
using Application.Context.Queries.GetAllTenantContexts;
using Application.Context.Queries.GetContextLookup;
using Application.Context.Queries.GetContextWithLookups;
using Application.Context.Queries.GetBulkContextWithLookups;
using Application.Context.Queries.GetPagedContexts;
using Application.Context.Queries.GetPagedTenantContexts;
using Application.Context.Queries.GetTenantContextLookup;
using Application.Context.Queries.GetTenantContextWithLookups;
using Application.Context.Queries.GetBulkTenantContextWithLookups;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Infrastructure.OpenApi;

namespace Web.Host.Controllers;

/// <summary>
/// Context controller for managing Context and TenantContext operations
/// </summary>
[Route("api/[controller]")]
public class ContextController : BaseApiController
{
    private readonly IMediator _mediator;
    private readonly ILogger<ContextController> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public ContextController(IMediator mediator, ILogger<ContextController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Validate tenant header
    /// </summary>
    private IActionResult ValidateTenantHeader()
    {
        if (!Request.Headers.ContainsKey("tenant"))
        {
            return BadRequest("Tenant header is required");
        }
        return null!;
    }

    #region Context (Global) Operations

    /// <summary>
    /// Create or update a single context
    /// </summary>
    [HttpPost("upsert-single")]
    public async Task<IActionResult> UpsertSingle([FromBody] UpsertContextCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(new { Id = result.Data, Message = "Context upserted successfully" });
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting context");
            return StatusCode(500, "An error occurred while upserting context");
        }
    }

    /// <summary>
    /// Create or update multiple contexts in batch
    /// </summary>
    [HttpPost("upsert-bulk")]
    public async Task<IActionResult> UpsertBulk([FromBody] UpsertBulkContextCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk upsert of contexts");
            return StatusCode(500, "An error occurred during bulk upsert of contexts");
        }
    }

    /// <summary>
    /// Get all contexts
    /// </summary>
    [HttpGet("get-all")]
    public async Task<IActionResult> GetAll([FromQuery] bool includeInactive = false,
        [FromQuery] string? category = null, [FromQuery] string? searchTerm = null)
    {
        try
        {
            var query = new GetAllContextsQuery
            {
                IncludeInactive = includeInactive,
                Category = category,
                SearchTerm = searchTerm
            };

            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all contexts");
            return StatusCode(500, "An error occurred while retrieving contexts");
        }
    }

    /// <summary>
    /// Get contexts with pagination
    /// </summary>
    [HttpGet("get-paged")]
    public async Task<IActionResult> GetPaged([FromQuery] int page = 1, [FromQuery] int pageSize = 10,
        [FromQuery] bool includeInactive = false, [FromQuery] string? category = null,
        [FromQuery] string? searchTerm = null)
    {
        try
        {
            var query = new GetPagedContextsQuery
            {
                PageNumber = page,
                PageSize = pageSize,
                IncludeInactive = includeInactive,
                Category = category,
                SearchTerm = searchTerm
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving paged contexts");
            return StatusCode(500, "An error occurred while retrieving paged contexts");
        }
    }

    /// <summary>
    /// Get context lookup data for dropdowns
    /// </summary>
    [HttpGet("lookup")]
    public async Task<IActionResult> GetLookup([FromQuery] string? category = null,
        [FromQuery] string? searchTerm = null, [FromQuery] bool includeInactive = false)
    {
        try
        {
            var query = new GetContextLookupQuery
            {
                Category = category,
                SearchTerm = searchTerm,
                IncludeInactive = includeInactive
            };

            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving context lookup data");
            return StatusCode(500, "An error occurred while retrieving context lookup data");
        }
    }

    /// <summary>
    /// Get context with its associated lookups by context ID
    /// </summary>
    [HttpGet("{contextId}/with-lookups")]
    public async Task<IActionResult> GetContextWithLookups(Guid contextId, [FromQuery] bool includeInactiveLookups = false)
    {
        try
        {
            var query = new GetContextWithLookupsQuery(contextId, includeInactiveLookups);
            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving context with lookups for ContextId: {ContextId}", contextId);
            return StatusCode(500, "An error occurred while retrieving context with lookups");
        }
    }

    /// <summary>
    /// Get multiple contexts with their associated lookups by context IDs
    /// </summary>
    [HttpPost("bulk/with-lookups")]
    public async Task<IActionResult> GetBulkContextWithLookups([FromBody] List<Guid> contextIds, [FromQuery] bool includeInactiveLookups = false)
    {
        try
        {
            if (contextIds == null || !contextIds.Any())
            {
                return BadRequest("Context IDs list cannot be empty");
            }

            if (contextIds.Count > 100) // Reasonable limit to prevent abuse
            {
                return BadRequest("Maximum 100 context IDs allowed per request");
            }

            var query = new GetBulkContextWithLookupsQuery(contextIds, includeInactiveLookups);
            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving bulk contexts with lookups for {Count} ContextIds", contextIds?.Count ?? 0);
            return StatusCode(500, "An error occurred while retrieving bulk contexts with lookups");
        }
    }

    #endregion

    #region Lookup Management Operations

    /// <summary>
    /// Create or update a single lookup
    /// </summary>
    [HttpPost("lookup/upsert")]
    public async Task<IActionResult> UpsertLookup([FromBody] UpsertLookupCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(new { Id = result.Data, Message = "Lookup upserted successfully" });
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting lookup");
            return StatusCode(500, "An error occurred while upserting lookup");
        }
    }

    /// <summary>
    /// Delete a lookup
    /// </summary>
    [HttpDelete("lookup/{lookupId}")]
    public async Task<IActionResult> DeleteLookup(Guid lookupId)
    {
        try
        {
            var command = new DeleteLookupCommand(lookupId);
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(new { Message = "Lookup deleted successfully" });
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting lookup with ID: {LookupId}", lookupId);
            return StatusCode(500, "An error occurred while deleting lookup");
        }
    }

    /// <summary>
    /// Bulk upsert lookups for a context
    /// </summary>
    [HttpPost("lookup/bulk-upsert")]
    public async Task<IActionResult> BulkUpsertLookups([FromBody] UpsertBulkLookupsCommand command)
    {
        try
        {
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk lookup upsert for ContextId: {ContextId}", command.ContextId);
            return StatusCode(500, "An error occurred during bulk lookup upsert");
        }
    }

    #endregion

    #region TenantContext (Tenant-Specific) Operations

    /// <summary>
    /// Create or update a single tenant context
    /// </summary>
    [HttpPost("tenant/upsert-single")]
    [TenantIdHeader]
    public async Task<IActionResult> UpsertSingleTenantContext([FromBody] UpsertTenantContextCommand command)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(new { Id = result.Data, Message = "Tenant context upserted successfully" });
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting tenant context");
            return StatusCode(500, "An error occurred while upserting tenant context");
        }
    }

    /// <summary>
    /// Create or update multiple tenant contexts in batch
    /// </summary>
    [HttpPost("tenant/upsert-bulk")]
    [TenantIdHeader]
    public async Task<IActionResult> UpsertBulkTenantContext([FromBody] UpsertBulkTenantContextCommand command)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk upsert of tenant contexts");
            return StatusCode(500, "An error occurred during bulk upsert of tenant contexts");
        }
    }

    /// <summary>
    /// Get all tenant contexts
    /// </summary>
    [HttpGet("tenant/get-all")]
    [TenantIdHeader]
    public async Task<IActionResult> GetAllTenantContexts([FromQuery] bool includeInactive = false,
        [FromQuery] string? category = null, [FromQuery] string? searchTerm = null)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var query = new GetAllTenantContextsQuery
            {
                IncludeInactive = includeInactive,
                Category = category,
                SearchTerm = searchTerm
            };

            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all tenant contexts");
            return StatusCode(500, "An error occurred while retrieving tenant contexts");
        }
    }

    /// <summary>
    /// Get tenant contexts with pagination
    /// </summary>
    [HttpGet("tenant/get-paged")]
    [TenantIdHeader]
    public async Task<IActionResult> GetPagedTenantContexts([FromQuery] int page = 1, [FromQuery] int pageSize = 10,
        [FromQuery] bool includeInactive = false, [FromQuery] string? category = null,
        [FromQuery] string? searchTerm = null)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var query = new GetPagedTenantContextsQuery
            {
                PageNumber = page,
                PageSize = pageSize,
                IncludeInactive = includeInactive,
                Category = category,
                SearchTerm = searchTerm
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving paged tenant contexts");
            return StatusCode(500, "An error occurred while retrieving paged tenant contexts");
        }
    }

    /// <summary>
    /// Get tenant context information based on tenant ID from header
    /// </summary>
    [HttpGet("get-tenant-context")]
    [TenantIdHeader]
    public async Task<IActionResult> GetTenantContext([FromQuery] string? category = null,
        [FromQuery] string? searchTerm = null, [FromQuery] bool includeInactive = false)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var query = new GetTenantContextLookupQuery
            {
                Category = category,
                SearchTerm = searchTerm,
                IncludeInactive = includeInactive
            };

            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting tenant context data");
            return StatusCode(500, "An error occurred while processing the request");
        }
    }

    /// <summary>
    /// Get tenant context lookup data for dropdowns
    /// </summary>
    [HttpGet("tenant/lookup")]
    [TenantIdHeader]
    public async Task<IActionResult> GetTenantContextLookup([FromQuery] string? category = null,
        [FromQuery] string? searchTerm = null, [FromQuery] bool includeInactive = false)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var query = new GetTenantContextLookupQuery
            {
                Category = category,
                SearchTerm = searchTerm,
                IncludeInactive = includeInactive
            };

            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tenant context lookup data");
            return StatusCode(500, "An error occurred while retrieving tenant context lookup data");
        }
    }

    /// <summary>
    /// Get tenant context with its associated tenant lookups by tenant context ID
    /// </summary>
    [HttpGet("tenant/{tenantContextId}/with-lookups")]
    [TenantIdHeader]
    public async Task<IActionResult> GetTenantContextWithLookups(Guid tenantContextId, [FromQuery] bool includeInactiveLookups = false)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var query = new GetTenantContextWithLookupsQuery(tenantContextId, includeInactiveLookups);
            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tenant context with lookups for TenantContextId: {TenantContextId}", tenantContextId);
            return StatusCode(500, "An error occurred while retrieving tenant context with lookups");
        }
    }

    /// <summary>
    /// Get multiple tenant contexts with their associated tenant lookups by tenant context IDs
    /// </summary>
    [HttpPost("tenant/bulk/with-lookups")]
    [TenantIdHeader]
    public async Task<IActionResult> GetBulkTenantContextWithLookups([FromBody] List<Guid> tenantContextIds, [FromQuery] bool includeInactiveLookups = false)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            if (tenantContextIds == null || !tenantContextIds.Any())
            {
                return BadRequest("Tenant context IDs list cannot be empty");
            }

            if (tenantContextIds.Count > 100) // Reasonable limit to prevent abuse
            {
                return BadRequest("Maximum 100 tenant context IDs allowed per request");
            }

            var query = new GetBulkTenantContextWithLookupsQuery(tenantContextIds, includeInactiveLookups);
            var result = await _mediator.Send(query);

            if (result.Succeeded)
            {
                return Ok(result.Data);
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving bulk tenant contexts with lookups for {Count} TenantContextIds", tenantContextIds?.Count ?? 0);
            return StatusCode(500, "An error occurred while retrieving bulk tenant contexts with lookups");
        }
    }

    #endregion

    #region TenantLookup Management Operations

    /// <summary>
    /// Create or update a single tenant lookup
    /// </summary>
    [HttpPost("tenant/lookup/upsert")]
    [TenantIdHeader]
    public async Task<IActionResult> UpsertTenantLookup([FromBody] UpsertTenantLookupCommand command)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(new { Id = result.Data, Message = "Tenant lookup upserted successfully" });
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error upserting tenant lookup");
            return StatusCode(500, "An error occurred while upserting tenant lookup");
        }
    }

    /// <summary>
    /// Delete a tenant lookup
    /// </summary>
    [HttpDelete("tenant/lookup/{tenantLookupId}")]
    [TenantIdHeader]
    public async Task<IActionResult> DeleteTenantLookup(Guid tenantLookupId)
    {
        var tenantValidation = ValidateTenantHeader();
        if (tenantValidation != null) return tenantValidation;

        try
        {
            var command = new DeleteTenantLookupCommand(tenantLookupId);
            var result = await _mediator.Send(command);

            if (result.Succeeded)
            {
                return Ok(new { Message = "Tenant lookup deleted successfully" });
            }

            return BadRequest(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant lookup with ID: {TenantLookupId}", tenantLookupId);
            return StatusCode(500, "An error occurred while deleting tenant lookup");
        }
    }

    #endregion
}
