import 'package:flutter/material.dart';

/// Navigation item model for hierarchical navigation structure
class NavItem {
  final String id;
  final String name;
  final String? title;
  final String? displayName;
  final IconData? iconData;
  final String? iconName; // Icon name from API
  final String? iconType; // Icon type (lucide, material, custom)
  final String? objectType; // For API calls
  final String? parentId;
  final int? hierarchyLevel;
  final String? hierarchyPath;
  final List<NavItem>? children; // Hierarchical support
  final bool isExpanded;
  final bool isActive;
  final bool isVisible;
  final int? sortOrder;
  final List<String>? permissions;
  final Map<String, dynamic>? metadata;

  const NavItem({
    required this.id,
    required this.name,
    this.displayName,
    this.iconData,
    this.iconName,
    this.iconType,
    this.objectType,
    this.parentId,
    this.hierarchyLevel,
    this.hierarchyPath,
    this.children,
    this.isExpanded = false,
    this.isActive = true,
    this.isVisible = true,
    this.sortOrder,
    this.permissions,
    this.metadata,
    this.title,
  });

  /// Create a copy of this NavItem with updated properties
  NavItem copyWith({
    String? id,
    String? name,
    String? displayName,
    IconData? iconData,
    String? iconName,
    String? iconType,
    String? objectType,
    String? parentId,
    int? hierarchyLevel,
    String? hierarchyPath,
    List<NavItem>? children,
    bool? isExpanded,
    bool? isActive,
    bool? isVisible,
    int? sortOrder,
    List<String>? permissions,
    Map<String, dynamic>? metadata,
    String? title,
  }) {
    return NavItem(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      iconData: iconData ?? this.iconData,
      iconName: iconName ?? this.iconName,
      iconType: iconType ?? this.iconType,
      objectType: objectType ?? this.objectType,
      parentId: parentId ?? this.parentId,
      hierarchyLevel: hierarchyLevel ?? this.hierarchyLevel,
      hierarchyPath: hierarchyPath ?? this.hierarchyPath,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
      isActive: isActive ?? this.isActive,
      isVisible: isVisible ?? this.isVisible,
      sortOrder: sortOrder ?? this.sortOrder,
      permissions: permissions ?? this.permissions,
      metadata: metadata ?? this.metadata,
      title: title ?? this.title,
    );
  }

  /// Check if this item has children
  bool get hasChildren => children != null && children!.isNotEmpty;

  /// Check if this item matches a search query
  @override
  String toString() {
    return 'NavItem{id: $id, name: $name, objectType: $objectType, parentId: $parentId, hierarchyLevel: $hierarchyLevel, children: ${children?.length ?? 0} items}';
  }

  bool matchesSearch(String query) {
    if (query.isEmpty) return true;

    final lowerQuery = query.toLowerCase();
    final lowerName = name.toLowerCase();

    // Check if current item matches
    if (lowerName.contains(lowerQuery)) return true;

    // Check if any children match
    if (hasChildren) {
      return children!.any((child) => child.matchesSearch(query));
    }

    return false;
  }

  /// Get all descendant items that match a search query
  List<NavItem> getMatchingDescendants(String query) {
    final List<NavItem> matches = [];

    if (matchesSearch(query)) {
      matches.add(this);
    }

    if (hasChildren) {
      for (final child in children!) {
        matches.addAll(child.getMatchingDescendants(query));
      }
    }

    return matches;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NavItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  String toShortString() {
    return 'NavItem{id: $id, name: $name, objectType: $objectType, hasChildren: $hasChildren}';
  }
}

