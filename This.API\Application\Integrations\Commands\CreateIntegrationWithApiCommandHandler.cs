using Application.Integrations.DTOs;
using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Integrations.Commands;

/// <summary>
/// Handler for CreateIntegrationWithApiCommand
/// </summary>
public class CreateIntegrationWithApiCommandHandler : IRequestHandler<CreateIntegrationWithApiCommand, Result<CreateIntegrationWithApiResponseDto>>
{
    private readonly IRepository<Integration> _integrationRepository;
    private readonly IRepository<IntegrationApi> _integrationApiRepository;
    private readonly IRepository<IntegrationConfiguration> _configurationRepository;
    private readonly IReadRepository<Product> _productRepository;
    private readonly IReadRepository<Domain.Entities.Object> _objectRepository;
    private readonly ILogger<CreateIntegrationWithApiCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateIntegrationWithApiCommandHandler(
        IRepository<Integration> integrationRepository,
        IRepository<IntegrationApi> integrationApiRepository,
        IRepository<IntegrationConfiguration> configurationRepository,
        IReadRepository<Product> productRepository,
        IReadRepository<Domain.Entities.Object> objectRepository,
        ILogger<CreateIntegrationWithApiCommandHandler> logger)
    {
        _integrationRepository = integrationRepository;
        _integrationApiRepository = integrationApiRepository;
        _configurationRepository = configurationRepository;
        _productRepository = productRepository;
        _objectRepository = objectRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<CreateIntegrationWithApiResponseDto>> Handle(
        CreateIntegrationWithApiCommand request, 
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting CreateIntegrationWithApi operation for Product {ProductId}, Integration {IntegrationName}",
            request.ProductId, request.Name);

        try
        {
            // Step 1: Validate dependencies
            var validationResult = await ValidateRequestAsync(request, cancellationToken);
            if (!validationResult.Succeeded)
            {
                return Result<CreateIntegrationWithApiResponseDto>.Failure(validationResult.Message ?? "Validation failed");
            }

            var (product, targetObject) = validationResult.Data!;

            // Step 2: Create Integration
            var integration = await CreateIntegrationAsync(request, cancellationToken);
            _logger.LogInformation("Created Integration with ID: {IntegrationId}", integration.Id);

            // Step 3: Create IntegrationApi (if provided)
            IntegrationApi? integrationApi = null;
            if (ShouldCreateIntegrationApi(request))
            {
                integrationApi = await CreateIntegrationApiAsync(request, cancellationToken);
                _logger.LogInformation("Created IntegrationApi with ID: {IntegrationApiId}", integrationApi.Id);
            }

            // Step 4: Create IntegrationConfiguration (if both API and Configuration are provided)
            IntegrationConfiguration? configuration = null;
            if (integrationApi != null && ShouldCreateConfiguration(request))
            {
                configuration = await CreateIntegrationConfigurationAsync(
                    integration.Id,
                    integrationApi.Id,
                    request,
                    cancellationToken);
                _logger.LogInformation("Created IntegrationConfiguration with ID: {ConfigurationId}", configuration.Id);
            }

            // Step 5: Build response
            var response = BuildResponse(integration, integrationApi, configuration, product, targetObject);

            _logger.LogInformation("Successfully completed CreateIntegrationWithApi operation. Created {RecordCount} records", 
                response.Summary.TotalRecordsCreated);

            return Result<CreateIntegrationWithApiResponseDto>.Success(response, 
                "Integration with API and configuration created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during CreateIntegrationWithApi operation for Product {ProductId}",
                request.ProductId);
            return Result<CreateIntegrationWithApiResponseDto>.Failure(
                $"Failed to create integration with API: {ex.Message}");
        }
    }

    /// <summary>
    /// Validate the request and dependencies
    /// </summary>
    private async Task<Result<(Product Product, Domain.Entities.Object? Object)>> ValidateRequestAsync(
        CreateIntegrationWithApiCommand request,
        CancellationToken cancellationToken)
    {
        // Validate required fields
        if (string.IsNullOrWhiteSpace(request.Name))
        {
            return Result<(Product, Domain.Entities.Object?)>.Failure("Integration name is required");
        }

        if (string.IsNullOrWhiteSpace(request.AuthType))
        {
            return Result<(Product, Domain.Entities.Object?)>.Failure("Authentication type is required");
        }

        // Validate IntegrationApi fields only if IntegrationApi is provided
        if (ShouldCreateIntegrationApi(request))
        {
            if (string.IsNullOrWhiteSpace(request.IntegrationApi!.Name))
            {
                return Result<(Product, Domain.Entities.Object?)>.Failure("IntegrationApi name is required when creating API");
            }

            if (string.IsNullOrWhiteSpace(request.IntegrationApi.EndpointUrl))
            {
                request.IntegrationApi.EndpointUrl = string.Empty;
            }
        }

        // Validate Product exists
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null)
        {
            return Result<(Product, Domain.Entities.Object?)>.Failure($"Product with ID {request.ProductId} not found");
        }

        // Validate Object exists only if Configuration is provided
        Domain.Entities.Object? targetObject = null;
        if (ShouldCreateConfiguration(request))
        {
            targetObject = await _objectRepository.GetByIdAsync(request.Configuration!.ObjectId, cancellationToken);
            if (targetObject == null)
            {
                return Result<(Product, Domain.Entities.Object?)>.Failure($"Object with ID {request.Configuration.ObjectId} not found");
            }
        }

        return Result<(Product, Domain.Entities.Object?)>.Success((product, targetObject));
    }

    /// <summary>
    /// Create Integration entity
    /// </summary>
    private async Task<Integration> CreateIntegrationAsync(
        CreateIntegrationWithApiCommand request,
        CancellationToken cancellationToken)
    {
        var integration = new Integration
        {
            ProductId = request.ProductId,
            Name = request.Name.Trim(),
            AuthType = request.AuthType.Trim(),
            AuthConfig = request.AuthConfig ?? string.Empty,
            IsActive = request.IsActive,
            SyncFrequency = request.SyncFrequencyMinutes.HasValue
                ? TimeSpan.FromMinutes(request.SyncFrequencyMinutes.Value)
                : null
        };

        return await _integrationRepository.AddAsync(integration, cancellationToken);
    }

    /// <summary>
    /// Create IntegrationApi entity
    /// </summary>
    private async Task<IntegrationApi> CreateIntegrationApiAsync(
        CreateIntegrationWithApiCommand request,
        CancellationToken cancellationToken)
    {
        
        var integrationApi = new IntegrationApi
        {
            ProductId = request.ProductId,
            Name = request.IntegrationApi.Name.Trim(),
            EndpointUrl = request.IntegrationApi.EndpointUrl.Trim(),
            Schema = request.IntegrationApi.Schema,
            IsActive = request.IntegrationApi.IsActive
        };

        return await _integrationApiRepository.AddAsync(integrationApi, cancellationToken);
    }

    /// <summary>
    /// Create IntegrationConfiguration entity
    /// </summary>
    private async Task<IntegrationConfiguration> CreateIntegrationConfigurationAsync(
        Guid integrationId,
        Guid integrationApiId,
        CreateIntegrationWithApiCommand request,
        CancellationToken cancellationToken)
    {
        var configuration = new IntegrationConfiguration
        {
            IntegrationId = integrationId,
            IntegrationApiId = integrationApiId,
            ObjectId = request.Configuration.ObjectId,
            Direction = request.Configuration.Direction,
            IsActive = request.Configuration.IsActive
        };

        return await _configurationRepository.AddAsync(configuration, cancellationToken);
    }

    /// <summary>
    /// Determine if IntegrationApi should be created
    /// </summary>
    private static bool ShouldCreateIntegrationApi(CreateIntegrationWithApiCommand request)
    {
        return request.IntegrationApi != null &&
               !string.IsNullOrWhiteSpace(request.IntegrationApi.Name);
    }

    /// <summary>
    /// Determine if IntegrationConfiguration should be created
    /// </summary>
    private static bool ShouldCreateConfiguration(CreateIntegrationWithApiCommand request)
    {
        return request.Configuration != null &&
               request.Configuration.ObjectId != Guid.Empty;
    }

    /// <summary>
    /// Build the response DTO
    /// </summary>
    private static CreateIntegrationWithApiResponseDto BuildResponse(
        Integration integration,
        IntegrationApi? integrationApi,
        IntegrationConfiguration? configuration,
        Product product,
        Domain.Entities.Object? targetObject)
    {
        var createdEntityIds = new List<Guid> { integration.Id };
        var createdCount = 1;
        var message = "Integration created successfully";

        if (integrationApi != null)
        {
            createdEntityIds.Add(integrationApi.Id);
            createdCount++;
            message = configuration != null
                ? "Integration, IntegrationApi, and IntegrationConfiguration created successfully"
                : "Integration and IntegrationApi created successfully";
        }

        if (configuration != null)
        {
            createdEntityIds.Add(configuration.Id);
            createdCount++;
        }

        return new CreateIntegrationWithApiResponseDto
        {
            Integration = new CreatedIntegrationDetailsDto
            {
                Id = integration.Id,
                ProductId = integration.ProductId,
                ProductName = product.Name,
                Name = integration.Name,
                AuthType = integration.AuthType,
                IsActive = integration.IsActive,
                SyncFrequency = integration.SyncFrequency,
                CreatedAt = integration.CreatedAt,
                CreatedBy = integration.CreatedBy
            },
            IntegrationApi = integrationApi != null ? new CreatedIntegrationApiDetailsDto
            {
                Id = integrationApi.Id,
                ProductId = integrationApi.ProductId,
                Name = integrationApi.Name,
                EndpointUrl = integrationApi.EndpointUrl,
                Schema = integrationApi.Schema,
                IsActive = integrationApi.IsActive,
                CreatedAt = integrationApi.CreatedAt,
                CreatedBy = integrationApi.CreatedBy
            } : null,
            Configuration = configuration != null ? new CreatedIntegrationConfigurationDetailsDto
            {
                Id = configuration.Id,
                IntegrationId = configuration.IntegrationId,
                IntegrationApiId = configuration.IntegrationApiId,
                ObjectId = configuration.ObjectId,
                ObjectName = targetObject?.Name ?? string.Empty,
                Direction = configuration.Direction,
                IsActive = configuration.IsActive,
                CreatedAt = configuration.CreatedAt,
                CreatedBy = configuration.CreatedBy
            } : null,
            Summary = new BulkCreationSummaryDto
            {
                TotalRecordsCreated = createdCount,
                CreatedEntityIds = createdEntityIds,
                OperationTimestamp = DateTime.UtcNow,
                Message = message
            }
        };
    }
}
