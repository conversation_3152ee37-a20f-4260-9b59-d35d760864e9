using Domain.Entities;
using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Database.Configuration.Entities;

/// <summary>
/// Configuration for Display entity
/// </summary>
public class DisplayConfig : IEntityTypeConfiguration<Display>
{
    public void Configure(EntityTypeBuilder<Display> builder)
    {
        builder.ToTable("Displays", "Genp");

        // Properties
        builder.Property(e => e.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasColumnType("TEXT");

        builder.Property(e => e.DisplayName)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.IsDefault)
            .HasDefaultValue(false);

        builder.Property(e => e.RouteTemplate)
            .HasMaxLength(500);

        builder.Property(e => e.Icon)
            .HasMaxLength(100);

        builder.Property(e => e.SortOrder)
            .HasDefaultValue(0);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(e => e.Id)
            .HasDatabaseName("IX_Displays_Id");

        builder.HasIndex(e => e.Name)
            .HasDatabaseName("IX_Displays_Name");

        builder.HasIndex(e => e.IsDefault)
            .HasDatabaseName("IX_Displays_IsDefault")
            .HasFilter("\"IsDefault\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_Displays_IsActive")
            .HasFilter("\"IsActive\" = true AND \"IsDeleted\" = false");

        builder.HasIndex(e => e.SortOrder)
            .HasDatabaseName("IX_Displays_SortOrder");

        // Multi-tenancy
        builder.IsMultiTenant();

        // Relationships
        builder.HasMany(e => e.DisplayActions)
            .WithOne(e => e.Display)
            .HasForeignKey(e => e.DisplayId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
