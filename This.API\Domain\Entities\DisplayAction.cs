using Domain.Common.Contracts;
using System.ComponentModel.DataAnnotations;

namespace Domain.Entities;

/// <summary>
/// Junction table linking Object, Display, and Action entities
/// </summary>
public class DisplayAction : AuditableEntity, IAggregateRoot
{
    /// <summary>
    /// Object ID - Foreign key to Object entity
    /// </summary>
    [Required]
    public Guid? ObjectId { get; set; }

    /// <summary>
    /// Display ID - Foreign key to Display entity
    /// </summary>
    [Required]
    public Guid DisplayId { get; set; }

    /// <summary>
    /// Action ID - Foreign key to Action entity
    /// </summary>
    [Required]
    public Guid ActionId { get; set; }

    /// <summary>
    /// Access level - 'Public', 'Protected', 'Private'
    /// </summary>
    [MaxLength(50)]
    public string AccessLevel { get; set; } = "Public";

    /// <summary>
    /// Whether this is a default action
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Sort order for action ordering
    /// </summary>
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// Whether the action is visible in toolbar
    /// </summary>
    public bool IsVisibleInToolbar { get; set; } = true;

    /// <summary>
    /// Whether the action is visible in context menu
    /// </summary>
    public bool IsVisibleInContextMenu { get; set; } = false;

    /// <summary>
    /// Whether the action is visible in row actions
    /// </summary>
    public bool IsVisibleInRowActions { get; set; } = false;

    // Navigation Properties
    /// <summary>
    /// Object entity
    /// </summary>
    public virtual Object? Object { get; set; } = null!;

    /// <summary>
    /// Display entity
    /// </summary>
    public virtual Display Display { get; set; } = null!;

    /// <summary>
    /// Action entity
    /// </summary>
    public virtual Action Action { get; set; } = null!;
}
