using Application.Context.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.Context.Queries.GetBulkContextWithLookups;

/// <summary>
/// Query to get multiple contexts with their associated lookups by context IDs
/// </summary>
public class GetBulkContextWithLookupsQuery : IRequest<Result<BulkContextWithLookupsDto>>
{
    /// <summary>
    /// List of Context IDs to retrieve
    /// </summary>
    public List<Guid> ContextIds { get; set; } = new();

    /// <summary>
    /// Whether to include inactive lookups
    /// </summary>
    public bool IncludeInactiveLookups { get; set; } = false;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetBulkContextWithLookupsQuery(List<Guid> contextIds, bool includeInactiveLookups = false)
    {
        ContextIds = contextIds ?? new List<Guid>();
        IncludeInactiveLookups = includeInactiveLookups;
    }

    /// <summary>
    /// Parameterless constructor for model binding
    /// </summary>
    public GetBulkContextWithLookupsQuery()
    {
    }
}
