using Application.RoleActions.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.RoleActions.Commands;

/// <summary>
/// Update role actions command
/// </summary>
public class UpdateRoleActionsCommand : IRequest<Result<RoleActionsResponse>>
{
    /// <summary>
    /// Role ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// List of action IDs to associate with the role
    /// </summary>
    public List<Guid> ActionIds { get; set; } = new();
}
