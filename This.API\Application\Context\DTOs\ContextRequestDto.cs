namespace Application.Context.DTOs;

/// <summary>
/// Request DTO for Context upsert operations
/// </summary>
public class ContextUpsertRequest
{
    /// <summary>
    /// Context data as key-value pairs
    /// </summary>
    public Dictionary<string, object?> ContextData { get; set; } = new();
}

/// <summary>
/// Request DTO for Context bulk upsert operations
/// </summary>
public class ContextUpsertBulkRequest
{
    /// <summary>
    /// List of context data for bulk operations
    /// </summary>
    public List<Dictionary<string, object?>> ContextDataList { get; set; } = new();

    /// <summary>
    /// Batch size for processing
    /// </summary>
    public int BatchSize { get; set; } = 1000;
}

/// <summary>
/// Request DTO for TenantContext upsert operations
/// </summary>
public class TenantContextUpsertRequest
{
    /// <summary>
    /// TenantContext data as key-value pairs
    /// </summary>
    public Dictionary<string, object?> TenantContextData { get; set; } = new();
}

/// <summary>
/// Request DTO for TenantContext bulk upsert operations
/// </summary>
public class TenantContextUpsertBulkRequest
{
    /// <summary>
    /// List of tenant context data for bulk operations
    /// </summary>
    public List<Dictionary<string, object?>> TenantContextDataList { get; set; } = new();

    /// <summary>
    /// Batch size for processing
    /// </summary>
    public int BatchSize { get; set; } = 1000;
}
