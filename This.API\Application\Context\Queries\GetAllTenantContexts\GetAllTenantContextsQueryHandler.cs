using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetAllTenantContexts;

/// <summary>
/// Handler for GetAllTenantContextsQuery
/// </summary>
public class GetAllTenantContextsQueryHandler : IRequestHandler<GetAllTenantContextsQuery, Result<List<TenantContextDto>>>
{
    private readonly IRepository<TenantContext> _tenantContextRepository;
    private readonly ILogger<GetAllTenantContextsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetAllTenantContextsQueryHandler(
        IRepository<TenantContext> tenantContextRepository,
        ILogger<GetAllTenantContextsQueryHandler> logger)
    {
        _tenantContextRepository = tenantContextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<List<TenantContextDto>>> Handle(GetAllTenantContextsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting all tenant contexts with filters: IncludeInactive={IncludeInactive}, Category={Category}, SearchTerm={SearchTerm}",
                request.IncludeInactive, request.Category, request.SearchTerm);

            // Create specification - tenant filtering is handled automatically by the repository
            var spec = new TenantContextLookupSpec(
                searchTerm: request.SearchTerm,
                category: request.Category,
                includeInactive: request.IncludeInactive);

            // Execute query using specification
            var tenantContexts = await _tenantContextRepository.ListAsync(spec, cancellationToken);

            // Map to DTOs
            var tenantContextDtos = tenantContexts.Adapt<List<TenantContextDto>>();

            _logger.LogInformation("Successfully retrieved {Count} tenant contexts", tenantContextDtos.Count);

            return Result<List<TenantContextDto>>.Success(tenantContextDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all tenant contexts");
            return Result<List<TenantContextDto>>.Failure($"Error retrieving tenant contexts: {ex.Message}");
        }
    }
}
