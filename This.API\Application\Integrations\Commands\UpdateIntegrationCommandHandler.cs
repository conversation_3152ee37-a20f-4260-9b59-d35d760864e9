using Application.Integrations.DTOs;
using Application.Integrations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Commands;

/// <summary>
/// Update integration command handler
/// </summary>
public class UpdateIntegrationCommandHandler : IRequestHandler<UpdateIntegrationCommand, Result<IntegrationDto>>
{
    private readonly IRepository<Integration> _integrationRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateIntegrationCommandHandler(IRepository<Integration> integrationRepository)
    {
        _integrationRepository = integrationRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<IntegrationDto>> Handle(UpdateIntegrationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing integration
            var integration = await _integrationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (integration == null)
            {
                return Result<IntegrationDto>.Failure("Integration not found.");
            }

            // Check if another integration with same name exists for this product
            var existingIntegrationSpec = new IntegrationByNameAndProductSpec(request.Name, integration.ProductId);
            var existingIntegration = await _integrationRepository.GetBySpecAsync(existingIntegrationSpec, cancellationToken);

            if (existingIntegration != null && existingIntegration.Id != request.Id)
            {
                return Result<IntegrationDto>.Failure("Integration with this name already exists for this product.");
            }

            // Update integration properties
            integration.Name = request.Name;
            integration.AuthType = request.AuthType;
            integration.AuthConfig = request.AuthConfig;
            integration.IsActive = request.IsActive;
            integration.SyncFrequency = request.SyncFrequency;

            await _integrationRepository.UpdateAsync(integration, cancellationToken);

            var integrationDto = integration.Adapt<IntegrationDto>();
            return Result<IntegrationDto>.Success(integrationDto);
        }
        catch (Exception ex)
        {
            return Result<IntegrationDto>.Failure($"Failed to update integration: {ex.Message}");
        }
    }
}
