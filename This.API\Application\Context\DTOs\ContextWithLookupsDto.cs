namespace Application.Context.DTOs;

/// <summary>
/// DTO for Context with its associated lookups
/// </summary>
public class ContextWithLookupsDto
{
    /// <summary>
    /// Context information
    /// </summary>
    public ContextDto Context { get; set; } = new();

    /// <summary>
    /// Associated lookups for this context
    /// </summary>
    public List<LookupDto> Lookups { get; set; } = new();

    /// <summary>
    /// Total count of lookups
    /// </summary>
    public int LookupsCount => Lookups.Count;
}

/// <summary>
/// DTO for TenantContext with its associated lookups
/// </summary>
public class TenantContextWithLookupsDto
{
    /// <summary>
    /// TenantContext information
    /// </summary>
    public TenantContextDto TenantContext { get; set; } = new();

    /// <summary>
    /// Associated tenant lookups for this context
    /// </summary>
    public List<TenantLookupDto> TenantLookups { get; set; } = new();

    /// <summary>
    /// Total count of tenant lookups
    /// </summary>
    public int TenantLookupsCount => TenantLookups.Count;
}

/// <summary>
/// DTO for bulk Context with lookups operations
/// </summary>
public class BulkContextWithLookupsDto
{
    /// <summary>
    /// List of contexts with their associated lookups
    /// </summary>
    public List<ContextWithLookupsDto> ContextsWithLookups { get; set; } = new();

    /// <summary>
    /// Total count of contexts returned
    /// </summary>
    public int TotalContextsCount => ContextsWithLookups.Count;

    /// <summary>
    /// Total count of all lookups across all contexts
    /// </summary>
    public int TotalLookupsCount => ContextsWithLookups.Sum(c => c.LookupsCount);

    /// <summary>
    /// List of context IDs that were not found
    /// </summary>
    public List<Guid> NotFoundContextIds { get; set; } = new();

    /// <summary>
    /// List of context IDs that were deleted
    /// </summary>
    public List<Guid> DeletedContextIds { get; set; } = new();
}

/// <summary>
/// DTO for bulk TenantContext with lookups operations
/// </summary>
public class BulkTenantContextWithLookupsDto
{
    /// <summary>
    /// List of tenant contexts with their associated lookups
    /// </summary>
    public List<TenantContextWithLookupsDto> TenantContextsWithLookups { get; set; } = new();

    /// <summary>
    /// Total count of tenant contexts returned
    /// </summary>
    public int TotalTenantContextsCount => TenantContextsWithLookups.Count;

    /// <summary>
    /// Total count of all tenant lookups across all tenant contexts
    /// </summary>
    public int TotalTenantLookupsCount => TenantContextsWithLookups.Sum(tc => tc.TenantLookupsCount);

    /// <summary>
    /// List of tenant context IDs that were not found
    /// </summary>
    public List<Guid> NotFoundTenantContextIds { get; set; } = new();

    /// <summary>
    /// List of tenant context IDs that were deleted
    /// </summary>
    public List<Guid> DeletedTenantContextIds { get; set; } = new();
}

/// <summary>
/// DTO for Lookup entity
/// </summary>
public class LookupDto
{
    /// <summary>
    /// Lookup ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Context ID
    /// </summary>
    public Guid ContextId { get; set; }

    /// <summary>
    /// Lookup value
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default value
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Additional value 1
    /// </summary>
    public string? Value1 { get; set; }

    /// <summary>
    /// Additional value 2
    /// </summary>
    public string? Value2 { get; set; }

    /// <summary>
    /// Display sequence
    /// </summary>
    public int ShowSequence { get; set; }

    /// <summary>
    /// Whether the lookup is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the lookup is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Created date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified date
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by user ID
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}

/// <summary>
/// DTO for TenantLookup entity
/// </summary>
public class TenantLookupDto
{
    /// <summary>
    /// TenantLookup ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Tenant ID
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// TenantContext ID
    /// </summary>
    public Guid TenantContextId { get; set; }

    /// <summary>
    /// Lookup value
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Whether this is the default value
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// Additional value 1
    /// </summary>
    public string? Value1 { get; set; }

    /// <summary>
    /// Additional value 2
    /// </summary>
    public string? Value2 { get; set; }

    /// <summary>
    /// Display sequence
    /// </summary>
    public int ShowSequence { get; set; }

    /// <summary>
    /// Whether the tenant lookup is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the tenant lookup is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Created date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified date
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by user ID
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}
