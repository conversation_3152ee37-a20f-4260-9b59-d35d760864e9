/**
 * StatusBadge Atom
 * Reusable status badge component with consistent styling
 */

import React from 'react';
import { Badge } from 'react-bootstrap';

export interface StatusBadgeProps {
  status: string;
  variant?: 'subscription' | 'template' | 'tenant' | 'custom';
  customVariant?: string;
  size?: 'sm' | 'lg';
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  variant = 'custom',
  customVariant,
  size,
  className = ''
}) => {
  // Get badge variant based on status and type
  const getBadgeVariant = (): string => {
    if (customVariant) return customVariant;

    const statusLower = status.toLowerCase();

    switch (variant) {
      case 'subscription':
        switch (statusLower) {
          case 'active':
            return 'success';
          case 'inactive':
          case 'suspended':
            return 'warning';
          case 'expired':
          case 'cancelled':
            return 'danger';
          case 'pending':
            return 'info';
          default:
            return 'secondary';
        }

      case 'template':
        switch (statusLower) {
          case 'live':
            return 'success';
          case 'beta':
            return 'warning';
          case 'archived':
            return 'secondary';
          case 'draft':
            return 'primary';
          default:
            return 'info';
        }

      case 'tenant':
        switch (statusLower) {
          case 'active':
            return 'success';
          case 'inactive':
            return 'warning';
          case 'suspended':
            return 'danger';
          default:
            return 'secondary';
        }

      default:
        // Generic status mapping
        switch (statusLower) {
          case 'active':
          case 'success':
          case 'completed':
          case 'approved':
            return 'success';
          case 'warning':
          case 'pending':
          case 'in-progress':
            return 'warning';
          case 'error':
          case 'failed':
          case 'rejected':
          case 'expired':
            return 'danger';
          case 'info':
          case 'draft':
            return 'info';
          default:
            return 'secondary';
        }
    }
  };

  // Format status text
  const formatStatus = (status: string): string => {
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  const badgeVariant = getBadgeVariant();
  const formattedStatus = formatStatus(status);

  return (
    <Badge 
      bg={badgeVariant}
      className={`${size === 'sm' ? 'badge-sm' : size === 'lg' ? 'badge-lg' : ''} ${className}`}
      title={`Status: ${formattedStatus}`}
    >
      {formattedStatus}
    </Badge>
  );
};
