using Abstraction.Database.Repositories;
using Application.DisplayManagement.DTOs;
using Domain.Entities;
using Finbuckle.MultiTenant;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.DisplayManagement.Commands;

/// <summary>
/// Bulk upsert Display with Actions command handler - hierarchical structure
/// </summary>
public class BulkUpsertDisplayWithActionsCommandHandler : IRequestHandler<BulkUpsertDisplayWithActionsCommand, Result<BulkDisplayWithActionsResponseDto>>
{
    private readonly IMediator _mediator;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly ITenantInfo _tenantInfo;
    private readonly ILogger<BulkUpsertDisplayWithActionsCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public BulkUpsertDisplayWithActionsCommandHandler(
        IMediator mediator,
        IRepository<Domain.Entities.Object> objectRepository,
        ITenantInfo tenantInfo,
        ILogger<BulkUpsertDisplayWithActionsCommandHandler> logger)
    {
        _mediator = mediator;
        _objectRepository = objectRepository;
        _tenantInfo = tenantInfo;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<BulkDisplayWithActionsResponseDto>> Handle(BulkUpsertDisplayWithActionsCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var errors = new List<string>();
        var displayResults = new List<DisplayWithActionsResponseDto>();
        
        try
        {
            _logger.LogInformation("Starting bulk hierarchical upsert operation for {DisplayCount} displays", 
                request.Displays.Count);

            // Step 1: Validate all ObjectIds exist across all displays
            var allObjectIds = request.Displays
                .SelectMany(d => d.Actions.Select(a => a.ObjectId))
                .Distinct()
                .ToList();
            
            await ValidateObjectsExistAsync(allObjectIds, cancellationToken);

            // Step 2: Process each display individually
            foreach (var displayDto in request.Displays)
            {
                try
                {
                    var singleCommand = displayDto.Adapt<UpsertDisplayWithActionsCommand>();
                    var singleResult = await _mediator.Send(singleCommand, cancellationToken);
                    
                    if (singleResult.Succeeded)
                    {
                        displayResults.Add(singleResult.Data);
                    }
                    else
                    {
                        errors.Add($"Display '{displayDto.Name}': {singleResult.Message}");
                        _logger.LogWarning("Failed to process display {DisplayName}: {Error}", 
                            displayDto.Name, singleResult.Message);
                    }
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Display '{displayDto.Name}': {ex.Message}";
                    errors.Add(errorMessage);
                    _logger.LogError(ex, "Error processing display {DisplayName}", displayDto.Name);
                }
            }

            stopwatch.Stop();

            // Step 3: Build bulk response
            var response = BuildBulkResponse(displayResults, errors, stopwatch.ElapsedMilliseconds);

            _logger.LogInformation("Bulk hierarchical upsert completed in {ElapsedMs}ms. Processed: {ProcessedCount}, Errors: {ErrorCount}",
                stopwatch.ElapsedMilliseconds, displayResults.Count, errors.Count);

            return Result<BulkDisplayWithActionsResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Critical error occurred during bulk hierarchical upsert operation");
            
            return Result<BulkDisplayWithActionsResponseDto>.Failure($"A critical error occurred during the bulk upsert operation: {ex.Message}");
        }
    }

    /// <summary>
    /// Validate that all ObjectIds exist
    /// </summary>
    private async Task ValidateObjectsExistAsync(List<Guid> objectIds, CancellationToken cancellationToken)
    {
        var missingObjectIds = new List<Guid>();
        
        foreach (var objectId in objectIds)
        {
            var existingObject = await _objectRepository.GetByIdAsync(objectId, cancellationToken);
            if (existingObject == null)
            {
                missingObjectIds.Add(objectId);
            }
        }

        if (missingObjectIds.Any())
        {
            throw new ArgumentException($"The following Object IDs were not found: {string.Join(", ", missingObjectIds)}");
        }
    }

    /// <summary>
    /// Build bulk response with statistics
    /// </summary>
    private static BulkDisplayWithActionsResponseDto BuildBulkResponse(
        List<DisplayWithActionsResponseDto> displayResults, 
        List<string> errors, 
        long processingTimeMs)
    {
        var totalDisplaysCreated = displayResults.Count(r => r.DisplayWasCreated);
        var totalDisplaysUpdated = displayResults.Count(r => !r.DisplayWasCreated);
        var totalActionsCreated = displayResults.SelectMany(r => r.ActionResults).Count(ar => ar.ActionWasCreated);
        var totalActionsUpdated = displayResults.SelectMany(r => r.ActionResults).Count(ar => !ar.ActionWasCreated);
        var totalDisplayActionsCreated = displayResults.SelectMany(r => r.ActionResults).Count(ar => ar.DisplayActionWasCreated);
        var totalDisplayActionsUpdated = displayResults.SelectMany(r => r.ActionResults).Count(ar => !ar.DisplayActionWasCreated);

        return new BulkDisplayWithActionsResponseDto
        {
            DisplayResults = displayResults,
            TotalDisplaysProcessed = displayResults.Count,
            TotalDisplaysCreated = totalDisplaysCreated,
            TotalDisplaysUpdated = totalDisplaysUpdated,
            TotalActionsProcessed = displayResults.SelectMany(r => r.ActionResults).Count(),
            TotalActionsCreated = totalActionsCreated,
            TotalActionsUpdated = totalActionsUpdated,
            TotalDisplayActionsProcessed = displayResults.SelectMany(r => r.ActionResults).Count(),
            TotalDisplayActionsCreated = totalDisplayActionsCreated,
            TotalDisplayActionsUpdated = totalDisplayActionsUpdated,
            ProcessingTimeMs = processingTimeMs,
            Errors = errors,
            IsSuccessful = errors.Count == 0,
            OperationSummary = BuildBulkOperationSummary(
                totalDisplaysCreated, totalDisplaysUpdated,
                totalActionsCreated, totalActionsUpdated,
                totalDisplayActionsCreated, totalDisplayActionsUpdated,
                errors.Count)
        };
    }

    /// <summary>
    /// Build bulk operation summary message
    /// </summary>
    private static string BuildBulkOperationSummary(
        int displaysCreated, int displaysUpdated,
        int actionsCreated, int actionsUpdated,
        int displayActionsCreated, int displayActionsUpdated,
        int errorCount)
    {
        var operations = new List<string>
        {
            $"{displaysCreated} displays created, {displaysUpdated} displays updated",
            $"{actionsCreated} actions created, {actionsUpdated} actions updated",
            $"{displayActionsCreated} relationships created, {displayActionsUpdated} relationships updated"
        };

        if (errorCount > 0)
        {
            operations.Add($"{errorCount} errors occurred");
        }

        return string.Join(", ", operations);
    }
}
