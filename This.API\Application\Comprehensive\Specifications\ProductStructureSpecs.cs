using Ardalis.Specification;
using Domain.Entities;

namespace Application.Comprehensive.Specifications;

/// <summary>
/// Specification to find existing Product by name for current tenant
/// </summary>
public class ProductByNameSpec : Specification<Product>, ISingleResultSpecification<Product>
{
    public ProductByNameSpec(string name)
    {
        Query.Where(p => p.Name == name && !p.IsDeleted);
    }
}

/// <summary>
/// Specification to find existing Object by name under a specific Product
/// </summary>
public class ObjectByNameAndProductSpec : Specification<Domain.Entities.Object>, ISingleResultSpecification<Domain.Entities.Object>
{
    public ObjectByNameAndProductSpec(string name, Guid productId)
    {
        Query.Where(o => o.Name == name && 
                         o.ProductId == productId && 
                         !o.IsDeleted);
    }
}

/// <summary>
/// Specification to find existing Metadata by name
/// </summary>
public class MetadataByNameSpec : Specification<Metadata>, ISingleResultSpecification<Metadata>
{
    public MetadataByNameSpec(string name)
    {
        Query.Where(m => m.Name == name && !m.IsDeleted);
    }
}

/// <summary>
/// Specification to check if ObjectMetadata relationship already exists
/// </summary>
public class ObjectMetadataExistsSpec : Specification<ObjectMetadata>, ISingleResultSpecification<ObjectMetadata>
{
    public ObjectMetadataExistsSpec(Guid objectId, Guid metadataId)
    {
        Query.Where(om => om.ObjectId == objectId && 
                          om.MetadataId == metadataId && 
                          !om.IsDeleted);
    }
}

/// <summary>
/// Specification to get all existing metadata for a specific object
/// </summary>
public class MetadataByObjectSpec : Specification<ObjectMetadata>
{
    public MetadataByObjectSpec(Guid objectId)
    {
        Query.Where(om => om.ObjectId == objectId && !om.IsDeleted);
        Query.Include(om => om.Metadata);
    }
}

/// <summary>
/// Specification to get all existing objects for a specific product
/// </summary>
public class ObjectsByProductSpec : Specification<Domain.Entities.Object>
{
    public ObjectsByProductSpec(Guid productId)
    {
        Query.Where(o => o.ProductId == productId && !o.IsDeleted);
    }
}
