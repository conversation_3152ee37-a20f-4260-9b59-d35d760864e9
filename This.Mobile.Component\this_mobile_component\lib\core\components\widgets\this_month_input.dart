import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widget_enums.dart';

/// Month option model
class MonthOption {
  final int value;
  final String name;
  final String shortName;

  const MonthOption({
    required this.value,
    required this.name,
    required this.shortName,
  });
}

/// A customizable month input widget following the 'this_componentName_relatedTo' naming convention
/// This widget handles month input with validation and various display formats
class ThisMonthInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final int? value; // 1-12
  final ValueChanged<int?> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final MonthDisplayFormat displayFormat;
  final bool showDropdown;
  final List<int>? allowedMonths;
  final String? Function(int?)? customValidation;

  const ThisMonthInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.displayFormat = MonthDisplayFormat.full,
    this.showDropdown = true,
    this.allowedMonths,
    this.customValidation,
  });

  @override
  State<ThisMonthInput> createState() => _ThisMonthInputState();
}

class _ThisMonthInputState extends State<ThisMonthInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];

  static const List<MonthOption> _allMonths = [
    MonthOption(value: 1, name: 'January', shortName: 'Jan'),
    MonthOption(value: 2, name: 'February', shortName: 'Feb'),
    MonthOption(value: 3, name: 'March', shortName: 'Mar'),
    MonthOption(value: 4, name: 'April', shortName: 'Apr'),
    MonthOption(value: 5, name: 'May', shortName: 'May'),
    MonthOption(value: 6, name: 'June', shortName: 'Jun'),
    MonthOption(value: 7, name: 'July', shortName: 'Jul'),
    MonthOption(value: 8, name: 'August', shortName: 'Aug'),
    MonthOption(value: 9, name: 'September', shortName: 'Sep'),
    MonthOption(value: 10, name: 'October', shortName: 'Oct'),
    MonthOption(value: 11, name: 'November', shortName: 'Nov'),
    MonthOption(value: 12, name: 'December', shortName: 'Dec'),
  ];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value?.toString() ?? '');
    _focusNode = FocusNode();
    _validateValue(widget.value);
  }

  @override
  void didUpdateWidget(ThisMonthInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value?.toString() ?? '';
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  List<MonthOption> get _availableMonths {
    if (widget.allowedMonths != null) {
      return _allMonths.where((month) => widget.allowedMonths!.contains(month.value)).toList();
    }
    return _allMonths;
  }

  String _getMonthDisplayName(int monthValue) {
    final month = _allMonths.firstWhere((m) => m.value == monthValue);
    switch (widget.displayFormat) {
      case MonthDisplayFormat.full:
        return month.name;
      case MonthDisplayFormat.short:
        return month.shortName;
      case MonthDisplayFormat.number:
        return monthValue.toString().padLeft(2, '0');
    }
  }

  List<String> _validateValue(int? value) {
    final errors = <String>[];

    // Required validation
    if (widget.required && value == null) {
      errors.add('${widget.label} is required');
      return errors;
    }

    if (value != null) {
      // Range validation
      if (value < 1 || value > 12) {
        errors.add('Month must be between 1 and 12');
      }

      // Allowed months validation
      if (widget.allowedMonths != null && !widget.allowedMonths!.contains(value)) {
        errors.add('This month is not allowed');
      }

      // Custom validation
      if (widget.customValidation != null) {
        final customError = widget.customValidation!(value);
        if (customError != null) {
          errors.add(customError);
        }
      }
    }

    return errors;
  }

  void _handleChange(String text) {
    int? newValue;
    if (text.isNotEmpty) {
      newValue = int.tryParse(text);
    }

    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  void _handleDropdownChange(int? newValue) {
    _controller.text = newValue?.toString() ?? '';
    widget.onChanged(newValue);

    // Real-time validation
    final errors = _validateValue(newValue);
    setState(() {
      _errors = errors;
    });

    // Notify parent of validation state
    widget.onValidation?.call(errors);
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Input Field or Dropdown
        if (widget.showDropdown)
          DropdownButtonFormField<int>(
            value: widget.value,
            onChanged: widget.disabled || widget.readOnly ? null : _handleDropdownChange,
            decoration: InputDecoration(
              hintText: widget.placeholder ?? 'Select month',
              hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
              errorText: hasErrors ? _errors.first : null,
              errorStyle: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
              suffixIcon: const Icon(Icons.calendar_month, size: 20),
            ),
            style: LexendTextStyles.lexend14Regular.copyWith(
              color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
            ),
            dropdownColor: ColorPalette.darkToneInk,
            items: _availableMonths.map((month) {
              return DropdownMenuItem<int>(
                value: month.value,
                child: Text(
                  _getMonthDisplayName(month.value),
                  style: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.black,
                  ),
                ),
              );
            }).toList(),
          )
        else
          TextFormField(
            controller: _controller,
            focusNode: _focusNode,
            enabled: !widget.disabled,
            readOnly: widget.readOnly,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(2),
              _MonthInputFormatter(),
            ],
            onChanged: _handleChange,
            decoration: InputDecoration(
              hintText: widget.placeholder ?? 'MM',
              hintStyle: LexendTextStyles.lexend14Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
              errorText: hasErrors ? _errors.first : null,
              errorStyle: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
              suffixIcon: const Icon(Icons.calendar_month, size: 20),
            ),
            style: LexendTextStyles.lexend14Regular.copyWith(
              color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
            ),
          ),

        // Helper text for allowed months
        if (!hasErrors && widget.allowedMonths != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Allowed months: ${widget.allowedMonths!.map((m) => _getMonthDisplayName(m)).join(', ')}',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          ),
      ],
    );
  }
}

/// Custom input formatter for month values
class _MonthInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;
    if (text.isEmpty) return newValue;

    final value = int.tryParse(text);
    if (value == null) return oldValue;

    // Limit to valid month range
    if (value > 12) {
      return oldValue;
    }

    return newValue;
  }
}
