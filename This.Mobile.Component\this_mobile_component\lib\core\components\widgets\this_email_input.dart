import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// A customizable email input widget following the 'this_componentName_relatedTo' naming convention
/// This widget handles email input with comprehensive validation and various email format options
class ThisEmailInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final String value;
  final ValueChanged<String> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  final List<String>? allowedDomains;
  final List<String>? blockedDomains;
  final bool requireTLD;
  final bool allowSubdomains;
  final bool allowInternational;
  final bool allowPlusAddressing;
  final bool allowDotAddressing;
  final int maxLength;
  final int minLength;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool autoFocus;
  final String? Function(String)? customValidation;

  const ThisEmailInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.allowedDomains,
    this.blockedDomains,
    this.requireTLD = true,
    this.allowSubdomains = true,
    this.allowInternational = true,
    this.allowPlusAddressing = true,
    this.allowDotAddressing = true,
    this.maxLength = 254,
    this.minLength = 5,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisEmailInput> createState() => _ThisEmailInputState();
}

class _ThisEmailInputState extends State<ThisEmailInput> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;

  // Email validation regex patterns
  static final RegExp _strictEmailRegex = RegExp(r'^[a-zA-Z0-9.!#$%&' "'" r'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$');
  static final RegExp _internationalEmailRegex = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
    _focusNode = FocusNode();

    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void didUpdateWidget(ThisEmailInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller.text = widget.value;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // Helper functions
  String _extractDomain(String email) {
    final parts = email.split('@');
    return parts.length == 2 ? parts[1].toLowerCase() : '';
  }

  String _extractLocalPart(String email) {
    final parts = email.split('@');
    return parts.length == 2 ? parts[0] : '';
  }

  bool _hasTLD(String domain) {
    final parts = domain.split('.');
    return parts.length >= 2 && parts.last.length >= 2;
  }

  bool _hasSubdomain(String domain) {
    final parts = domain.split('.');
    return parts.length > 2;
  }

  bool _hasPlusAddressing(String localPart) {
    return localPart.contains('+');
  }

  bool _hasDotAddressing(String localPart) {
    return localPart.contains('.');
  }

  bool _isInternationalDomain(String domain) {
    return RegExp(r'[^\x00-\x7F]').hasMatch(domain);
  }

  List<String> _validateValue(String value) {
    final errors = <String>[];

    // 1. Required validation (highest priority)
    if (widget.required && value.trim().isEmpty) {
      errors.add('${widget.label} is required');
      return errors;
    }

    // Skip other validations if empty and not required
    if (value.trim().isEmpty && !widget.required) {
      return errors;
    }

    // 2. Length validation
    if (value.length < widget.minLength) {
      errors.add('${widget.label} must be at least ${widget.minLength} characters');
      return errors;
    }

    if (value.length > widget.maxLength) {
      errors.add('${widget.label} must be at most ${widget.maxLength} characters');
      return errors;
    }

    // 3. Basic email format validation
    final regex = widget.allowInternational ? _internationalEmailRegex : _strictEmailRegex;
    if (!regex.hasMatch(value)) {
      errors.add('${widget.label} must be a valid email address');
      return errors;
    }

    final domain = _extractDomain(value);
    final localPart = _extractLocalPart(value);

    // 4. TLD validation
    if (widget.requireTLD && domain.isNotEmpty && !_hasTLD(domain)) {
      errors.add('${widget.label} must have a valid top-level domain');
      return errors;
    }

    // 5. Subdomain validation
    if (!widget.allowSubdomains && domain.isNotEmpty && _hasSubdomain(domain)) {
      errors.add('${widget.label} cannot contain subdomains');
      return errors;
    }

    // 6. International domain validation
    if (!widget.allowInternational && domain.isNotEmpty && _isInternationalDomain(domain)) {
      errors.add('${widget.label} must use ASCII characters only');
      return errors;
    }

    // 7. Plus addressing validation
    if (!widget.allowPlusAddressing && localPart.isNotEmpty && _hasPlusAddressing(localPart)) {
      errors.add('${widget.label} cannot contain plus (+) addressing');
      return errors;
    }

    // 8. Dot addressing validation
    if (!widget.allowDotAddressing && localPart.isNotEmpty && _hasDotAddressing(localPart)) {
      errors.add('${widget.label} cannot contain dots (.) in the local part');
      return errors;
    }

    // 9. Allowed domains validation
    if (widget.allowedDomains != null && widget.allowedDomains!.isNotEmpty && domain.isNotEmpty) {
      final isAllowed = widget.allowedDomains!.any((allowed) => domain == allowed.toLowerCase() || (widget.allowSubdomains && domain.endsWith('.${allowed.toLowerCase()}')));
      if (!isAllowed) {
        errors.add('${widget.label} must be from an allowed domain: ${widget.allowedDomains!.join(', ')}');
        return errors;
      }
    }

    // 10. Blocked domains validation
    if (widget.blockedDomains != null && widget.blockedDomains!.isNotEmpty && domain.isNotEmpty) {
      final isBlocked = widget.blockedDomains!.any((blocked) => domain == blocked.toLowerCase() || (widget.allowSubdomains && domain.endsWith('.${blocked.toLowerCase()}')));
      if (isBlocked) {
        errors.add('${widget.label} cannot be from a blocked domain: ${widget.blockedDomains!.join(', ')}');
        return errors;
      }
    }

    // 11. Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(value);
      if (customError != null) {
        errors.add(customError);
        return errors;
      }
    }

    return errors;
  }

  void _handleChange(String value) {
    widget.onChanged(value);

    // Real-time validation (only if not validating on blur)
    if (!widget.validateOnBlur) {
      final errors = _validateValue(value);
      setState(() {
        _errors = errors;
        _isValidated = value.trim().isNotEmpty;
      });

      // Notify parent of validation state
      widget.onValidation?.call(errors);
    }
  }

  void _handleBlur() {
    // Validate on blur if enabled
    if (widget.validateOnBlur) {
      final errors = _validateValue(widget.value);
      setState(() {
        _errors = errors;
        _isValidated = widget.value.trim().isNotEmpty;
      });

      // Notify parent of validation state
      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated || widget.value.trim().isEmpty) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  List<String> _getHelperTexts() {
    final helpers = <String>[];

    if (widget.allowedDomains != null && widget.allowedDomains!.isNotEmpty) {
      helpers.add('Allowed domains: ${widget.allowedDomains!.join(', ')}');
    }

    if (widget.blockedDomains != null && widget.blockedDomains!.isNotEmpty) {
      helpers.add('Blocked domains: ${widget.blockedDomains!.join(', ')}');
    }

    if (!widget.allowPlusAddressing) {
      helpers.add('Plus (+) addressing not allowed');
    }

    if (!widget.allowSubdomains) {
      helpers.add('Subdomains not allowed');
    }

    if (!widget.allowInternational) {
      helpers.add('ASCII characters only');
    }

    return helpers;
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors && widget.value.trim().isNotEmpty;
    final helperTexts = _getHelperTexts();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Input Field
        TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          enabled: !widget.disabled,
          readOnly: widget.readOnly,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onChanged: _handleChange,
          onFieldSubmitted: (_) => _handleBlur(),
          onTapOutside: (_) => _handleBlur(),
          decoration: InputDecoration(
            hintText: widget.placeholder ?? 'Enter email address...',
            hintStyle: LexendTextStyles.lexend14Regular.copyWith(
              color: ColorPalette.placeHolderTextColor,
            ),
            prefixIcon: widget.showIcon ? Icon(Icons.email, size: 20, color: ColorPalette.placeHolderTextColor) : null,
            suffixIcon: _getValidationIcon(),
            errorText: hasErrors ? _errors.first : null,
            errorStyle: LexendTextStyles.lexend12Regular.copyWith(
              color: const Color(0xFFC73E1D),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.gray300),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.gray300),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(
                color: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.black),
                width: 2,
              ),
            ),
          ),
          style: LexendTextStyles.lexend14Regular.copyWith(
            color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
          ),
          inputFormatters: [
            LengthLimitingTextInputFormatter(widget.maxLength),
          ],
          autocorrect: false,
          enableSuggestions: false,
        ),

        // Helper texts (only show when no errors)
        if (!hasErrors && helperTexts.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: helperTexts
                  .map((text) => Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Text(
                          text,
                          style: LexendTextStyles.lexend12Regular.copyWith(
                            color: ColorPalette.placeHolderTextColor,
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
      ],
    );
  }
}
