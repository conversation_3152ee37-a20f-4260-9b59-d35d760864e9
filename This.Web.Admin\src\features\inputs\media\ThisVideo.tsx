// components/ThisVideo.tsx
import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Video as VideoIcon,
  X,
  Check,
  AlertCircle,
  Download,
  Play,
  Pause,
  Clock
} from 'lucide-react';

interface ThisVideoProps {
  id: string;
  label: string;
  value: File[];
  onChange: (files: File[]) => void;
  onValidation?: (errors: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  multiple?: boolean;
  maxFiles?: number;
  minFiles?: number;
  maxFileSize?: number; // in bytes
  minFileSize?: number; // in bytes
  maxTotalSize?: number; // total size of all files in bytes
  // Video-specific validations
  maxDuration?: number; // in seconds
  minDuration?: number; // in seconds
  maxWidth?: number; // in pixels
  minWidth?: number; // in pixels
  maxHeight?: number; // in pixels
  minHeight?: number; // in pixels
  aspectRatio?: number; // width/height ratio
  aspectRatioTolerance?: number; // tolerance for aspect ratio (default: 0.1)
  maxFrameRate?: number; // frames per second
  minFrameRate?: number; // frames per second
  maxBitrate?: number; // in kbps
  minBitrate?: number; // in kbps
  allowedFormats?: string[]; // ['mp4', 'webm', 'ogg', 'avi', 'mov']
  blockedFormats?: string[]; // formats to block
  allowedCodecs?: string[]; // ['h264', 'vp8', 'vp9', 'av1']
  blockedCodecs?: string[]; // codecs to block
  // Display options
  showPreview?: boolean;
  previewSize?: 'small' | 'medium' | 'large';
  showVideoInfo?: boolean;
  showDuration?: boolean;
  showDimensions?: boolean;
  showControls?: boolean;
  allowDownload?: boolean;
  allowRemove?: boolean;
  allowFullscreen?: boolean;
  autoPlay?: boolean;
  muted?: boolean;
  dragAndDrop?: boolean;
  customValidation?: (files: File[], videoData: VideoData[]) => string | null;
}

interface VideoData {
  file: File;
  duration: number;
  width: number;
  height: number;
  aspectRatio: number;
  frameRate?: number;
  bitrate?: number;
  codec?: string;
  url: string;
}

interface ValidationRule {
  test: (files: File[], videoData: VideoData[]) => boolean;
  message: string;
}

const ThisVideo: React.FC<ThisVideoProps> = ({
  id,
  label,
  value,
  onChange,
  onValidation,
  disabled = false,
  readOnly = false,
  helpText,
  placeholder = 'Choose videos or drag and drop',
  required = false,
  multiple = false,
  maxFiles = 5,
  minFiles = 0,
  maxFileSize = 100 * 1024 * 1024, // 100MB
  minFileSize = 0,
  maxTotalSize = 500 * 1024 * 1024, // 500MB
  maxDuration,
  minDuration,
  maxWidth,
  minWidth,
  maxHeight,
  minHeight,
  aspectRatio,
  aspectRatioTolerance = 0.1,
  allowedFormats = ['mp4', 'webm', 'ogg', 'avi', 'mov'],
  blockedFormats,
  showPreview = true,
  previewSize = 'medium',
  showVideoInfo = true,
  showDuration = true,
  showDimensions = true,
  showControls = true,
  allowDownload = false,
  allowRemove = true,

  autoPlay = false,
  muted = true,
  dragAndDrop = true,
  customValidation
}) => {
  const [errors, setErrors] = useState<string[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const [videoData, setVideoData] = useState<VideoData[]>([]);
  const [playingVideo, setPlayingVideo] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Helper functions
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const getFileExtension = (filename: string): string => {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
  };

  const getTotalFileSize = (files: File[]): number => {
    return files.reduce((total, file) => total + file.size, 0);
  };

  const loadVideoData = useCallback(async (files: File[]): Promise<VideoData[]> => {
    const videoDataPromises = files.map((file) => {
      return new Promise<VideoData>((resolve, reject) => {
        const video = document.createElement('video');
        const url = URL.createObjectURL(file);

        video.onloadedmetadata = () => {
          const width = video.videoWidth;
          const height = video.videoHeight;
          const duration = video.duration;
          const aspectRatio = width / height;

          resolve({
            file,
            duration,
            width,
            height,
            aspectRatio,
            url
          });
        };

        video.onerror = () => {
          reject(new Error(`Failed to load video: ${file.name}`));
        };

        video.src = url;
      });
    });

    try {
      return await Promise.all(videoDataPromises);
    } catch (error) {
      console.error('Error loading video data:', error);
      return [];
    }
  }, []);

  // Update video data when files change
  useEffect(() => {
    if (value.length > 0) {
      loadVideoData(value).then(setVideoData);
    } else {
      setVideoData([]);
    }
  }, [value, loadVideoData]);

  // Validation rules in priority order
  const getValidationRules = (): ValidationRule[] => {
    const rules: ValidationRule[] = [];

    // 1. Required validation (highest priority)
    if (required) {
      rules.push({
        test: (files) => files.length > 0,
        message: `${label} is required`
      });
    }

    // 2. File count validation
    if (minFiles > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.length >= minFiles;
        },
        message: `${label} must have at least ${minFiles} video${minFiles > 1 ? 's' : ''}`
      });
    }

    if (maxFiles > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.length <= maxFiles;
        },
        message: `${label} can have at most ${maxFiles} video${maxFiles > 1 ? 's' : ''}`
      });
    }

    // 3. File size validation
    if (maxFileSize > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => file.size <= maxFileSize);
        },
        message: `Each video must be smaller than ${formatFileSize(maxFileSize)}`
      });
    }

    if (minFileSize > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => file.size >= minFileSize);
        },
        message: `Each video must be larger than ${formatFileSize(minFileSize)}`
      });
    }

    // 4. Total size validation
    if (maxTotalSize > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return getTotalFileSize(files) <= maxTotalSize;
        },
        message: `Total video size must be smaller than ${formatFileSize(maxTotalSize)}`
      });
    }

    // 5. Video format validation
    if (allowedFormats && allowedFormats.length > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return files.every(file => allowedFormats.includes(getFileExtension(file.name)));
        },
        message: `Only these video formats are allowed: ${allowedFormats.join(', ')}`
      });
    }

    if (blockedFormats && blockedFormats.length > 0) {
      rules.push({
        test: (files) => {
          if (files.length === 0) return !required;
          return !files.some(file => blockedFormats.includes(getFileExtension(file.name)));
        },
        message: `These video formats are not allowed: ${blockedFormats.join(', ')}`
      });
    }

    // 6. Video duration validation
    if (maxDuration) {
      rules.push({
        test: (files, vidData) => {
          if (files.length === 0) return !required;
          return vidData.every(data => data.duration <= maxDuration);
        },
        message: `Video duration must be at most ${formatDuration(maxDuration)}`
      });
    }

    if (minDuration) {
      rules.push({
        test: (files, vidData) => {
          if (files.length === 0) return !required;
          return vidData.every(data => data.duration >= minDuration);
        },
        message: `Video duration must be at least ${formatDuration(minDuration)}`
      });
    }

    // 7. Video dimension validation
    if (maxWidth) {
      rules.push({
        test: (files, vidData) => {
          if (files.length === 0) return !required;
          return vidData.every(data => data.width <= maxWidth);
        },
        message: `Video width must be at most ${maxWidth} pixels`
      });
    }

    if (minWidth) {
      rules.push({
        test: (files, vidData) => {
          if (files.length === 0) return !required;
          return vidData.every(data => data.width >= minWidth);
        },
        message: `Video width must be at least ${minWidth} pixels`
      });
    }

    if (maxHeight) {
      rules.push({
        test: (files, vidData) => {
          if (files.length === 0) return !required;
          return vidData.every(data => data.height <= maxHeight);
        },
        message: `Video height must be at most ${maxHeight} pixels`
      });
    }

    if (minHeight) {
      rules.push({
        test: (files, vidData) => {
          if (files.length === 0) return !required;
          return vidData.every(data => data.height >= minHeight);
        },
        message: `Video height must be at least ${minHeight} pixels`
      });
    }

    // 8. Aspect ratio validation
    if (aspectRatio) {
      rules.push({
        test: (files, vidData) => {
          if (files.length === 0) return !required;
          return vidData.every(data =>
            Math.abs(data.aspectRatio - aspectRatio) <= aspectRatioTolerance
          );
        },
        message: `Video aspect ratio must be approximately ${aspectRatio.toFixed(2)} (${aspectRatio > 1 ? 'landscape' : aspectRatio < 1 ? 'portrait' : 'square'})`
      });
    }

    // 9. Custom validation
    if (customValidation) {
      rules.push({
        test: (files, vidData) => {
          const customError = customValidation(files, vidData);
          return customError === null;
        },
        message: customValidation(value, videoData) || ''
      });
    }

    return rules;
  };

  const validateVideos = (files: File[], vidData: VideoData[]): string[] => {
    const rules = getValidationRules();

    // Return only the first error found (most important)
    for (const rule of rules) {
      if (!rule.test(files, vidData)) {
        return [rule.message];
      }
    }

    return [];
  };

  const handleFileChange = useCallback(async (newFiles: File[]) => {
    const vidData = await loadVideoData(newFiles);
    const validationErrors = validateVideos(newFiles, vidData);
    setErrors(validationErrors);
    setIsValidated(true);
    onValidation?.(validationErrors);
    onChange(newFiles);
  }, [onChange, onValidation, loadVideoData]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const combinedFiles = multiple ? [...value, ...files] : files;
    handleFileChange(combinedFiles);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled && !readOnly && dragAndDrop) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    if (disabled || readOnly || !dragAndDrop) return;

    const files = Array.from(event.dataTransfer.files).filter(file =>
      file.type.startsWith('video/')
    );
    const combinedFiles = multiple ? [...value, ...files] : files;
    handleFileChange(combinedFiles);
  };

  const removeVideo = (index: number) => {
    if (disabled || readOnly || !allowRemove) return;

    const newFiles = value.filter((_, i) => i !== index);
    handleFileChange(newFiles);
  };

  const downloadVideo = (file: File) => {
    if (!allowDownload) return;

    const url = URL.createObjectURL(file);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const openFileDialog = () => {
    if (!disabled && !readOnly) {
      fileInputRef.current?.click();
    }
  };

  const toggleVideoPlay = (url: string) => {
    setPlayingVideo(playingVideo === url ? null : url);
  };

  const hasErrors = errors.length > 0;
  const totalSize = getTotalFileSize(value);

  const getPreviewSizeClass = () => {
    switch (previewSize) {
      case 'small': return 'video-preview-small';
      case 'large': return 'video-preview-large';
      default: return 'video-preview-medium';
    }
  };

  return (
    <div className="file-input-container">
      {/* Label */}
      <label className="text-input-label" htmlFor={id}>
        {label}
        {required && <span className="required-indicator">*</span>}
        {helpText && (
          <span
            className="text-input-info-icon"
            data-tooltip={helpText}
            aria-label={helpText}
          />
        )}
      </label>

      {/* Video Input Wrapper */}
      <div className="text-input-wrapper">
        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          id={id}
          multiple={multiple}
          disabled={disabled}
          onChange={handleInputChange}
          className="hidden"
          accept="video/*"
        />

        {/* Drop Zone */}
        <div
          className={`video-drop-zone ${hasErrors ? 'has-error' : ''} ${disabled ? 'disabled' : ''} ${isDragOver ? 'drag-over' : ''} ${readOnly ? 'readonly' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={openFileDialog}
        >
          <div className="file-drop-content">
            <VideoIcon className="file-upload-icon" size={24} />
            <p className="file-drop-text">
              {value.length > 0 ? `${value.length} video${value.length > 1 ? 's' : ''} selected` : placeholder}
            </p>
            {dragAndDrop && !readOnly && !disabled && (
              <p className="file-drop-hint">
                Click to browse or drag and drop videos here
              </p>
            )}
            {!dragAndDrop && !readOnly && !disabled && (
              <p className="file-drop-hint">
                Click to browse videos
              </p>
            )}
          </div>
        </div>

        {/* Video Preview Grid */}
        {showPreview && value.length > 0 && videoData.length > 0 && (
          <div className="video-preview-grid">
            {videoData.map((data, index) => (
              <div key={`${data.file.name}-${index}`} className={`video-preview-item ${getPreviewSizeClass()}`}>
                <div className="video-preview-container">
                  <video
                    src={data.url}
                    className="video-preview"
                    controls={showControls}
                    autoPlay={autoPlay && playingVideo === data.url}
                    muted={muted}
                    preload="metadata"
                  />

                  {/* Video Actions Overlay */}
                  <div className="video-actions-overlay">
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleVideoPlay(data.url);
                      }}
                      className="video-action-button play"
                      title={playingVideo === data.url ? "Pause" : "Play"}
                    >
                      {playingVideo === data.url ? <Pause size={14} /> : <Play size={14} />}
                    </button>
                    {allowDownload && (
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          downloadVideo(data.file);
                        }}
                        className="video-action-button"
                        title="Download video"
                      >
                        <Download size={14} />
                      </button>
                    )}
                    {allowRemove && !readOnly && !disabled && (
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeVideo(index);
                        }}
                        className="video-action-button remove"
                        title="Remove video"
                      >
                        <X size={14} />
                      </button>
                    )}
                  </div>
                </div>

                {/* Video Info */}
                {showVideoInfo && (
                  <div className="video-info">
                    <div className="video-name">{data.file.name}</div>
                    <div className="video-meta">
                      <span className="video-size">{formatFileSize(data.file.size)}</span>
                      {showDuration && (
                        <span className="video-duration">
                          <Clock size={12} />
                          {formatDuration(data.duration)}
                        </span>
                      )}
                      {showDimensions && (
                        <span className="video-dimensions">
                          {data.width} × {data.height}
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}

            {/* Total Size Display */}
            {value.length > 1 && (
              <div className="file-total-size">
                Total: {formatFileSize(totalSize)}
                {maxTotalSize > 0 && ` / ${formatFileSize(maxTotalSize)}`}
              </div>
            )}
          </div>
        )}

        {/* Validation Icon */}
        {isValidated && (
          <div className="validation-icon">
            {!hasErrors ? (
              <Check className="text-green-500" size={16} />
            ) : (
              <AlertCircle className="text-red-500" size={16} />
            )}
          </div>
        )}

        {/* Error Messages */}
        {hasErrors && (
          <div className="text-input-errors" role="alert" id={`${id}-error`}>
            <p className="error-message">
              {errors[0]}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThisVideo;
