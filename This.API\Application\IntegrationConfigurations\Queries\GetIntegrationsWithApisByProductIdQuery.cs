using Application.IntegrationConfigurations.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.IntegrationConfigurations.Queries;

/// <summary>
/// Get integrations with API info by product ID query
/// </summary>
public class GetIntegrationsWithApisByProductIdQuery : IRequest<Result<List<IntegrationWithApiInfoDto>>>
{
    /// <summary>
    /// Product ID to filter integrations
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Filter by active status (optional)
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Constructor
    /// </summary>
    public GetIntegrationsWithApisByProductIdQuery(Guid productId, bool? isActive = null)
    {
        ProductId = productId;
        IsActive = isActive;
    }
}

/// <summary>
/// Integration with API info DTO
/// </summary>
public class IntegrationWithApiInfoDto
{
    /// <summary>
    /// Integration ID
    /// </summary>
    public Guid IntegrationId { get; set; }

    /// <summary>
    /// Integration name
    /// </summary>
    public string IntegrationName { get; set; } = string.Empty;

    /// <summary>
    /// Product ID
    /// </summary>
    public Guid ProductId { get; set; }

    /// <summary>
    /// Product name
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Authentication type
    /// </summary>
    public string AuthType { get; set; } = string.Empty;

    /// <summary>
    /// Whether the integration is active
    /// </summary>
    public bool IsIntegrationActive { get; set; }

    /// <summary>
    /// Sync frequency
    /// </summary>
    public TimeSpan? SyncFrequency { get; set; }

    /// <summary>
    /// Last synchronization timestamp
    /// </summary>
    public DateTime? LastSyncAt { get; set; }

    /// <summary>
    /// Integration created at
    /// </summary>
    public DateTime IntegrationCreatedAt { get; set; }

    /// <summary>
    /// Integration created by
    /// </summary>
    public Guid? IntegrationCreatedBy { get; set; }

    /// <summary>
    /// List of configured APIs for this integration
    /// </summary>
    public List<ApiConfigurationInfoDto> ConfiguredApis { get; set; } = new();

    /// <summary>
    /// Total number of configured APIs
    /// </summary>
    public int TotalApis => ConfiguredApis.Count;

    /// <summary>
    /// Number of active APIs
    /// </summary>
    public int ActiveApis => ConfiguredApis.Count(api => api.IsApiActive && api.IsConfigActive);
}

/// <summary>
/// API configuration info DTO
/// </summary>
public class ApiConfigurationInfoDto
{
    /// <summary>
    /// IntegrationApi ID
    /// </summary>
    public Guid ApiId { get; set; }

    /// <summary>
    /// API name
    /// </summary>
    public string ApiName { get; set; } = string.Empty;

    /// <summary>
    /// API endpoint URL
    /// </summary>
    public string EndpointUrl { get; set; } = string.Empty;

    /// <summary>
    /// API schema
    /// </summary>
    public string? Schema { get; set; }

    /// <summary>
    /// Whether the API is active
    /// </summary>
    public bool IsApiActive { get; set; }

    /// <summary>
    /// Configuration ID
    /// </summary>
    public Guid ConfigurationId { get; set; }

    /// <summary>
    /// Object ID this configuration applies to
    /// </summary>
    public Guid ObjectId { get; set; }

    /// <summary>
    /// Object name
    /// </summary>
    public string ObjectName { get; set; } = string.Empty;

    /// <summary>
    /// Data flow direction
    /// </summary>
    public string? Direction { get; set; }

    /// <summary>
    /// Whether the configuration is active
    /// </summary>
    public bool IsConfigActive { get; set; }

    /// <summary>
    /// Configuration created at
    /// </summary>
    public DateTime ConfigCreatedAt { get; set; }

    /// <summary>
    /// Configuration created by
    /// </summary>
    public Guid? ConfigCreatedBy { get; set; }
}
