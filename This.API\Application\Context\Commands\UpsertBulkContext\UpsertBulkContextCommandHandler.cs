using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.Context.Commands.UpsertBulkContext;

/// <summary>
/// Handler for UpsertBulkContextCommand
/// </summary>
public class UpsertBulkContextCommandHandler : IRequestHandler<UpsertBulkContextCommand, Result<BulkUpsertContextResponse>>
{
    private readonly IRepository<Domain.Entities.Context> _contextRepository;
    private readonly ILogger<UpsertBulkContextCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertBulkContextCommandHandler(
        IRepository<Domain.Entities.Context> contextRepository,
        ILogger<UpsertBulkContextCommandHandler> logger)
    {
        _contextRepository = contextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<BulkUpsertContextResponse>> Handle(UpsertBulkContextCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new BulkUpsertContextResponse();

        try
        {
            _logger.LogInformation("Starting bulk upsert of {Count} contexts", request.Contexts.Count);

            foreach (var contextItem in request.Contexts)
            {
                try
                {
                    Domain.Entities.Context? context;

                    if (contextItem.Id.HasValue && contextItem.Id.Value != Guid.Empty)
                    {
                        // Update existing context
                        context = await _contextRepository.GetByIdAsync(contextItem.Id.Value, cancellationToken);
                        if (context == null)
                        {
                            response.Errors.Add($"Context with ID {contextItem.Id.Value} not found");
                            response.FailedCount++;
                            continue;
                        }

                        context.Name = contextItem.Name;
                        context.Description = contextItem.Description;
                        context.Category = contextItem.Category;
                        context.IsActive = contextItem.IsActive;

                        await _contextRepository.UpdateAsync(context, cancellationToken);
                        response.UpdatedCount++;
                        response.UpdatedIds.Add(context.Id);
                    }
                    else
                    {
                        // Create new context
                        context = new Domain.Entities.Context
                        {
                            Id = Guid.NewGuid(),
                            Name = contextItem.Name,
                            Description = contextItem.Description,
                            Category = contextItem.Category,
                            IsActive = contextItem.IsActive,
                            IsDeleted = false
                        };

                        await _contextRepository.AddAsync(context, cancellationToken);
                        response.CreatedCount++;
                        response.CreatedIds.Add(context.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing context: {Name}", contextItem.Name);
                    response.Errors.Add($"Error processing context '{contextItem.Name}': {ex.Message}");
                    response.FailedCount++;
                }
            }

            stopwatch.Stop();
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Bulk upsert completed: Created={Created}, Updated={Updated}, Failed={Failed}, Time={Time}ms",
                response.CreatedCount, response.UpdatedCount, response.FailedCount, response.ProcessingTimeMs);

            return Result<BulkUpsertContextResponse>.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            _logger.LogError(ex, "Bulk upsert failed after {Time}ms", response.ProcessingTimeMs);
            return Result<BulkUpsertContextResponse>.Failure($"Bulk upsert failed: {ex.Message}");
        }
    }
}
