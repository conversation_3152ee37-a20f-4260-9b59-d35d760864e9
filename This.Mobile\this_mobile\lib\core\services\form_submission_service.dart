import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/dynamic_form_value.dart';

class FormSubmissionService {
  final String baseUrl;
  final String tenant;

  FormSubmissionService({required this.baseUrl, required this.tenant});

  Future<http.Response> upsertFormData(Map<String, dynamic> payload) async {
    final url = '$baseUrl/api/objectvalues/upsert-single-with-metadata';
    final response = await http.post(
      Uri.parse(url),
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'tenant': tenant,
      },
      body: json.encode(payload),
    );
    return response;
  }
}
