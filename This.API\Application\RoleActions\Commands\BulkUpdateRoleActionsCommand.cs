using Application.RoleActions.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.RoleActions.Commands;

/// <summary>
/// Bulk update role actions command
/// </summary>
public class BulkUpdateRoleActionsCommand : IRequest<Result<List<RoleActionsResponse>>>
{
    /// <summary>
    /// List of role-action updates
    /// </summary>
    public List<UpdateRoleActionsRequest> RoleActions { get; set; } = new();
}
