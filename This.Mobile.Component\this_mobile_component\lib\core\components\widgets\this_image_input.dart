import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// Image model for image input
class SelectedImage {
  final String name;
  final String path;
  final int size;
  final int? width;
  final int? height;
  final DateTime dateModified;

  const SelectedImage({
    required this.name,
    required this.path,
    required this.size,
    this.width,
    this.height,
    required this.dateModified,
  });

  String get sizeFormatted {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String get dimensionsFormatted {
    if (width != null && height != null) {
      return '${width}x${height}px';
    }
    return 'Unknown dimensions';
  }
}

/// A customizable image input widget following the 'this_componentName_input' naming convention
/// This widget handles image upload with validation based on API configuration
class ThisImageInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final List<SelectedImage> value;
  final ValueChanged<List<SelectedImage>> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;

  // API-based validation parameters
  final String? validationPattern;
  final String? requiredErrorMessage;
  final String? patternErrorMessage;
  final String? fileTypeErrorMessage;
  final String? fileSizeErrorMessage;

  // Image-specific parameters from API
  final bool allowsMultiple;
  final List<String>? allowedFileTypes; // e.g., ['jpg', 'png', 'gif']
  final int? maxFileSizeBytes;
  final int? maxSelections;
  final int? maxWidth;
  final int? maxHeight;
  final int? minWidth;
  final int? minHeight;
  final bool showPreview;
  final bool allowCamera;
  final bool allowGallery;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool autoFocus;
  final String? Function(List<SelectedImage>)? customValidation;

  const ThisImageInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.validationPattern,
    this.requiredErrorMessage,
    this.patternErrorMessage,
    this.fileTypeErrorMessage,
    this.fileSizeErrorMessage,
    this.allowsMultiple = false,
    this.allowedFileTypes,
    this.maxFileSizeBytes,
    this.maxSelections,
    this.maxWidth,
    this.maxHeight,
    this.minWidth,
    this.minHeight,
    this.showPreview = true,
    this.allowCamera = true,
    this.allowGallery = true,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisImageInput> createState() => _ThisImageInputState();
}

class _ThisImageInputState extends State<ThisImageInput> {
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;
  bool _isUploading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();

    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  List<String> get _allowedExtensions {
    if (widget.allowedFileTypes == null || widget.allowedFileTypes!.isEmpty) {
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    }
    return widget.allowedFileTypes!.map((type) => type.toLowerCase()).toList();
  }

  List<String> _validateValue(List<SelectedImage> images) {
    final errors = <String>[];

    // 1. Required validation
    if (widget.required && images.isEmpty) {
      errors.add(widget.requiredErrorMessage ?? '${widget.label} is required');
      return errors;
    }

    // Skip other validations if empty and not required
    if (images.isEmpty && !widget.required) {
      return errors;
    }

    // 2. Multiple images validation
    if (!widget.allowsMultiple && images.length > 1) {
      errors.add('Only one image is allowed');
      return errors;
    }

    // 3. Max selections validation
    if (widget.maxSelections != null && images.length > widget.maxSelections!) {
      errors.add('Maximum ${widget.maxSelections} images allowed');
      return errors;
    }

    // 4. File size validation
    if (widget.maxFileSizeBytes != null) {
      for (final image in images) {
        if (image.size > widget.maxFileSizeBytes!) {
          errors.add(widget.fileSizeErrorMessage ?? 'Image size exceeds limit: ${image.name}');
          return errors;
        }
      }
    }

    // 5. Image dimensions validation
    for (final image in images) {
      if (widget.minWidth != null && image.width != null && image.width! < widget.minWidth!) {
        errors.add('Image width too small: ${image.name} (min: ${widget.minWidth}px)');
        return errors;
      }

      if (widget.maxWidth != null && image.width != null && image.width! > widget.maxWidth!) {
        errors.add('Image width too large: ${image.name} (max: ${widget.maxWidth}px)');
        return errors;
      }

      if (widget.minHeight != null && image.height != null && image.height! < widget.minHeight!) {
        errors.add('Image height too small: ${image.name} (min: ${widget.minHeight}px)');
        return errors;
      }

      if (widget.maxHeight != null && image.height != null && image.height! > widget.maxHeight!) {
        errors.add('Image height too large: ${image.name} (max: ${widget.maxHeight}px)');
        return errors;
      }
    }

    // 6. Pattern validation (for file names)
    if (widget.validationPattern != null) {
      final regex = RegExp(widget.validationPattern!);
      for (final image in images) {
        if (!regex.hasMatch(image.name)) {
          errors.add(widget.patternErrorMessage ?? 'Invalid image name: ${image.name}');
          return errors;
        }
      }
    }

    // 7. Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(images);
      if (customError != null) {
        errors.add(customError);
        return errors;
      }
    }

    return errors;
  }

  Future<SelectedImage?> _createSelectedImage(XFile file) async {
    try {
      final fileObj = File(file.path);
      final stat = await fileObj.stat();

      // Get image dimensions
      int? width, height;
      try {
        final image = await decodeImageFromList(await fileObj.readAsBytes());
        width = image.width;
        height = image.height;
      } catch (e) {
        // If we can't decode dimensions, continue without them
      }

      return SelectedImage(
        name: file.name,
        path: file.path,
        size: stat.size,
        width: width,
        height: height,
        dateModified: stat.modified,
      );
    } catch (e) {
      return null;
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    if (widget.disabled || widget.readOnly || _isUploading) return;

    setState(() {
      _isUploading = true;
    });

    try {
      if (widget.allowsMultiple && source == ImageSource.gallery) {
        final List<XFile> files = await _picker.pickMultiImage();
        if (files.isNotEmpty) {
          final selectedImages = <SelectedImage>[];

          for (final file in files) {
            final selectedImage = await _createSelectedImage(file);
            if (selectedImage != null) {
              selectedImages.add(selectedImage);
            }
          }

          final newImages = [...widget.value, ...selectedImages];
          widget.onChanged(newImages);

          // Validate
          final errors = _validateValue(newImages);
          setState(() {
            _errors = errors;
            _isValidated = true;
          });

          widget.onValidation?.call(errors);
        }
      } else {
        final XFile? file = await _picker.pickImage(source: source);
        if (file != null) {
          final selectedImage = await _createSelectedImage(file);
          if (selectedImage != null) {
            List<SelectedImage> newImages;
            if (widget.allowsMultiple) {
              newImages = [...widget.value, selectedImage];
            } else {
              newImages = [selectedImage];
            }

            widget.onChanged(newImages);

            // Validate
            final errors = _validateValue(newImages);
            setState(() {
              _errors = errors;
              _isValidated = true;
            });

            widget.onValidation?.call(errors);
          }
        }
      }
    } catch (e) {
      setState(() {
        _errors = ['Error picking image: $e'];
        _isValidated = true;
      });
      widget.onValidation?.call(_errors);
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _showImageSourceDialog() async {
    if (!widget.allowCamera && !widget.allowGallery) return;

    if (widget.allowCamera && widget.allowGallery) {
      await showModalBottomSheet(
        context: context,
        backgroundColor: ColorPalette.darkToneInk,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (context) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Image Source',
                style: LexendTextStyles.lexend16Bold.copyWith(
                  color: ColorPalette.black,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: ColorPalette.black),
                title: Text(
                  'Camera',
                  style: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.black,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library, color: ColorPalette.black),
                title: Text(
                  'Gallery',
                  style: LexendTextStyles.lexend14Regular.copyWith(
                    color: ColorPalette.black,
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        ),
      );
    } else if (widget.allowCamera) {
      await _pickImage(ImageSource.camera);
    } else if (widget.allowGallery) {
      await _pickImage(ImageSource.gallery);
    }
  }

  void _removeImage(int index) {
    final newImages = List<SelectedImage>.from(widget.value);
    newImages.removeAt(index);
    widget.onChanged(newImages);

    // Validate
    final errors = _validateValue(newImages);
    setState(() {
      _errors = errors;
      _isValidated = true;
    });

    widget.onValidation?.call(errors);
  }

  void _handleBlur() {
    if (widget.validateOnBlur) {
      final errors = _validateValue(widget.value);
      setState(() {
        _errors = errors;
        _isValidated = widget.value.isNotEmpty;
      });

      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated || widget.value.isEmpty) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  String _getDisplayText() {
    if (widget.value.isEmpty) {
      return widget.placeholder ?? 'Choose image${widget.allowsMultiple ? 's' : ''}...';
    }

    if (widget.value.length == 1) {
      return widget.value.first.name;
    } else {
      return '${widget.value.length} images selected';
    }
  }

  String _getAllowedTypesText() {
    return 'Allowed: ${_allowedExtensions.join(', ').toUpperCase()}';
  }

  String _getMaxSizeText() {
    if (widget.maxFileSizeBytes == null) return '';

    final maxSize = widget.maxFileSizeBytes!;
    if (maxSize < 1024) return 'Max size: ${maxSize}B';
    if (maxSize < 1024 * 1024) return 'Max size: ${(maxSize / 1024).toStringAsFixed(1)}KB';
    if (maxSize < 1024 * 1024 * 1024) return 'Max size: ${(maxSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return 'Max size: ${(maxSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String _getDimensionsText() {
    final parts = <String>[];

    if (widget.minWidth != null || widget.minHeight != null) {
      final minW = widget.minWidth ?? 0;
      final minH = widget.minHeight ?? 0;
      parts.add('Min: ${minW}x${minH}px');
    }

    if (widget.maxWidth != null || widget.maxHeight != null) {
      final maxW = widget.maxWidth ?? '∞';
      final maxH = widget.maxHeight ?? '∞';
      parts.add('Max: ${maxW}x${maxH}px');
    }

    return parts.join(', ');
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors && widget.value.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled ? ColorPalette.placeHolderTextColor : ColorPalette.black,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // Image picker button
        Focus(
          focusNode: _focusNode,
          onFocusChange: (focused) {
            if (!focused) _handleBlur();
          },
          child: GestureDetector(
            onTap: _showImageSourceDialog,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: hasErrors ? const Color(0xFFC73E1D) : (isValid ? ColorPalette.green : ColorPalette.gray300),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(6),
                color: _isUploading ? ColorPalette.gray300.withValues(alpha: 0.1) : null,
              ),
              child: Column(
                children: [
                  if (_isUploading) ...[
                    const CircularProgressIndicator(),
                    const SizedBox(height: 8),
                    Text(
                      'Processing...',
                      style: LexendTextStyles.lexend14Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                  ] else ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (widget.showIcon) ...[
                          Icon(
                            Icons.add_photo_alternate,
                            size: 24,
                            color: ColorPalette.placeHolderTextColor,
                          ),
                          const SizedBox(width: 8),
                        ],
                        Expanded(
                          child: Text(
                            _getDisplayText(),
                            style: LexendTextStyles.lexend14Regular.copyWith(
                              color: widget.value.isEmpty ? ColorPalette.placeHolderTextColor : ColorPalette.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        if (_getValidationIcon() != null) _getValidationIcon()!,
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to select image${widget.allowsMultiple ? 's' : ''}',
                      style: LexendTextStyles.lexend12Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),

        // Selected images preview
        if (widget.value.isNotEmpty && widget.showPreview) ...[
          const SizedBox(height: 12),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: widget.value.length,
            itemBuilder: (context, index) {
              final image = widget.value[index];

              return Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: ColorPalette.gray300.withValues(alpha: 0.3)),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Image.file(
                        File(image.path),
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: ColorPalette.darkToneInk.withValues(alpha: 0.3),
                          child: const Icon(
                            Icons.broken_image,
                            color: ColorPalette.placeHolderTextColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                  if (!widget.disabled && !widget.readOnly)
                    Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () => _removeImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: const Color(0xFFC73E1D),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 12,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  // Image info overlay
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(6),
                          bottomRight: Radius.circular(6),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            image.name,
                            style: LexendTextStyles.lexend10Regular.copyWith(
                              color: Colors.white,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                          Text(
                            '${image.sizeFormatted} • ${image.dimensionsFormatted}',
                            style: const TextStyle(
                              fontSize: 8,
                              color: Colors.white70,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],

        // Error message
        if (hasErrors)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _errors.first,
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
            ),
          ),

        // Helper text
        if (!hasErrors) ...[
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getAllowedTypesText(),
                  style: LexendTextStyles.lexend12Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
                if (widget.maxFileSizeBytes != null)
                  Text(
                    _getMaxSizeText(),
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                if (_getDimensionsText().isNotEmpty)
                  Text(
                    _getDimensionsText(),
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                if (widget.allowsMultiple && widget.maxSelections != null)
                  Text(
                    'Maximum ${widget.maxSelections} images',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
