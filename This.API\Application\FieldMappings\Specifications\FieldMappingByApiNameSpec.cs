using Ardalis.Specification;
using Domain.Entities;

namespace Application.FieldMappings.Specifications;

/// <summary>
/// Specification to get field mappings by API name with related entity details
/// </summary>
public class FieldMappingByApiNameSpec : Specification<FieldMapping>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public FieldMappingByApiNameSpec(string apiName, bool includeInactive = false)
    {
        Query.Where(fm => fm.ApiName == apiName);

        if (!includeInactive)
        {
            Query.Where(fm => fm.IsActive && !fm.IsDeleted);
        }

        // Include related entities for complete information
        Query.Include(fm => fm.ObjectMetadata)
             .ThenInclude(om => om.Metadata)
             .ThenInclude(m => m.DataType);

        Query.Include(fm => fm.ObjectMetadata)
             .ThenInclude(om => om.Object);

        Query.Include(fm => fm.User);
        Query.Include(fm => fm.Role);

        // Order by source field for consistent processing
        Query.OrderBy(fm => fm.SourceField);
    }
}
