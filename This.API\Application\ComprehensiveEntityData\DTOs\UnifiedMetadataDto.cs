namespace Application.ComprehensiveEntityData.DTOs;

/// <summary>
/// Unified DTO that consolidates Metadata and DataType fields with priority-based field consolidation
/// Priority: Metadata values take precedence over DataType values, with fallback to DataType when Metadata is null/empty
/// </summary>
public class UnifiedMetadataDto
{
    // Core Identity Fields
    public Guid? MetadataId { get; set; }
    public Guid? DataTypeId { get; set; }
    public string? Name { get; set; }

    // DataType-Only Fields (no consolidation needed)
    public string? DataTypeName { get; set; }
    public string? DataTypeDisplayName { get; set; }
    public string? Category { get; set; }
    public string? UiComponent { get; set; }
    public int? DecimalPlaces { get; set; }
    public decimal? StepValue { get; set; }
    public string? HtmlAttributes { get; set; }
    public string? FileSizeErrorMessage { get; set; }

    // Metadata-Only Fields (no consolidation needed)
    public Guid? ContextId { get; set; }
    public Guid? TenantContextId { get; set; }
    public Guid? ObjectLookupId { get; set; }

    // Display and Action Management Fields (Metadata-Only)
    /// <summary>
    /// Whether this metadata field is editable
    /// </summary>
    public bool? IsEditable { get; set; }

    /// <summary>
    /// Whether this metadata field is searchable
    /// </summary>
    public bool? IsSearchable { get; set; }

    /// <summary>
    /// Whether this metadata field is sortable
    /// </summary>
    public bool? IsSortable { get; set; }

    /// <summary>
    /// Sort order for this metadata field
    /// </summary>
    public int? SortOrder { get; set; }

    /// <summary>
    /// Display format for this metadata field
    /// </summary>
    public string? DisplayFormat { get; set; }

    // CONSOLIDATED FIELDS (Priority: Metadata > DataType)
    // These fields exist in both entities and use priority-based consolidation

    /// <summary>
    /// Validation pattern (Metadata takes priority over DataType)
    /// </summary>
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// Minimum length (Metadata takes priority over DataType)
    /// </summary>
    public int? MinLength { get; set; }

    /// <summary>
    /// Maximum length (Metadata takes priority over DataType)
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Minimum value (Metadata takes priority over DataType)
    /// </summary>
    public decimal? MinValue { get; set; }

    /// <summary>
    /// Maximum value (Metadata takes priority over DataType)
    /// </summary>
    public decimal? MaxValue { get; set; }

    /// <summary>
    /// Is required flag (Metadata takes priority over DataType)
    /// </summary>
    public bool? IsRequired { get; set; }

    /// <summary>
    /// Placeholder text (Metadata takes priority over DataType)
    /// </summary>
    public string? Placeholder { get; set; }

    /// <summary>
    /// Maximum selections (Metadata takes priority over DataType)
    /// </summary>
    public int? MaxSelections { get; set; }

    /// <summary>
    /// Allowed file types (Metadata takes priority over DataType)
    /// </summary>
    public string? AllowedFileTypes { get; set; }

    /// <summary>
    /// Maximum file size in bytes (Metadata takes priority over DataType)
    /// </summary>
    public long? MaxFileSizeBytes { get; set; }

    /// <summary>
    /// General error message (Metadata takes priority over DataType)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Display label (Metadata takes priority over DataType)
    /// </summary>
    public string? DisplayLabel { get; set; }

    /// <summary>
    /// Help text (Metadata takes priority over DataType)
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// Field order (Metadata takes priority over DataType)
    /// </summary>
    public int? FieldOrder { get; set; }

    /// <summary>
    /// Is visible flag (Metadata takes priority over DataType)
    /// </summary>
    public bool? IsVisible { get; set; }

    /// <summary>
    /// Is readonly flag (Metadata takes priority over DataType)
    /// </summary>
    public bool? IsReadonly { get; set; }

    /// <summary>
    /// Required error message (Metadata takes priority over DataType)
    /// </summary>
    public string? RequiredErrorMessage { get; set; }

    /// <summary>
    /// Pattern error message (Metadata takes priority over DataType)
    /// </summary>
    public string? PatternErrorMessage { get; set; }

    /// <summary>
    /// Min length error message (Metadata takes priority over DataType)
    /// </summary>
    public string? MinLengthErrorMessage { get; set; }

    /// <summary>
    /// Max length error message (Metadata takes priority over DataType)
    /// </summary>
    public string? MaxLengthErrorMessage { get; set; }

    /// <summary>
    /// Min value error message (Metadata takes priority over DataType)
    /// </summary>
    public string? MinValueErrorMessage { get; set; }

    /// <summary>
    /// Max value error message (Metadata takes priority over DataType)
    /// </summary>
    public string? MaxValueErrorMessage { get; set; }

    /// <summary>
    /// File type error message (Metadata takes priority over DataType)
    /// </summary>
    public string? FileTypeErrorMessage { get; set; }

    /// <summary>
    /// Default options (Metadata takes priority over DataType)
    /// </summary>
    public string? DefaultOptions { get; set; }

    /// <summary>
    /// Allows multiple selections (Metadata takes priority over DataType)
    /// </summary>
    public bool? AllowsMultiple { get; set; }

    /// <summary>
    /// Allows custom options (Metadata takes priority over DataType)
    /// </summary>
    public bool? AllowsCustomOptions { get; set; }

    /// <summary>
    /// Input type (Metadata takes priority over DataType)
    /// </summary>
    public string? InputType { get; set; }

    /// <summary>
    /// Input mask (Metadata takes priority over DataType)
    /// </summary>
    public string? InputMask { get; set; }

    // Link Information (from ProductMetadata/ObjectMetadata)
    public Guid? MetadataLinkId { get; set; }
    public bool? IsUnique { get; set; }
    public bool? MetadataLinkIsActive { get; set; }
    public bool? IsVisibleInList { get; set; }
    public bool? IsVisibleInEdit { get; set; }
    public bool? IsVisibleInCreate { get; set; }
    public bool? IsVisibleInView { get; set; }
    public bool? IsCalculated { get; set; }

    /// <summary>
    /// Static method to create unified metadata from separate Metadata and DataType data
    /// Implements priority-based field consolidation logic
    /// </summary>
    public static UnifiedMetadataDto CreateFromSeparateData(
        // Metadata fields
        Guid? metadataId, string? metadataName, string? metadataDisplayLabel, string? metadataHelpText,
        int? metadataFieldOrder, bool? metadataIsVisible, bool? metadataIsReadonly,
        string? metadataValidationPattern, int? metadataMinLength, int? metadataMaxLength,
        decimal? metadataMinValue, decimal? metadataMaxValue, bool? metadataIsRequired,
        string? metadataPlaceholder, int? metadataMaxSelections, string? metadataAllowedFileTypes,
        long? metadataMaxFileSizeBytes, string? metadataErrorMessage, string? metadataRequiredErrorMessage,
        string? metadataPatternErrorMessage, string? metadataMinLengthErrorMessage, string? metadataMaxLengthErrorMessage,
        string? metadataMinValueErrorMessage, string? metadataMaxValueErrorMessage, string? metadataFileTypeErrorMessage,
        string? metadataDefaultOptions, bool? metadataAllowsMultiple, bool? metadataAllowsCustomOptions,
        string? metadataInputType, string? metadataInputMask, Guid? contextId, Guid? tenantContextId, Guid? objectLookupId,
        bool? metadataIsEditable, bool? metadataIsSearchable, bool? metadataIsSortable, int? metadataSortOrder, string? metadataDisplayFormat,
        
        // DataType fields
        Guid? dataTypeId, string? dataTypeName, string? dataTypeDisplayName, string? dataTypeCategory,
        string? dataTypeUiComponent, string? dataTypeValidationPattern, int? dataTypeMinLength, int? dataTypeMaxLength,
        decimal? dataTypeMinValue, decimal? dataTypeMaxValue, int? dataTypeDecimalPlaces, decimal? dataTypeStepValue,
        bool? dataTypeIsRequired, string? dataTypeInputType, string? dataTypeInputMask, string? dataTypePlaceholder,
        string? dataTypeHtmlAttributes, string? dataTypeDefaultOptions, bool? dataTypeAllowsMultiple,
        bool? dataTypeAllowsCustomOptions, int? dataTypeMaxSelections, string? dataTypeAllowedFileTypes,
        long? dataTypeMaxFileSizeBytes, string? dataTypeRequiredErrorMessage, string? dataTypePatternErrorMessage,
        string? dataTypeMinLengthErrorMessage, string? dataTypeMaxLengthErrorMessage, string? dataTypeMinValueErrorMessage,
        string? dataTypeMaxValueErrorMessage, string? dataTypeFileTypeErrorMessage, string? dataTypeFileSizeErrorMessage,
        string? dataTypeErrorMessage, string? dataTypeDisplayLabel, string? dataTypeHelpText, int? dataTypeFieldOrder,
        bool? dataTypeIsVisible, bool? dataTypeIsReadonly,
        
        // Link fields
        Guid? metadataLinkId = null, bool? isUnique = null, bool? metadataLinkIsActive = null,
        bool? isVisibleInList = null, bool? isVisibleInEdit = null, bool? isVisibleInCreate = null,
        bool? isVisibleInView = null, bool? isCalculated = null)
    {
        return new UnifiedMetadataDto
        {
            // Core Identity
            MetadataId = metadataId,
            DataTypeId = dataTypeId,
            Name = metadataName,

            // DataType-Only Fields
            DataTypeName = dataTypeName,
            DataTypeDisplayName = dataTypeDisplayName,
            Category = dataTypeCategory,
            UiComponent = dataTypeUiComponent,
            DecimalPlaces = dataTypeDecimalPlaces,
            StepValue = dataTypeStepValue,
            HtmlAttributes = dataTypeHtmlAttributes,
            FileSizeErrorMessage = dataTypeFileSizeErrorMessage,

            // Metadata-Only Fields
            ContextId = contextId,
            TenantContextId = tenantContextId,
            ObjectLookupId = objectLookupId,

            // Display and Action Management Fields
            IsEditable = metadataIsEditable,
            IsSearchable = metadataIsSearchable,
            IsSortable = metadataIsSortable,
            SortOrder = metadataSortOrder,
            DisplayFormat = metadataDisplayFormat,

            // PRIORITY-BASED CONSOLIDATION (Metadata > DataType)
            ValidationPattern = !string.IsNullOrEmpty(metadataValidationPattern) ? metadataValidationPattern : dataTypeValidationPattern,
            MinLength = metadataMinLength ?? dataTypeMinLength,
            MaxLength = metadataMaxLength ?? dataTypeMaxLength,
            MinValue = metadataMinValue ?? dataTypeMinValue,
            MaxValue = metadataMaxValue ?? dataTypeMaxValue,
            IsRequired = metadataIsRequired ?? dataTypeIsRequired,
            Placeholder = !string.IsNullOrEmpty(metadataPlaceholder) ? metadataPlaceholder : dataTypePlaceholder,
            MaxSelections = metadataMaxSelections ?? dataTypeMaxSelections,
            AllowedFileTypes = !string.IsNullOrEmpty(metadataAllowedFileTypes) ? metadataAllowedFileTypes : dataTypeAllowedFileTypes,
            MaxFileSizeBytes = metadataMaxFileSizeBytes ?? dataTypeMaxFileSizeBytes,
            ErrorMessage = !string.IsNullOrEmpty(metadataErrorMessage) ? metadataErrorMessage : dataTypeErrorMessage,
            DisplayLabel = !string.IsNullOrEmpty(metadataDisplayLabel) ? metadataDisplayLabel : dataTypeDisplayLabel,
            HelpText = !string.IsNullOrEmpty(metadataHelpText) ? metadataHelpText : dataTypeHelpText,
            FieldOrder = metadataFieldOrder ?? dataTypeFieldOrder,
            IsVisible = metadataIsVisible ?? dataTypeIsVisible,
            IsReadonly = metadataIsReadonly ?? dataTypeIsReadonly,
            RequiredErrorMessage = !string.IsNullOrEmpty(metadataRequiredErrorMessage) ? metadataRequiredErrorMessage : dataTypeRequiredErrorMessage,
            PatternErrorMessage = !string.IsNullOrEmpty(metadataPatternErrorMessage) ? metadataPatternErrorMessage : dataTypePatternErrorMessage,
            MinLengthErrorMessage = !string.IsNullOrEmpty(metadataMinLengthErrorMessage) ? metadataMinLengthErrorMessage : dataTypeMinLengthErrorMessage,
            MaxLengthErrorMessage = !string.IsNullOrEmpty(metadataMaxLengthErrorMessage) ? metadataMaxLengthErrorMessage : dataTypeMaxLengthErrorMessage,
            MinValueErrorMessage = !string.IsNullOrEmpty(metadataMinValueErrorMessage) ? metadataMinValueErrorMessage : dataTypeMinValueErrorMessage,
            MaxValueErrorMessage = !string.IsNullOrEmpty(metadataMaxValueErrorMessage) ? metadataMaxValueErrorMessage : dataTypeMaxValueErrorMessage,
            FileTypeErrorMessage = !string.IsNullOrEmpty(metadataFileTypeErrorMessage) ? metadataFileTypeErrorMessage : dataTypeFileTypeErrorMessage,
            DefaultOptions = !string.IsNullOrEmpty(metadataDefaultOptions) ? metadataDefaultOptions : dataTypeDefaultOptions,
            AllowsMultiple = metadataAllowsMultiple ?? dataTypeAllowsMultiple,
            AllowsCustomOptions = metadataAllowsCustomOptions ?? dataTypeAllowsCustomOptions,
            InputType = !string.IsNullOrEmpty(metadataInputType) ? metadataInputType : dataTypeInputType,
            InputMask = !string.IsNullOrEmpty(metadataInputMask) ? metadataInputMask : dataTypeInputMask,

            // Link Information
            MetadataLinkId = metadataLinkId,
            IsUnique = isUnique,
            MetadataLinkIsActive = metadataLinkIsActive,
            IsVisibleInList = isVisibleInList,
            IsVisibleInEdit = isVisibleInEdit,
            IsVisibleInCreate = isVisibleInCreate,
            IsVisibleInView = isVisibleInView,
            IsCalculated = isCalculated
        };
    }
}

/// <summary>
/// Updated MetadataWithValuesDto that uses the unified metadata DTO
/// </summary>
public class UnifiedMetadataWithValuesDto
{
    /// <summary>
    /// Unified metadata information (consolidates Metadata and DataType)
    /// </summary>
    public UnifiedMetadataDto Metadata { get; set; } = new();

    /// <summary>
    /// Values for this metadata
    /// </summary>
    public List<ValueInfoDto> Values { get; set; } = new();
}
