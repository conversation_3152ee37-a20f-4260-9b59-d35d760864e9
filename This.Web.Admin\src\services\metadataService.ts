// Service for fetching and managing metadata from localStorage
import type { DynamicColumnDefinition } from './objectDataService';
import { contextLookupCacheService } from './contextLookupCacheService';
import { env } from '../config/environment';
import { getCurrentApplicationDetails } from '../shared/testData';

// Simple event emitter implementation for browser
class EventEmitter {
  private listeners: Record<string, Array<(...args: any[]) => void>> = {};

  on(event: string, listener: (...args: any[]) => void): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(listener);
  }

  off(event: string, listener: (...args: any[]) => void): void {
    if (!this.listeners[event]) return;
    const index = this.listeners[event].indexOf(listener);
    if (index > -1) {
      this.listeners[event].splice(index, 1);
    }
  }

  emit(event: string, ...args: any[]): void {
    if (!this.listeners[event]) return;
    for (const listener of [...this.listeners[event]]) {
      try {
        listener(...args);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    }
  }

  setMaxListeners(): void {
    // No-op in this simple implementation
  }
}

// Event types for metadata service
type MetadataEvent = 'UPDATED' | 'ERROR' | 'CACHE_MISS';

// Declare global types for better type safety
declare global {
  interface Window {
    __METADATA_SERVICE_DEBUG__?: boolean;
  }
}

/**
 * Represents metadata for an object stored in localStorage
 */
export interface StoredObjectMetadata {
  objectName: string;
  columnDefinitions: DynamicColumnDefinition[];
  timestamp: string;
  version?: string;
  expiresAt?: string;
  [key: string]: any; // Allow for additional properties
}

/**
 * Standardized response format for metadata operations
 */
export interface ObjectMetadataResponse<T = StoredObjectMetadata> {
  success: boolean;
  metadata: T | null;
  error?: string;
  timestamp?: string;
  fromCache?: boolean;
  version?: string;
}

/**
 * Options for fetching metadata
 */
export interface MetadataFetchOptions {
  forceRefresh?: boolean;
  maxAge?: number; // in milliseconds
  version?: string;
}

/**
 * Error class for metadata-related errors
 */
export class MetadataError extends Error {
  public code: string;
  public details?: Record<string, any>;

  constructor(
    message: string,
    code: string = 'METADATA_ERROR',
    details?: Record<string, any>
  ) {
    super(message);
    this.name = 'MetadataError';
    this.code = code;
    this.details = details;
  }
}

// Remove duplicate MetadataEventHandler type

/**
 * Service for managing object metadata stored in localStorage
 */
export class MetadataService extends EventEmitter {
  private static instance: MetadataService;
  private readonly STORAGE_PREFIX = 'object-metadata-';
  private readonly CACHE_TTL = 1000 * 60 * 30; // 30 minutes

  private cache: Map<string, { data: StoredObjectMetadata; expiresAt: number }> = new Map();
  private refreshInProgress: Map<string, Promise<StoredObjectMetadata | null>> = new Map();

  private constructor() {
    super();
    // Initialize the listeners object
    this.setMaxListeners();
  }

  /**
   * Get the singleton instance of the MetadataService
   */
  public static getInstance(): MetadataService {
    if (!MetadataService.instance) {
      MetadataService.instance = new MetadataService();
    }
    return MetadataService.instance;
  }

  /**
   * Subscribe to metadata events
   */
  public onEvent(handler: (event: MetadataEvent, payload: any) => void): () => void {
    const wrappedHandler = (payload: any) => {
      if (payload?.event) {
        const event = payload.event;
        if (event === 'UPDATED' || event === 'ERROR' || event === 'CACHE_MISS') {
          handler(event, payload);
        }
      }
    };
    
    // Add the wildcard listener
    this.on('*', wrappedHandler);
    
    // Return cleanup function
    return () => {
      this.off('*', wrappedHandler);
    };
  }

  /**
   * Emit a typed event
   */
  private emitEvent(event: MetadataEvent, payload: Record<string, any>): void {
    // Safe emit with error handling
    try {
      const eventPayload = { event, ...payload };
      
      // Emit the wildcard event with full payload
      this.emit('*', eventPayload);
      
      // Emit the specific event
      this.emit(event, payload);
    } catch (error) {
      console.error('Error emitting event:', error);
    }
  }

  /**
   * Get metadata for a specific object with caching and refresh support
   * @param objectName - Name of the object to get metadata for
   * @param options - Options for fetching metadata
   * @returns Promise resolving to the metadata response
   */
  public async getObjectMetadata(
    objectName: string,
    options: MetadataFetchOptions = {}
  ): Promise<ObjectMetadataResponse> {
    const { forceRefresh = false, maxAge, version } = options;
    const cacheKey = this.getCacheKey(objectName);
    const now = Date.now();

    try {
      // Check in-memory cache first
      const cached = this.cache.get(cacheKey);
      if (cached && !forceRefresh && now < cached.expiresAt) {
        if (!version || cached.data.version === version) {
          return {
            success: true,
            metadata: cached.data,
            timestamp: cached.data.timestamp,
            fromCache: true,
            version: cached.data.version
          };
        }
      }

      // Check if a refresh is already in progress
      if (this.refreshInProgress.has(cacheKey)) {
        const metadata = await this.refreshInProgress.get(cacheKey)!;
        if (metadata) {
          return {
            success: true,
            metadata,
            timestamp: metadata.timestamp,
            version: metadata.version
          };
        }
      }

      // Get from localStorage
      const stored = localStorage.getItem(cacheKey);
      if (stored) {
        try {
          const metadata = JSON.parse(stored) as StoredObjectMetadata;
          const isExpired = maxAge 
            ? now - new Date(metadata.timestamp).getTime() > maxAge
            : false;

          if (!forceRefresh && !isExpired && (!version || metadata.version === version)) {
            // Cache in memory
            this.cache.set(cacheKey, {
              data: metadata,
              expiresAt: now + this.CACHE_TTL
            });

            return {
              success: true,
              metadata,
              timestamp: metadata.timestamp,
              fromCache: true,
              version: metadata.version
            };
          }
        } catch (e) {
          console.warn('Error parsing cached metadata, forcing refresh', e);
        }
      }

      // If we're here, we need to refresh the metadata
      const refreshPromise = this.refreshMetadata(objectName);
      this.refreshInProgress.set(cacheKey, refreshPromise);

      try {
        const metadata = await refreshPromise;
        if (!metadata) {
          throw new MetadataError(
            `No metadata found for object '${objectName}'`,
            'METADATA_NOT_FOUND'
          );
        }

        return {
          success: true,
          metadata,
          timestamp: metadata.timestamp,
          version: metadata.version
        };
      } finally {
        this.refreshInProgress.delete(cacheKey);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get metadata';
      const errorCode = error instanceof MetadataError ? error.code : 'METADATA_FETCH_ERROR';
      
      this.emitEvent('ERROR', {
        objectName,
        error: errorMessage,
        code: errorCode
      });

      return {
        success: false,
        metadata: null,
        error: errorMessage,
        timestamp: new Date().toISOString(),
        version
      };
    }
  }

  /**
   * Refresh metadata for an object by fetching from the comprehensive-entity API
   */
  private async refreshMetadata(objectName: string): Promise<StoredObjectMetadata | null> {
    try {
      const applicationDetails = getCurrentApplicationDetails();

      if (!applicationDetails) {
        throw new Error('Application details not available. Cannot build API configuration.');
      }

      const API_URL = `${env.API_BASE_URL}api/comprehensive-entity/${applicationDetails.id}`;
      const API_HEADERS = {
        'accept': 'text/plain',
        'tenant': applicationDetails.tenantId
      };

      const response = await fetch(API_URL, {
        method: 'GET',
        headers: API_HEADERS,
        signal: AbortSignal.timeout(30000) // 30 second timeout
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const jsonData = await response.json();

      if (!jsonData.succeeded) {
        throw new Error(jsonData.message || 'API returned unsuccessful response');
      }

      // Find the specific object in the metadata response
      const objectMetadata = this.findObjectInMetadata(jsonData, objectName);
      if (!objectMetadata) {
        throw new MetadataError(
          `No metadata found for object '${objectName}'`,
          'METADATA_NOT_FOUND'
        );
      }

      // Convert API metadata to column definitions format
      const columnDefinitions = this.convertApiMetadataToColumnDefinitions(objectMetadata.metadata);

      // Extract context and tenant context IDs and fetch bulk lookups
      await this.fetchAndCacheBulkLookups(columnDefinitions);

      const metadata: StoredObjectMetadata = {
        objectName,
        columnDefinitions,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        expiresAt: new Date(Date.now() + this.CACHE_TTL).toISOString(),
        // Store the complete API metadata for direct access
        apiMetadata: objectMetadata.metadata
      };

      // Update cache
      this.cache.set(this.getCacheKey(objectName), {
        data: metadata,
        expiresAt: Date.now() + this.CACHE_TTL
      });

      // Also store in localStorage for persistence
      localStorage.setItem(this.getCacheKey(objectName), JSON.stringify(metadata));

      this.emitEvent('UPDATED', {
        objectName,
        timestamp: metadata.timestamp,
        version: metadata.version
      });

      return metadata;
    } catch (error) {
      this.emitEvent('ERROR', {
        objectName,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: 'METADATA_REFRESH_ERROR'
      });
      throw error;
    }
  }

  /**
   * Fetch and cache bulk context and tenant context lookups
   */
  private async fetchAndCacheBulkLookups(columnDefinitions: DynamicColumnDefinition[]): Promise<void> {
    try {
      // Extract context and tenant context IDs from column definitions
      const { contextIds, tenantContextIds } = contextLookupCacheService.extractContextIds(columnDefinitions);

      // ALWAYS call bulk context APIs when comprehensive-entity API is called (regardless of cache status)
      // This ensures fresh data is always fetched when the main API is called

      // Fetch bulk contexts if we have context IDs (ALWAYS)
      if (contextIds.length > 0) {
        const contextResult = await contextLookupCacheService.fetchAndCacheBulkContexts(contextIds);
      }

      // Fetch bulk tenant contexts if we have tenant context IDs (ALWAYS)
      if (tenantContextIds.length > 0) {
        const tenantContextResult = await contextLookupCacheService.fetchAndCacheBulkTenantContexts(tenantContextIds);
      }

      // Log cache statistics
      const cacheStats = contextLookupCacheService.getCacheStats();
    } catch (error) {
      console.error('Error fetching and caching bulk lookups from comprehensive-entity API:', error);
      // Don't throw error to avoid breaking the main metadata loading flow
    }
  }

  /**
   * Find object metadata in the comprehensive entity response
   */
  private findObjectInMetadata(metadataResponse: any, objectName: string): any | null {
    for (const product of metadataResponse.data.products) {
      // Search directly in product's rootObjects
      const rootObject = this.searchObjectInHierarchy(product.rootObjects || [], objectName);
      if (rootObject) return rootObject;
    }
    return null;
  }

  /**
   * Recursively search for an object in the hierarchy
   */
  private searchObjectInHierarchy(objects: any[], objectName: string): any | null {
    for (const obj of objects) {
      if (obj.name === objectName) {
        return obj;
      }

      // Search in child objects
      if (obj.childObjects && obj.childObjects.length > 0) {
        const found = this.searchObjectInHierarchy(obj.childObjects, objectName);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * Convert API metadata format to DynamicColumnDefinition format (updated for metadata)
   */
  private convertApiMetadataToColumnDefinitions(apiMetadata: any[]): DynamicColumnDefinition[] {
    return apiMetadata.map(metadataItem => {
      // metadataItem is now MetadataWithValues
      const metadata = metadataItem.metadata; // Get Metadata from MetadataWithValues

      return {
        field: metadata.name || metadata.id,
        headerName: metadata.displayLabel || metadata.name,
        type: this.mapCategoryToColumnType(metadata.category),
        sortable: true,
        filter: true,
        width: 150,
        // Properties from metadata (no more separate dataType)
        helpText: metadata.helpText,
        fieldOrder: metadata.fieldOrder,
        isVisible: metadata.isVisible,
        isReadonly: metadata.isReadonly,
        validationPattern: metadata.validationPattern,
        minLength: metadata.minLength,
        maxLength: metadata.maxLength,
        minValue: metadata.minValue,
        maxValue: metadata.maxValue,
        isRequired: metadata.isRequired,
        placeholder: metadata.placeholder,
        defaultOptions: metadata.defaultOptions,
        maxSelections: metadata.maxSelections,
        allowedFileTypes: metadata.allowedFileTypes,
        maxFileSize: metadata.maxFileSize,
        requiredErrorMessage: metadata.requiredErrorMessage,
        patternErrorMessage: metadata.patternErrorMessage,
        minLengthErrorMessage: metadata.minLengthErrorMessage,
        maxLengthErrorMessage: metadata.maxLengthErrorMessage,
        minValueErrorMessage: metadata.minValueErrorMessage,
        maxValueErrorMessage: metadata.maxValueErrorMessage,
        fileTypeErrorMessage: metadata.fileTypeErrorMessage,
        maxFileSizeBytes: metadata.maxFileSizeBytes,
        inputType: metadata.inputType,
        inputMask: metadata.inputMask,
        allowsMultiple: metadata.allowsMultiple,
        allowsCustomOptions: metadata.allowsCustomOptions,
        // FIXED: Use correct field names from backend UnifiedMetadataDto
        contextId: metadata.contextId,
        tenantContextId: metadata.tenantContextId,
        objectLookupId: metadata.objectLookupId,
        uiComponent: metadata.uiComponent,
        // Metadata properties (previously from dataType)
        category: metadata.category,
        decimalPlaces: metadata.decimalPlaces,
        stepValue: metadata.stepValue,
        htmlAttributes: metadata.htmlAttributes,
        // Link information (now part of metadata)
        metadataLink: {
          objectMetaDataId: metadata.metadataLinkId || '',
          isUnique: metadata.isUnique || false,
          isActive: metadata.isVisibleInList ?? true, // Note: API doesn't have metadataLinkIsActive, using isVisibleInList as fallback
          shouldVisibleInList: metadata.isVisibleInList,
          shouldVisibleInEdit: metadata.isVisibleInEdit,
          shouldVisibleInCreate: metadata.isVisibleInCreate,
          shouldVisibleInView: metadata.isVisibleInView,
          isCalculate: metadata.isCalculated
        },
        // Store complete metadata for direct access
        metadata: metadata,
        _apiMetadata: metadataItem
      } as DynamicColumnDefinition;
    });
  }

  /**
   * Map metadata category to column types (updated for metadata)
   */
  private mapCategoryToColumnType(category?: string): 'string' | 'number' | 'date' | 'boolean' {
    if (!category) return 'string';

    switch (category.toLowerCase()) {
      case 'number':
      case 'integer':
      case 'decimal':
      case 'float':
      case 'double':
      case 'int':
        return 'number';
      case 'date':
      case 'datetime':
      case 'timestamp':
      case 'time':
        return 'date';
      case 'boolean':
      case 'bool':
        return 'boolean';
      default:
        return 'string';
    }
  }

  /**
   * Get the cache key for an object
   */
  private getCacheKey(objectName: string): string {
    return `${this.STORAGE_PREFIX}${objectName}`;
  }

  /**
   * Check if metadata exists for a given object
   * @param objectName - Name of the object to check
   * @returns Boolean indicating if metadata exists
   */
  public hasMetadata(objectName: string): boolean {
    return (
      this.cache.has(this.getCacheKey(objectName)) ||
      localStorage.getItem(this.getCacheKey(objectName)) !== null
    );
  }

  /**
   * Get the timestamp of when the metadata was last updated
   * @param objectName - Name of the object
   * @returns ISO timestamp string or null if not found
   */
  public getMetadataTimestamp(objectName: string): string | null {
    try {
      const stored = localStorage.getItem(`${this.STORAGE_PREFIX}${objectName}`);
      if (!stored) return null;
      
      const metadata = JSON.parse(stored) as StoredObjectMetadata;
      return metadata.timestamp || null;
    } catch {
      return null;
    }
  }

  /**
   * Clear cached metadata for a specific object
   * @param objectName - Name of the object to clear metadata for
   * @param clearPersisted - Whether to clear from localStorage (default: true)
   * @returns Boolean indicating success
   */
  public clearMetadata(objectName: string, clearPersisted: boolean = true): boolean {
    const cacheKey = this.getCacheKey(objectName);
    try {
      // Clear in-memory cache
      this.cache.delete(cacheKey);
      
      // Clear from localStorage if requested
      if (clearPersisted) {
        localStorage.removeItem(cacheKey);
      }

      this.emitEvent('UPDATED', {
        objectName,
        cleared: true,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.emitEvent('ERROR', {
        objectName,
        error: errorMessage,
        code: 'CLEAR_METADATA_ERROR'
      });
      return false;
    }
  }

  /**
   * Clear all cached metadata
   * @param clearPersisted - Whether to clear from localStorage (default: true)
   */
  public clearAllMetadata(clearPersisted: boolean = true): void {
    // Clear in-memory cache
    this.cache.clear();
    
    // Clear localStorage if requested
    if (clearPersisted) {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(this.STORAGE_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
    }

    this.emitEvent('UPDATED', {
      allCleared: true,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Preload metadata for multiple objects
   */
  public async preloadMetadata(
    objectNames: string[],
    options: MetadataFetchOptions = {}
  ): Promise<Map<string, StoredObjectMetadata | null>> {
    const results = new Map<string, StoredObjectMetadata | null>();
    
    await Promise.all(
      objectNames.map(async (objectName) => {
        try {
          const result = await this.getObjectMetadata(objectName, options);
          results.set(objectName, result.metadata);
        } catch (error) {
          console.warn(`Failed to preload metadata for ${objectName}:`, error);
          results.set(objectName, null);
        }
      })
    );

    return results;
  }
}

// Create and export singleton instance
export const metadataService = MetadataService.getInstance();

// Export the service instance as default
export default metadataService;
