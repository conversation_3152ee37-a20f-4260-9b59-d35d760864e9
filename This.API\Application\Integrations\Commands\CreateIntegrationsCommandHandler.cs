using Application.Integrations.DTOs;
using Application.Integrations.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Commands;

/// <summary>
/// Create multiple integrations command handler
/// </summary>
public class CreateIntegrationsCommandHandler : IRequestHandler<CreateIntegrationsCommand, Result<List<IntegrationDto>>>
{
    private readonly IRepository<Integration> _integrationRepository;
    private readonly IReadRepository<Product> _productRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public CreateIntegrationsCommandHandler(
        IRepository<Integration> integrationRepository,
        IReadRepository<Product> productRepository)
    {
        _integrationRepository = integrationRepository;
        _productRepository = productRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<List<IntegrationDto>>> Handle(CreateIntegrationsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (!request.Integrations.Any())
            {
                return Result<List<IntegrationDto>>.Failure("No integrations provided.");
            }

            var createdIntegrations = new List<Integration>();
            var errors = new List<string>();

            // Validate all products exist first
            var productIds = request.Integrations.Select(i => i.ProductId).Distinct().ToList();
            var products = new Dictionary<Guid, Product>();
            
            foreach (var productId in productIds)
            {
                var product = await _productRepository.GetByIdAsync(productId, cancellationToken);
                if (product == null)
                {
                    errors.Add($"Product with ID {productId} not found.");
                }
                else
                {
                    products[productId] = product;
                }
            }

            if (errors.Any())
            {
                return Result<List<IntegrationDto>>.Failure(string.Join("; ", errors));
            }

            // Process each integration
            foreach (var integrationRequest in request.Integrations)
            {
                try
                {
                    // Check if integration with same name already exists for this product
                    var existingIntegrationSpec = new IntegrationByNameAndProductSpec(integrationRequest.Name, integrationRequest.ProductId);
                    var existingIntegration = await _integrationRepository.GetBySpecAsync(existingIntegrationSpec, cancellationToken);

                    if (existingIntegration != null)
                    {
                        errors.Add($"Integration '{integrationRequest.Name}' already exists for product {integrationRequest.ProductId}.");
                        continue;
                    }

                    // Create new integration
                    var integration = new Integration
                    {
                        ProductId = integrationRequest.ProductId,
                        Name = integrationRequest.Name,
                        AuthType = integrationRequest.AuthType,
                        AuthConfig = integrationRequest.AuthConfig,
                        IsActive = integrationRequest.IsActive,
                        SyncFrequency = integrationRequest.SyncFrequency
                    };

                    createdIntegrations.Add(integration);
                }
                catch (Exception ex)
                {
                    errors.Add($"Failed to process integration '{integrationRequest.Name}': {ex.Message}");
                }
            }

            if (!createdIntegrations.Any())
            {
                return Result<List<IntegrationDto>>.Failure($"No integrations were created. Errors: {string.Join("; ", errors)}");
            }

            // Bulk insert all valid integrations
            var savedIntegrations = await _integrationRepository.AddRangeAsync(createdIntegrations, cancellationToken);

            var integrationDtos = savedIntegrations.Adapt<List<IntegrationDto>>();

            var result = Result<List<IntegrationDto>>.Success(integrationDtos);
            if (errors.Any())
            {
                var warningMessage = string.Join("; ", errors.Select(e => $"Warning: {e}"));
                result.Message = warningMessage;
            }

            return result;
        }
        catch (Exception ex)
        {
            return Result<List<IntegrationDto>>.Failure($"Failed to create integrations: {ex.Message}");
        }
    }
}
