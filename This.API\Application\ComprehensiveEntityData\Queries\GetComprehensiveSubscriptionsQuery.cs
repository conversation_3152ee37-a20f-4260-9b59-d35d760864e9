using Application.ComprehensiveEntityData.DTOs;
using MediatR;
using Shared.Common.Response;

namespace Application.ComprehensiveEntityData.Queries;

/// <summary>
/// Query to get comprehensive subscription data across all tenants
/// </summary>
public class GetComprehensiveSubscriptionsQuery : IRequest<Result<ComprehensiveSubscriptionResponseDto>>
{
    /// <summary>
    /// Filter by specific tenant ID (optional)
    /// </summary>
    public string? TenantId { get; set; }

    /// <summary>
    /// Filter by product ID (optional)
    /// </summary>
    public Guid? ProductId { get; set; }

    /// <summary>
    /// Filter by subscription status (optional)
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Filter by subscription type (optional)
    /// </summary>
    public string? SubscriptionType { get; set; }

    /// <summary>
    /// Filter by active status (optional)
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter by expired status (optional)
    /// </summary>
    public bool? IsExpired { get; set; }

    /// <summary>
    /// Filter by pricing tier (optional)
    /// </summary>
    public string? PricingTier { get; set; }

    /// <summary>
    /// Search term for subscription type, product name, or tenant name (optional)
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by start date from (optional)
    /// </summary>
    public DateTime? StartDateFrom { get; set; }

    /// <summary>
    /// Filter by start date to (optional)
    /// </summary>
    public DateTime? StartDateTo { get; set; }

    /// <summary>
    /// Filter by end date from (optional)
    /// </summary>
    public DateTime? EndDateFrom { get; set; }

    /// <summary>
    /// Filter by end date to (optional)
    /// </summary>
    public DateTime? EndDateTo { get; set; }

    /// <summary>
    /// Filter subscriptions expiring within specified days (optional)
    /// </summary>
    public int? ExpiringWithinDays { get; set; }

    /// <summary>
    /// Page number for pagination (default: 1)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Page size for pagination (default: 50, max: 1000)
    /// </summary>
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// Order by field (default: CreatedAt)
    /// Valid values: CreatedAt, StartDate, EndDate, SubscriptionType, Status, TenantId, ProductName
    /// </summary>
    public string OrderBy { get; set; } = "CreatedAt";

    /// <summary>
    /// Order direction (default: desc)
    /// Valid values: asc, desc
    /// </summary>
    public string OrderDirection { get; set; } = "desc";

    /// <summary>
    /// Include summary statistics (default: true)
    /// </summary>
    public bool IncludeSummary { get; set; } = true;
}
