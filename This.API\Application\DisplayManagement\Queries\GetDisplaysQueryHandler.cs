using Abstraction.Database.Repositories;
using Application.DisplayManagement.DTOs;
using Application.DisplayManagement.Specifications;
using Domain.Entities;
using Mapster;
using MediatR;
using Shared.Common.Response;

namespace Application.DisplayManagement.Queries;

/// <summary>
/// Get displays query handler
/// </summary>
public class GetDisplaysQueryHandler : IRequestHandler<GetDisplaysQuery, Result<List<DisplayDto>>>
{
    private readonly IRepository<Display> _repository;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetDisplaysQueryHandler(IRepository<Display> repository)
    {
        _repository = repository;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<List<DisplayDto>>> Handle(GetDisplaysQuery request, CancellationToken cancellationToken)
    {
        var spec = new DisplaysWithFilterSpec(
            searchTerm: request.SearchTerm,
            isActive: request.IsActive,
            orderBy: request.OrderBy);

        var displays = await _repository.ListAsync(spec, cancellationToken);

        var dtos = displays.Adapt<List<DisplayDto>>();

        return Result<List<DisplayDto>>.Success(dtos);
    }
}
