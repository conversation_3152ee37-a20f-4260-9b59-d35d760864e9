using Application.Context.DTOs;
using Application.Context.Specifications;
using Abstraction.Database.Repositories;
using Domain.Entities;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Queries.GetTenantContextWithLookups;

/// <summary>
/// Handler for GetTenantContextWithLookupsQuery
/// </summary>
public class GetTenantContextWithLookupsQueryHandler : IRequestHandler<GetTenantContextWithLookupsQuery, Result<TenantContextWithLookupsDto>>
{
    private readonly IRepository<TenantContext> _tenantContextRepository;
    private readonly IRepository<TenantLookup> _tenantLookupRepository;
    private readonly ILogger<GetTenantContextWithLookupsQueryHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public GetTenantContextWithLookupsQueryHandler(
        IRepository<TenantContext> tenantContextRepository,
        IRepository<TenantLookup> tenantLookupRepository,
        ILogger<GetTenantContextWithLookupsQueryHandler> logger)
    {
        _tenantContextRepository = tenantContextRepository;
        _tenantLookupRepository = tenantLookupRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the query
    /// </summary>
    public async Task<Result<TenantContextWithLookupsDto>> Handle(GetTenantContextWithLookupsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting tenant context with lookups for TenantContextId: {TenantContextId}, IncludeInactiveLookups: {IncludeInactiveLookups}",
                request.TenantContextId, request.IncludeInactiveLookups);

            // Get the tenant context - tenant filtering is handled automatically by the repository
            var tenantContext = await _tenantContextRepository.GetByIdAsync(request.TenantContextId, cancellationToken);
            if (tenantContext == null)
            {
                return Result<TenantContextWithLookupsDto>.Failure($"TenantContext with ID {request.TenantContextId} not found");
            }

            // Check if tenant context is deleted
            if (tenantContext.IsDeleted)
            {
                return Result<TenantContextWithLookupsDto>.Failure($"TenantContext with ID {request.TenantContextId} has been deleted");
            }

            // Create specification for tenant lookups
            var tenantLookupSpec = new TenantLookupsByTenantContextIdSpec(
                tenantContextId: request.TenantContextId,
                includeInactive: request.IncludeInactiveLookups);

            // Get associated tenant lookups - tenant filtering is handled automatically by the repository
            var tenantLookups = await _tenantLookupRepository.ListAsync(tenantLookupSpec, cancellationToken);

            // Map to DTOs
            var tenantContextDto = tenantContext.Adapt<TenantContextDto>();
            var tenantLookupDtos = tenantLookups.Adapt<List<TenantLookupDto>>();

            var result = new TenantContextWithLookupsDto
            {
                TenantContext = tenantContextDto,
                TenantLookups = tenantLookupDtos
            };

            _logger.LogInformation("Successfully retrieved tenant context with {TenantLookupCount} tenant lookups for TenantContextId: {TenantContextId}",
                tenantLookupDtos.Count, request.TenantContextId);

            return Result<TenantContextWithLookupsDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting tenant context with lookups for TenantContextId: {TenantContextId}", request.TenantContextId);
            return Result<TenantContextWithLookupsDto>.Failure($"Error retrieving tenant context with lookups: {ex.Message}");
        }
    }
}
