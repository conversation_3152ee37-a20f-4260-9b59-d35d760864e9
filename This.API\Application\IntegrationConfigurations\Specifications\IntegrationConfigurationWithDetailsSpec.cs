using Ardalis.Specification;
using Domain.Entities;

namespace Application.IntegrationConfigurations.Specifications;

/// <summary>
/// Specification to get integration configuration with related entity details
/// </summary>
public class IntegrationConfigurationWithDetailsSpec : Specification<IntegrationConfiguration>
{
    /// <summary>
    /// Constructor
    /// </summary>
    public IntegrationConfigurationWithDetailsSpec(Guid id)
    {
        Query.Where(ic => ic.Id == id)
             .Include(ic => ic.Integration)
             .Include(ic => ic.IntegrationApi)
             .Include(ic => ic.Object);
    }
}
