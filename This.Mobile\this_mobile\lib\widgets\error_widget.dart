import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../utils/constants.dart';

/// Custom error widget with retry functionality
class CustomErrorWidget extends StatelessWidget {
  final String? title;
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;
  final Color? iconColor;

  const CustomErrorWidget({
    super.key,
    this.title,
    required this.message,
    this.onRetry,
    this.icon,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.error_outline,
              size: AppConstants.iconSizeXXL,
              color: iconColor ?? AppColors.error,
            ),
            const SizedBox(height: AppConstants.spacingM),
            if (title != null) ...[
              Text(
                title!,
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.error,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.spacingS),
            ],
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: AppConstants.spacingL),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(LucideIcons.refreshCw),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Error widget for navigation loading failures
class NavigationErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const NavigationErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return CustomErrorWidget(
      title: 'Navigation Error',
      message: message,
      onRetry: onRetry,
      icon: LucideIcons.navigation,
      iconColor: AppColors.warning,
    );
  }
}

/// Error widget for data loading failures
class DataErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const DataErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingL),
        child: CustomErrorWidget(
          title: 'Data Loading Error',
          message: message,
          onRetry: onRetry,
          icon: LucideIcons.database,
          iconColor: AppColors.error,
        ),
      ),
    );
  }
}

/// Network error widget
class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return CustomErrorWidget(
      title: 'Network Error',
      message: 'Please check your internet connection and try again.',
      onRetry: onRetry,
      icon: LucideIcons.wifiOff,
      iconColor: AppColors.warning,
    );
  }
}

/// Empty state widget
class EmptyStateWidget extends StatelessWidget {
  final String title;
  final String message;
  final IconData? icon;
  final Widget? action;

  const EmptyStateWidget({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? LucideIcons.inbox,
              size: AppConstants.iconSizeXXL,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: AppConstants.spacingM),
            Text(
              title,
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.spacingS),
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
            if (action != null) ...[
              const SizedBox(height: AppConstants.spacingL),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}

/// Welcome state widget
class WelcomeStateWidget extends StatelessWidget {
  const WelcomeStateWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'Welcome to Dynamic Inventory',
      message: 'Select an item from the navigation menu to get started',
      icon: LucideIcons.package,
    );
  }
}

/// No data available widget
class NoDataWidget extends StatelessWidget {
  final String itemName;

  const NoDataWidget({
    super.key,
    required this.itemName,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingL),
        child: EmptyStateWidget(
          title: 'No Data Available for $itemName',
          message: 'This section doesn\'t contain any data yet.',
          icon: Icons.inbox,
        ),
      ),
    );
  }
}

/// Search no results widget
class SearchNoResultsWidget extends StatelessWidget {
  final String searchQuery;
  final VoidCallback? onClearSearch;

  const SearchNoResultsWidget({
    super.key,
    required this.searchQuery,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'No Results Found',
      message: 'No items match your search for "$searchQuery"',
      icon: LucideIcons.search,
      action: onClearSearch != null
          ? TextButton.icon(
              onPressed: onClearSearch,
              icon: const Icon(LucideIcons.x),
              label: const Text('Clear Search'),
            )
          : null,
    );
  }
}

/// Generic error boundary widget
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace? stackTrace)? errorBuilder;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!, _stackTrace);
      }
      return CustomErrorWidget(
        title: 'Something went wrong',
        message: _error.toString(),
        onRetry: () {
          setState(() {
            _error = null;
            _stackTrace = null;
          });
        },
      );
    }

    return widget.child;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    FlutterError.onError = (FlutterErrorDetails details) {
      setState(() {
        _error = details.exception;
        _stackTrace = details.stack;
      });
    };
  }
}

/// Timeout error widget
class TimeoutErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const TimeoutErrorWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return CustomErrorWidget(
      title: 'Request Timeout',
      message: 'The request took too long to complete. Please try again.',
      onRetry: onRetry,
      icon: LucideIcons.clock,
      iconColor: AppColors.warning,
    );
  }
}

/// Server error widget
class ServerErrorWidget extends StatelessWidget {
  final int? statusCode;
  final VoidCallback? onRetry;

  const ServerErrorWidget({
    super.key,
    this.statusCode,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    String message = 'A server error occurred. Please try again later.';
    if (statusCode != null) {
      message = 'Server error ($statusCode). Please try again later.';
    }

    return CustomErrorWidget(
      title: 'Server Error',
      message: message,
      onRetry: onRetry,
      icon: LucideIcons.server,
      iconColor: AppColors.error,
    );
  }
}
