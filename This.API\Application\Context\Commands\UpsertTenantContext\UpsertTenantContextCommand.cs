using MediatR;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertTenantContext;

/// <summary>
/// Command to upsert a single tenant context
/// </summary>
public class UpsertTenantContextCommand : IRequest<Result<Guid>>
{
    /// <summary>
    /// TenantContext ID (optional for insert)
    /// </summary>
    public Guid? Id { get; set; }

    /// <summary>
    /// Name of the tenant context
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the tenant context
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Category for grouping tenant contexts
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Whether the tenant context is active
    /// </summary>
    public bool IsActive { get; set; } = true;
}
