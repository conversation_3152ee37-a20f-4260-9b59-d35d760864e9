import { type TreeNode, type NodeType } from '../components/TreeView/TreeView';
import { generateGuid } from './metadataUtils';

// Helper function to clean metadata by removing internal IDs and any other unwanted fields
const cleanMetadata = (metadata: any) => {
  if (!Array.isArray(metadata)) return [];
  return metadata.map(item => {
    // Remove _internalId and id fields, keep only the essential fields
    const cleanItem: any = {
      name: item.name,
      type: item.type,
      description: item.description,
      required: item.required,
      isActive: item.isActive
    };

    // Only add defaultValue if it exists
    if (item.defaultValue !== undefined && item.defaultValue !== null && item.defaultValue !== '') {
      cleanItem.defaultValue = item.defaultValue;
    }

    return cleanItem;
  });
};

// Recursive function to process nodes and their children
const processNode = (node: any): any => {
  console.log('🔍 Processing node:', node.name, node.type, 'displays:', node.displays);

  const result: any = {
    name: node.name,
    type: node.type,
    metadata: cleanMetadata(node.metadata || [])
  };

  // Add displays configuration for object nodes
  if (node.type === 'object') {
    let selectedDisplays: any[] = [];

    if (node.displays && node.displays.length > 0) {
      // Filter only selected displays
      selectedDisplays = node.displays.filter((display: any) => display.isSelected);
    } else {
      // If no displays configured, add default displays with their actions
      selectedDisplays = [
        {
          name: 'List',
          displayName: 'List View',
          isDefault: true,
          routeTemplate: '/objects/list',
          icon: 'list',
          sortOrder: 1,
          actions: [
            {
              name: 'Create',
              description: 'Add new item',
              navigationTarget: '/objects/create',
              icon: 'plus',
              buttonStyle: 'Primary'
            },
            {
              name: 'View',
              description: 'View item',
              navigationTarget: '/objects/view/{id}',
              icon: 'eye',
              buttonStyle: 'Secondary'
            }
          ]
        },
        {
          name: 'Create',
          displayName: 'Create Form',
          isDefault: false,
          routeTemplate: '/objects/create',
          icon: 'plus',
          sortOrder: 2,
          actions: [
            {
              name: 'Save',
              description: 'Save item',
              endpointTemplate: '/api/objects',
              icon: 'save',
              buttonStyle: 'Primary',
              successMessage: 'Item saved successfully',
              errorMessage: 'Failed to save item'
            },
            {
              name: 'Cancel',
              description: 'Cancel creation',
              navigationTarget: '/objects',
              icon: 'x',
              buttonStyle: 'Secondary'
            }
          ]
        },
        {
          name: 'Update',
          displayName: 'Update Form',
          isDefault: false,
          routeTemplate: '/objects/update/{id}',
          icon: 'edit',
          sortOrder: 3,
          actions: [
            {
              name: 'Save',
              description: 'Update item',
              endpointTemplate: '/api/objects/{id}',
              icon: 'save',
              buttonStyle: 'Primary',
              successMessage: 'Item updated successfully',
              errorMessage: 'Failed to update item'
            },
            {
              name: 'Cancel',
              description: 'Cancel update',
              navigationTarget: '/objects',
              icon: 'x',
              buttonStyle: 'Secondary'
            }
          ]
        },
        {
          name: 'View',
          displayName: 'Detail View',
          isDefault: false,
          routeTemplate: '/objects/view/{id}',
          icon: 'eye',
          sortOrder: 4,
          actions: [
            {
              name: 'Edit',
              description: 'Edit item',
              navigationTarget: '/objects/edit/{id}',
              icon: 'edit',
              buttonStyle: 'Primary'
            }
          ]
        }
      ];
    }

    // Add displays to result with actions nested under each display
    if (selectedDisplays.length > 0) {
      result.displays = selectedDisplays.map((display: any) => {
        const displayResult: any = {
          name: display.name,
          displayName: display.displayName,
          isDefault: display.isDefault,
          routeTemplate: display.routeTemplate || `/objects/${display.name.toLowerCase()}`,
          icon: display.icon || display.name.toLowerCase(),
          sortOrder: display.sortOrder || 1
        };

        // Add actions nested under this display
        if (display.actions && display.actions.length > 0) {
          const selectedActions = display.actions.filter((action: any) => action.isSelected !== false);
          if (selectedActions.length > 0) {
            displayResult.actions = selectedActions.map((action: any) => {
              const actionResult: any = {
                name: action.name,
                icon: action.icon,
                buttonStyle: action.buttonStyle
              };

              // Add description if available
              if (action.description) {
                actionResult.description = action.description;
              }

              // Add endpoint or navigation target based on action type
              if (action.type === 'API' && action.endpoint) {
                actionResult.endpointTemplate = action.endpoint;
              } else if (action.type === 'Navigation' && action.navigationTarget) {
                actionResult.navigationTarget = action.navigationTarget;
              } else if (action.endpointTemplate) {
                actionResult.endpointTemplate = action.endpointTemplate;
              } else if (action.navigationTarget) {
                actionResult.navigationTarget = action.navigationTarget;
              }

              // Add optional messages if they exist
              if (action.confirmationMessage) {
                actionResult.confirmationMessage = action.confirmationMessage;
              }
              if (action.successMessage) {
                actionResult.successMessage = action.successMessage;
              }
              if (action.errorMessage) {
                actionResult.errorMessage = action.errorMessage;
              }

              return actionResult;
            });
          }
        }

        return displayResult;
      });
    }
  }

  // Process children recursively if they exist
  if (node.children && node.children.length > 0) {
    // Filter and process child objects
    const childObjects = node.children
      .filter((child: any) => child.type === 'object')
      .map((child: any) => processNode(child));

    // Only add objects array if there are child objects
    if (childObjects.length > 0) {
      result.objects = childObjects;
    }
  }

  return result;
};

// Convert tree data to template JSON format
export const convertTreeDataToTemplate = (treeData: TreeNode[]) => {
  console.log('🔍 Converting tree data to template:', treeData);

  const templateJson = {
    products: treeData.map(product => {
      const processed = processNode(product);
      console.log('🔍 Processed product:', processed);
      return processed;
    })
  };

  console.log('🔍 Final template JSON:', templateJson);
  return templateJson;
};

// Helper function to add internal IDs to metadata
const addInternalIds = (metadata: any[]) => {
  return metadata.map(item => ({
    ...item,
    _internalId: generateGuid()
  }));
};

// Convert template product to TreeNode
export const convertTemplateToTreeNode = (templateProduct: any): TreeNode => {
  const node: TreeNode = {
    id: generateGuid(), // Generate new ID for UI management
    name: templateProduct.name,
    type: templateProduct.type as NodeType,
    children: [],
    isOpen: true,
    metadata: addInternalIds(templateProduct.metadata || [])
  };

  // Add displays and actions configuration if they exist
  if (templateProduct.displays) {
    (node as any).displays = templateProduct.displays.map((display: any) => ({
      ...display,
      id: generateGuid(), // Add UI ID for display management
      isSelected: true, // Mark as selected when loading from template
      actions: (display.actions || []).map((action: any) => ({
        ...action,
        id: generateGuid(), // Add UI ID for action management
        isSelected: true, // Mark as selected when loading from template
        type: action.endpointTemplate ? 'API' : 'Navigation', // Determine type from data
        endpoint: action.endpointTemplate, // Map endpointTemplate to endpoint for UI
        navigationTarget: action.navigationTarget
      }))
    }));
  }

  // Convert objects to children recursively
  if (templateProduct.objects) {
    node.children = templateProduct.objects.map((obj: any) => {
      const childNode: TreeNode = {
        id: generateGuid(), // Generate new ID for UI management
        name: obj.name,
        type: obj.type as NodeType,
        children: [],
        isOpen: true,
        metadata: addInternalIds(obj.metadata || [])
      };

      // Add displays and actions configuration for child objects
      if (obj.displays) {
        (childNode as any).displays = obj.displays.map((display: any) => ({
          ...display,
          id: generateGuid(),
          isSelected: true, // Mark as selected when loading from template
          actions: (display.actions || []).map((action: any) => ({
            ...action,
            id: generateGuid(),
            isSelected: true, // Mark as selected when loading from template
            type: action.endpointTemplate ? 'API' : 'Navigation', // Determine type from data
            endpoint: action.endpointTemplate, // Map endpointTemplate to endpoint for UI
            navigationTarget: action.navigationTarget
          }))
        }));
      }

      // Recursively process nested objects
      if (obj.objects && obj.objects.length > 0) {
        childNode.children = obj.objects.map((nestedObj: any) => convertTemplateToTreeNode(nestedObj));
      }

      return childNode;
    });
  }

  // Convert roles to children
  if (templateProduct.roles) {
    const roleChildren = templateProduct.roles.map((role: any) => ({
      id: generateGuid(), // Generate new ID for UI management
      name: role.name,
      type: role.type as NodeType,
      children: [],
      isOpen: true,
      metadata: addInternalIds(role.metadata || [])
    }));
    node.children = [...(node.children || []), ...roleChildren];
  }

  return node;
};
