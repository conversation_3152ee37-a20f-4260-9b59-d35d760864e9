/**
 * Subscription Utilities
 * Helper functions for subscription data processing and formatting
 */

import type { SubscriptionDto } from '../api/subscriptionApiService';

export class SubscriptionUtils {
  /**
   * Calculate user count from metadata count
   */
  static calculateUserCount(metadataCount: number): number {
    // Simple calculation - can be made more sophisticated based on business rules
    return Math.max(1, Math.floor(metadataCount / 5));
  }

  /**
   * Format price from pricing tier
   */
  static formatPrice(pricingTier: string): string {
    const priceMap: Record<string, string> = {
      'starter': '$29/month',
      'standard': '$49/month',
      'professional': '$99/month',
      'enterprise': '$199/month',
      'basic': '$19/month',
      'premium': '$79/month',
    };

    return priceMap[pricingTier.toLowerCase()] || '$0/month';
  }

  /**
   * Get status badge variant for Bootstrap
   */
  static getStatusBadgeVariant(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
      case 'suspended':
        return 'warning';
      case 'expired':
      case 'cancelled':
        return 'danger';
      case 'pending':
        return 'info';
      default:
        return 'secondary';
    }
  }

  /**
   * Get subscription type badge variant for Bootstrap
   */
  static getTypeBadgeVariant(type: string): string {
    switch (type.toLowerCase()) {
      case 'enterprise':
        return 'dark';
      case 'premium':
      case 'professional':
        return 'primary';
      case 'standard':
        return 'info';
      case 'basic':
      case 'starter':
        return 'secondary';
      default:
        return 'light';
    }
  }

  /**
   * Check if subscription is expiring soon
   */
  static isExpiringSoon(subscription: SubscriptionDto, days: number = 30): boolean {
    if (!subscription.endDate || subscription.daysUntilExpiration === null) {
      return false;
    }
    return subscription.daysUntilExpiration <= days && subscription.daysUntilExpiration > 0;
  }

  /**
   * Check if subscription is expired
   */
  static isExpired(subscription: SubscriptionDto): boolean {
    return subscription.isExpired || (subscription.daysUntilExpiration !== null && subscription.daysUntilExpiration <= 0);
  }

  /**
   * Get subscription display name
   */
  static getDisplayName(subscription: SubscriptionDto): string {
    return subscription.productName || `Subscription ${subscription.id.substring(0, 8)}`;
  }

  /**
   * Format subscription period for display
   */
  static formatPeriod(subscription: SubscriptionDto): string {
    const startDate = new Date(subscription.startDate).toLocaleDateString();
    const endDate = subscription.endDate 
      ? new Date(subscription.endDate).toLocaleDateString() 
      : 'No end date';
    
    return `${startDate} - ${endDate}`;
  }

  /**
   * Get subscription status text with additional context
   */
  static getStatusText(subscription: SubscriptionDto): string {
    if (this.isExpired(subscription)) {
      return 'Expired';
    }
    
    if (this.isExpiringSoon(subscription)) {
      return `${subscription.status} (${subscription.daysUntilExpiration} days left)`;
    }
    
    return subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1);
  }

  /**
   * Sort subscriptions by priority (expired, expiring, active, inactive)
   */
  static sortByPriority(subscriptions: SubscriptionDto[]): SubscriptionDto[] {
    return subscriptions.sort((a, b) => {
      // Priority order: expired, expiring soon, active, inactive
      const getPriority = (sub: SubscriptionDto): number => {
        if (this.isExpired(sub)) return 1;
        if (this.isExpiringSoon(sub)) return 2;
        if (sub.status === 'active') return 3;
        return 4;
      };

      const priorityA = getPriority(a);
      const priorityB = getPriority(b);

      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // Secondary sort by creation date (newest first)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }

  /**
   * Filter subscriptions by status
   */
  static filterByStatus(subscriptions: SubscriptionDto[], status: string): SubscriptionDto[] {
    if (status === 'all') return subscriptions;
    
    return subscriptions.filter(sub => {
      switch (status) {
        case 'expired':
          return this.isExpired(sub);
        case 'expiring':
          return this.isExpiringSoon(sub);
        case 'active':
          return sub.status === 'active' && !this.isExpired(sub);
        case 'inactive':
          return sub.status === 'inactive';
        default:
          return sub.status === status;
      }
    });
  }

  /**
   * Group subscriptions by tenant
   */
  static groupByTenant(subscriptions: SubscriptionDto[]): Record<string, SubscriptionDto[]> {
    return subscriptions.reduce((groups, subscription) => {
      const tenantName = subscription.tenantName || subscription.tenantId;
      if (!groups[tenantName]) {
        groups[tenantName] = [];
      }
      groups[tenantName].push(subscription);
      return groups;
    }, {} as Record<string, SubscriptionDto[]>);
  }

  /**
   * Group subscriptions by product
   */
  static groupByProduct(subscriptions: SubscriptionDto[]): Record<string, SubscriptionDto[]> {
    return subscriptions.reduce((groups, subscription) => {
      const productName = subscription.productName || subscription.productId;
      if (!groups[productName]) {
        groups[productName] = [];
      }
      groups[productName].push(subscription);
      return groups;
    }, {} as Record<string, SubscriptionDto[]>);
  }

  /**
   * Calculate subscription statistics
   */
  static calculateStats(subscriptions: SubscriptionDto[]) {
    const total = subscriptions.length;
    const active = subscriptions.filter(s => s.status === 'active' && !this.isExpired(s)).length;
    const expired = subscriptions.filter(s => this.isExpired(s)).length;
    const expiring = subscriptions.filter(s => this.isExpiringSoon(s)).length;
    const inactive = subscriptions.filter(s => s.status === 'inactive').length;

    const byType = subscriptions.reduce((acc, sub) => {
      acc[sub.subscriptionType] = (acc[sub.subscriptionType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byTenant = subscriptions.reduce((acc, sub) => {
      const tenantName = sub.tenantName || sub.tenantId;
      acc[tenantName] = (acc[tenantName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      active,
      expired,
      expiring,
      inactive,
      byType,
      byTenant,
      uniqueTenants: Object.keys(byTenant).length,
      uniqueProducts: new Set(subscriptions.map(s => s.productId)).size,
    };
  }

  /**
   * Validate subscription data
   */
  static validateSubscription(subscription: Partial<SubscriptionDto>): string[] {
    const errors: string[] = [];

    if (!subscription.productId) {
      errors.push('Product ID is required');
    }

    if (!subscription.subscriptionType) {
      errors.push('Subscription type is required');
    }

    if (!subscription.status) {
      errors.push('Status is required');
    }

    if (!subscription.startDate) {
      errors.push('Start date is required');
    }

    if (subscription.startDate && subscription.endDate) {
      const startDate = new Date(subscription.startDate);
      const endDate = new Date(subscription.endDate);
      
      if (endDate <= startDate) {
        errors.push('End date must be after start date');
      }
    }

    return errors;
  }

  /**
   * Convert legacy subscription format to new format
   */
  static convertLegacyFormat(legacySubscription: any): SubscriptionDto {
    return {
      id: legacySubscription.id,
      tenantId: legacySubscription.tenantId || '',
      tenantName: legacySubscription.tenantName || null,
      productId: legacySubscription.productId || '',
      productName: legacySubscription.productName || legacySubscription.name || null,
      subscriptionType: legacySubscription.subscriptionType || legacySubscription.type || '',
      status: legacySubscription.status || '',
      startDate: legacySubscription.startDate || '',
      endDate: legacySubscription.endDate || null,
      autoRenew: legacySubscription.autoRenew || false,
      pricingTier: legacySubscription.pricingTier || null,
      version: legacySubscription.version || '1.0.0',
      templateJson: legacySubscription.templateJson || '{}',
      isActive: legacySubscription.isActive !== undefined ? legacySubscription.isActive : true,
      metadataCount: legacySubscription.metadataCount || 0,
      createdAt: legacySubscription.createdAt || new Date().toISOString(),
      createdBy: legacySubscription.createdBy || '',
      modifiedAt: legacySubscription.modifiedAt || null,
      modifiedBy: legacySubscription.modifiedBy || null,
      isExpired: legacySubscription.isExpired || false,
      daysUntilExpiration: legacySubscription.daysUntilExpiration || null,
    };
  }
}
