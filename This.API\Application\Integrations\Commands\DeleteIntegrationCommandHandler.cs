using Abstraction.Database.Repositories;
using Domain.Entities;
using MediatR;
using Shared.Common.Response;

namespace Application.Integrations.Commands;

/// <summary>
/// Delete integration command handler
/// </summary>
public class DeleteIntegrationCommandHandler : IRequestHandler<DeleteIntegrationCommand, Result<bool>>
{
    private readonly IRepository<Integration> _integrationRepository;

    /// <summary>
    /// Constructor
    /// </summary>
    public DeleteIntegrationCommandHandler(IRepository<Integration> integrationRepository)
    {
        _integrationRepository = integrationRepository;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<bool>> Handle(DeleteIntegrationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Get existing integration
            var integration = await _integrationRepository.GetByIdAsync(request.Id, cancellationToken);
            if (integration == null)
            {
                return Result<bool>.Failure("Integration not found.");
            }

            // Soft delete the integration
            await _integrationRepository.DeleteAsync(integration, cancellationToken);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return Result<bool>.Failure($"Failed to delete integration: {ex.Message}");
        }
    }
}
