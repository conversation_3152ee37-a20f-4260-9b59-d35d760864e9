/**
 * API Services Index
 * Centralized exports for all API services
 */

// Import services for local use
import { subscriptionApiService } from './subscriptionApiService';
import { templateApiService } from './templateApiService';
import { tenantApiService } from './tenantApiService';
import { subscriptionCreationService } from './subscriptionCreationService';
import { dualApiService } from './dualApiService';

// Export API service classes
export { SubscriptionApiService, subscriptionApiService } from './subscriptionApiService';
export { TemplateApiService, templateApiService } from './templateApiService';
export { TenantApiService, tenantApiService } from './tenantApiService';
export { SubscriptionCreationService, subscriptionCreationService } from './subscriptionCreationService';

// Export dual API service
export { DualApiService, dualApiService, apiOperations } from './dualApiService';

// Export types from subscription service
export type {
  SubscriptionDto,
  SubscriptionSummary,
  GetSubscriptionsParams,
  SubscriptionsResponse,
  CreateSubscriptionRequest,
  UpdateSubscriptionRequest,
} from './subscriptionApiService';

// Export types from template service
export type {
  TemplateDto,
  GetTemplatesParams,
  TemplatesResponse,
  CreateTemplateRequest,
  UpdateTemplateRequest,
  GroupedTemplate,
} from './templateApiService';

// Export types from tenant service
export type {
  TenantDto,
  GetTenantsParams,
  TenantsResponse,
  CreateTenantRequest,
  UpdateTenantRequest,
  TenantApiResponse,
} from './tenantApiService';

// Export types from subscription creation service
export type {
  CreateProductStructureRequest,
  CreateProductStructureResponse,
  SubscriptionCreationWorkflowRequest,
  SubscriptionCreationWorkflowResponse,
} from './subscriptionCreationService';

// Export types from dual API service
export type {
  ApiEndpoint,
  ApiServiceConfig,
} from './dualApiService';

// Convenience object for accessing all services
export const apiServices = {
  subscription: subscriptionApiService,
  template: templateApiService,
  tenant: tenantApiService,
  subscriptionCreation: subscriptionCreationService,
  dualApi: dualApiService,
} as const;

// Service factory for dependency injection or testing
export class ApiServiceFactory {
  private static services = new Map<string, any>();

  /**
   * Register a service instance
   */
  static register<T>(name: string, service: T): void {
    this.services.set(name, service);
  }

  /**
   * Get a service instance
   */
  static get<T>(name: string): T {
    const service = this.services.get(name);
    if (!service) {
      throw new Error(`Service '${name}' not found. Make sure it's registered.`);
    }
    return service;
  }

  /**
   * Check if a service is registered
   */
  static has(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Clear all registered services
   */
  static clear(): void {
    this.services.clear();
  }

  /**
   * Get all registered service names
   */
  static getServiceNames(): string[] {
    return Array.from(this.services.keys());
  }
}

// Register default services
ApiServiceFactory.register('subscription', subscriptionApiService);
ApiServiceFactory.register('template', templateApiService);
ApiServiceFactory.register('tenant', tenantApiService);
ApiServiceFactory.register('subscriptionCreation', subscriptionCreationService);
ApiServiceFactory.register('dualApi', dualApiService);

// Utility functions for common operations
export const ApiUtils = {
  /**
   * Get all loading states from all services
   */
  getAllLoadingStates(): Record<string, any> {
    return {
      subscription: {
        getSubscriptions: subscriptionApiService.getLoadingState('getSubscriptions'),
        createSubscription: subscriptionApiService.getLoadingState('createSubscription'),
        updateSubscription: subscriptionApiService.getLoadingState('updateSubscription'),
        deleteSubscription: subscriptionApiService.getLoadingState('deleteSubscription'),
      },
      template: {
        getTemplates: templateApiService.getLoadingState('getTemplates'),
        createTemplate: templateApiService.getLoadingState('createTemplate'),
        updateTemplate: templateApiService.getLoadingState('updateTemplate'),
        deleteTemplate: templateApiService.getLoadingState('deleteTemplate'),
      },
      tenant: {
        getTenants: tenantApiService.getLoadingState('getTenants'),
        createTenant: tenantApiService.getLoadingState('createTenant'),
        updateTenant: tenantApiService.getLoadingState('updateTenant'),
      },
      subscriptionCreation: {
        createWorkflow: subscriptionCreationService.getLoadingState?.('createWorkflow') || { isLoading: false, error: null },
      },
    };
  },

  /**
   * Clear all loading states from all services
   */
  clearAllLoadingStates(): void {
    // Clear subscription service states
    ['getSubscriptions', 'createSubscription', 'updateSubscription', 'deleteSubscription'].forEach(op => {
      subscriptionApiService.clearLoadingState(op);
    });

    // Clear template service states
    ['getTemplates', 'createTemplate', 'updateTemplate', 'deleteTemplate'].forEach(op => {
      templateApiService.clearLoadingState(op);
    });

    // Clear tenant service states
    ['getTenants', 'createTenant', 'updateTenant'].forEach(op => {
      tenantApiService.clearLoadingState(op);
    });

    // Clear subscription creation service states
    if (subscriptionCreationService.clearLoadingState) {
      subscriptionCreationService.clearLoadingState('createWorkflow');
    }
  },

  /**
   * Check if any service is currently loading
   */
  isAnyServiceLoading(): boolean {
    const loadingStates = this.getAllLoadingStates();
    
    for (const serviceStates of Object.values(loadingStates)) {
      for (const state of Object.values(serviceStates as Record<string, any>)) {
        if (state.isLoading) {
          return true;
        }
      }
    }
    
    return false;
  },

  /**
   * Get all current errors from all services
   */
  getAllErrors(): Record<string, any> {
    const loadingStates = this.getAllLoadingStates();
    const errors: Record<string, any> = {};

    for (const [serviceName, serviceStates] of Object.entries(loadingStates)) {
      const serviceErrors: Record<string, any> = {};
      
      for (const [operation, state] of Object.entries(serviceStates as Record<string, any>)) {
        if (state.error) {
          serviceErrors[operation] = state.error;
        }
      }
      
      if (Object.keys(serviceErrors).length > 0) {
        errors[serviceName] = serviceErrors;
      }
    }

    return errors;
  },

  /**
   * Format error message for display
   */
  formatErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.data?.message) {
      return error.data.message;
    }
    
    return 'An unexpected error occurred';
  },
};

// Export default services for backward compatibility
export default apiServices;
