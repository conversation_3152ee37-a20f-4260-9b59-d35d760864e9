using Abstraction.Database.Repositories;
using Application.ActionManagement.DTOs;
using Application.ActionManagement.Specifications;
using Application.DisplayActionManagement.DTOs;
using Application.DisplayActionManagement.Specifications;
using Application.DisplayManagement.DTOs;
using Application.DisplayManagement.Specifications;
using Domain.Entities;
using Finbuckle.MultiTenant;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;
using System.Diagnostics;

namespace Application.DisplayManagement.Commands;

/// <summary>
/// Upsert Display with Actions command handler - hierarchical structure
/// </summary>
public class UpsertDisplayWithActionsCommandHandler : IRequestHandler<UpsertDisplayWithActionsCommand, Result<DisplayWithActionsResponseDto>>
{
    private readonly IRepository<Display> _displayRepository;
    private readonly IRepository<Domain.Entities.Action> _actionRepository;
    private readonly IRepository<DisplayAction> _displayActionRepository;
    private readonly IRepository<Domain.Entities.Object> _objectRepository;
    private readonly ITenantInfo _tenantInfo;
    private readonly ILogger<UpsertDisplayWithActionsCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertDisplayWithActionsCommandHandler(
        IRepository<Display> displayRepository,
        IRepository<Domain.Entities.Action> actionRepository,
        IRepository<DisplayAction> displayActionRepository,
        IRepository<Domain.Entities.Object> objectRepository,
        ITenantInfo tenantInfo,
        ILogger<UpsertDisplayWithActionsCommandHandler> logger)
    {
        _displayRepository = displayRepository;
        _actionRepository = actionRepository;
        _displayActionRepository = displayActionRepository;
        _objectRepository = objectRepository;
        _tenantInfo = tenantInfo;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<DisplayWithActionsResponseDto>> Handle(UpsertDisplayWithActionsCommand request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting hierarchical upsert operation for Display: {DisplayName} with {ActionCount} actions", 
                request.Name, request.Actions.Count);

            // Step 1: Validate all ObjectIds exist
            var objectIds = request.Actions.Select(a => a.ObjectId).Distinct().ToList();
            await ValidateObjectsExistAsync(objectIds, cancellationToken);

            // Step 2: Upsert Display
            var (display, displayWasCreated) = await UpsertDisplayAsync(request, cancellationToken);

            // Step 3: Process all Actions and DisplayActions
            var actionResults = new List<ActionResultDto>();
            
            foreach (var actionDto in request.Actions)
            {
                var actionResult = await ProcessActionAsync(actionDto, display.Id, cancellationToken);
                actionResults.Add(actionResult);
            }

            stopwatch.Stop();

            // Step 4: Build response
            var response = new DisplayWithActionsResponseDto
            {
                DisplayId = display.Id,
                Display = display.Adapt<DisplayDto>(),
                ActionResults = actionResults,
                DisplayWasCreated = displayWasCreated,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds,
                OperationSummary = BuildOperationSummary(displayWasCreated, actionResults)
            };

            _logger.LogInformation("Hierarchical upsert completed successfully in {ElapsedMs}ms. Display: {DisplayOperation}, Actions: {ActionCount}",
                stopwatch.ElapsedMilliseconds,
                displayWasCreated ? "Created" : "Updated",
                actionResults.Count);

            return Result<DisplayWithActionsResponseDto>.Success(response);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error occurred during hierarchical upsert operation for Display: {DisplayName}",
                request.Name);
            
            return Result<DisplayWithActionsResponseDto>.Failure($"An error occurred during the upsert operation: {ex.Message}");
        }
    }

    /// <summary>
    /// Validate that all ObjectIds exist
    /// </summary>
    private async Task ValidateObjectsExistAsync(List<Guid> objectIds, CancellationToken cancellationToken)
    {
        foreach (var objectId in objectIds)
        {
            var existingObject = await _objectRepository.GetByIdAsync(objectId, cancellationToken);
            if (existingObject == null)
            {
                throw new ArgumentException($"Object with ID '{objectId}' not found.");
            }
        }
    }

    /// <summary>
    /// Upsert Display entity
    /// </summary>
    private async Task<(Display display, bool wasCreated)> UpsertDisplayAsync(UpsertDisplayWithActionsCommand request, CancellationToken cancellationToken)
    {
        var existingDisplay = await _displayRepository.GetBySpecAsync(
            new DisplayByNameSpec(request.Name), cancellationToken);

        if (existingDisplay != null)
        {
            // Update existing display
            existingDisplay.Description = request.Description;
            existingDisplay.DisplayName = request.DisplayName;
            existingDisplay.IsDefault = request.IsDefault;
            existingDisplay.RouteTemplate = request.RouteTemplate;
            existingDisplay.Icon = request.Icon;
            existingDisplay.SortOrder = request.SortOrder;
            existingDisplay.IsActive = request.IsActive;
            await _displayRepository.UpdateAsync(existingDisplay, cancellationToken);
            return (existingDisplay, false);
        }
        else
        {
            // Create new display
            var newDisplay = new Display
            {
                Name = request.Name,
                Description = request.Description,
                DisplayName = request.DisplayName,
                IsDefault = request.IsDefault,
                RouteTemplate = request.RouteTemplate,
                Icon = request.Icon,
                SortOrder = request.SortOrder,
                IsActive = request.IsActive,
            };

            var createdDisplay = await _displayRepository.AddAsync(newDisplay, cancellationToken);
            return (createdDisplay, true);
        }
    }

    /// <summary>
    /// Process Action and DisplayAction for a single action
    /// </summary>
    private async Task<ActionResultDto> ProcessActionAsync(ActionWithDisplayActionDto actionDto, Guid displayId, CancellationToken cancellationToken)
    {
        // Upsert Action
        var (action, actionWasCreated) = await UpsertActionAsync(actionDto, cancellationToken);

        // Upsert DisplayAction relationship
        var (displayAction, displayActionWasCreated) = await UpsertDisplayActionAsync(actionDto, displayId, action.Id, cancellationToken);

        return new ActionResultDto
        {
            ActionId = action.Id,
            DisplayActionId = displayAction.Id,
            Action = action.Adapt<ActionDto>(),
            DisplayAction = displayAction.Adapt<DisplayActionDto>(),
            ActionWasCreated = actionWasCreated,
            DisplayActionWasCreated = displayActionWasCreated
        };
    }

    /// <summary>
    /// Upsert Action entity
    /// </summary>
    private async Task<(Domain.Entities.Action action, bool wasCreated)> UpsertActionAsync(ActionWithDisplayActionDto actionDto, CancellationToken cancellationToken)
    {
        var existingAction = await _actionRepository.GetBySpecAsync(
            new ActionByNameSpec(actionDto.Name), cancellationToken);

        if (existingAction != null)
        {
            // Update existing action
            existingAction.Description = actionDto.Description;
            existingAction.EndpointTemplate = actionDto.EndpointTemplate;
            existingAction.NavigationTarget = actionDto.NavigationTarget;
            existingAction.Icon = actionDto.Icon;
            existingAction.ButtonStyle = actionDto.ButtonStyle;
            existingAction.ConfirmationMessage = actionDto.ConfirmationMessage;
            existingAction.SuccessMessage = actionDto.SuccessMessage;
            existingAction.ErrorMessage = actionDto.ErrorMessage;
            existingAction.IsActive = actionDto.IsActive;

            await _actionRepository.UpdateAsync(existingAction, cancellationToken);
            return (existingAction, false);
        }
        else
        {
            // Create new action
            var newAction = new Domain.Entities.Action
            {
                Name = actionDto.Name,
                Description = actionDto.Description,
                EndpointTemplate = actionDto.EndpointTemplate,
                NavigationTarget = actionDto.NavigationTarget,
                Icon = actionDto.Icon,
                ButtonStyle = actionDto.ButtonStyle,
                ConfirmationMessage = actionDto.ConfirmationMessage,
                SuccessMessage = actionDto.SuccessMessage,
                ErrorMessage = actionDto.ErrorMessage,
                IsActive = actionDto.IsActive,
            };

            var createdAction = await _actionRepository.AddAsync(newAction, cancellationToken);
            return (createdAction, true);
        }
    }

    /// <summary>
    /// Upsert DisplayAction relationship
    /// </summary>
    private async Task<(DisplayAction displayAction, bool wasCreated)> UpsertDisplayActionAsync(
        ActionWithDisplayActionDto actionDto, Guid displayId, Guid actionId, CancellationToken cancellationToken)
    {
        var existingDisplayAction = await _displayActionRepository.GetBySpecAsync(
            new DisplayActionByObjectDisplayActionSpec(actionDto.ObjectId, displayId, actionId), cancellationToken);

        if (existingDisplayAction != null)
        {
            // Update existing DisplayAction
            existingDisplayAction.AccessLevel = actionDto.AccessLevel;
            existingDisplayAction.IsDefault = actionDto.IsDefault;
            existingDisplayAction.SortOrder = actionDto.SortOrder;
            existingDisplayAction.IsVisibleInToolbar = actionDto.IsVisibleInToolbar;
            existingDisplayAction.IsVisibleInContextMenu = actionDto.IsVisibleInContextMenu;
            existingDisplayAction.IsVisibleInRowActions = actionDto.IsVisibleInRowActions;
            existingDisplayAction.IsActive = actionDto.DisplayActionIsActive;

            await _displayActionRepository.UpdateAsync(existingDisplayAction, cancellationToken);
            return (existingDisplayAction, false);
        }
        else
        {
            // Create new DisplayAction
            var newDisplayAction = new DisplayAction
            {
                ObjectId = actionDto.ObjectId,
                DisplayId = displayId,
                ActionId = actionId,
                AccessLevel = actionDto.AccessLevel,
                IsDefault = actionDto.IsDefault,
                SortOrder = actionDto.SortOrder,
                IsVisibleInToolbar = actionDto.IsVisibleInToolbar,
                IsVisibleInContextMenu = actionDto.IsVisibleInContextMenu,
                IsVisibleInRowActions = actionDto.IsVisibleInRowActions,
                IsActive = actionDto.DisplayActionIsActive,
            };

            var createdDisplayAction = await _displayActionRepository.AddAsync(newDisplayAction, cancellationToken);
            return (createdDisplayAction, true);
        }
    }

    /// <summary>
    /// Build operation summary message
    /// </summary>
    private static string BuildOperationSummary(bool displayWasCreated, List<ActionResultDto> actionResults)
    {
        var operations = new List<string>();

        if (displayWasCreated) operations.Add("Display created");
        else operations.Add("Display updated");

        var actionsCreated = actionResults.Count(r => r.ActionWasCreated);
        var actionsUpdated = actionResults.Count(r => !r.ActionWasCreated);
        var displayActionsCreated = actionResults.Count(r => r.DisplayActionWasCreated);
        var displayActionsUpdated = actionResults.Count(r => !r.DisplayActionWasCreated);

        operations.Add($"{actionsCreated} actions created, {actionsUpdated} actions updated");
        operations.Add($"{displayActionsCreated} relationships created, {displayActionsUpdated} relationships updated");

        return string.Join(", ", operations);
    }
}
