using Application.DataTransformation.DTOs;
using Application.DataTransformation.Services;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.DataTransformation.Commands;

/// <summary>
/// Handler for TransformDataCommand
/// </summary>
public class TransformDataCommandHandler : IRequestHandler<TransformDataCommand, Result<DataTransformationResultDto>>
{
    private readonly IDataTransformationService _dataTransformationService;
    private readonly ILogger<TransformDataCommandHandler> _logger;

    public TransformDataCommandHandler(
        IDataTransformationService dataTransformationService,
        ILogger<TransformDataCommandHandler> logger)
    {
        _dataTransformationService = dataTransformationService;
        _logger = logger;
    }

    public async Task<Result<DataTransformationResultDto>> Handle(TransformDataCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing data transformation request for API: {ApiName}", request.ApiName);

            var result = await _dataTransformationService.TransformDataAsync(
                request.ApiName,
                request.JsonData,
                request.TenantId,
                request.UserId,
                cancellationToken);

            if (result.Succeeded)
            {
                _logger.LogInformation(
                    "Data transformation completed successfully for API: {ApiName}. Created {Count} ObjectValues",
                    request.ApiName, result.Data.ObjectValuesCreated);
            }
            else
            {
                _logger.LogWarning(
                    "Data transformation failed for API: {ApiName}. Error: {Error}",
                    request.ApiName, result.Message);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in TransformDataCommandHandler for API: {ApiName}", request.ApiName);
            return Result<DataTransformationResultDto>.Failure($"Command handling failed: {ex.Message}");
        }
    }
}
