/**
 * Dual API Service
 * Centralized service for managing calls to both Primary and Secondary APIs
 */

import { API_CONFIG } from '../../config/apiConfig';
import { HttpClientFactory } from '../httpClient';

/**
 * API Service Types
 */
export type ApiEndpoint = 'primary' | 'secondary';

/**
 * API Service Configuration
 */
export interface ApiServiceConfig {
  endpoint: ApiEndpoint;
  path: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  tenant?: string;
}

/**
 * Dual API Service Class
 * Handles routing requests to the appropriate API endpoint
 */
export class DualApiService {
  /**
   * Get the currently selected tenant from localStorage
   */
  private getCurrentTenant(): string | undefined {
    try {
      return localStorage.getItem('selectedTenantId') || undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * Get the full URL for an API call
   */
  private getFullUrl(config: ApiServiceConfig): string {
    const baseUrl = config.endpoint === 'primary' 
      ? API_CONFIG.PRIMARY.BASE_URL 
      : API_CONFIG.SECONDARY.BASE_URL;
    
    return `${baseUrl}${config.path}`;
  }

  /**
   * Get headers for API call
   */
  private getHeaders(config: ApiServiceConfig): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...config.headers
    };

    // Add tenant header if specified
    if (config.tenant) {
      headers['tenant'] = config.tenant;
    }

    return headers;
  }

  /**
   * Make API call
   */
  async call<T = any>(config: ApiServiceConfig, data?: any): Promise<T> {
    const headers = this.getHeaders(config);
    const method = config.method || 'GET';

    try {
      // Get the appropriate HTTP client based on endpoint
      const httpClient = config.endpoint === 'primary'
        ? HttpClientFactory.primaryClient
        : HttpClientFactory.secondaryClient;

      // Make the API call using the HTTP client
      const response = await httpClient.request<T>(config.path, {
        method,
        headers,
        body: method !== 'GET' ? JSON.stringify(data) : undefined,
        tenant: config.tenant
      });

      return response.data;
    } catch (error) {
      console.error(`API call failed [${config.endpoint}]:`, {
        path: config.path,
        method,
        error
      });
      throw error;
    }
  }

  /**
   * Primary API calls (Port 7222)
   */
  async primary<T = any>(path: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data?: any, headers?: Record<string, string>, tenant?: string): Promise<T> {
    return this.call<T>({
      endpoint: 'primary',
      path,
      method,
      headers,
      tenant
    }, data);
  }

  /**
   * Secondary API calls (Port 7243)
   */
  async secondary<T = any>(path: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data?: any, headers?: Record<string, string>, tenant?: string): Promise<T> {
    return this.call<T>({
      endpoint: 'secondary',
      path,
      method,
      headers,
      tenant
    }, data);
  }
}

/**
 * Singleton instance
 */
export const dualApiService = new DualApiService();

/**
 * API Endpoint Routing Configuration
 * Documents which operations use which API endpoint
 */
export const API_ROUTING = {
  // PRIMARY API (Port 7222) Operations
  PRIMARY_OPERATIONS: [
    'GET /subscriptions',
    'GET /subscriptions/{id}',
    'GET /subscriptions/stats',
    'GET /subscriptions/search',
    'GET /templates',
    'GET /templates/{id}',
    'GET /templates/live',
    'GET /templates/stage/{stage}',
    'POST /templates',
    'PUT /templates',
    'DELETE /templates/{id}',
    'GET /comprehensive-entity',
    'GET /objectvalues',
    'GET /fieldmappings',
    'POST /tenants/upsert',
    'POST /comprehensive-entity/create-product-structure',
    'POST /subscriptions'
  ],

  // SECONDARY API (Port 7243) Operations
  SECONDARY_OPERATIONS: [
    'GET /tenants',
    'GET /tenants/{id}',
    'POST /tenants/{id}/validate',
    'POST /tenants',
    'PUT /tenants'
  ]
} as const;

/**
 * Helper functions for specific operations
 */
export const apiOperations = {
  // Subscription operations (Primary API)
  subscriptions: {
    getAll: (params?: any, tenant?: string) => {
      // Get current tenant from localStorage if not provided
      const getCurrentTenant = () => {
        try {
          return localStorage.getItem('selectedTenantId') || undefined;
        } catch {
          return undefined;
        }
      };
      const finalTenant = tenant || getCurrentTenant();
      console.log('📋 Getting subscriptions with tenant:', finalTenant);
      return dualApiService.primary('/subscriptions', 'GET', params, {}, finalTenant);
    },
    getById: (id: string, tenant?: string) => {
      // Get current tenant from localStorage if not provided
      const getCurrentTenant = () => {
        try {
          return localStorage.getItem('selectedTenantId') || undefined;
        } catch {
          return undefined;
        }
      };
      const finalTenant = tenant || getCurrentTenant();
      console.log('📄 Getting subscription by ID with tenant:', finalTenant);
      return dualApiService.primary(`/subscriptions/${id}`, 'GET', {}, {}, finalTenant);
    },
    getStats: (tenant?: string) => {
      // Get current tenant from localStorage if not provided
      const getCurrentTenant = () => {
        try {
          return localStorage.getItem('selectedTenantId') || undefined;
        } catch {
          return undefined;
        }
      };
      const finalTenant = tenant || getCurrentTenant();
      console.log('📊 Getting subscription stats with tenant:', finalTenant);
      return dualApiService.primary('/subscriptions/stats', 'GET', {}, {}, finalTenant);
    },
    search: (query: string, tenant?: string) => {
      // Get current tenant from localStorage if not provided
      const getCurrentTenant = () => {
        try {
          return localStorage.getItem('selectedTenantId') || undefined;
        } catch {
          return undefined;
        }
      };
      const finalTenant = tenant || getCurrentTenant();
      console.log('🔍 Searching subscriptions with tenant:', finalTenant);
      return dualApiService.primary('/subscriptions/search', 'GET', { query }, {}, finalTenant);
    }
  },

  // Template operations (Primary API)
  templates: {
    getAll: (params?: any) => dualApiService.primary('/templates', 'GET', params),
    getById: (id: string) => dualApiService.primary(`/templates/${id}`, 'GET'),
    getLive: () => dualApiService.primary('/templates/live', 'GET'),
    getByStage: (stage: string) => dualApiService.primary(`/templates/stage/${stage}`, 'GET'),
    create: (data: any) => dualApiService.primary('/templates', 'POST', data),
    update: (data: any) => dualApiService.primary('/templates', 'PUT', data),
    delete: (id: string) => dualApiService.primary(`/templates/${id}`, 'DELETE')
  },

  // Tenant operations (Mixed API)
  tenants: {
    getAll: (params?: any) => dualApiService.secondary('/tenants', 'GET', params),
    getById: (id: string) => dualApiService.secondary(`/tenants/${id}`, 'GET'),
    create: (data: any) => dualApiService.secondary('/tenants', 'POST', data),
    upsert: (data: any) => dualApiService.primary('/tenants/upsert', 'POST', data), // Use Primary API (Port 7222)
    update: (data: any) => dualApiService.secondary('/tenants', 'PUT', data),
    validate: (id: string) => dualApiService.secondary(`/tenants/${id}/validate`, 'POST')
  },

  // Subscription creation workflow (Primary API)
  subscriptionCreation: {
    createTenant: (data: any) => dualApiService.secondary('/tenants', 'POST', data),
    createProductStructure: (data: any, tenant?: string) => {
      // Get current tenant from localStorage if not provided
      const getCurrentTenant = () => {
        try {
          return localStorage.getItem('selectedTenantId') || undefined;
        } catch {
          return undefined;
        }
      };
      const finalTenant = tenant || getCurrentTenant();
      console.log('🏗️ Creating product structure with tenant:', finalTenant);
      return dualApiService.primary('/comprehensive-entity/create-product-structure', 'POST', data, {}, finalTenant);
    },
    createSubscription: (data: any, tenant?: string) => {
      // Get current tenant from localStorage if not provided
      const getCurrentTenant = () => {
        try {
          return localStorage.getItem('selectedTenantId') || undefined;
        } catch {
          return undefined;
        }
      };
      const finalTenant = tenant || getCurrentTenant();
      console.log('🎫 Creating subscription with tenant:', finalTenant);
      return dualApiService.primary('/subscriptions', 'POST', data, {}, finalTenant);
    }
  }
};

/**
 * Default export
 */
export default dualApiService;
