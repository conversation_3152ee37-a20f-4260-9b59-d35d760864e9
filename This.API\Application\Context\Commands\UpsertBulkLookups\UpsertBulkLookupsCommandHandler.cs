using Application.Context.Commands.UpsertBulkLookups;
using Application.Context.Specifications;
using Domain.Entities;
using Abstraction.Database.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Common.Response;

namespace Application.Context.Commands.UpsertBulkLookups;

/// <summary>
/// Handler for UpsertBulkLookupsCommand
/// </summary>
public class UpsertBulkLookupsCommandHandler : IRequestHandler<UpsertBulkLookupsCommand, Result<UpsertBulkLookupsResponse>>
{
    private readonly IRepository<Lookup> _lookupRepository;
    private readonly IRepository<Domain.Entities.Context> _contextRepository;
    private readonly ILogger<UpsertBulkLookupsCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpsertBulkLookupsCommandHandler(
        IRepository<Lookup> lookupRepository,
        IRepository<Domain.Entities.Context> contextRepository,
        ILogger<UpsertBulkLookupsCommandHandler> logger)
    {
        _lookupRepository = lookupRepository;
        _contextRepository = contextRepository;
        _logger = logger;
    }

    /// <summary>
    /// Handle the command
    /// </summary>
    public async Task<Result<UpsertBulkLookupsResponse>> Handle(UpsertBulkLookupsCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var response = new UpsertBulkLookupsResponse();

            // Validate that the context exists
            var context = await _contextRepository.GetByIdAsync(request.ContextId, cancellationToken);
            if (context == null)
            {
                response.Errors.Add($"Context with ID '{request.ContextId}' not found.");
                return Result<UpsertBulkLookupsResponse>.Failure("Context not found.");
            }

            // Get existing lookups for this context
            var spec = new LookupsByContextIdSpec(request.ContextId, includeInactive: true);
            var existingLookups = await _lookupRepository.ListAsync(spec, cancellationToken);

            var defaultLookupId = Guid.Empty;
            var hasNewDefault = false;

            foreach (var lookupRequest in request.Lookups)
            {
                try
                {
                    Lookup lookup;

                    if (lookupRequest.Id.HasValue)
                    {
                        // Update existing lookup
                        lookup = existingLookups.FirstOrDefault(l => l.Id == lookupRequest.Id.Value);
                        if (lookup == null)
                        {
                            response.Errors.Add($"Lookup with ID '{lookupRequest.Id}' not found.");
                            continue;
                        }

                        lookup.Value = lookupRequest.Value;
                        lookup.IsDefault = lookupRequest.IsDefault;
                        lookup.Value1 = lookupRequest.Value1;
                        lookup.Value2 = lookupRequest.Value2;
                        lookup.ShowSequence = lookupRequest.ShowSequence;
                        lookup.IsActive = lookupRequest.IsActive;
                        lookup.ModifiedAt = DateTime.UtcNow;

                        await _lookupRepository.UpdateAsync(lookup, cancellationToken);
                        response.UpdatedCount++;
                    }
                    else
                    {
                        // Create new lookup
                        lookup = new Lookup
                        {
                            Id = Guid.NewGuid(),
                            ContextId = request.ContextId,
                            Value = lookupRequest.Value,
                            IsDefault = lookupRequest.IsDefault,
                            Value1 = lookupRequest.Value1,
                            Value2 = lookupRequest.Value2,
                            ShowSequence = lookupRequest.ShowSequence,
                            IsActive = lookupRequest.IsActive,
                            IsDeleted = false,
                            CreatedAt = DateTime.UtcNow
                        };

                        await _lookupRepository.AddAsync(lookup, cancellationToken);
                        response.CreatedCount++;
                    }

                    response.LookupIds.Add(lookup.Id);

                    // Track if we have a new default
                    if (lookupRequest.IsDefault)
                    {
                        if (hasNewDefault)
                        {
                            response.Errors.Add("Multiple lookups cannot be set as default. Only the last one will be kept as default.");
                        }
                        defaultLookupId = lookup.Id;
                        hasNewDefault = true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing lookup: {Value}", lookupRequest.Value);
                    response.Errors.Add($"Error processing lookup '{lookupRequest.Value}': {ex.Message}");
                }
            }

            // Ensure only one default lookup exists
            if (hasNewDefault)
            {
                var allContextLookups = await _lookupRepository.ListAsync(spec, cancellationToken);
                foreach (var lookup in allContextLookups.Where(l => l.Id != defaultLookupId && l.IsDefault))
                {
                    lookup.IsDefault = false;
                    lookup.ModifiedAt = DateTime.UtcNow;
                    await _lookupRepository.UpdateAsync(lookup, cancellationToken);
                }
            }

            _logger.LogInformation("Bulk upsert completed. Created: {CreatedCount}, Updated: {UpdatedCount}, Errors: {ErrorCount}",
                response.CreatedCount, response.UpdatedCount, response.Errors.Count);

            return Result<UpsertBulkLookupsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk lookup upsert for ContextId: {ContextId}", request.ContextId);
            return Result<UpsertBulkLookupsResponse>.Failure("An error occurred during bulk lookup upsert.");
        }
    }
}
