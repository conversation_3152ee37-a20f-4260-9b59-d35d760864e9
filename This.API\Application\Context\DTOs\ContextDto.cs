namespace Application.Context.DTOs;

/// <summary>
/// DTO for Context entity
/// </summary>
public class ContextDto
{
    /// <summary>
    /// Context ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Name of the context
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the context
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Category for grouping contexts
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Whether the context is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the context is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Created date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified date
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by user ID
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}

/// <summary>
/// DTO for TenantContext entity
/// </summary>
public class TenantContextDto
{
    /// <summary>
    /// TenantContext ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Tenant ID
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Name of the tenant context
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Optional description of the tenant context
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Category for grouping tenant contexts
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Whether the tenant context is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the tenant context is deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Created date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid? CreatedBy { get; set; }

    /// <summary>
    /// Modified date
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Modified by user ID
    /// </summary>
    public Guid? ModifiedBy { get; set; }
}

/// <summary>
/// DTO for Context lookup/dropdown
/// </summary>
public class ContextLookupDto
{
    /// <summary>
    /// Context ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Context name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Context category
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Whether the context is active
    /// </summary>
    public bool IsActive { get; set; }
}

/// <summary>
/// DTO for TenantContext lookup/dropdown
/// </summary>
public class TenantContextLookupDto
{
    /// <summary>
    /// TenantContext ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// TenantContext name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// TenantContext category
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// Whether the tenant context is active
    /// </summary>
    public bool IsActive { get; set; }
}
