import 'package:flutter/material.dart';
import '../../models/field_metadata.dart';
import 'widgets.dart'; // This imports all the widget exports

/// Enhanced dynamic form field widget that supports all core components
class DynamicFormField extends StatelessWidget {
  final FieldMetadata metadata;
  final dynamic value;
  final Function(dynamic) onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool disabled;
  final bool readOnly;
  final String? Function(dynamic)? customValidation;

  const DynamicFormField({
    super.key,
    required this.metadata,
    required this.value,
    required this.onChanged,
    this.onValidation,
    this.disabled = false,
    this.readOnly = false,
    this.customValidation,
  });

  @override
  Widget build(BuildContext context) {
    Widget field;
    final inputType = metadata.inputType.toLowerCase();

    // Handle different input types with comprehensive support
    switch (inputType) {
      case 'text':
        field = ThisTextInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          placeholder: metadata.placeholder,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          minLength: metadata.minLength,
          maxLength: metadata.maxLength,
          pattern: metadata.validationPattern,
          helpText: metadata.helpText,
          customValidation: customValidation != null ? (val) => customValidation!(val) : null,
        );
        break;

      case 'textarea':
        field = ThisTextareaInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          placeholder: metadata.placeholder,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          minLength: metadata.minLength,
          maxLength: metadata.maxLength,
          helpText: metadata.helpText,
          customValidation: customValidation != null ? (val) => customValidation!(val) : null,
        );
        break;

      case 'email':
        field = ThisEmailInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          placeholder: metadata.placeholder,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
          customValidation: customValidation != null ? (val) => customValidation!(val) : null,
        );
        break;

      case 'phone':
        field = ThisPhoneInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          placeholder: metadata.placeholder,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
          customValidation: null, // Phone validation handled internally
        );
        break;

      case 'number':
        field = ThisNumberInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          placeholder: metadata.placeholder,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
          customValidation: customValidation != null ? (val) => customValidation!(val) : null,
        );
        break;

      case 'currency':
        field = ThisCurrencyInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          placeholder: metadata.placeholder,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
          customValidation: customValidation != null ? (val) => customValidation!(val) : null,
        );
        break;

      case 'percentage':
        field = ThisPercentageInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          placeholder: metadata.placeholder,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
          customValidation: customValidation != null ? (val) => customValidation!(val) : null,
        );
        break;

      case 'checkbox':
        field = ThisCheckboxInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: _parseCheckboxValue(value),
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
          options: _getCheckboxOptions(),
        );
        break;

      case 'radio':
        field = ThisRadioInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
          options: _getRadioOptions(),
        );
        break;

      case 'dropdown':
      case 'select':
        field = ThisDropdownInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          placeholder: metadata.placeholder,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
          options: _getDropdownOptions(),
        );
        break;

      case 'time':
        field = ThisTimeInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: _parseTimeValue(value),
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
        );
        break;

      case 'year':
        field = ThisYearInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: _parseIntValue(value),
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
        );
        break;

      case 'month':
        field = ThisMonthInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: _parseIntValue(value),
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
        );
        break;

      case 'day':
        field = ThisDayInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: _parseIntValue(value),
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: metadata.helpText,
        );
        break;

      case 'datetime-local':
      case 'date':
        field = _buildDatePicker(context);
        break;

      default:
        // Fallback to text input for unknown types
        field = ThisTextInput(
          id: metadata.id,
          label: metadata.displayLabel,
          value: value?.toString() ?? '',
          onChanged: (val) => onChanged(val),
          onValidation: onValidation,
          placeholder: metadata.placeholder,
          required: metadata.isRequired,
          disabled: disabled || metadata.isReadonly,
          readOnly: readOnly,
          helpText: 'Unsupported field type: $inputType',
          customValidation: customValidation != null ? (val) => customValidation!(val) : null,
        );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: field,
    );
  }

  /// Parse checkbox value from dynamic input
  List<String> _parseCheckboxValue(dynamic value) {
    if (value == null) return [];
    if (value is List<String>) return value;
    if (value is List) return value.map((e) => e.toString()).toList();
    if (value is String) {
      if (value.isEmpty) return [];
      // Handle comma-separated values
      return value.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
    }
    if (value is bool) return value ? ['true'] : [];
    return [value.toString()];
  }

  /// Get checkbox options from metadata
  List<CheckboxOption> _getCheckboxOptions() {
    if (metadata.values == null) {
      // For boolean checkboxes
      return [
        CheckboxOption(value: 'true', label: metadata.displayLabel),
      ];
    }

    return metadata.values!.map((value) {
      if (value is Map<String, dynamic>) {
        return CheckboxOption(
          value: value['value']?.toString() ?? '',
          label: value['label']?.toString() ?? value['value']?.toString() ?? '',
        );
      }
      return CheckboxOption(
        value: value.toString(),
        label: value.toString(),
      );
    }).toList();
  }

  /// Get radio options from metadata
  List<RadioOption> _getRadioOptions() {
    if (metadata.values == null) return [];

    return metadata.values!.map((value) {
      if (value is Map<String, dynamic>) {
        return RadioOption(
          value: value['value']?.toString() ?? '',
          label: value['label']?.toString() ?? value['value']?.toString() ?? '',
        );
      }
      return RadioOption(
        value: value.toString(),
        label: value.toString(),
      );
    }).toList();
  }

  /// Get dropdown options from metadata
  List<DropdownOption> _getDropdownOptions() {
    if (metadata.values == null) return [];

    return metadata.values!.map((value) {
      if (value is Map<String, dynamic>) {
        return DropdownOption(
          value: value['value']?.toString() ?? '',
          label: value['label']?.toString() ?? value['value']?.toString() ?? '',
        );
      }
      return DropdownOption(
        value: value.toString(),
        label: value.toString(),
      );
    }).toList();
  }

  /// Parse time value from dynamic input
  TimeOfDay? _parseTimeValue(dynamic value) {
    if (value == null) return null;
    if (value is TimeOfDay) return value;
    if (value is String) {
      try {
        final parts = value.split(':');
        if (parts.length >= 2) {
          final hour = int.parse(parts[0]);
          final minute = int.parse(parts[1]);
          return TimeOfDay(hour: hour, minute: minute);
        }
      } catch (e) {
        // Invalid time format
      }
    }
    return null;
  }

  /// Parse integer value from dynamic input
  int? _parseIntValue(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    if (value is double) return value.round();
    return null;
  }

  /// Build date picker widget
  Widget _buildDatePicker(BuildContext context) {
    return GestureDetector(
      onTap: disabled || metadata.isReadonly || readOnly ? null : () async {
        final picked = await showDatePicker(
          context: context,
          initialDate: _parseDateValue(value) ?? DateTime.now(),
          firstDate: DateTime(1900),
          lastDate: DateTime(2100),
        );
        if (picked != null) {
          onChanged(picked);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: disabled || metadata.isReadonly || readOnly
                  ? Colors.grey
                  : Colors.blue,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    metadata.displayLabel,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatDateValue(value) ?? metadata.placeholder ?? 'Select date',
                    style: TextStyle(
                      fontSize: 16,
                      color: value != null ? Colors.black : Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Parse date value from dynamic input
  DateTime? _parseDateValue(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String) {
      return DateTime.tryParse(value);
    }
    return null;
  }

  /// Format date value for display
  String? _formatDateValue(dynamic value) {
    final date = _parseDateValue(value);
    if (date == null) return null;
    return '${date.day}/${date.month}/${date.year}';
  }
}
